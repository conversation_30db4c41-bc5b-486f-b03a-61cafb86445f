package com.mb.laos.service.impl;

import com.google.api.client.util.Objects;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.WebSocketProperties;
import com.mb.laos.controller.request.NotificationBalanceRequest;
import com.mb.laos.controller.request.NotificationRequest;
import com.mb.laos.controller.request.NotificationSearchRequest;
import com.mb.laos.enums.AlertType;
import com.mb.laos.enums.AnnouncementCode;
import com.mb.laos.enums.BillingType;
import com.mb.laos.enums.CurrencyType;
import com.mb.laos.enums.DrCrType;
import com.mb.laos.enums.EndUserType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.EventStatus;
import com.mb.laos.enums.InformationTemplateType;
import com.mb.laos.enums.NotificationStatus;
import com.mb.laos.enums.NotificationType;
import com.mb.laos.enums.PremiumAccNumberStatus;
import com.mb.laos.enums.Resource;
import com.mb.laos.enums.TransactionType;
import com.mb.laos.enums.TransferTransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.firebase.FCMService;
import com.mb.laos.firebase.FirebaseData;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.AnnouncementType;
import com.mb.laos.model.Customer;
import com.mb.laos.model.CustomerSupport;
import com.mb.laos.model.CustomerSupportPhoneNumber;
import com.mb.laos.model.Event;
import com.mb.laos.model.FileEntry;
import com.mb.laos.model.FileExtra;
import com.mb.laos.model.InformationTemplate;
import com.mb.laos.model.Merchant;
import com.mb.laos.model.MoneyAccount;
import com.mb.laos.model.NotiTransaction;
import com.mb.laos.model.Notification;
import com.mb.laos.model.PremiumAccNumber;
import com.mb.laos.model.ReceiveTransferMoney;
import com.mb.laos.model.SavingAccount;
import com.mb.laos.model.Transaction;
import com.mb.laos.model.TransactionSavingAccount;
import com.mb.laos.model.dto.InformationTemplateContentDTO;
import com.mb.laos.model.dto.NotiTransactionDTO;
import com.mb.laos.model.dto.NotificationDTO;
import com.mb.laos.model.dto.NotificationQualityDTO;
import com.mb.laos.model.search.NotificationSearch;
import com.mb.laos.repository.AnnouncementTypeRepository;
import com.mb.laos.repository.CustomerRepository;
import com.mb.laos.repository.CustomerSupportPhoneNumberRepository;
import com.mb.laos.repository.CustomerSupportRepository;
import com.mb.laos.repository.EventRepository;
import com.mb.laos.repository.InformationTemplateContentRepository;
import com.mb.laos.repository.InformationTemplateRepository;
import com.mb.laos.repository.MerchantRepository;
import com.mb.laos.repository.MoneyAccountRepository;
import com.mb.laos.repository.NotiTransactionRepository;
import com.mb.laos.repository.NotificationRepository;
import com.mb.laos.repository.PremiumAccNumberRepository;
import com.mb.laos.repository.ReceiveTransferMoneyRepository;
import com.mb.laos.repository.SavingAccountRepository;
import com.mb.laos.repository.TransactionRepository;
import com.mb.laos.repository.TransactionSavingAccountRepository;
import com.mb.laos.service.EndUserService;
import com.mb.laos.service.NotificationService;
import com.mb.laos.service.StorageService;
import com.mb.laos.service.mapper.InformationTemplateContentMapper;
import com.mb.laos.service.mapper.NotiTransactionMapper;
import com.mb.laos.service.mapper.NotificationMapper;
import com.mb.laos.util.DateUtil;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.time.temporal.ChronoUnit.DAYS;

@Service // Đánh dấu class này là một Spring Service component
@RequiredArgsConstructor // Tự động tạo constructor với các final fields
public class NotificationServiceImpl implements NotificationService {

    // Loại end user từ properties (USER hoặc CUSTOMER)
    @Value("${notification.end-user-type}")
    private String endUserType;

    // Khoảng thời gian cho phép giữa fromDate và toDate (ngày)
    @Value("${notification.time-between}")
    private int timeBetween;

    // Khoảng thời gian cho phép từ fromDate đến hiện tại (ngày)
    @Value("${notification.timeline-between}")
    private int timelineBetween;

    // Số ngày tối đa cho phép tìm kiếm dữ liệu
    @Value("${notification.max-period-search-data}")
    private int maxPeriodSearchData;

    // URL để lấy image từ Firebase, mặc định là localhost
    @Value("${notification.url-get-image-firebase:localhost}")
    private String urlGetImageFirebase;

    // Service để gửi Firebase Cloud Messaging
    private final FCMService fcmService;

    // Repository để thao tác với bảng Notification trong database
    private final NotificationRepository notificationRepository;

    // Repository để thao tác với bảng NotiTransaction trong database
    private final NotiTransactionRepository notiTransactionRepository;

    // Mapper để chuyển đổi giữa Notification entity và NotificationDTO
    private final NotificationMapper notificationMapper;

    // Properties chứa cấu hình WebSocket
    private final WebSocketProperties webSocketProperties;

    // Template để gửi message qua WebSocket
    private final SimpMessagingTemplate simpMessagingTemplate;

    // Service để xử lý thông tin end user
    private final EndUserService endUserService;

    // Repository để thao tác với bảng Transaction trong database
    private final TransactionRepository transactionRepository;

    // Repository để thao tác với bảng Event trong database
    private final EventRepository eventRepository;

    // Repository để thao tác với bảng AnnouncementType trong database
    private final AnnouncementTypeRepository announcementTypeRepository;

    // Mapper để chuyển đổi giữa NotiTransaction entity và NotiTransactionDTO
    private final NotiTransactionMapper notiTransactionMapper;

    // Service để xử lý file storage
    private final StorageService storageService;
    // Repository để thao tác với bảng Merchant trong database
    private final MerchantRepository merchantRepository;
    // Repository để thao tác với bảng MoneyAccount trong database
    private final MoneyAccountRepository moneyAccountRepository;
    // Repository để thao tác với bảng Customer trong database
    private final CustomerRepository customerRepository;
    // Repository để thao tác với bảng SavingAccount trong database
    private final SavingAccountRepository savingAccountRepository;
    // Repository để thao tác với bảng TransactionSavingAccount trong database
    private final TransactionSavingAccountRepository transactionSavingAccountRepository;
    // Repository để thao tác với bảng ReceiveTransferMoney trong database
    private final ReceiveTransferMoneyRepository receiveTransferMoneyRepository;
    // Repository để thao tác với bảng PremiumAccNumber trong database
    private final PremiumAccNumberRepository premiumAccNumberRepository;
    // Repository để thao tác với bảng CustomerSupport trong database
    private final CustomerSupportRepository customerSupportRepository;
    // Repository để thao tác với bảng CustomerSupportPhoneNumber trong database
    private final CustomerSupportPhoneNumberRepository customerSupportPhoneNumberRepository;
    // Repository để thao tác với bảng InformationTemplate trong database
    private final InformationTemplateRepository informationTemplateRepository;
    // Mapper để chuyển đổi giữa InformationTemplateContent entity và DTO
    private final InformationTemplateContentMapper informationTemplateContentMapper;
    // Repository để thao tác với bảng InformationTemplateContent trong database
    private final InformationTemplateContentRepository informationTemplateContentRepository;


    @Override
    public void subscribeTopic(List<String> tokens, String topicName) {
        // Đăng ký danh sách device tokens vào topic Firebase
        this.fcmService.subscribeTopic(tokens, topicName);
    }

    @Override
    public void unsubscribeTopic(List<String> tokens, String topicName) {
        // Hủy đăng ký danh sách device tokens khỏi topic Firebase
        this.fcmService.unsubscribeTopic(tokens, topicName);
    }

    @Override
    public Page<NotificationDTO> getNotification(NotificationSearchRequest request) {
        // Tạo đối tượng Pageable cho phân trang
        Pageable pageable = PageRequest.of(request.getPageIndex(), request.getPageSize());

        // Khởi tạo danh sách notification
        List<Notification> page = new ArrayList<>();

        // Khởi tạo số lượng notification
        long count = 0;

        // Lấy ID của end user hiện tại
        Long endUserId = this.endUserService.getClassPk();

        // Lấy classPk theo username, endUserType và status SUCCESS (đã gửi)
        if (Validator.equals(this.endUserType, EndUserType.USER.toString())) {
            // Nếu end user type là USER
            NotificationSearch notificationSearch = NotificationSearch.builder()
                    .notificationStatus(NotificationStatus.SUCCESS) // Chỉ lấy notification đã gửi thành công
                    .endUserType(EndUserType.USER) // End user type là USER
                    .classPk(endUserId) // ID của user hiện tại
                    .build();

            // Lấy danh sách notification theo điều kiện tìm kiếm và phân trang
            page = this.notificationRepository.getNotification(notificationSearch, pageable);
            // Đếm tổng số notification thỏa mãn điều kiện
            count = this.notificationRepository.count(notificationSearch);
        } else {
            // Nếu end user type là CUSTOMER
            NotificationSearch notificationSearch = NotificationSearch.builder()
                    .notificationStatus(NotificationStatus.SUCCESS) // Chỉ lấy notification đã gửi thành công
                    .endUserType(EndUserType.CUSTOMER) // End user type là CUSTOMER
                    .classPk(endUserId) // ID của customer hiện tại
                    .build();

            // Tìm danh sách event ID theo loại alert
            List<Long> eventIds = this.findEventIdsByAlertType(request.getAlertType());

            // Nếu alert type là OTHER thì thêm điều kiện đặc biệt
            if (Validator.equals(request.getAlertType(), AlertType.OTHER)) {
                // Thêm event ID = 0 (notification không có event)
                eventIds.add(0L);
                // Set thời gian từ (maxPeriodSearchData ngày trước + 1)
                notificationSearch.setFromTime(InstantUtil.getInstantFromLocalDateTime
                        (LocalDateUtil.getStartOfDay(LocalDate.now().minusDays(this.maxPeriodSearchData).plusDays(1)), Labels.getDefaultZoneId()));
                // Set thời gian đến (cuối ngày hôm nay)
                notificationSearch.setToTime(InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getEndOfDay(LocalDate.now()), Labels.getDefaultZoneId()));
            }

            // Set danh sách event ID vào điều kiện tìm kiếm
            notificationSearch.setEventIds(eventIds);

            // Lấy danh sách notification theo điều kiện tìm kiếm và phân trang
            page = this.notificationRepository.getNotification(notificationSearch, pageable);
            // Đếm tổng số notification thỏa mãn điều kiện
            count = this.notificationRepository.count(notificationSearch);
        }

        // Sắp xếp danh sách notification theo thời gian publish giảm dần (mới nhất trước)
        List<Notification> notifications = page.stream()
                .sorted(Comparator.comparing(Notification::getPublishTime).reversed())
                .collect(Collectors.toList());

        // Chuyển đổi danh sách entity thành DTO
        List<NotificationDTO> notificationDTOS = this.notificationMapper.toDto(notifications);
        // Enrich URL icon cho notification
        enrichUrl(notificationDTOS);

        // Trả về Page object chứa danh sách DTO và thông tin phân trang
        return new PageImpl<>(notificationDTOS, pageable, count);
    }

    // Method private để enrich URL icon cho danh sách notification
    private void enrichUrl(List<NotificationDTO> notificationDTOS) {
        // Lấy danh sách file icon từ storage theo event ID
        List<FileEntry> fileEntries = this.storageService.getFileEntries(Event.class.getName(), notificationDTOS.stream().map(NotificationDTO::getEventId).collect(Collectors.toList()), Resource.ICON);
        // Duyệt qua từng file entry
        for (FileEntry fileEntry : fileEntries) {
            // Duyệt qua từng notification DTO
            for (NotificationDTO notificationDTO : notificationDTOS) {
                // Nếu class PK của file entry trùng với event ID của notification
                if (fileEntry.getClassPk().equals(notificationDTO.getEventId())) {
                    // Set icon URL theo format: urlGetImageFirebase/fileId/normalizeName/classPk
                    notificationDTO.setIconUrl(String.format("%s/%s/%s/%s", urlGetImageFirebase, fileEntry.getFileId(), fileEntry.getNormalizeName(), fileEntry.getClassPk()));
                }
            }
        }
    }

    @Override
    public Page<NotificationDTO> getNotificationBalance(NotificationBalanceRequest request, Pageable pageable) {
        // Giới hạn truy vấn trong khoảng thời gian (kiểm tra khoảng cách giữa fromDate và toDate)
        long betweenDay = DAYS.between(request.getFromDate(), request.getToDate());

        // Nếu khoảng thời gian vượt quá giới hạn cho phép thì throw exception
        if (betweenDay > this.timeBetween - 1) {
            throw new BadRequestAlertException(ErrorCode.MSG1096);
        }

        // Giới hạn truy vấn trong mốc thời gian (kiểm tra khoảng cách từ fromDate đến hiện tại)
        long timelineDay = DAYS.between(request.getFromDate(), LocalDate.now());

        // Nếu mốc thời gian vượt quá giới hạn cho phép thì throw exception
        if (timelineDay > this.timelineBetween - 1) {
            throw new BadRequestAlertException(ErrorCode.MSG1148);
        }

        // Khởi tạo danh sách notification
        List<Notification> page = new ArrayList<>();

        // Khởi tạo số lượng notification
        long count = 0;

        // Lấy ID của end user hiện tại
        Long endUserId = this.endUserService.getClassPk();

        // Lấy classPk theo username, endUserType và status SUCCESS (đã gửi)
        if (Validator.equals(this.endUserType, EndUserType.USER.toString())) {
            // Nếu end user type là USER
            NotificationSearch notificationSearch = NotificationSearch.builder()
                    .notificationStatus(NotificationStatus.SUCCESS) // Chỉ lấy notification đã gửi thành công
                    .endUserType(EndUserType.USER) // End user type là USER
                    .notificationTypes(Collections.singletonList(NotificationType.TRANSFER_TRANSACTION)) // Chỉ lấy notification giao dịch chuyển tiền
                    .fromTime(InstantUtil.getInstantFromLocalDateTime(
                            LocalDateUtil.getStartOfDay(request.getFromDate()), Labels.getDefaultZoneId())) // Thời gian bắt đầu từ đầu ngày fromDate
                    .toTime(InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getEndOfDay(request.getToDate()),
                            Labels.getDefaultZoneId())) // Thời gian kết thúc đến cuối ngày toDate
                    .classPk(endUserId) // ID của user hiện tại
                    .build();

            // Lấy danh sách notification theo điều kiện tìm kiếm và phân trang
            page = this.notificationRepository.getNotification(notificationSearch, pageable);

            // Đếm tổng số notification thỏa mãn điều kiện
            count = this.notificationRepository.count(notificationSearch);
        } else {
            // Nếu end user type là CUSTOMER
            NotificationSearch notificationSearch = NotificationSearch.builder()
                    .notificationStatus(NotificationStatus.SUCCESS) // Chỉ lấy notification đã gửi thành công
                    .endUserType(EndUserType.CUSTOMER) // End user type là CUSTOMER
                    .notificationTypes(Collections.singletonList(NotificationType.TRANSFER_TRANSACTION)) // Chỉ lấy notification giao dịch chuyển tiền
                    .fromTime(InstantUtil.getInstantFromLocalDateTime(
                            LocalDateUtil.getStartOfDay(request.getFromDate()), Labels.getDefaultZoneId())) // Thời gian bắt đầu từ đầu ngày fromDate
                    .toTime(InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getEndOfDay(request.getToDate()),
                            Labels.getDefaultZoneId())) // Thời gian kết thúc đến cuối ngày toDate
                    .classPk(endUserId) // ID của customer hiện tại
                    .build();

            // Lấy danh sách notification theo điều kiện tìm kiếm và phân trang
            page = this.notificationRepository.getNotification(notificationSearch, pageable);

            // Đếm tổng số notification thỏa mãn điều kiện
            count = this.notificationRepository.count(notificationSearch);
        }

        // Sắp xếp danh sách notification theo thời gian publish giảm dần (mới nhất trước)
        List<Notification> notifications = page.stream()
                .sorted(Comparator.comparing(Notification::getPublishTime).reversed())
                .collect(Collectors.toList());

        // Chuyển đổi danh sách entity thành DTO
        List<NotificationDTO> notificationDTOS = this.notificationMapper.toDto(notifications);

        // Enrich thông tin transaction cho notification balance
        this.enrichInfoOfTransaction(notificationDTOS);
        // Trả về Page object chứa danh sách DTO và thông tin phân trang
        return new PageImpl<>(notificationDTOS, pageable, count);
    }

    /**
     * Tìm event theo loại thông báo hiển thị trên app
     *
     * @param alertType Loại alert (ADVERTISEMENT hoặc OTHER)
     * @return Danh sách event ID
     */
    public List<Long> findEventIdsByAlertType(AlertType alertType) {
        // Khởi tạo danh sách announcement codes
        List<AnnouncementCode> announcementCodes;
        // Nếu alert type là ADVERTISEMENT
        if (Objects.equal(alertType, AlertType.ADVERTISEMENT)) {
            // Lấy announcement codes cho quảng cáo và sự kiện
            announcementCodes = Arrays.asList(
                    AnnouncementCode.ADVERTISING_ANNOUNCEMENT, // Thông báo quảng cáo
                    AnnouncementCode.EVEN_ANNOUNCEMENT); // Thông báo sự kiện
        } else {
            // Nếu alert type không phải ADVERTISEMENT thì lấy các loại khác
            announcementCodes = Arrays.asList(
                    AnnouncementCode.SECURITY_ANNOUNCEMENT, // Thông báo bảo mật
                    AnnouncementCode.OTHER_ANNOUNCEMENT, // Thông báo khác
                    AnnouncementCode.ACCOUNT_UPDATE_ANNOUNCEMENT); // Thông báo cập nhật tài khoản
        }

        // Tìm tất cả announcement type có các code trên đã được tạo và đang ACTIVE
        List<AnnouncementType> announcementTypes = this.announcementTypeRepository
                .findByAnnouncementTypeCodeInAndStatus(announcementCodes, EntityStatus.ACTIVE.getStatus());

        // Lấy danh sách announcement type ID
        List<Long> announcementTypeIds = announcementTypes.stream()
                .map(AnnouncementType::getAnnouncementTypeId)
                .collect(Collectors.toList());

        // Chỉ lấy thông báo hiển thị lên app trong vòng maxPeriodSearchData ngày (thường là 90 ngày)
        List<Event> events = this.eventRepository.findByAnnouncementTypeIdInAndEventStatusAndStatusAndExpectedNotificationAtAfter(
                announcementTypeIds, // Danh sách announcement type ID
                EventStatus.DONE, // Event status phải là DONE
                EntityStatus.ACTIVE.getStatus(), // Entity status phải là ACTIVE
                LocalDateTime.of(LocalDate.now().minusDays(this.maxPeriodSearchData).plusDays(1), LocalTime.MIN) // Thời gian từ maxPeriodSearchData ngày trước + 1
        );

        // Trả về danh sách event ID
        return events.stream().map(Event::getEventId).collect(Collectors.toList());
    }

    @Override
    public Boolean markRead(NotificationRequest notificationRequest) {
        // Kiểm tra notification ID không được null
        if (Validator.isNull(notificationRequest.getNotificationId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1018);
        }

        // Tìm notification theo ID, nếu không tìm thấy thì throw exception
        Notification notification = this.notificationRepository.findById(notificationRequest.getNotificationId())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1090));

        // Chuyển đổi entity thành DTO
        NotificationDTO notificationDTO = this.notificationMapper.toDto(notification);
        // Đánh dấu notification đã đọc
        notificationDTO.readNotification();
        // Lưu notification đã cập nhật vào database
        this.notificationRepository.save(this.notificationMapper.toEntity(notificationDTO));
        // Trả về TRUE để báo thành công
        return Boolean.TRUE;
    }

    @Override
    public void markReadAll() {
        // Lấy ID của end user hiện tại
        Long classPk = this.endUserService.getClassPk();

        // Lấy tất cả notification của user hiện tại với status SUCCESS
        List<Notification> notifications = this.notificationRepository
                .findAllByClassPkAndEndUserTypeAndNotificationStatus(classPk, EndUserType.valueOf(this.endUserType),
                        NotificationStatus.SUCCESS);

        // Chuyển đổi danh sách entity thành DTO
        List<NotificationDTO> notificationDTOS = this.notificationMapper.toDto(notifications);
        // Lọc ra những notification chưa đọc
        List<NotificationDTO> notificationUnread = notificationDTOS.stream().filter(item -> !item.isRead())
                .collect(Collectors.toList());

        // Đánh dấu tất cả notification chưa đọc thành đã đọc
        notificationUnread.forEach(NotificationDTO::readNotification);

        // Lưu tất cả notification đã cập nhật vào database
        this.notificationRepository.saveAll(this.notificationMapper.toEntity(notificationUnread));
    }

    @Override
    public NotificationQualityDTO countNotificationUnread() {
        // Lấy ID của end user hiện tại
        Long classPk = this.endUserService.getClassPk();

        // Lấy tất cả notification của user hiện tại với status SUCCESS
        List<Notification> notifications = this.notificationRepository.findAllByClassPkAndNotificationStatus(
                classPk, NotificationStatus.SUCCESS);

        // Tạo DTO để chứa thông tin số lượng notification
        NotificationQualityDTO notificationQuality = new NotificationQualityDTO();
        // Đếm tổng số notification chưa đọc
        notificationQuality.setTotalQuantity(notifications.stream().filter(item -> !item.isRead()).count());
        // Đếm số notification balance chưa đọc (notification liên quan đến số dư)
        notificationQuality.setBalanceQuantity(notifications.stream().filter(item -> !item.isRead()
                && NotificationType.getNotificationTypeBalance().contains(item.getNotificationType())).count());

        // Trả về DTO chứa thông tin số lượng
        return notificationQuality;
    }

    // Method private để parse notification DTO thành map data
    private Map<String, String> parseDataToMap(NotificationDTO notification) {
        // Tạo map để chứa dữ liệu
        Map<String, String> data = new HashMap<>();

        // Thêm title vào data map
        data.put("title", notification.getTitle());
        // Thêm content vào data map
        data.put("content", notification.getContent());
        // Thêm description vào data map
        data.put("description", notification.getDescription());

        // Trả về data map đã được populate
        return data;
    }

    @Override
    public void sendNotification(NotificationDTO notificationDTO) {
        // Parse notification DTO thành map data
        Map<String, String> data = this.parseDataToMap(notificationDTO);

        // Tạo Firebase data object để gửi notification
        FirebaseData firebaseData = FirebaseData.builder()
                .title(notificationDTO.getTitle()) // Tiêu đề notification
                .body(notificationDTO.getContent()) // Nội dung notification
                .data(data) // Dữ liệu bổ sung
                .build();

        // Gửi notification qua WebSocket tới topic cụ thể của user
        this.simpMessagingTemplate.convertAndSend(
                this.webSocketProperties.getTopicNotification() + notificationDTO.getClassPk(), firebaseData);
    }

    @Override
    public NotificationDTO getDetail(NotificationRequest request) {
        // Kiểm tra request có notification ID không
        if (request.getNotificationId() != null) {
            // Nếu có notification ID thì tìm theo notification ID
            return this.findById(request.getNotificationId());
        } else if (request.getEventId() != null) {
            // Nếu có event ID thì tìm theo event ID
            return this.findByEventId(request.getEventId());
        } else {
            // Nếu không có cả hai thì throw exception
            throw new BadRequestAlertException(ErrorCode.MSG1090);
        }
    }

    // Method private để tìm notification theo event ID
    private NotificationDTO findByEventId(Long eventId) {
        // Lấy ID của end user hiện tại
        Long classPk = this.endUserService.getClassPk();

        // Tìm notification theo event ID và class PK, lấy notification đầu tiên
        Notification notification = this.notificationRepository.findByEventIdAndClassPk(eventId, classPk)
                .stream().findFirst().orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1090));

        // Đánh dấu notification đã đọc
        notification.setRead(true);
        // Lưu notification đã cập nhật vào database
        this.notificationRepository.save(notification);

        // Chuyển đổi entity thành DTO và trả về
        return this.notificationMapper.toDto(notification);
    }

    /**
     * Tìm notification theo ID và status
     *
     * @param id ID của notification
     * @return NotificationDTO
     */
    private NotificationDTO findById(Long id) {
        // Lấy danh sách ngôn ngữ hỗ trợ
        List<Locale> languages = Labels.LOCALES;
        // Lấy ID của end user hiện tại
        Long classPk = this.endUserService.getClassPk();

        // Tìm notification theo ID và class PK, nếu không tìm thấy thì throw exception
        Notification notification = this.notificationRepository.findByNotificationIdAndClassPk(id, classPk)
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1090));
        // Đánh dấu notification đã đọc
        notification.setRead(true);
        // Lưu notification đã cập nhật vào database
        this.notificationRepository.save(notification);

        // Lấy danh sách file icon từ storage theo event ID
        List<FileEntry> fileEntry = storageService.getFileEntries(Event.class.getName(), notification.getEventId(), Resource.ICON);

        // Chuyển đổi entity thành DTO
        NotificationDTO notificationDTO = this.notificationMapper.toDto(notification);

        // Kiểm tra content có chứa thông tin premium account number không
        if (Validator.isNotNull(notificationDTO.getContent())) {
            // Duyệt qua từng ngôn ngữ
            for (Locale language : languages) {
                // Lấy prefix của premium account number theo ngôn ngữ
                String prefix = Labels.getLabels(LabelKey.LABEL_PREMIUM_ACC_NUMBER, language);
                // Nếu content chứa prefix thì enrich content premium account number
                if (Validator.isNotNull(notificationDTO.getContent()) && notificationDTO.getContentNoHtml().contains(prefix)) {
                    this.enrichContentPremiumAccNumber(notificationDTO);
                    break;
                }
            }
        }

        // Tạo notification request để lấy thông tin transaction
        NotificationRequest notificationRequest = new NotificationRequest();
        notificationRequest.setNotificationId(notification.getNotificationId());
        // Lấy thông tin transaction liên quan đến notification
        NotiTransactionDTO notiTransactionDTO = this.getNotiTransaction(notificationRequest);

        // Set thông tin transaction vào notification DTO
        notificationDTO.setNotiTransactionDTO(notiTransactionDTO);

        // Nếu có file icon
        if (Validator.isNotNull(fileEntry)) {
            // Kiểm tra class PK của file entry trùng với event ID
            if (fileEntry.get(0).getClassPk().equals(notificationDTO.getEventId())) {
                // Set icon URL theo format: urlGetImageFirebase/fileId/normalizeName/classPk
                notificationDTO.setIconUrl(String.format("%s/%s/%s/%s", urlGetImageFirebase, fileEntry.get(0).getFileId(),
                        fileEntry.get(0).getNormalizeName(), fileEntry.get(0).getClassPk()));
            }
        }

        // Trả về notification DTO đã được enrich đầy đủ thông tin
        return notificationDTO;
    }

    // Method private để enrich content premium account number
    private void enrichContentPremiumAccNumber(NotificationDTO notificationDTO) {
        // Lấy ngôn ngữ từ request
        String language = Labels.getLanguageFromRequest();
        // Tìm customer theo class PK
        Customer customer = this.customerRepository.findByCustomerId(notificationDTO.getClassPk());
        // Tìm premium account number của customer (không bị xóa hoặc thu hồi)
        PremiumAccNumber premiumAccNumber = this.premiumAccNumberRepository.findByCustomerIdAndStatusNotIn(
                notificationDTO.getClassPk(), Arrays.asList(EntityStatus.DELETED.getStatus(), PremiumAccNumberStatus.REVERT.getValue()));

        // Nếu tìm thấy premium account number
        if (Validator.isNotNull(premiumAccNumber)) {
            // Kiểm tra content có chứa premium account number không
            if (notificationDTO.getContentNoHtml().contains(premiumAccNumber.getPremiumAccNumber())) {
                // Khởi tạo phone number
                String phoneNumber = null;
                // Tìm customer support đang active
                CustomerSupport customerSupport = this.customerSupportRepository.findByStatus(EntityStatus.ACTIVE.getStatus());

                // Nếu tìm thấy customer support
                if (Validator.isNotNull(customerSupport)) {
                    // Lấy danh sách số điện thoại customer support
                    List<CustomerSupportPhoneNumber> customerSupportPhoneNumbers = this.customerSupportPhoneNumberRepository
                            .findAllByCustomerSupportIdAndStatus(customerSupport.getCustomerSupportId(), EntityStatus.ACTIVE.getStatus());

                    // Chuyển đổi thành danh sách string
                    List<String> phoneNumbers = customerSupportPhoneNumbers.stream()
                            .map(CustomerSupportPhoneNumber::getPhoneNumber).collect(Collectors.toList());

                    // Nếu có nhiều số thì join bằng dấu phẩy, nếu chỉ có 1 số thì lấy số đó
                    phoneNumber = phoneNumbers.size() > 1 ? String.join(StringPool.COMMA, phoneNumbers) : phoneNumbers.get(0);
                }

                // Tìm information template cho lucky account number
                InformationTemplate informationTemplate = this.informationTemplateRepository
                        .findByInformationTemplateCodeAndStatus(InformationTemplateType.LUCKY_ACCOUNT_NUMBER.name(), EntityStatus.ACTIVE.getStatus());

                // Nếu tìm thấy information template
                if (Validator.isNotNull(informationTemplate)) {
                    // Format tổng số tiền với currency LAK
                    String totalAmount = StringUtil.formatMoney(premiumAccNumber.getTotalPrice()) + StringPool.SPACE + CurrencyType.LAK.name();
                    // Chuyển đổi payment due date thành LocalDate
                    LocalDate date = premiumAccNumber.getPaymentDueDate().atZone(ZoneId.systemDefault()).toLocalDate();
                    // Tạo formatter cho ngày tháng
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.SHORT_DATE_PATTERN_DASH);
                    // Format payment due date
                    String paymentDueDate = date.format(formatter);

                    // Lấy content template theo ngôn ngữ
                    InformationTemplateContentDTO informationTemplateContent =
                            this.informationTemplateContentMapper
                                    .toDto(this.informationTemplateContentRepository
                                            .findByInformationTemplateIdAndLanguage(informationTemplate.getInformationTemplateId(), language));

                    // Format content với các tham số: paymentDueDate, fullname, premiumAccNumber, totalAmount, phoneNumber, email
                    notificationDTO.setContent(MessageFormat.format(
                            informationTemplateContent.getContent(),
                            paymentDueDate, // Ngày hết hạn thanh toán
                            customer.getFullname().toUpperCase(), // Tên khách hàng viết hoa
                            premiumAccNumber.getPremiumAccNumber(), // Số tài khoản đẹp
                            totalAmount, // Tổng số tiền
                            phoneNumber, // Số điện thoại hỗ trợ
                            Validator.isNotNull(customerSupport) ? customerSupport.getEmail() : null // Email hỗ trợ
                    ));
                }
            } else {
                // Nếu content không chứa premium account number thì set content = null
                notificationDTO.setContent(null);
            }
        } else {
            // Nếu không tìm thấy premium account number thì set content = null
            notificationDTO.setContent(null);
        }
    }

    // Method private để enrich thông tin transaction cho notification (method phức tạp nhất)
    private NotiTransactionDTO enrichInfoOfTransaction(NotificationDTO notificationDTO) {
        // Tạo DTO để chứa thông tin transaction
        NotiTransactionDTO notiTransactionDTO = new NotiTransactionDTO();

        // Tìm noti transaction theo notification ID và status ACTIVE
        Optional<NotiTransaction> notiTransactionOptional = this.notiTransactionRepository
                .findByNotificationIdAndStatus(notificationDTO.getNotificationId(), EntityStatus.ACTIVE.getStatus());

        // Flag để kiểm tra có transaction ID hợp lệ không
        boolean isCheck = false;

        // Nếu có receiveTransferMoneyId thì transactionId == 0L
        if (notiTransactionOptional.isPresent()) {
            long transactionId = notiTransactionOptional.get().getTransactionId();
            // Kiểm tra transaction ID hợp lệ (khác -2L và 0L)
            if (transactionId != -2L && transactionId != 0L) {
                isCheck = true;
            }
        }

        // Sử dụng cho luồng nhận dữ liệu từ T24 để gửi thông báo cộng tiền
        if (notiTransactionOptional.isPresent() && Validator.isNotNull(notiTransactionOptional.get().getReceiveTransferMoneyId())) {
            // Tìm receive transfer money theo ID và status ACTIVE
            ReceiveTransferMoney receiveTransferMoney = this.receiveTransferMoneyRepository
                    .findByReceiveTransferMoneyIdAndStatus(notiTransactionOptional.get().getReceiveTransferMoneyId(), EntityStatus.ACTIVE.getStatus());

            // Nếu tìm thấy receive transfer money
            if (Validator.isNotNull(receiveTransferMoney)) {
                // Set reference ID từ transaction ID
                notiTransactionDTO.setReferenceId(receiveTransferMoney.getTransactionId());
                // Kiểm tra Dr/Cr type để xác định loại giao dịch
                if (Validator.equals(receiveTransferMoney.getDrCr(), DrCrType.C.name())) {
                    // Nếu là Credit thì set beneficiary account và transaction type PLUS
                    notiTransactionDTO.setBeneficiaryAccount(receiveTransferMoney.getCustomerAccountNumber());
                    notiTransactionDTO.setTransactionType(TransactionType.PLUS);
                } else {
                    // Nếu là Debit thì set payment account và transaction type MINUS
                    notiTransactionDTO.setPaymentAccount(receiveTransferMoney.getCustomerAccountNumber());
                    notiTransactionDTO.setTransactionType(TransactionType.MINUS);
                }

                // Set các thông tin cơ bản từ noti transaction
                notiTransactionDTO.setStatus(notiTransactionOptional.get().getStatus());
                notiTransactionDTO.setNotiTransactionId(notiTransactionOptional.get().getNotiTransactionId());
                notiTransactionDTO.setNotificationId(notiTransactionOptional.get().getNotificationId());
                notiTransactionDTO.setReceiveTransferMoneyId(receiveTransferMoney.getReceiveTransferMoneyId());
                // Set currency từ receive transfer money
                notiTransactionDTO.setCurrency(receiveTransferMoney.getTransactionCurrency());
                // Set transfer type là TRANSFER_MONEY
                notiTransactionDTO.setTransferType(TransferType.TRANSFER_MONEY);
                // Set transfer transaction type là INTER_BANK
                notiTransactionDTO.setTransferTransactionType(TransferTransactionType.INTER_BANK);

                // Lấy message cho notification
                String message = getMessage(notificationDTO, true);
                notiTransactionDTO.setMessage(message);
                // Format và set amount
                notiTransactionDTO.setAmount(StringUtil.formatNumberDoubleValue(receiveTransferMoney.getTransactionAmount()));
            }

        }

        // Nếu có noti transaction và có transaction ID hợp lệ
        if (notiTransactionOptional.isPresent() && Validator.equals(true, isCheck)) {
            // Tìm transaction theo transfer transaction ID và status ACTIVE
            Transaction transaction = this.transactionRepository
                    .findByTransferTransactionIdAndStatus(notiTransactionOptional.get().getTransactionId(), EntityStatus.ACTIVE.getStatus());

            // Tìm transaction theo transfer transaction ID và transaction ID
            Optional<Transaction> transactionOptional = this.transactionRepository
                    .findByTransferTransactionIdAndTransactionId(transaction.getTransferTransactionId(), transaction.getTransactionId());

            // Nếu không tìm thấy transaction thì throw exception
            if (!transactionOptional.isPresent()) {
                throw new BadRequestAlertException(ErrorCode.MSG100167);
            }

            // Lấy transaction để xử lý
            Transaction transactionSet = transactionOptional.get();
            // Chuyển đổi noti transaction thành DTO
            notiTransactionDTO = this.notiTransactionMapper.toDto(notiTransactionOptional.get());

            // Xác định beneficiary account number (ưu tiên beneficiaryAccountNumber, nếu null thì dùng target)
            String beneficiaryAccountNumber = Validator.isNull(transactionSet.getBeneficiaryAccountNumber()) ? transactionSet.getTarget() : transactionSet.getBeneficiaryAccountNumber();

            // Tìm money account của beneficiary và payment
            List<MoneyAccount> beneficiaryAccounts = this.moneyAccountRepository.findByAccountNumberAndStatus(beneficiaryAccountNumber, EntityStatus.ACTIVE.getStatus());
            List<MoneyAccount> paymentAccounts = this.moneyAccountRepository.findByAccountNumberAndStatus(transactionSet.getCustomerAccNumber(), EntityStatus.ACTIVE.getStatus());
            // Khởi tạo danh sách customer ID
            List<Long> customerIds = new ArrayList<>();
            List<Long> paymentAccountCustomer = new ArrayList<>();

            // Lấy customer ID từ beneficiary accounts
            if (Validator.isNotNull(beneficiaryAccounts)) {
                beneficiaryAccounts.forEach(item -> customerIds.add(item.getCustomerId()));
            }
            // Lấy customer ID từ payment accounts
            if (Validator.isNotNull(paymentAccounts)) {
                paymentAccounts.forEach(item -> paymentAccountCustomer.add(item.getCustomerId()));
            }

            // Tìm customer theo customer ID (beneficiary)
            Optional<Customer> customer = this.customerRepository.findByActiveStatusAndStatusAndCustomerIdIn(EntityStatus.ACTIVE.getStatus(), EntityStatus.ACTIVE.getStatus(), customerIds);
            // Tìm customer theo customer ID (payment)
            Optional<Customer> paymentCustomer = this.customerRepository.findByActiveStatusAndStatusAndCustomerIdIn(EntityStatus.ACTIVE.getStatus(), EntityStatus.ACTIVE.getStatus(), paymentAccountCustomer);

            // Kiểm tra notification thuộc về customer thực hiện giao dịch
            if (Validator.equals(notificationDTO.getClassPk(), Long.valueOf(transactionSet.getCustomerId()))) {
                // Set transaction type (PLUS/MINUS)
                notiTransactionDTO.setTransactionType(this.getTransactionType(notificationDTO));

                // Khởi tạo merchant
                Merchant merchant = null;

                // Xử lý merchant theo loại giao dịch
                if (Validator.equals(TransferTransactionType.QR_CODE_INTERNAL_MERCHANT, transactionSet.getType())) {
                    // Nếu là QR code internal merchant thì tìm merchant theo merchant code
                    merchant = this.merchantRepository.findByMerchantCodeAndStatusAndParentIdIsNotNull(transactionSet.getTarget(), EntityStatus.ACTIVE.getStatus());

                } else if (Validator.equals(TransferType.INSURANCE.toString(), transactionSet.getTransferType().toString())
                        || Validator.equals(TransferType.CASH_IN.toString(), transactionSet.getTransferType().toString())) {
                    // Nếu là INSURANCE hoặc CASH_IN thì tìm merchant theo account number (parent merchant)
                    merchant = this.merchantRepository
                            .findByMerchantAccountNumberAndStatusAndParentIdNull(transactionSet.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus());
                } else {
                    // Xử lý các loại giao dịch khác
                    List<Merchant> merchants = new ArrayList<>();
                    if (Validator.equals(TransferType.TOPUP.toString(), transactionSet.getTransferType().toString())
                            || Validator.equals(TransferType.BILLING.toString(), transactionSet.getTransferType().toString())
                    ) {
                        // Nếu là TOPUP hoặc BILLING thì tìm merchant theo account number và service type
                        merchants = this.merchantRepository
                                .findAllByMerchantAccountNumberAndServiceTypeAndStatus(
                                        transactionSet.getBeneficiaryAccountNumber(), transactionSet.getTransferType().toString(), EntityStatus.ACTIVE.getStatus());
                    }

                    // Nếu tìm thấy merchants thì lấy merchant đầu tiên
                    if (Validator.isNotNull(merchants)) {
                        merchant = merchants.stream().findFirst().get();
                    }
                }

                // Với thanh toán merchant thì hiển thị tên merchant cho người nhận tiền
                if (Validator.isNotNull(merchant)) {
                    // Set tên merchant làm beneficiary account name
                    notiTransactionDTO.setBeneficiaryAccountName(merchant.getMerchantName());
                } else if (customer.isPresent() && !Validator.equals(transactionSet.getTransferType(), TransferType.PREMIUM_ACCOUNT_NUMBER)) {
                    // Nếu có customer và không phải premium account number thì dùng tên customer
                    notiTransactionDTO.setBeneficiaryAccountName(customer.get().getFullname());
                } else {
                    // Nếu không có merchant và customer thì dùng beneficiary customer name từ transaction
                    notiTransactionDTO.setBeneficiaryAccountName(transaction.getBeneficiaryCustomerName());
                }

                // Set tên tài khoản thanh toán (customer thực hiện giao dịch)
                notiTransactionDTO.setPaymentAccountName(this.customerRepository.findByCustomerIdAndStatus(notificationDTO.getClassPk(), EntityStatus.ACTIVE.getStatus()).get().getFullname());

                // Set beneficiary account (trừ trường hợp premium account number)
                if (!Validator.equals(transactionSet.getTransferType(), TransferType.PREMIUM_ACCOUNT_NUMBER)) {
                    notiTransactionDTO.setBeneficiaryAccount(Validator.isNull(transactionSet.getBeneficiaryAccountNumber()) ? transactionSet.getTarget() : transactionSet.getBeneficiaryAccountNumber());
                }

            } else {
                // Nếu notification không thuộc về customer thực hiện giao dịch (người nhận tiền)
                // Set transaction type là PLUS (cộng tiền)
                notiTransactionDTO.setTransactionType(TransactionType.PLUS);
                // Set tên beneficiary account từ customer
                if (customer.isPresent()) {
                    notiTransactionDTO.setBeneficiaryAccountName(customer.get().getFullname());
                }
                // Set tên payment account từ payment customer
                if (paymentCustomer.isPresent()) {
                    notiTransactionDTO.setPaymentAccountName(paymentCustomer.get().getFullname());
                }
            }

            // Xử lý đặc biệt cho SETTLEMENT (thanh lý tài khoản tiết kiệm)
            if (Validator.equals(transaction.getTransferType(), TransferType.SETTLEMENT)) {
                // Tìm saving account theo saving account number và status INACTIVE
                SavingAccount savingAccount = this.savingAccountRepository
                        .findBySavingAccountNumberAndStatus(transactionOptional.get().getTarget(), EntityStatus.INACTIVE.getStatus());
                // Lấy amount after settlement, nếu null thì = 0
                Long amtAfterSettlement = Validator.isNull(savingAccount) ? 0L : savingAccount.getAmtAfterSettlement();

                // Set amount = saving amount + amount after settlement
                notiTransactionDTO.setAmount(String.valueOf(savingAccount.getSavingAmount() + amtAfterSettlement));
            } else {
                // Xử lý amount cho các loại giao dịch khác
                // Thông báo cộng tiền không cộng phí
                if (Validator.equals(notiTransactionDTO.getTransactionType(), TransactionType.PLUS)) {
                    // Nếu là PLUS thì dùng transaction amount gốc
                    notiTransactionDTO.setAmount(StringUtil.formatNumberDoubleValue(transactionSet.getTransactionAmount()));
                } else {
                    // Nếu là MINUS thì dùng actual transaction amount (có thể bao gồm phí)
                    notiTransactionDTO.setAmount(StringUtil.formatNumberDoubleValue(this.getActualTransactionAmount(transaction, notificationDTO, transactionSet.getCustomerId())));
                }
            }

            // Set các thông tin cơ bản từ transaction
            notiTransactionDTO.setTransactionCode(transactionSet.getTransactionCode()); // Mã giao dịch
            notiTransactionDTO.setCurrency(transactionSet.getTransactionCurrency()); // Loại tiền tệ
            notiTransactionDTO.setBankCode(transactionSet.getBankCode()); // Mã ngân hàng
            notiTransactionDTO.setTransferType(transactionSet.getTransferType()); // Loại chuyển khoản
            notiTransactionDTO.setTransferTransactionType(transactionSet.getType()); // Loại giao dịch chuyển khoản

            // Tìm transaction saving account theo transfer transaction ID
            Optional<TransactionSavingAccount> transactionSavingAccount = this.transactionSavingAccountRepository
                    .findByTransferTransactionIdAndStatus(notiTransactionDTO.getTransactionId(), EntityStatus.ACTIVE.getStatus());
            // Khởi tạo saving account
            SavingAccount savingAccount = null;

            // Kiểm tra điều kiện để lấy saving account
            if ((transactionSavingAccount.isPresent() && Validator.equals(TransactionType.MINUS, notiTransactionDTO.getTransactionType())
                    && Validator.equals(TransferType.SAVING_ACCOUNT, transaction.getTransferType())) // Trường hợp MINUS và SAVING_ACCOUNT
                    || transactionSavingAccount.isPresent() && Validator.equals(TransactionType.PLUS, notiTransactionDTO.getTransactionType()) && Validator.equals(TransferType.SETTLEMENT, transaction.getTransferType())) { // Trường hợp PLUS và SETTLEMENT

                // Tìm saving account theo saving account ID và status khác DELETED
                savingAccount = this.savingAccountRepository
                        .findBySavingAccountIdAndStatusNot(transactionSavingAccount.get().getSavingAccountId(), EntityStatus.DELETED.getStatus());
            }

            // Xử lý beneficiary account cho saving account
            if (Validator.isNotNull(savingAccount)) {
                // Với luồng tiết kiệm mở tài khoản, tài khoản thụ hưởng là tài khoản tiết kiệm
                if (Validator.equals(transactionOptional.get().getTransferType(), TransferType.SAVING_ACCOUNT)
                        || Validator.equals(transactionOptional.get().getTransferType(), TransferType.DEPOSIT_MORE)) {
                    // Set beneficiary account là saving account number
                    notiTransactionDTO.setBeneficiaryAccount(savingAccount.getSavingAccountNumber());
                } else {
                    // Set beneficiary account là receiving account number
                    notiTransactionDTO.setBeneficiaryAccount(savingAccount.getReceivingAccountNumber());
                }
            } else if (!Validator.equals(transaction.getTransferType(), TransferType.PREMIUM_ACCOUNT_NUMBER)) {
                // Nếu không có saving account và không phải premium account number thì dùng beneficiary account number
                notiTransactionDTO.setBeneficiaryAccount(beneficiaryAccountNumber);
            }

            // Set payment account (nếu có saving account và là SETTLEMENT thì dùng saving account number, ngược lại dùng customer account number)
            notiTransactionDTO.setPaymentAccount((Validator.isNotNull(savingAccount) && Validator.equals(TransferType.SETTLEMENT, transaction.getTransferType())) ? savingAccount.getSavingAccountNumber() : transactionSet.getCustomerAccNumber());

            // Set reference ID nếu có transfer transaction type
            if (Validator.isNotNull(notiTransactionDTO.getTransferTransactionType())) {
                // Set reference ID từ transaction ID
                notiTransactionDTO.setReferenceId(transaction.getTransactionId());
            }

            // Lấy message cho notification
            String message = getMessage(notificationDTO, false);
            if (Validator.isNull(message)) {
                // Nếu không có message thì dùng message từ transaction
                notiTransactionDTO.setMessage(transactionOptional.get().getMessage());
            } else {
                // Nếu có message thì dùng message đã lấy
                notiTransactionDTO.setMessage(message);
            }
        } else if (notiTransactionOptional.isPresent() && Validator.equals(notiTransactionOptional.get().getTransactionId(), -2)) {
            // Xử lý trường hợp đặc biệt khi transaction ID = -2

            // Set các thông tin cơ bản từ noti transaction
            notiTransactionDTO.setNotificationId(notiTransactionOptional.get().getNotiTransactionId());
            notiTransactionDTO.setNotificationId(notiTransactionOptional.get().getNotificationId());
            notiTransactionDTO.setStatus(notiTransactionOptional.get().getStatus());
            // Set transaction type là PLUS
            notiTransactionDTO.setTransactionType(TransactionType.PLUS);

            // Lấy amount từ notification content
            String amount = this.getAmount(notificationDTO);
            notiTransactionDTO.setAmount(amount);
            // Set currency mặc định là LAK
            notiTransactionDTO.setCurrency("LAK");
            // Set transfer type là SETTLEMENT
            notiTransactionDTO.setTransferType(TransferType.SETTLEMENT);

            // Lấy beneficiary account từ notification content
            String beneficiaryAccount = this.getBeneficiaryAccount(notificationDTO);
            notiTransactionDTO.setBeneficiaryAccount(beneficiaryAccount);
            // Lấy message cho notification
            String message = getMessage(notificationDTO, true);
            notiTransactionDTO.setMessage(message);
            // Extract payment account từ message
            notiTransactionDTO.setPaymentAccount(StringUtil.extractNumberWithMessage(message));
        }
        // Trả về noti transaction DTO đã được enrich đầy đủ thông tin
        return notiTransactionDTO;
    }

    // Method private để enrich thông tin transaction cho danh sách notification (batch processing)
    private void enrichInfoOfTransaction(List<NotificationDTO> notificationDTOS) {
        // Lấy danh sách notification ID từ notification DTOs
        List<Long> notificationIds = notificationDTOS.stream()
                .map(NotificationDTO::getNotificationId)
                .collect(Collectors.toList());

        // Tìm tất cả noti transaction theo notification IDs và status ACTIVE
        List<NotiTransaction> notiTransactions = this.notiTransactionRepository
                .findAllByNotificationIdInAndStatus(notificationIds, EntityStatus.ACTIVE.getStatus());

        // Lấy danh sách transaction ID từ noti transactions
        List<Long> transactionIds = notiTransactions.stream()
                .map(NotiTransaction::getTransactionId)
                .collect(Collectors.toList());

        // Tìm tất cả transaction theo transfer transaction IDs và status ACTIVE
        List<Transaction> transactions = this.transactionRepository.findAllByTransferTransactionIdInAndStatus(
                transactionIds, EntityStatus.ACTIVE.getStatus());

        // Duyệt qua từng notification DTO để enrich thông tin
        notificationDTOS.forEach(notificationDTO -> {
            // Tìm noti transaction tương ứng với notification
            Optional<NotiTransaction> notiTransactionOptional = notiTransactions.stream()
                    .filter(item -> Validator.equals(Long.valueOf(item.getNotificationId()),
                            notificationDTO.getNotificationId()))
                    .findFirst();

            // Nếu tìm thấy noti transaction
            if (notiTransactionOptional.isPresent()) {
                // Chuyển đổi noti transaction thành DTO và set vào notification
                notificationDTO.setNotiTransactionDTO(this.notiTransactionMapper.toDto(notiTransactionOptional.get()));

                // Tìm transaction tương ứng với noti transaction
                Optional<Transaction> optionalTransaction = transactions.stream()
                        .filter(transaction ->
                                Validator.equals(transaction.getTransferTransactionId(),
                                        notificationDTO.getNotiTransactionDTO().getTransactionId()))
                        .findFirst();

                // Nếu tìm thấy giao dịch => dựa vào transaction để trả ra các giá trị cộng hay trừ tiền, kèm theo
                // các giá trị khác trong transaction
                if (optionalTransaction.isPresent()) {
                    // Lấy transaction để xử lý
                    Transaction transaction = optionalTransaction.get();

                    // Lấy noti transaction DTO để enrich thông tin
                    NotiTransactionDTO notiTransactionDTO = notificationDTO.getNotiTransactionDTO();

                    // Xác định beneficiary account (ưu tiên beneficiaryAccountNumber, nếu null thì dùng target)
                    String beneficiaryAccount = Validator.isNull(transaction.getBeneficiaryAccountNumber()) ? transaction.getTarget() : transaction.getBeneficiaryAccountNumber();

                    // Tìm money account của beneficiary và payment
                    List<MoneyAccount> beneficiaryAccounts = this.moneyAccountRepository.findByAccountNumberAndStatus(beneficiaryAccount, EntityStatus.ACTIVE.getStatus());
                    List<MoneyAccount> paymentAccounts = this.moneyAccountRepository.findByAccountNumberAndStatus(transaction.getCustomerAccNumber(), EntityStatus.ACTIVE.getStatus());
                    // Khởi tạo danh sách customer ID
                    List<Long> customerIds = new ArrayList<>();
                    List<Long> paymentCustomers = new ArrayList<>();

                    // Lấy customer ID từ beneficiary accounts
                    if (Validator.isNotNull(beneficiaryAccounts)) {
                        beneficiaryAccounts.forEach(item -> customerIds.add(item.getCustomerId()));
                    }
                    // Lấy customer ID từ payment accounts
                    if (Validator.isNotNull(paymentAccounts)) {
                        paymentAccounts.forEach(item -> paymentCustomers.add(item.getCustomerId()));
                    }

                    // Tìm customer theo customer ID (beneficiary)
                    Optional<Customer> customer = this.customerRepository.findByActiveStatusAndStatusAndCustomerIdIn(EntityStatus.ACTIVE.getStatus(), EntityStatus.ACTIVE.getStatus(), customerIds);
                    // Tìm customer theo customer ID (payment)
                    Optional<Customer> paymentCustomer = this.customerRepository.findByActiveStatusAndStatusAndCustomerIdIn(EntityStatus.ACTIVE.getStatus(), EntityStatus.ACTIVE.getStatus(), paymentCustomers);

                    // Kiểm tra notification thuộc về customer thực hiện giao dịch
                    if (Validator.equals(notificationDTO.getClassPk(), Long.valueOf(transaction.getCustomerId()))) {
                        // Set transaction type (PLUS/MINUS)
                        notiTransactionDTO.setTransactionType(this.getTransactionType(notificationDTO));

                        // Với thanh toán merchant hiển thị tên merchant cho tài khoản người nhận
                        Merchant merchant = null;

                        // Xử lý merchant theo loại giao dịch
                        if (Validator.equals(TransferTransactionType.QR_CODE_INTERNAL_MERCHANT, transaction.getType())) {
                            // Nếu là QR code internal merchant thì tìm merchant theo merchant code
                            merchant = merchantRepository.findByMerchantCodeAndStatusAndParentIdIsNotNull(
                                    transaction.getTarget(), EntityStatus.ACTIVE.getStatus());
                        } else if (Validator.equals(TransferType.INSURANCE.toString(), transaction.getTransferType().toString())
                                || Validator.equals(TransferType.CASH_IN.toString(), transaction.getTransferType().toString())) {
                            // Nếu là INSURANCE hoặc CASH_IN thì tìm merchant theo account number (parent merchant)
                            merchant = this.merchantRepository
                                    .findByMerchantAccountNumberAndStatusAndParentIdNull(transaction.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus());

                        } else {
                            // Xử lý các loại giao dịch khác
                            List<Merchant> merchants = new ArrayList<>();

                            if (Validator.equals(TransferType.TOPUP.toString(), transaction.getTransferType().toString())
                                    || Validator.equals(TransferType.BILLING.toString(), transaction.getTransferType().toString())) {
                                // Nếu là TOPUP hoặc BILLING thì tìm merchant theo account number và service type
                                merchants = this.merchantRepository
                                        .findAllByMerchantAccountNumberAndServiceTypeAndStatus(
                                                transaction.getBeneficiaryAccountNumber(), transaction.getTransferType().toString(), EntityStatus.ACTIVE.getStatus());
                            }

                            // Nếu tìm thấy merchants thì lấy merchant đầu tiên
                            if (Validator.isNotNull(merchants)) {
                                merchant = merchants.stream().findFirst().get();
                            }
                        }

                        // Set tên của tài khoản thanh toán (customer thực hiện giao dịch)
                        notiTransactionDTO.setPaymentAccountName(this.customerRepository
                                .findByCustomerIdAndStatus(notificationDTO.getClassPk(), EntityStatus.ACTIVE.getStatus()).get().getFullname());

                        // Set tên beneficiary account theo thứ tự ưu tiên
                        if (Validator.isNotNull(merchant)) {
                            // Nếu có merchant thì dùng tên merchant
                            notiTransactionDTO.setBeneficiaryAccountName(merchant.getMerchantName());
                        } else if (customer.isPresent() && !Validator.equals(transaction.getTransferType(), TransferType.PREMIUM_ACCOUNT_NUMBER)) {
                            // Nếu có customer và không phải premium account number thì dùng tên customer
                            notiTransactionDTO.setBeneficiaryAccountName(customer.get().getFullname());
                        } else {
                            // Nếu không có merchant và customer thì dùng beneficiary customer name từ transaction
                            notiTransactionDTO.setBeneficiaryAccountName(transaction.getBeneficiaryCustomerName());
                        }
                    } else {
                        // Nếu notification không thuộc về customer thực hiện giao dịch (người nhận tiền)
                        // Set transaction type là PLUS (cộng tiền)
                        notiTransactionDTO.setTransactionType(TransactionType.PLUS);
                        // Set tên beneficiary account từ customer nếu có
                        customer.ifPresent(value -> notiTransactionDTO.setBeneficiaryAccountName(value.getFullname()));
                        // Set tên payment account từ payment customer nếu có
                        paymentCustomer.ifPresent(value -> notiTransactionDTO.setPaymentAccountName(value.getFullname()));
                    }

                    // Tìm saving account theo target và status INACTIVE (cho SETTLEMENT)
                    SavingAccount savingAccount = this.savingAccountRepository
                            .findBySavingAccountNumberAndStatus(transaction.getTarget(), EntityStatus.INACTIVE.getStatus());
                    // Xử lý amount cho saving account
                    if (Validator.isNotNull(savingAccount)) {
                        // Lấy amount after settlement, nếu null thì = 0
                        Long amtAfterSettlement = Validator.isNull(savingAccount.getAmtAfterSettlement()) ? 0L : savingAccount.getAmtAfterSettlement();

                        // Set amount = transaction amount + amount after settlement
                        notiTransactionDTO.setAmount(String.valueOf(transaction.getTransactionAmount() + amtAfterSettlement));
                    } else {
                        // Xử lý amount cho các loại giao dịch khác
                        if (Validator.equals(notiTransactionDTO.getTransactionType(), TransactionType.PLUS)) {
                            // Nếu là PLUS thì dùng transaction amount gốc
                            notiTransactionDTO.setAmount(StringUtil.formatNumberDoubleValue(transaction.getTransactionAmount()));
                        } else {
                            // Nếu là MINUS thì dùng actual transaction amount (có thể bao gồm phí)
                            notiTransactionDTO.setAmount(StringUtil.formatNumberDoubleValue(this.getActualTransactionAmount(transaction, notificationDTO, transaction.getCustomerId())));
                        }
                    }

                    // Set các thông tin cơ bản từ transaction
                    notiTransactionDTO.setTransactionCode(transaction.getTransactionCode()); // Mã giao dịch
                    notiTransactionDTO.setCurrency(transaction.getTransactionCurrency()); // Loại tiền tệ
                    notiTransactionDTO.setBankCode(transaction.getBankCode()); // Mã ngân hàng
                    notiTransactionDTO.setTransferTransactionType(transaction.getType()); // Loại giao dịch chuyển khoản

                    // Tìm transaction saving account theo transfer transaction ID
                    Optional<TransactionSavingAccount> transactionSavingAccount = this.transactionSavingAccountRepository
                            .findByTransferTransactionIdAndStatus(notiTransactionDTO.getTransactionId(), EntityStatus.ACTIVE.getStatus());
                    // Khởi tạo saving account
                    SavingAccount savingAcct = null;

                    // Kiểm tra điều kiện để lấy saving account
                    if ((transactionSavingAccount.isPresent()
                            && Validator.equals(notiTransactionDTO.getTransactionType(), TransactionType.MINUS) && Validator.equals(TransferType.SAVING_ACCOUNT, transaction.getTransferType())) // Trường hợp MINUS và SAVING_ACCOUNT
                            || (transactionSavingAccount.isPresent() && Validator.equals(notiTransactionDTO.getTransactionType(), TransactionType.PLUS) && Validator.equals(TransferType.SETTLEMENT, transaction.getTransferType())) // Trường hợp PLUS và SETTLEMENT
                    ) {
                        // Tìm saving account theo saving account ID và status khác DELETED
                        savingAcct = this.savingAccountRepository
                                .findBySavingAccountIdAndStatusNot(transactionSavingAccount.get().getSavingAccountId(), EntityStatus.DELETED.getStatus());
                    }

                    // Xử lý beneficiary account cho saving account
                    if (Validator.isNotNull(savingAcct)) {
                        // Với mở mới, tài khoản thụ hưởng là tài khoản tiết kiệm
                        if (Validator.equals(TransferType.SAVING_ACCOUNT, transaction.getTransferType())
                                || Validator.equals(TransferType.DEPOSIT_MORE, transaction.getTransferType())) {
                            // Set beneficiary account là saving account number
                            notiTransactionDTO.setBeneficiaryAccount(savingAcct.getSavingAccountNumber());
                        } else {
                            // Set beneficiary account là receiving account number
                            notiTransactionDTO.setBeneficiaryAccount(savingAcct.getReceivingAccountNumber());
                        }
                    } else if (!Validator.equals(transaction.getTransferType(), TransferType.PREMIUM_ACCOUNT_NUMBER)) {
                        // Nếu không có saving account và không phải premium account number thì dùng beneficiary account
                        notiTransactionDTO.setBeneficiaryAccount(beneficiaryAccount);
                    }

                    // Set payment account (nếu có saving account và là SETTLEMENT thì dùng saving account number, ngược lại dùng customer account number)
                    notiTransactionDTO.setPaymentAccount((Validator.isNotNull(savingAcct) && Validator.equals(TransferType.SETTLEMENT, transaction.getTransferType())) ? savingAcct.getSavingAccountNumber() : transaction.getCustomerAccNumber());

                    // Set reference ID nếu có transfer transaction type
                    if (Validator.isNotNull(notiTransactionDTO.getTransferTransactionType())) {
                        // Set reference ID từ transaction ID
                        notiTransactionDTO.setReferenceId(transaction.getTransactionId());
                    }

                    // Lấy message cho notification
                    String message = getMessage(notificationDTO, false);

                    // Set message
                    if (Validator.isNull(message)) {
                        // Nếu không có message thì dùng message từ transaction
                        notiTransactionDTO.setMessage(transaction.getMessage());
                    } else {
                        // Nếu có message thì dùng message đã lấy
                        notiTransactionDTO.setMessage(message);
                    }

                } else if (notiTransactionOptional.isPresent() && Validator.equals(notiTransactionOptional.get().getTransactionId(), -2)) {
                    // Xử lý trường hợp đặc biệt khi transaction ID = -2
                    // Chuyển đổi noti transaction thành DTO và set vào notification
                    notificationDTO.setNotiTransactionDTO(this.notiTransactionMapper.toDto(notiTransactionOptional.get()));
                    // Lấy noti transaction DTO để enrich thông tin
                    NotiTransactionDTO notiTransactionDTO = notificationDTO.getNotiTransactionDTO();

                    // Set các thông tin cơ bản từ noti transaction
                    notiTransactionDTO.setNotificationId(notiTransactionOptional.get().getNotiTransactionId());
                    notiTransactionDTO.setNotificationId(notiTransactionOptional.get().getNotificationId());
                    notiTransactionDTO.setStatus(notiTransactionOptional.get().getStatus());
                    // Set transaction type là PLUS
                    notiTransactionDTO.setTransactionType(TransactionType.PLUS);

                    // Lấy amount từ notification content
                    String amount = this.getAmount(notificationDTO);
                    notiTransactionDTO.setAmount(amount);
                    // Set currency mặc định là LAK
                    notiTransactionDTO.setCurrency("LAK");
                    // Set transfer type là SETTLEMENT
                    notiTransactionDTO.setTransferType(TransferType.SETTLEMENT);

                    // Lấy beneficiary account từ notification content
                    String beneficiaryAccount = this.getBeneficiaryAccount(notificationDTO);
                    notiTransactionDTO.setBeneficiaryAccount(beneficiaryAccount);
                    // Lấy message cho notification
                    String message = getMessage(notificationDTO, true);
                    notiTransactionDTO.setMessage(message);
                    // Extract payment account từ message
                    notiTransactionDTO.setPaymentAccount(StringUtil.extractNumberWithMessage(message));
                } else if (Validator.isNotNull(notiTransactionOptional.get().getReceiveTransferMoneyId())) {
                    // Thông báo chi tiết cho nhận dữ liệu từ T24 gửi thông báo cộng tiền cho khách hàng
                    // Tìm receive transfer money theo ID và status ACTIVE
                    ReceiveTransferMoney receiveTransferMoney = this.receiveTransferMoneyRepository
                            .findByReceiveTransferMoneyIdAndStatus(notiTransactionOptional.get().getReceiveTransferMoneyId(), EntityStatus.ACTIVE.getStatus());

                    // Chuyển đổi noti transaction thành DTO và set vào notification
                    notificationDTO.setNotiTransactionDTO(this.notiTransactionMapper.toDto(notiTransactionOptional.get()));
                    // Lấy noti transaction DTO để enrich thông tin
                    NotiTransactionDTO notiTransactionDTO = notificationDTO.getNotiTransactionDTO();

                    // Kiểm tra Dr/Cr type để xác định loại giao dịch
                    if (Validator.equals(receiveTransferMoney.getDrCr(), DrCrType.C.name())) {
                        // Nếu là Credit thì set transaction type PLUS và beneficiary account
                        notiTransactionDTO.setTransactionType(TransactionType.PLUS);
                        notiTransactionDTO.setBeneficiaryAccount(receiveTransferMoney.getCustomerAccountNumber());
                    } else {
                        // Nếu là Debit thì set transaction type MINUS và payment account
                        notiTransactionDTO.setTransactionType(TransactionType.MINUS);
                        notiTransactionDTO.setPaymentAccount(receiveTransferMoney.getCustomerAccountNumber());
                    }

                    // Set các thông tin cơ bản từ receive transfer money và noti transaction
                    notiTransactionDTO.setReferenceId(receiveTransferMoney.getTransactionId()); // Reference ID từ transaction ID
                    notiTransactionDTO.setStatus(notiTransactionOptional.get().getStatus()); // Status từ noti transaction
                    notiTransactionDTO.setNotiTransactionId(notiTransactionOptional.get().getNotiTransactionId()); // Noti transaction ID
                    notiTransactionDTO.setNotificationId(notiTransactionOptional.get().getNotificationId()); // Notification ID
                    notiTransactionDTO.setReceiveTransferMoneyId(receiveTransferMoney.getReceiveTransferMoneyId()); // Receive transfer money ID
                    notiTransactionDTO.setCurrency(receiveTransferMoney.getTransactionCurrency()); // Currency từ receive transfer money
                    notiTransactionDTO.setTransferType(TransferType.TRANSFER_MONEY); // Transfer type là TRANSFER_MONEY
                    notiTransactionDTO.setTransferTransactionType(TransferTransactionType.INTER_BANK); // Transfer transaction type là INTER_BANK

                    // Lấy message cho notification
                    String message = getMessage(notificationDTO, true);
                    notiTransactionDTO.setMessage(message);

                    // Format và set amount từ receive transfer money
                    notiTransactionDTO.setAmount(StringUtil.formatNumberDoubleValue(receiveTransferMoney.getTransactionAmount()));
                }
            }
        });

    }

    // Method private để lấy beneficiary account từ notification content
    private String getBeneficiaryAccount(NotificationDTO notificationDTO) {
        // Khởi tạo beneficiary account
        String beneficiaryAccount = null;
        // Lấy danh sách ngôn ngữ hỗ trợ
        List<Locale> languages = Labels.LOCALES;

        // Duyệt qua từng ngôn ngữ để tìm beneficiary account
        for (int i = 0; i < languages.size(); i++) {
            Locale language = languages.get(i);
            // Tạo prefix để tìm kiếm trong content (ví dụ: "Tài khoản thụ hưởng: ")
            String prefix = Labels.getLabels(LabelKey.LABEL_BENEFICIARY_ACCOUNT, language) + StringPool.COLON + StringPool.SPACE;

            // Tìm vị trí bắt đầu của prefix trong content
            int startIndex = notificationDTO.getContentNoHtml().indexOf(prefix);
            // Tìm vị trí kết thúc (dấu chấm) sau prefix
            int endIndex = notificationDTO.getContentNoHtml().indexOf(StringPool.PERIOD, startIndex);

            // Nếu tìm thấy cả start và end index
            if (startIndex != -1 && endIndex != -1) {
                // Extract beneficiary account từ content và trim whitespace
                beneficiaryAccount = notificationDTO.getContentNoHtml().substring(startIndex + prefix.length(), endIndex).trim();
            }
        }
        // Trả về beneficiary account đã extract
        return beneficiaryAccount;
    }

    // Method private để xác định transaction type từ notification content
    private TransactionType getTransactionType(NotificationDTO notificationDTO) {
        // Khởi tạo transaction type
        TransactionType transactionType;

        // Kiểm tra content có chứa dấu "+" không
        if (notificationDTO.getContentNoHtml().contains(StringPool.PLUS)) {
            // Nếu có dấu "+" thì là giao dịch cộng tiền
            transactionType = TransactionType.PLUS;
        } else {
            // Nếu không có dấu "+" thì là giao dịch trừ tiền
            transactionType = TransactionType.MINUS;
        }

        // Trả về transaction type đã xác định
        return transactionType;
    }

    // Method private để lấy amount từ notification content
    private String getAmount(NotificationDTO notificationDTO) {
        // Lấy danh sách ngôn ngữ hỗ trợ
        List<Locale> languages = Labels.LOCALES;
        // Khởi tạo amount
        String amount = "";

        // Duyệt qua từng ngôn ngữ để tìm amount
        for (int i = 0; i < languages.size(); i++) {
            Locale language = languages.get(i);
            // Tạo prefix để tìm kiếm trong content (ví dụ: "Số tiền giao dịch: +")
            String prefix = Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION, language) + StringPool.COLON + StringPool.SPACE + StringPool.PLUS;

            // Tìm vị trí bắt đầu của prefix trong content
            int startIndex = notificationDTO.getContentNoHtml().indexOf(prefix);
            // Tìm vị trí kết thúc (trước " LAK") sau prefix
            int endIndex = notificationDTO.getContentNoHtml().indexOf(StringPool.SPACE + "LAK", startIndex);

            // Nếu tìm thấy cả start và end index
            if (startIndex != -1 && endIndex != -1) {
                // Extract amount từ content và trim whitespace
                amount = notificationDTO.getContentNoHtml().substring(startIndex + prefix.length(), endIndex).trim();
            }
        }
        // Trả về amount đã loại bỏ dấu phẩy
        return amount.replace(StringPool.COMMA, StringPool.BLANK);
    }

    // Method private static để lấy message từ notification content
    private static String getMessage(NotificationDTO notificationDTO, boolean isSavingAccount) {
        // Lấy danh sách ngôn ngữ hỗ trợ
        List<Locale> languages = Labels.LOCALES;
        // Khởi tạo message
        String message = "";
        // Duyệt qua từng ngôn ngữ để tìm message
        for (int i = 0; i < languages.size(); i++) {
            Locale language = languages.get(i);
            // Khởi tạo end index
            Integer endIndex = null;
            // Tạo prefix để tìm kiếm trong content (ví dụ: "Nội dung giao dịch: ")
            String prefix = Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT, language) + StringPool.COLON + StringPool.SPACE;

            // Tìm vị trí bắt đầu của prefix trong content
            int startIndex = notificationDTO.getContentNoHtml().indexOf(prefix);
            // Xác định end index dựa trên loại account
            if (isSavingAccount) {
                // Nếu là saving account thì tìm dấu chấm
                endIndex = notificationDTO.getContentNoHtml().indexOf(StringPool.PERIOD, startIndex);
            } else {
                // Nếu không phải saving account thì tìm " -"
                endIndex = notificationDTO.getContentNoHtml().indexOf(StringPool.SPACE + StringPool.DASH, startIndex);
            }

            // Nếu tìm thấy cả start và end index
            if (startIndex != -1 && endIndex != -1 && Validator.isNotNull(endIndex)) {
                // Extract message từ content và trim whitespace
                message = notificationDTO.getContentNoHtml().substring(startIndex + prefix.length(), endIndex).trim();
                // Thoát khỏi loop khi tìm thấy
                break;
            }
        }
        // Trả về message đã extract
        return message;
    }

    @Override
    public byte[] getIconByte(String classPk, String fileEntryId, String normalizeName) {
        try {
            // Kiểm tra event có tồn tại và không bị xóa
            if (!this.eventRepository.existsByEventIdAndStatusNot(Long.parseLong(classPk),
                    EntityStatus.DELETED.getStatus())) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                                new Object[]{Labels.getLabels(LabelKey.LABEL_NOTIFICATION)}),
                        Event.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST);
            }

            // Lấy file extra từ storage service
            FileExtra file = this.storageService.getFileExtra(Long.valueOf(fileEntryId), normalizeName,
                    Event.class.getName(), Long.parseLong(classPk), Resource.ICON);

            // Chuyển đổi input stream thành byte array và trả về
            return IOUtils.toByteArray(file.getInputStream());
        } catch (Exception e) {
            // Nếu có lỗi thì throw exception
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_NOTIFICATION)}),
                    Event.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST);
        }
    }

    @Override
    public NotiTransactionDTO getNotiTransaction(NotificationRequest request) {
        // Lấy ID của end user hiện tại
        Long classPk = this.endUserService.getClassPk();

        // Khởi tạo noti transaction DTO
        NotiTransactionDTO notiTransactionDTO;

        // Tìm notification theo notification ID và class PK, nếu không tìm thấy thì throw exception
        Notification notification = this.notificationRepository.findByNotificationIdAndClassPk(request.getNotificationId(), classPk)
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1090));

        // Kiểm tra notification type có phải TRANSFER_TRANSACTION không
        if (!notification.getNotificationType().equals(NotificationType.TRANSFER_TRANSACTION)) {
            // Nếu không phải thì trả về null
            return null;
        }

        // Enrich thông tin transaction cho notification và trả về
        notiTransactionDTO = this.enrichInfoOfTransaction(this.notificationMapper.toDto(notification));

        // Trả về noti transaction DTO đã được enrich
        return notiTransactionDTO;
    }

    // Method private để tính actual transaction amount (số tiền thực tế bao gồm phí và chiết khấu)
    private double getActualTransactionAmount(Transaction transaction, NotificationDTO notificationDTO, Long
            customerId) {
        // Lấy transaction amount gốc
        double transactionAmount = transaction.getTransactionAmount();

        // Kiểm tra notification thuộc về customer thực hiện giao dịch
        if (Validator.equals(notificationDTO.getClassPk(), customerId)) {
            // Tính actual transaction amount: amount - discount fixed - discount % + transaction fee
            double actualTransactionAmount = transactionAmount - transaction.getDiscountFixed() - transactionAmount * transaction.getDiscount() / 100 + transaction.getTransactionFee();

            // Xử lý đặc biệt cho billing WATER và ELECTRIC
            if (Validator.equals(transaction.getBillingType(), BillingType.WATER.toString()) || Validator.equals(transaction.getBillingType(), BillingType.ELECTRIC.toString())) {
                // Nếu actual amount < transaction amount thì dùng transaction amount gốc
                if (actualTransactionAmount < transaction.getTransactionAmount()) {
                    actualTransactionAmount = transactionAmount;
                }
            } else if (Validator.equals(transaction.getTransferType(), TransferType.PREMIUM_ACCOUNT_NUMBER)) {
                // Nếu là premium account number thì dùng total amount
                actualTransactionAmount = transaction.getTotalAmount();
            }
            // Format và trả về actual transaction amount
            return StringUtil.formatMoneyCurrencyForeign(actualTransactionAmount);
        }

        // Nếu không phải customer thực hiện giao dịch thì trả về transaction amount gốc
        return StringUtil.formatMoneyCurrencyForeign(transactionAmount);
    }
}
