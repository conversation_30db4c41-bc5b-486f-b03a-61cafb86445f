package com.mb.laos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum NotificationType {
    CHANGE_PASSWORD_PERIODICALLY,

    TRANSFER_TRANSACTION,

    APPROVAL_LOCK_AND_UNLOCK,

    PAYMENT_DEADLINE_LUCKY_ACC_NUMBER,

    SYSTEM,

    OTHER;

    public static List<NotificationType> getNotificationTypeBalance() {
        List<NotificationType> result = new ArrayList<>();
        result.add(NotificationType.TRANSFER_TRANSACTION);
        return result;
    }
}
