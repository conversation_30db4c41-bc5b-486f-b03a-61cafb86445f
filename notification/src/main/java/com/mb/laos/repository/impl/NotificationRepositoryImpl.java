package com.mb.laos.repository.impl;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import com.mb.laos.enums.NotificationType;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Notification;
import com.mb.laos.model.search.NotificationSearch;
import com.mb.laos.repository.extend.NotificationExtend;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;

@Repository
public class NotificationRepositoryImpl implements NotificationExtend {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<Notification> getNotification(NotificationSearch search, Pageable pageable) {
        StringBuilder sql = new StringBuilder(1);
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT e FROM Notification e ");
        sql.append(this.queryNotificationBalance(search, values));
        sql.append(QueryUtil.createOrderQuery(Notification.class, search.getOrderByType(), search.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), Notification.class);

        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

            query.setMaxResults(pageable.getPageSize());
        } else {
            query.setFirstResult(QueryUtil.FIRST_INDEX);

            query.setMaxResults(QueryUtil.MAX_RESULT);
        }

        return query.getResultList();
    }

    @Override
    public Long count(NotificationSearch search) {
        StringBuilder sql = new StringBuilder(1);
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT COUNT(1) FROM Notification e ");
        sql.append(this.queryNotificationBalance(search, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    private String queryNotificationBalance(NotificationSearch notificationSearch, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder(1);

        sql.append(" WHERE 1 = 1 AND e.classPk = :classPk ");
        sql.append(" AND e.endUserType = :endUserType ");
        sql.append(" AND e.notificationStatus = :notificationStatus ");

        if (Validator.isNotNull(notificationSearch.getFromTime())
                && Validator.isNotNull(notificationSearch.getToTime())) {
            sql.append(" AND e.publishTime BETWEEN :fromTime AND :toTime ");

            values.put("fromTime",
                    LocalDateTime.ofInstant(notificationSearch.getFromTime(), Labels.getDefaultZoneId()));

            values.put("toTime", LocalDateTime.ofInstant(notificationSearch.getToTime(), Labels.getDefaultZoneId()));
        }

        if (Validator.isNull(notificationSearch.getNotificationTypes())) {
            sql.append(" AND e.notificationType not in :notificationTypes ");
            values.put("notificationTypes", Collections.singleton(NotificationType.TRANSFER_TRANSACTION));
        } else {
            sql.append(" AND e.notificationType in :notificationTypes ");
            values.put("notificationTypes", notificationSearch.getNotificationTypes());
        }

        if (Validator.isNotNull(notificationSearch.getEventIds())) {
            sql.append(" AND e.eventId in :eventIds ");

            values.put("eventIds", notificationSearch.getEventIds());
        }

        // map value
        values.put("classPk", notificationSearch.getClassPk());
        values.put("endUserType", notificationSearch.getEndUserType());
        values.put("notificationStatus", notificationSearch.getNotificationStatus());

        return sql.toString();
    }
}
