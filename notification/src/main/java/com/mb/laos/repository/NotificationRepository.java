package com.mb.laos.repository;

import com.mb.laos.enums.EndUserType;
import com.mb.laos.enums.NotificationStatus;
import com.mb.laos.enums.NotificationType;
import com.mb.laos.model.Notification;
import com.mb.laos.model.dto.NotificationDTO;
import com.mb.laos.repository.extend.NotificationExtend;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface NotificationRepository extends JpaRepository<Notification, Long>, NotificationExtend {

    List<Notification> findTop500ByNotificationStatusAndNotificationTypeInAndPublishTimeIsBefore(NotificationStatus notificationStatus,
                                                                                                 List<NotificationType> notificationType,
                                                                                                 LocalDateTime now);

    List<Notification> findAllByClassPkAndEndUserTypeAndNotificationTypeAndNotificationStatus(Long classPk,
                                                                                              EndUserType endUserType, NotificationType type, NotificationStatus status);

    List<Notification> findAllByClassPkAndEndUserTypeAndNotificationStatus(Long classPk,
                                                                           EndUserType endUserType, NotificationStatus status);

    Page<Notification> findAllByClassPkAndEndUserTypeAndNotificationStatus(Long classPk, EndUserType endUserType,
                                                                           NotificationStatus status, Pageable pageable);

    Optional<Notification> findByNotificationIdAndClassPk(Long id, Long classPk);

    List<Notification> findByEventIdAndClassPk(Long eventId, Long classPk);

    List<Notification> findAllByClassPkAndNotificationStatus(Long classPk, NotificationStatus status);

    List<Notification> findByClassPkAndEndUserTypeAndNotificationStatus(Long classPk, EndUserType endUserType,
                                                                        NotificationStatus status);

    List<Notification> findAllByEventIdIn(List<Long> eventIds);

    @Query("select new com.mb.laos.model.dto.NotificationDTO(e.eventId, e.notificationStatus, count(e)) from Notification e where e.eventId in :eventIds group by e.eventId, e.notificationStatus")
    List<NotificationDTO> countEventStatus(@Param("eventIds") List<Long> eventIds);
}
