package com.mb.laos.repository;

import com.mb.laos.enums.AnnouncementCode;
import com.mb.laos.model.AnnouncementType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface AnnouncementTypeRepository extends JpaRepository<AnnouncementType, Long> {
    boolean existsByAnnouncementTypeIdAndStatus(Long announcementTypeId, int status);

    Optional<AnnouncementType> findByAnnouncementTypeIdAndStatus(Long announcementTypeId, int status);

    List<AnnouncementType> findByAnnouncementTypeIdInAndStatus(List<Long> announcementTypeIds, int status);

    Optional<AnnouncementType> findByAnnouncementTypeCodeAndStatus(AnnouncementCode announcementCode, int status);

    List<AnnouncementType> findByAnnouncementTypeCodeInAndStatus(List<AnnouncementCode> announcementCodes, int status);

    @Query("from AnnouncementType a where (:keyword is null or a.announcementTypeName" +
            " like %:keyword% ) and a.status = :status")
    Page<AnnouncementType> searchAutoComplete(String keyword, int status, Pageable pageable);
}
