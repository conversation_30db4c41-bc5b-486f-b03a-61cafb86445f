package com.mb.laos.repository;

import com.mb.laos.model.NotiTransaction;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface NotiTransactionRepository extends JpaRepository<NotiTransaction, Long> {
    List<NotiTransaction> findAllByNotificationIdInAndStatus(List<Long> notificationIds, int status);

    Optional<NotiTransaction> findByNotificationIdAndStatus(Long notificationId, int status);
}
