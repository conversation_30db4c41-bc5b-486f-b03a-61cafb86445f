package com.mb.laos.firebase;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.ExecutionException;

import com.google.firebase.messaging.*;
import com.mb.laos.util.StringUtil;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class FCMService {

    private final FirebaseMessaging firebaseMessaging;

    public FCMService(FirebaseMessaging firebaseMessaging) {
        this.firebaseMessaging = firebaseMessaging;
    }

    /**
     * send message to device
     *
     * @param firebaseData
     * @param tokenDevice
     */
    public void sendToDevice(FirebaseData firebaseData, String tokenDevice) {
        String response;

        Notification notification = this.buildNotification(firebaseData);

        Message message = Message.builder()
                .setToken(tokenDevice)
                .setNotification(notification)
                .setApnsConfig(getApnsConfig(firebaseData))
                .setAndroidConfig(getAndroidConfig(firebaseData))
                .putAllData(firebaseData.getData())
                .build();

        try {
            response = sendMessage(message);

            _log.info("Message sent successfully with messageId --->"
                    + response.substring(response.lastIndexOf("/") + 1));
        } catch (ExecutionException e) {
            _log.error("Fail to send firebase notification, ExecutionException", e);
        } catch (InterruptedException e) {
            _log.error("Fail to send firebase notification, InterruptedException", e);

            Thread.currentThread().interrupt();
        } catch (Exception e) {
            _log.error("Error when send firebase notification ", e);
        }
    }

    /**
     * send message to topic
     *
     * @param firebaseData
     * @param topic
     */
    public void sendToTopic(FirebaseData firebaseData, String topic) {
        String response;
        Notification notification = this.buildNotification(firebaseData);

        Message message = Message.builder()
                .setNotification(notification)
                .setApnsConfig(getApnsConfig(firebaseData))
                .setAndroidConfig(getAndroidConfig(firebaseData))
                .setTopic(topic)
                .putAllData(firebaseData.getData())
                .build();

        try {
            response = sendMessage(message);
            _log.info("Message sent successfully to topic --->" + topic + " with messageId -->"
                    + response.substring(response.lastIndexOf("/") + 1));
        } catch (ExecutionException e) {
            _log.error("Fail to send firebase notification, ExecutionException", e);
        } catch (InterruptedException e) {
            _log.error("Fail to send firebase notification, InterruptedException", e);

            Thread.currentThread().interrupt();
        }
    }

    /**
     * subscribe tokens via topic to listen message
     *
     * @param tokens
     * @param topic
     */
    public void subscribeTopic(List<String> tokens, String topic) {
        TopicManagementResponse response;
        try {
            response = this.firebaseMessaging.subscribeToTopic(tokens, topic);
            _log.info("Message sent successfully to topic --->" + topic + " with success count -->"
                    + response.getSuccessCount());
        } catch (FirebaseMessagingException e) {
            _log.error("Exception when subscribe token to topic " + topic + " with error " + e);
        }
    }

    public void unsubscribeTopic(List<String> tokenNeedUnsubscribes, String topic) {
        TopicManagementResponse response;
        try {
            response = this.firebaseMessaging.unsubscribeFromTopic(tokenNeedUnsubscribes, topic);
            _log.info("Unsubscribe token successfully from topic --->" + topic + " with success count -->"
                    + response.getSuccessCount());
        } catch (FirebaseMessagingException e) {
            _log.error("Exception when unsubscribe token to topic " + topic + " with error " + e);
        }
    }

    private Notification buildNotification(FirebaseData firebaseData) {
        Notification.Builder builder = Notification.builder()
                .setTitle(firebaseData.getTitle())
                .setBody(firebaseData.getBody()); // bo html format
        if (firebaseData.getImageUrl() != null) {
            builder.setImage(firebaseData.getImageUrl());
        }
        return builder.build();
    }

    /**
     * send a message
     *
     * @param message
     * @return
     * @throws InterruptedException
     * @throws ExecutionException
     */
    private String sendMessage(Message message) throws InterruptedException, ExecutionException {
        return firebaseMessaging.sendAsync(message).get();
    }

    /**
     * get Android Config
     *
     * @return
     */
    private AndroidConfig getAndroidConfig() {
        return AndroidConfig.builder()
                .setTtl(Duration.ofMinutes(2).toMillis())
                .setPriority(AndroidConfig.Priority.HIGH)
                .setNotification(AndroidNotification.builder().build())
                .build();
    }

    private AndroidConfig getAndroidConfig(FirebaseData firebaseData) {
        AndroidNotification.Builder notiBuilder = AndroidNotification.builder();
        notiBuilder.setTitle(firebaseData.getTitle());
        notiBuilder.setBody(firebaseData.getBody());
        if (firebaseData.getImageUrl() != null) {
            notiBuilder.setImage(firebaseData.getImageUrl());
        }
        AndroidConfig.Builder builder = AndroidConfig.builder()
                .setTtl(Duration.ofMinutes(2).toMillis())
                .setPriority(AndroidConfig.Priority.HIGH)
                .setNotification(notiBuilder.build());
        return builder.build();
    }

    /**
     * get IOS config
     *
     * @param title
     * @param body
     * @return
     */
    private ApnsConfig getApnsConfig(String title, String body) {
        ApsAlert alert = ApsAlert.builder()
                .setTitle(title)
                .setBody(body)
                .build();

        Aps aps = Aps.builder()
                .setAlert(alert)
                .build();

        return ApnsConfig.builder()
                .setAps(aps)
                .build();
    }

    private ApnsConfig getApnsConfig(FirebaseData firebaseData) {
        ApsAlert alert = ApsAlert.builder()
                .setTitle(firebaseData.getTitle())
                .setBody(firebaseData.getBody())
                .build();

        Aps aps = Aps.builder()
                .setAlert(alert)
                .build();

        ApnsFcmOptions.Builder fcmOptionsBuilder = ApnsFcmOptions.builder();
        if (firebaseData.getImageUrl() != null) {
            fcmOptionsBuilder.setImage(firebaseData.getImageUrl());
        }

        return ApnsConfig.builder()
                .setAps(aps)
                .setFcmOptions(fcmOptionsBuilder.build())
                .build();
    }

    /**
     * get Builder of Message with android and ios config
     *
     * @param title
     * @param body
     * @return
     */
    private Message.Builder getMessageBuilder(String title, String body) {
        AndroidConfig androidConfig = getAndroidConfig();
        ApnsConfig apnsConfig = getApnsConfig(title, body);
        return Message.builder()
                .setApnsConfig(apnsConfig)
                .setAndroidConfig(androidConfig);
    }

}
