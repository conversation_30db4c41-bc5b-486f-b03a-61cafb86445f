package com.mb.laos.controller.request;

import com.mb.laos.api.request.Request;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.validator.ValidationConstraint;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class NotificationBalanceClientRequest extends Request {

    @Valid
    @Size(max = ValidationConstraint.LENGTH.NOTIFICATIONS_MAX_LENGTH, message = LabelKey.ERROR_NOTIFICATIONS_IS_MAX_LENGTH)
    @Size(min = ValidationConstraint.LENGTH.NOTIFICATIONS_MIN_LENGTH, message = LabelKey.ERROR_NOTIFICATIONS_IS_MIN_LENGTH)
    private List<NotificationClientRequest> notifications;
}
