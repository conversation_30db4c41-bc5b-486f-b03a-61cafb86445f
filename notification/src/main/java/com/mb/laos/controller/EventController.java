package com.mb.laos.controller;

import com.mb.laos.annotation.MaxSize;
import com.mb.laos.controller.request.EventCreateRequest;
import com.mb.laos.controller.request.EventUpdateRequest;
import com.mb.laos.model.dto.CustomerDTO;
import com.mb.laos.model.dto.EventDTO;
import com.mb.laos.model.search.EventSearch;
import com.mb.laos.model.search.FileEntrySearch;
import com.mb.laos.request.IdRequest;
import com.mb.laos.service.EventService;
import com.mb.laos.util.PropKey;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/events")
@Validated
@RequiredArgsConstructor
public class EventController {
    private final EventService eventService;

    @PostMapping("/search")
    @PreAuthorize("hasPrivilege('NOTIFICATION_READ')")
    public ResponseEntity<Page<EventDTO>> search(@RequestBody EventSearch eventSearch) {
        Page<EventDTO> eventDTOS = eventService.search(eventSearch);
        return ResponseEntity.ok().body(eventDTOS);
    }

    @PostMapping("/create")
    @PreAuthorize("hasPrivilege('NOTIFICATION_CREATE')")
    public ResponseEntity<EventDTO> create(@Valid EventCreateRequest request, @RequestParam(value = "files", required = false)
    @MaxSize(property = PropKey.MAX_SIZE_ATTACHMENT) MultipartFile files) {
        EventDTO eventDTO = eventService.create(request, files);
        return ResponseEntity.ok().body(eventDTO);
    }

    @PostMapping("/delete")
    @PreAuthorize("hasPrivilege('NOTIFICATION_DELETE')")
    public void delete(@RequestBody @Valid IdRequest request) {
        eventService.delete(request.getId());
    }

    @PostMapping("/detail")
    @PreAuthorize("hasPrivilege('NOTIFICATION_READ')")
    public ResponseEntity<EventDTO> getDetail(@RequestBody @Valid IdRequest request) {
        EventDTO eventDTO = eventService.getDetail(request.getId());
        return ResponseEntity.ok().body(eventDTO);
    }

    @PostMapping("/update")
    @PreAuthorize("hasPrivilege('NOTIFICATION_WRITE')")
    public ResponseEntity<EventDTO> update(@Valid EventUpdateRequest request, @RequestParam(name = "files", required = false)
    @MaxSize(property = PropKey.MAX_SIZE_ATTACHMENT, required = false) MultipartFile files) {
        EventDTO eventDTO = eventService.update(request, files);
        return ResponseEntity.ok(eventDTO);
    }

    @PostMapping("/send")
    @PreAuthorize("hasPrivilege('NOTIFICATION_SEND')")
    public void sendNotification(@Valid EventUpdateRequest request, @RequestParam(name = "files", required = false)
    @MaxSize(property = PropKey.MAX_SIZE_ATTACHMENT, required = false) MultipartFile files) {
        eventService.sendNotification(request, files);
    }

    @PostMapping("/upload-customer")
    @PreAuthorize("hasPrivilege('NOTIFICATION_CREATE')")
    public ResponseEntity<List<CustomerDTO>> uploadFileCustomer(HttpServletRequest request,
                                                                @RequestParam("file") @MaxSize(property = PropKey.MAX_SIZE_ATTACHMENT) MultipartFile file) {
        List<CustomerDTO> customerDTOS = eventService.uploadCustomer(file);
        return ResponseEntity.ok().body(customerDTOS);
    }

    @PostMapping("/customer-event/download-template")
    @PreAuthorize("hasPrivilege('NOTIFICATION_CREATE')")
    public ResponseEntity<Void> downloadTemplateCustomer(HttpServletResponse response) {
        this.eventService.downloadTemplateCustomer(response);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/export-receiver")
    @PreAuthorize("hasPrivilege('NOTIFICATION_READ')")
    public ResponseEntity<Void> export(HttpServletResponse response, @RequestBody @Valid IdRequest request) {
        this.eventService.exportReceiver(response, request.getId());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/icon")
    @PreAuthorize("hasPrivilege('NOTIFICATION_READ')")
    public ResponseEntity<InputStreamResource> getIcon(@RequestBody @Valid FileEntrySearch search) {
        return this.eventService.getIcon(search);
    }

    @PostMapping("/download-template")
    @PreAuthorize("hasPrivilege('NOTIFICATION_IMPORT')")
    public ResponseEntity<Void> downloadTemplate(HttpServletResponse response) {
        this.eventService.downloadTemplate(response);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/import-customers")
    @PreAuthorize("hasPrivilege('NOTIFICATION_IMPORT')")
    public ResponseEntity<List<CustomerDTO>> importExcel(HttpServletResponse response, @RequestParam(value = "file") @MaxSize(property = PropKey.MAX_SIZE_IMPORT_NOTI_CUSTOMER) MultipartFile file) {
        return ResponseEntity.ok().body(this.eventService.importCustomers(response, file));
    }

}
