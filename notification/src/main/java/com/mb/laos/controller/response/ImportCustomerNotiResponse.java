package com.mb.laos.controller.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.model.dto.CustomerNotiDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImportCustomerNotiResponse {
    boolean isSuccess;
    Set<CustomerNotiDTO> customerDTOList;
    List<ImportCustomerNotiFailedResponse> customerNotiFailedResponses;
}
