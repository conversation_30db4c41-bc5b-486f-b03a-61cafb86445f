/**
 * 
 */
package com.mb.laos.model;

import java.io.Serializable;
import java.time.Instant;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@NoArgsConstructor
public class PasswordVerify implements Serializable {
	private static final long serialVersionUID = -6418103567204222713L;

	private int attemptRemaining;

	private boolean successed = false;

	private Instant lockTime = Instant.now();

	/**
	 * @param attemptRemaining
	 */
	public PasswordVerify(int attemptRemaining) {
		super();
		
		this.attemptRemaining = attemptRemaining;
	}

}
