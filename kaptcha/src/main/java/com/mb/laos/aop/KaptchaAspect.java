package com.mb.laos.aop;

import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.util.ApiConstants;
import com.mb.laos.api.util.HttpUtil;
import com.mb.laos.configuration.KaptchaProperties;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.service.KaptchaService;
import com.mb.laos.service.LoginAttemptService;
import com.mb.laos.util.CaptchaConstants;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 02/07/2021 - LinhLH: Create new
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class KaptchaAspect {
    private final KaptchaProperties kaptchaProperties;

    private final PasswordEncoder encoder;

    private final LoginAttemptService loginAttemptService;

    private final KaptchaService kaptchaService;

    @Before("@annotation(com.mb.laos.annotation.RequiredKaptcha)")
    public void requiredKaptcha() throws Throwable {
        String headerName = this.kaptchaProperties.getHeaderName();

        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();

        if (requestAttributes == null) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_INCORRECT_CAPTCHA),
                            CaptchaConstants.EntityName.CAPTCHA, LabelKey.ERROR_INCORRECT_CAPTCHA);
        }

        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();

        String captcha = request.getHeader(headerName);

        String transactionId = request.getHeader(ApiConstants.HttpHeaders.X_TRANSACTION_ID);

        _log.info("captcha: " + captcha);
        _log.info("transactionId: " + transactionId);

        if (Validator.isNull(captcha) || Validator.isNull(transactionId)
                        || !this.encoder.matches(captcha, transactionId)
                        || !this.kaptchaService.validate(transactionId, captcha)) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_INCORRECT_CAPTCHA),
                            CaptchaConstants.EntityName.CAPTCHA, LabelKey.ERROR_INCORRECT_CAPTCHA);
        }
    }

    @Before("@annotation(com.mb.laos.annotation.LoginKaptcha)")
    public void loginKaptcha() throws Throwable {
        String headerName = this.kaptchaProperties.getHeaderName();

        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();

        if (requestAttributes == null) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_INCORRECT_CAPTCHA),
                            CaptchaConstants.EntityName.CAPTCHA, LabelKey.ERROR_INCORRECT_CAPTCHA);
        }

        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        
        // check nếu ip đang không bị block thì bỏ qua
        if (!this.loginAttemptService.isRequiredCaptcha(HttpUtil.getClientIP(request))) {
            return;
        }

        String captcha = request.getHeader(headerName);

        String transactionId = request.getHeader(ApiConstants.HttpHeaders.X_TRANSACTION_ID);

        _log.info("captcha: " + captcha);
        _log.info("transactionId: " + transactionId);

        if (Validator.isNull(captcha) || Validator.isNull(transactionId)
                        || !this.encoder.matches(captcha, transactionId)
                        || !this.kaptchaService.validate(transactionId, captcha)) {
            Map<String, Object> params = this.kaptchaService.generateRequired();

            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_INCORRECT_CAPTCHA),
                            CaptchaConstants.EntityName.CAPTCHA, LabelKey.ERROR_INCORRECT_CAPTCHA, params);
        }
    }
}
