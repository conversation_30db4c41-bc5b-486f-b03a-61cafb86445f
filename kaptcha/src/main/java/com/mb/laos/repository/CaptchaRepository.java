/**
 * 
 */
package com.mb.laos.repository;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import com.mb.laos.cache.util.AnnotationCacheConstants;

/**
 * <AUTHOR>
 *
 */
public interface CaptchaRepository {

	/**
	 * @param key
	 * @param data
	 */
	@CachePut(cacheNames = AnnotationCacheConstants.Others.CAPTCHA, key = "#key", unless = "#result == null")
	default String put(String key, String data) {
		return data;
	}

	/**
	 * @param key
	 * @return
	 */
	@Cacheable(cacheNames = AnnotationCacheConstants.Others.CAPTCHA, key = "#key", unless = "#result == null")
	default String getIfPresent(String key) {
		return null;
	}

	/**
	 * @param key
	 */
	@CacheEvict(cacheNames = AnnotationCacheConstants.Others.CAPTCHA, key = "#key")
	default String invalidate(String key) {
		return key;
	}

}
