/**
 * 
 */
package com.mb.laos.repository;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import com.mb.laos.cache.util.AnnotationCacheConstants;

/**
 * <AUTHOR>
 *
 */
public interface LoginFailedRepository {

	/**
	 * @param key
	 * @param data
	 */
	@CachePut(cacheNames = AnnotationCacheConstants.Others.LOGIN_FAILED, key = "#key", unless = "#result == null")
	default Integer put(String key, Integer value) {
		return value;
	}

	/**
	 * @param key
	 * @return
	 */
	@Cacheable(cacheNames = AnnotationCacheConstants.Others.LOGIN_FAILED, key = "#key", unless = "#result == null")
	default Integer getIfPresent(String key) {
		return null;
	}

	/**
	 * @param key
	 */
	@CacheEvict(cacheNames = AnnotationCacheConstants.Others.LOGIN_FAILED, key = "#key")
	default String invalidate(String key) {
		return key;
	}

}
