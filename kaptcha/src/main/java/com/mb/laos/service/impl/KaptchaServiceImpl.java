package com.mb.laos.service.impl;

import java.awt.image.BufferedImage;
import java.util.HashMap;
import java.util.Map;

import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import com.google.code.kaptcha.Producer;
import com.mb.laos.model.dto.CaptchaDTO;
import com.mb.laos.repository.CaptchaRepository;
import com.mb.laos.service.KaptchaService;
import com.mb.laos.util.FileUtil;
import com.mb.laos.util.Validator;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 07/07/2021 - LinhLH: Create new
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KaptchaServiceImpl implements KaptchaService {

	private final Producer captchaProducer;

	private final PasswordEncoder encoder;

	private final CaptchaRepository captchaRepository;

	@Override
	public CaptchaDTO generate() {
		String capText = this.captchaProducer.createText();

		String transactionId = this.encoder.encode(capText);

		this.captchaRepository.put(transactionId, capText);
		// create the image with the text
		BufferedImage bi = this.captchaProducer.createImage(capText);

		return new CaptchaDTO(transactionId, FileUtil.getImageSrcBase64String(bi, "jpg"));
	}

	@Override
	public Map<String, Object> generateRequired() {
		String capText = this.captchaProducer.createText();

		String transactionId = this.encoder.encode(capText);

		this.captchaRepository.put(transactionId, capText);
		// create the image with the text
		BufferedImage bi = this.captchaProducer.createImage(capText);

		Map<String, Object> data = new HashMap<>();

		data.put("captcha", FileUtil.getImageSrcBase64String(bi, "jpg"));
		data.put("transactionId", transactionId);
		data.put("captchaRequired", true);

		return data;
	}

	@Override
	public boolean validate(String transactionId, String text) {
		String textInCache = this.captchaRepository.getIfPresent(transactionId);

		if (Validator.isNotNull(textInCache) && textInCache.equals(text)) {
			// invalidate captcha
			this.captchaRepository.invalidate(transactionId);

			return true;
		}

		return false;
	}
}
