package com.mb.laos.service.impl;

import org.springframework.stereotype.Service;
import com.mb.laos.repository.LoginFailedRepository;
import com.mb.laos.security.configuration.AuthenticationProperties;
import com.mb.laos.service.LoginAttemptService;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 07/07/2021 - LinhLH: Create new
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoginAttemptServiceImpl implements LoginAttemptService {
	private final AuthenticationProperties authenticationProperties;

	private final LoginFailedRepository loginFailedRepository;

	@Override
	public void loginSucceeded(String key) {
		this.loginFailedRepository.invalidate(key);
	}

	private int getAttempts(String key) {
		Integer attempts = this.loginFailedRepository.getIfPresent(key);

		return Validator.isNotNull(attempts) ? attempts : 0;
	}

	@Override
	public void loginFailed(String key) {
		int attempts = this.getAttempts(key);

		attempts++;

		_log.warn("User with ip {} login failure for {} times", key, attempts);

		this.loginFailedRepository.put(key, attempts);
	}

	@Override
	public boolean isRequiredCaptcha(final String key) {
		return this.getAttempts(key) >= this.authenticationProperties.getLoginMaxAttemptTime();
	}
}
