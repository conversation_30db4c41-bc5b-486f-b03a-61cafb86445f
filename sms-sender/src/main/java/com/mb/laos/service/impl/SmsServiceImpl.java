package com.mb.laos.service.impl;

import com.mb.laos.api.consumer.SmsSender;
import com.mb.laos.api.request.SmsRequest;
import com.mb.laos.api.response.SmsResponse;
import com.mb.laos.api.response.SmsResponse.Result;
import com.mb.laos.configuration.SmsProperties;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.ContentTemplate;
import com.mb.laos.model.SmsLog;
import com.mb.laos.repository.ContentTemplateRepository;
import com.mb.laos.repository.SmsRepository;
import com.mb.laos.service.SmsService;
import com.mb.laos.util.DiagnosticContextUtil;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j // Tự động tạo logger instance cho class
@Service // Đánh dấu class này là một Spring Service component
@RequiredArgsConstructor // Tự động tạo constructor với các final fields
public class SmsServiceImpl implements SmsService {

    // Repository để thao tác với bảng ContentTemplate trong database
    private final ContentTemplateRepository contentTemplateRepository;

    // Properties chứa cấu hình SMS (URL, credentials, etc.)
    private final SmsProperties properties;

    // Consumer để gửi SMS qua external service
    private final SmsSender smsSender;

    // Repository để thao tác với bảng SmsLog trong database
    private final SmsRepository smsRepository;

    @Override
    public SmsResponse send(SmsRequest request) {
        // Set URL từ properties vào request
        request.setUrl(this.properties.getUrl());
        // Gửi SMS qua SMS sender và nhận response
        SmsResponse smsResponse = this.smsSender.send(request);

        // Kiểm tra nếu gửi SMS thất bại thì log error
        if (!Validator.equals(smsResponse.getResult(), SmsResponse.Result.SUCCESS.getStatus())) {
            _log.error("Sms fail to send to phone number {} : {}", request.getTo(), smsResponse.getMessage());
        }

        // Tạo SMS log từ request và response để lưu vào database
        SmsLog sms = this.enrichSms(request, smsResponse);
        // Lưu SMS log vào database
        this.smsRepository._save(sms);

        // Trả về SMS response
        return smsResponse;
    }

    @Override
    public SmsResponse send(String phoneNumber, String code, Map<String, String> params) {
        // Tìm content template theo template code và status ACTIVE
        ContentTemplate template =
                this.contentTemplateRepository.findByTemplateCodeAndStatus(code,
                        EntityStatus.ACTIVE.getStatus());

        // Nếu không tìm thấy template thì log error và trả về response FAILURE
        if (Validator.isNull(template)) {
            _log.error("Cannot find content template with code {}", code);

            // Trả về SMS response với result FAILURE và error message
            return new SmsResponse(Result.FAILURE, Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                    new Object[]{Labels.getLabels(LabelKey.LABEL_TEMPLATE)}));
        }

        // Tạo SMS request từ template và parameters
        SmsRequest request = SmsRequest.builder()
                .clientMessageId(DiagnosticContextUtil.getClientMessageId()) // Client message ID cho tracking
                .method(HttpMethod.POST) // HTTP method POST
                .to(phoneNumber) // Số điện thoại người nhận
                .title(template.getTitle()) // Tiêu đề từ template
                .content(StringUtil.replaceMapValue(template.getContent(), params)) // Nội dung với params được replace
                .build();

        // Nếu debug mode enabled thì log SMS content
        if (_log.isDebugEnabled()) {
            _log.debug("Sms content: {}", request.getContent());
        }

        // Gọi method send với SMS request đã tạo
        return this.send(request);
    }

    // Method private để tạo SMS log từ request và response
    private SmsLog enrichSms(SmsRequest smsRequest, SmsResponse smsResponse) {
        // Tạo SMS log entity mới
        SmsLog sms = new SmsLog();
        // Set người gửi từ SMS request
        sms.setFromUser(smsRequest.getFrom());
        // Set người nhận từ SMS request
        sms.setToUser(smsRequest.getTo());
        // Set nội dung SMS từ request
        sms.setContent(smsRequest.getContent());
        // Set loại SMS từ request
        sms.setType(smsRequest.getType());
        // Set status từ SMS response (SUCCESS/FAILURE)
        sms.setStatus(smsResponse.getResult());
        // Trả về SMS log đã được enrich
        return sms;
    }

}
