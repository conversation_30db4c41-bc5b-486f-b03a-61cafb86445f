# Giải thích chi tiết class ApiGeeSender

## Tổng quan
Class `ApiGeeSender` là **centralized HTTP client** chuyên dụng để gọi các API qua **ApiGee Gateway**. Đây là layer quan trọng giữa MB App và T24/LAPNET systems.

## Vai trò trong Architecture
```
MB App → ApiGeeSender → ApiGee Gateway → T24/LAPNET → Core Banking
```

## Phân tích từng component

### 1. Class-level annotations và dependencies
```java
@Slf4j                    // Lombok: Tự động tạo logger
@RequiredArgsConstructor  // Lombok: Constructor với final fields
@Component               // Spring: Dependency injection
public class ApiGeeSender {
    private final RestTemplate restTemplate;        // HTTP client
    private final Gson gson;                        // JSON serializer
    private final ApiGeeTokenService apiGeeTokenService; // OAuth token manager
}
```

**Dependencies:**
- **RestTemplate**: Configured HTTP client với connection pooling
- **Gson**: JSON serializer với custom date formats
- **ApiGeeTokenService**: OAuth token lifecycle management

### 2. Method sendToApiGee() - Core HTTP client

#### Method signature:
```java
@Retryable(value = UnauthorizedException.class, maxAttemptsExpression = "${consumer.api-gee.retry.max-attempts:2}",
        backoff = @Backoff(delayExpression = "${consumer.api-gee.retry.max-delay:100}"))
@OutboundRequestLog
public <T extends ApiGeeResponse, V extends Request> T sendToApiGee(V request, Class<T> clazz,
                                                                    MultiValueMap<String, String> params)
```

**Annotations:**
- **@Retryable**: Auto-retry cho UnauthorizedException (token expired)
- **@OutboundRequestLog**: Custom logging cho audit trail
- **Generic types**: Type-safe request/response handling

#### Step 1: Prepare headers
```java
HttpHeaders headers = this.getHttpHeaders(request.getClientMessageId(), request.getApiGeeTransactionId());
```
- OAuth Authorization header
- Content-Type: application/json
- Client Message ID cho correlation
- Transaction ID cho tracking

#### Step 2: Build URI
```java
UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(request.getUrl());
```
- URL encoding và query parameter handling
- Support cả absolute và relative URLs

#### Step 3: Create HTTP entity
```java
if (Validator.isNotNull(params) && !params.isEmpty()) {
    // GET REQUEST - query parameters
    builder = builder.queryParams(params);
    entity = new HttpEntity<>(headers);
} else {
    // POST REQUEST - request body
    entity = new HttpEntity<>(request, headers);
}
```

**Request types:**
- **GET**: Query parameters trong URL, no body
- **POST**: Request object trong body, no query params

#### Step 4: Execute HTTP call
```java
ResponseEntity<String> response = this.restTemplate.exchange(uri, request.getMethod(), entity, String.class);
```
- Dynamic HTTP method (GET, POST, PUT, DELETE...)
- Return raw String để có full control over parsing
- RestTemplate handles connection pooling và timeouts

#### Step 5: Parse response
```java
T apigeeResponse = GsonUtil.canJsonParse(response) ? this.gson.fromJson(body, clazz) : null;
```
- Validate JSON format trước khi parse
- Type-safe deserialization với generic class
- Handle non-JSON responses (HTML error pages...)

#### Step 6: Handle success/error
```java
if (response.getStatusCode().equals(HttpStatus.OK)) {
    // SUCCESS: Return parsed object
    return apigeeResponse;
} else {
    // ERROR: Handle different error scenarios
}
```

### 3. Error Handling Strategy

#### Token Expiry (Automatic retry):
```java
if (Validator.equals(apigeeResponse.getErrorCode(), T24ErrorCode.TOKEN_IS_INVALID.getCode())) {
    this.apiGeeTokenService.refreshApiGeeToken();
    throw new UnauthorizedException(T24ErrorCode.TOKEN_IS_INVALID.getErrorCode());
}
```
- Detect token expiry từ T24 error code
- Refresh token automatically
- Throw UnauthorizedException để trigger @Retryable

#### T24/LAPNET Errors:
```java
throw new HttpResponseException(response.getStatusCodeValue(), apigeeResponse.getErrorCode(),
        errorDescription, apigeeResponse.getErrorDetail(), null);
```
- Preserve original error codes từ T24/LAPNET
- Include full error context
- Structured error handling

#### Unexpected Errors:
```java
throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_AN_UNEXPECTED_ERROR_HAS_OCCURRED),
        ErrorCode.MSG1037.name(), LabelKey.ERROR_AN_UNEXPECTED_ERROR_HAS_OCCURRED);
```
- Generic error cho network issues
- User-friendly error messages
- Fallback error handling

### 4. Method getHttpHeaders() - Header preparation

#### OAuth Token Handling:
```java
try {
    token = this.apiGeeTokenService.getApiGeeToken();  // From cache/database
} catch (Exception e) {
    token = this.apiGeeTokenService.refreshApiGeeToken(); // Force refresh
}
```

**Token Strategy:**
- **Primary**: Get từ cache/database (fast)
- **Fallback**: Refresh từ OAuth server (slower)
- **Resilient**: Always có token để authenticate

#### Header Composition:
```java
headers.set(SecurityConstants.Header.AUTHORIZATION_HEADER,
        HeaderUtil.getAuthorization(token.getTokenType(), token.getAccessToken()));
headers.add(ApiConstants.HttpHeaders.CLIENT_MESSAGE_ID, clientMessageId);
headers.add(ApiConstants.HttpHeaders.TRANSACTION_ID, transactionId);
```

**Headers:**
- **Authorization**: Bearer token cho OAuth 2.0
- **Content-Type**: application/json
- **Accept**: application/json
- **Client-Message-ID**: Request correlation
- **Transaction-ID**: Business tracking

## Retry Mechanism

### Configuration:
```java
@Retryable(value = UnauthorizedException.class, 
          maxAttemptsExpression = "${consumer.api-gee.retry.max-attempts:2}",
          backoff = @Backoff(delayExpression = "${consumer.api-gee.retry.max-delay:100}"))
```

### Retry Flow:
```
1st Call → Token Expired → Refresh Token → 2nd Call → Success
```

### Benefits:
- **Transparent**: Caller không cần biết về retry
- **Configurable**: Max attempts và delay configurable
- **Efficient**: Chỉ retry cho token expiry

## Performance Characteristics

### Connection Management:
- **Connection Pooling**: RestTemplate với connection pool
- **Keep-Alive**: HTTP connections reused
- **Timeout**: Configurable read/connect timeouts

### Token Caching:
- **Cache Hit**: < 10ms (from memory/database)
- **Cache Miss**: 200-500ms (OAuth call)
- **Token Refresh**: Automatic và transparent

### Typical Latencies:
- **Internal T24**: 200-800ms
- **External LAPNET**: 2-5 seconds
- **Token Refresh**: 200-500ms additional

## Security Features

### OAuth 2.0 Integration:
- **Access Tokens**: Short-lived (15-60 minutes)
- **Refresh Tokens**: Long-lived (24 hours)
- **Automatic Refresh**: Transparent token renewal

### Request Security:
- **TLS 1.3**: All communications encrypted
- **Token Masking**: Sensitive data masked in logs
- **Audit Trail**: Complete request/response logging

### Error Security:
- **Error Sanitization**: No sensitive data in error messages
- **Rate Limiting**: ApiGee handles rate limiting
- **Circuit Breaker**: Fail-fast cho downstream issues

## Integration Points

### Upstream Callers:
- **ApiGeeTransferServiceImpl**: Account verification
- **ApiGeeCustomerServiceImpl**: Customer operations
- **ApiGeeNotificationServiceImpl**: Notification services

### Downstream Systems:
- **T24 Core Banking**: Internal operations
- **LAPNET Network**: Interbank operations
- **OAuth Server**: Token management

## Configuration Properties

### Retry Configuration:
```yaml
consumer:
  api-gee:
    retry:
      max-attempts: 2      # Maximum retry attempts
      max-delay: 100       # Delay between retries (ms)
```

### RestTemplate Configuration:
```yaml
http:
  client:
    connection-timeout: 5000    # Connection timeout (ms)
    read-timeout: 30000         # Read timeout (ms)
    max-connections: 100        # Connection pool size
```

## Monitoring và Observability

### Logging:
- **Request/Response**: Full audit trail
- **Performance**: Latency metrics
- **Errors**: Detailed error context

### Metrics:
- **Success Rate**: HTTP 200 vs errors
- **Latency**: P50, P95, P99 response times
- **Token Refresh**: Frequency và success rate

### Alerts:
- **High Error Rate**: > 5% error rate
- **High Latency**: > 10 seconds response time
- **Token Issues**: Frequent refresh failures

Class `ApiGeeSender` là **critical infrastructure** component, đảm bảo reliable và secure communication với T24/LAPNET systems!
