# G<PERSON><PERSON><PERSON> thích chi tiết 6 QR Parsing Methods

## Tổng quan
6 methods này tạo thành **QR Code parsing engine** hoàn chỉnh trong QrCodeServiceImpl, x<PERSON> lý nhiều loại QR Code khác nhau với algorithms phức tạp.

## Method Flow Diagram
```
parseQr() → getQrTagValues() → validateMandatoryFields() → getMandatory()
                ↓
getValueFromQrCodeMerchant() → getValueMbLaos()
```

## 1. Method `parseQr()` - Core Parsing Engine

### Mục đích:
Parse QR Code string thành structured tag-value pairs theo EMVCo standard.

### Algorithm:
```java
while (i + 4 <= qrCode.length()) {
    String id = qrCode.substring(i, i + 2);        // Field ID (2 chars)
    int length = Integer.parseInt(qrCode.substring(i + 2, i + 4)); // Length (2 chars)
    String value = qrCode.substring(i + 4, i + 4 + length);       // Value (length chars)
    
    if (isTemplate(id)) {
        // Recursive parsing cho template fields (26-51)
        List<QrTagValue> subTags = parseQr(value);
        // Add với composite ID: "26.00", "26.01"...
    } else {
        // Simple field
        response.add(new QrTagValue(id, length, value));
    }
}
```

### Template Fields (26-51):
- **Merchant Account Info**: Chứa nested sub-fields
- **Recursive parsing**: Parse value như QR Code con
- **Composite IDs**: "26.00", "26.01", "26.02"...

### Error Handling:
- **Length validation**: Tránh buffer overflow
- **NumberFormatException**: Invalid length format
- **Unified exception**: BadRequestAlertException(MSG1134)

## 2. Method `getQrTagValues()` - Response Population

### Mục đích:
Parse QR Code và populate QrCodeResponse với basic information.

### Processing Steps:
```java
1. List<QrTagValue> tagValues = parseQr(qrCode);
2. validateMandatoryFields(tagValues);
3. Set basic fields: payloadFormatIndicator, pointInitMethod
4. Extract transactionAmount (field 54)
5. Extract currencyCode (field 53) → map to currency name
6. Extract merchantName (field 60)
```

### Field Mapping:
| QR Field | Response Field | Processing |
|----------|----------------|------------|
| **Field 00** | payloadFormatIndicator | Direct copy |
| **Field 01** | pointInitMethod | Direct copy |
| **Field 54** | transactionAmount | Convert to BigDecimal |
| **Field 53** | beneficiaryCurrency | Map via currency repository |
| **Field 60** | merchantName | Direct copy |

### Business Logic:
- **QR Type**: Always LAPNET
- **Payment Type**: From configuration
- **Transfer Type**: INTERNATIONAL_BANK
- **Amount**: "0" for STATIC QR, actual value for DYNAMIC

## 3. Method `validateMandatoryFields()` - Validation Engine

### Mục đích:
Validate QR Code có đầy đủ mandatory fields theo EMVCo standard.

### Validation Rules:
```java
1. Check all fields from getMandatory()
2. Check at least 1 merchant account info field (26-51)
3. Return false if any mandatory field missing
```

### Mandatory Fields Check:
```java
Set<String> mandatory = getMandatory();
for (String item : mandatory) {
    boolean isFound = tags.stream().anyMatch(tag -> tag.getId().equals(item));
    if (!isFound) return false;
}
```

### Merchant Account Info Check:
```java
return tags.stream().anyMatch(t -> {
    int id = Integer.parseInt(t.getId().split("\\.")[0]);
    return id >= 26 && id <= 51;
});
```

## 4. Method `getMandatory()` - Mandatory Fields Definition

### Mục đích:
Define tất cả mandatory fields theo EMVCo standard.

### Mandatory Fields List:
| Field ID | Field Name | Purpose |
|----------|------------|---------|
| **26.00** | Globally Unique Identifier | Application ID |
| **26.01** | IIN | Bank identifier |
| **52** | Merchant Category Code | Merchant classification |
| **53** | Transaction Currency | Currency code |
| **58** | Country Code | Country identifier |
| **60** | Merchant Name | Merchant display name |
| **61** | Merchant City | Merchant location |
| **63** | CRC | Integrity checksum |

### Configuration Driven:
```java
mandatory.add(qrProperties.getMerchantAccountInfo().getGloballyId());
mandatory.add(qrProperties.getMerchantAccountInfo().getIinId());
mandatory.add(qrProperties.getMerchantCategoryId());
// ... other fields from configuration
```

## 5. Method `getValueFromQrCodeMerchant()` - MB Bank Merchant Parser

### Mục đích:
Parse QR Code merchant nội bộ MB Bank với format đặc biệt.

### Parsing Algorithm:
```java
while (i < qrCode.length()) {
    QrStructure qrStructure = convertStructureDataInQRCode(qrCode.substring(i));
    
    if (qrStructure.getId() == 0) {
        // Payload Format Indicator
    } else if (qrStructure.getId() == 1) {
        // Point of Initiation Method
    } else if (qrStructure.getId() > 1 && qrStructure.getId() < MAX_MERCHANT_ACCOUNT) {
        // Merchant Account Info
        if (qrStructure.getId().equals(laosId)) {
            getValueMbLaos(response, qrCode, start, length);
        }
    }
}
```

### Khác biệt với EMVCo:
- **Custom format**: MB Bank proprietary format
- **Different structure**: Không theo EMVCo standard
- **Internal processing**: Không qua LAPNET network

## 6. Method `getValueMbLaos()` - MB Laos Merchant Data Extractor

### Mục đích:
Extract merchant-specific data từ MB Laos QR Code.

### Sub-field Processing:
```java
switch (qrStructure.getId()) {
    case 1: qrCodeResponse.setAccountNumber(data);      // Account number
    case 2: qrCodeResponse.setMasterMerchantName(data); // Master merchant name
    case 3: qrCodeResponse.setMerchantId(data);         // Merchant ID
    case 4: qrCodeResponse.setMerchantName(data);       // Display name
}
```

### Field Mapping:
| Sub-field ID | Response Field | Purpose |
|--------------|----------------|---------|
| **1** | accountNumber | Merchant account |
| **2** | masterMerchantName | Official merchant name |
| **3** | merchantId | Unique merchant identifier |
| **4** | merchantName | Customer-facing name |

## Integration Flow

### 1. EMVCo QR Code Flow:
```
QR String → parseQr() → getQrTagValues() → validateMandatoryFields() → Response
```

### 2. MB Merchant QR Code Flow:
```
QR String → getValueFromQrCodeMerchant() → getValueMbLaos() → Response
```

### 3. Validation Flow:
```
Tag Values → validateMandatoryFields() → getMandatory() → Boolean Result
```

## Error Handling Strategy

### Parse Errors:
- **Invalid format**: BadRequestAlertException(MSG1134)
- **Length mismatch**: Buffer overflow protection
- **Number format**: Invalid field ID/length

### Validation Errors:
- **Missing mandatory**: BadRequestAlertException(MSG1134)
- **Invalid currency**: BadRequestAlertException(MSG101037)
- **Missing merchant**: BadRequestAlertException(MSG101282)

## Performance Characteristics

### parseQr():
- **Time Complexity**: O(n) where n = QR Code length
- **Space Complexity**: O(m) where m = number of fields
- **Recursive calls**: For template fields only

### validateMandatoryFields():
- **Time Complexity**: O(k*m) where k = mandatory fields, m = total fields
- **Early termination**: Return false on first missing field

### getValueFromQrCodeMerchant():
- **Time Complexity**: O(n) single pass
- **Memory usage**: Minimal, reuse QrStructure objects

## Business Rules

### QR Type Detection:
- **EMVCo format**: Use parseQr() + getQrTagValues()
- **MB format**: Use getValueFromQrCodeMerchant()
- **Validation**: Always required

### Currency Handling:
- **ISO 4217 codes**: "418" → "LAK", "116" → "KHR"
- **Repository lookup**: Active currencies only
- **Fallback**: Default currency if not found

### Merchant Information:
- **Multiple names**: Master name vs display name
- **Account mapping**: Merchant ID → Account number
- **Validation**: All merchant fields required

Những methods này tạo thành **comprehensive QR parsing system** hỗ trợ multiple formats và standards!
