# G<PERSON><PERSON>i thích chi tiết class HeaderUtil

## Tổng quan
Class `HeaderUtil` là **utility class** chuyên dụng để tạo và quản lý HTTP headers trong hệ thống MB Laos. Đ<PERSON>y là **centralized header management** cho tất cả HTTP communications.

## Vai trò trong hệ thống
```
REST Controllers → HeaderUtil → HTTP Response Headers → Client
HTTP Clients → HeaderUtil → HTTP Request Headers → External APIs
```

## Phân loại methods theo chức năng

### 1. Alert Headers (Response Messages)
Tạo custom headers để truyền messages từ server về client.

#### createAlert() - Base alert method:
```java
public static HttpHeaders createAlert(String message, String param)
```
**Headers được tạo:**
- `X-Action-Message`: Message cần hiển thị
- `X-Action-Params`: Parameters đã URL-encoded

**Usage:**
```java
HttpHeaders headers = HeaderUtil.createAlert("Customer created successfully", "12345");
// X-Action-Message: Customer created successfully
// X-Action-Params: 12345
```

#### Entity CRUD Alert Methods:
| Method | Action | Message Format |
|--------|--------|----------------|
| `createEntityCreationAlert()` | CREATE | "A new {entity} is created with identifier {id}" |
| `createEntityUpdateAlert()` | UPDATE | "A {entity} is updated with identifier {id}" |
| `createEntityDeletionAlert()` | DELETE | "A {entity} is deleted with identifier {id}" |

**I18N Support:**
```java
// enableTranslation = true
createEntityCreationAlert("mblaos", true, "customer", "12345");
// → Message: "mblaos.customer.created"

// enableTranslation = false  
createEntityCreationAlert("mblaos", false, "customer", "12345");
// → Message: "A new customer is created with identifier 12345"
```

#### createFailureAlert() - Error handling:
```java
public static HttpHeaders createFailureAlert(boolean enableTranslation, String errorKey, 
                                           Object[] params, String defaultMessage)
```
**Headers được tạo:**
- `X-Action-Message`: Error message hoặc error key
- `X-Action-Message-Key`: Error key để client lookup
- `X-Action-Params`: Error parameters (comma-separated)

**Usage:**
```java
HeaderUtil.createFailureAlert(false, "error.account.notfound", 
                             new Object[]{"12345"}, "Account not found");
// X-Action-Message: Account not found
// X-Action-Message-Key: error.account.notfound
// X-Action-Params: 12345
```

### 2. Authorization Headers
Tạo Authorization headers cho các loại authentication.

#### Basic Authentication:
```java
// From username/password
String auth = HeaderUtil.getBasicAuthorization("user", "pass");
// → "Basic dXNlcjpwYXNz"

// From pre-encoded token
String auth = HeaderUtil.getBasicAuthorization("dXNlcjpwYXNz");
// → "Basic dXNlcjpwYXNz"
```

**Base64 Encoding Process:**
```
"user:pass" → UTF-8 bytes → Base64 encode → "dXNlcjpwYXNz"
```

#### Bearer Authentication:
```java
String auth = HeaderUtil.getBearerAuthorization("eyJhbGciOiJIUzI1NiIs...");
// → "Bearer eyJhbGciOiJIUzI1NiIs..."
```

#### Flexible Authorization:
```java
// With token type
String auth = HeaderUtil.getAuthorization("Bearer", "token123");
// → "Bearer token123"

// Without token type (defaults to Bearer)
String auth = HeaderUtil.getAuthorization(null, "token123");
// → "Bearer token123"

// Custom token type
String auth = HeaderUtil.getAuthorization("Custom", "token123");
// → "Custom token123"
```

### 3. Content-Type Headers
Tạo standard HTTP headers cho different content types.

#### JSON Headers:
```java
HttpHeaders headers = HeaderUtil.getTypeJsonHeaders();
```
**Headers được tạo:**
- `Content-Type: application/json`
- `Accept: application/json`
- `User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)...`

#### Form-Encoded Headers:
```java
HttpHeaders headers = HeaderUtil.getTypeUrlEncodeHeaders();
```
**Headers được tạo:**
- `Content-Type: application/x-www-form-urlencoded`
- `Accept: application/json`
- `User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)...`

## Usage Patterns

### 1. REST Controller Response Headers:
```java
@PostMapping("/customers")
public ResponseEntity<Customer> createCustomer(@RequestBody Customer customer) {
    Customer saved = customerService.save(customer);
    
    HttpHeaders headers = HeaderUtil.createEntityCreationAlert(
        "mblaos", true, "customer", saved.getId());
    
    return ResponseEntity.ok().headers(headers).body(saved);
}
```

### 2. HTTP Client Request Headers:
```java
// ApiGeeSender usage
HttpHeaders headers = HeaderUtil.getTypeJsonHeaders();
headers.set("Authorization", HeaderUtil.getBearerAuthorization(token));
headers.add("Transaction-ID", transactionId);

HttpEntity<Request> entity = new HttpEntity<>(request, headers);
```

### 3. Error Response Headers:
```java
@ExceptionHandler(EntityNotFoundException.class)
public ResponseEntity<ErrorResponse> handleNotFound(EntityNotFoundException ex) {
    HttpHeaders headers = HeaderUtil.createFailureAlert(
        false, "error.entity.notfound", 
        new Object[]{ex.getEntityId()}, 
        "Entity not found");
    
    return ResponseEntity.status(404).headers(headers).body(errorResponse);
}
```

## Security Considerations

### 1. Basic Authentication:
- **Base64 is NOT encryption**: Easily decoded
- **Use HTTPS only**: Credentials visible in plain text over HTTP
- **Rotate credentials**: Regular password changes

### 2. Bearer Tokens:
- **Token expiry**: Implement token refresh logic
- **Secure storage**: Don't log tokens in plain text
- **HTTPS required**: Tokens can be intercepted

### 3. User-Agent Spoofing:
- **WAF bypass**: Browser-like User-Agent avoids blocking
- **Rate limiting**: Some APIs treat different User-Agents differently
- **Monitoring**: Consistent User-Agent helps with request tracking

## Performance Optimizations

### 1. StringBuilder Usage:
```java
StringBuilder sb = new StringBuilder(3);  // Pre-allocate capacity
```
- **Memory efficient**: Avoid string concatenation overhead
- **Capacity hint**: Reduce array resizing

### 2. String Constants:
```java
ApiConstants.Header.BASIC_START  // "Basic "
ApiConstants.Header.BEARER_START // "Bearer "
```
- **Memory sharing**: String literals are interned
- **Performance**: No string creation overhead

### 3. Header Reuse:
```java
private static HttpHeaders getHeaders() {
    // Base headers reused by multiple methods
}
```
- **Code reuse**: Common headers in one place
- **Consistency**: Same base headers everywhere

## Integration Points

### Upstream Callers:
- **REST Controllers**: Response headers với messages
- **ApiGeeSender**: Request headers cho external APIs
- **Exception Handlers**: Error response headers

### Header Constants:
```java
ApiConstants.HttpHeaders.X_ACTION_MESSAGE      // "X-Action-Message"
ApiConstants.HttpHeaders.X_ACTION_MESSAGE_KEY  // "X-Action-Message-Key"
ApiConstants.HttpHeaders.X_ACTION_PARAMS       // "X-Action-Params"
```

### Client-Side Processing:
```javascript
// Frontend JavaScript
const message = response.headers['X-Action-Message'];
const params = response.headers['X-Action-Params'];
showNotification(message, params);
```

## Error Handling

### 1. Encoding Errors:
```java
try {
    URLEncoder.encode(param, StandardCharsets.UTF_8.toString());
} catch (UnsupportedEncodingException e) {
    // UTF-8 always supported - this never happens
}
```

### 2. Null Safety:
```java
if (Validator.isNotNull(params)) {
    headers.add(ApiConstants.HttpHeaders.X_ACTION_PARAMS, 
               StringUtil.join(params, StringPool.COMMA));
}
```

### 3. Logging:
```java
_log.error("Entity processing failed, {}", defaultMessage);
```

## Best Practices

### 1. Utility Class Design:
- **Static methods only**: No instance state
- **Private constructor**: Prevent instantiation
- **Final class**: Prevent inheritance

### 2. Header Naming:
- **Custom headers**: Use X- prefix
- **Consistent naming**: Follow established patterns
- **Case sensitivity**: HTTP headers are case-insensitive

### 3. Content Encoding:
- **URL encoding**: For header values với special characters
- **UTF-8**: Standard encoding cho international characters
- **Base64**: For binary data in headers

Class `HeaderUtil` là **foundation** cho HTTP communication, đảm bảo consistent và secure header management across toàn bộ hệ thống!
