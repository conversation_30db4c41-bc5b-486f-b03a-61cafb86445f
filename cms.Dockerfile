FROM openjdk:8-jdk-alpine as build
WORKDIR /workspace/app
COPY . .
RUN ./mvnw package

FROM openjdk:8-jdk-alpine
ENV SPRING_PROFILES_ACTIVE prod
ENV JAVA_OPTS -Xmx768m -Xss256k -XX:+UseG1GC -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap -XX:MaxRAMFraction=1
VOLUME /tmp
WORKDIR /app
COPY --from=build /workspace/app/cms-admin/target/*.war /app/app.war
ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom $JAVA_OPTS -jar app.war
