package com.mb.laos.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.interceptor.CacheErrorHandler;

@SuppressWarnings("NullableProblems")
@Slf4j
public class RedisCacheErrorHandler implements CacheErrorHandler {

    @Override
    public void handleCacheGetError(RuntimeException exception, Cache cache, Object key) {
        _log.info("Unable to get from cache " + cache.getName() + " : " + exception);
    }

    @Override
    public void handleCachePutError(RuntimeException exception, Cache cache, Object key, Object value) {
        _log.info("Unable to put into cache " + cache.getName() + " : " + exception);
    }

    @Override
    public void handleCacheEvictError(RuntimeException exception, Cache cache, Object key) {
        _log.info("Unable to evict from cache " + cache.getName() + " : " + exception);
    }

    @Override
    public void handleCacheClearError(RuntimeException exception, Cache cache) {
        _log.info("Unable to clean cache " + cache.getName() + " : " + exception);
    }
}
