# G<PERSON><PERSON><PERSON> thích chi tiết class AccNumberVerifyDTO

## Tổng quan
Class `AccNumberVerifyDTO` là **response DTO** chính từ T24/LAPNET account verification, chứa tất cả thông tin cần thiết để thực hiện transfer transaction.

## Vai trò trong hệ thống
```
T24/LAPNET → JSON Response → AccNumberVerifyDTO → Transfer Processing
```

## Ph<PERSON> loại fields theo chức năng

### 1. Account Information
Thông tin cơ bản về tài khoản được verify.

| Field | Type | Mô tả | Example |
|-------|------|-------|---------|
| **accountName** | String | Tên chủ tài khoản | "John Doe" |
| **accountCurrency** | String | Currency của account | "LAK", "KHR", "USD" |
| **accountLocation** | String | Địa chỉ/vị trí account | "Vientiane Branch" |
| **accountPhone** | String | Phone (masked) | "***1234" |
| **creditCard** | String | Credit card (masked) | "****1234" |

### 2. Transaction Control
Thông tin control transaction flow và requirements.

| Field | Type | Mô tả | Business Logic |
|-------|------|-------|----------------|
| **internationalQuery** | String | "Y"/"N" | Y = cần tỷ giá, N = nội địa |
| **purposerequired** | String | "Y"/"N" | Y = bắt buộc nhập purpose |
| **purpose** | String | Mục đích GD | Compliance requirement |

### 3. Fee Information
Complex nested structure cho fee calculation.

#### Fee Structure:
```json
{
  "feelist": {
    "LAK": [
      {
        "feeamount": "5000",
        "feepercent": "0.1", 
        "from": "0"
      }
    ],
    "KHR": [
      {
        "feeamount": 1250.0,
        "feepercent": "0.1",
        "from": "0"
      }
    ]
  }
}
```

#### Fee Calculation Logic:
```java
// LAK Fee (String type)
String lakFeeAmount = "5000";           // Fixed fee
String lakFeePercent = "0.1";          // 0.1% of amount
String lakFrom = "0";                   // Apply from 0 LAK

// KHR Fee (Double type)  
Double khrFeeAmount = 1250.0;          // Fixed fee
String khrFeePercent = "0.1";          // 0.1% of amount
String khrFrom = "0";                   // Apply from 0 KHR

// Total Fee = Fixed Fee + (Amount * Percentage / 100)
```

### 4. Exchange Rate Information
Thông tin tỷ giá cho international transactions.

#### CrossBorderExchangeRate Structure:
```json
{
  "crossborderexchangerate": {
    "id": "LAK_KHR_001",
    "ccy": "KHR",
    "rate": "0.2", 
    "lastupdate": "2023-12-01T10:30:00"
  }
}
```

#### Exchange Rate Usage:
```java
// Convert LAK to KHR
double lakAmount = 100000;              // 100,000 LAK
double rate = 0.2;                      // 1 LAK = 0.2 KHR
double khrAmount = lakAmount * rate;    // 20,000 KHR
```

### 5. Tracking Information
Fields cho audit trail và reconciliation.

| Field | Type | Purpose | Example |
|-------|------|---------|---------|
| **referenceNumber** | String | T24/LAPNET reference | "REF123456789" |
| **time** | String | Response timestamp | "2023-12-01T10:30:00" |
| **terminal** | String | Processing terminal | "T24_NODE_01" |
| **invoice** | String | Bill reference | "INV001" |

### 6. LAPNET Original Message
Thông tin original request từ LAPNET (chỉ có trong interbank).

#### OriginalMessage Structure:
```json
{
  "originalmessage": {
    "frommember": "MBLAOS",
    "fromuser": "USER123",
    "fromuserfullname": "John Doe", 
    "fromaccounttype": "SAVINGS",
    "fromaccount": "**********",
    "totype": "CURRENT",
    "toaccount": "**********", 
    "tomember": "BCEL",
    "reference": "REF123456",
    "time": "2023-12-01T10:30:00"
  }
}
```

## JSON Mapping Strategy

### 1. SerializedName Annotations:
```java
@SerializedName(value = "accountname", alternate = {"accountName"})
private String accountName;

@SerializedName("feelist")
private Fee feeList;

@SerializedName("crossborderexchangerate") 
private CrossBorderExchangeRate crossBorderExchangeRate;
```

### 2. Flexible Field Mapping:
- **Primary name**: T24/LAPNET standard format
- **Alternate names**: Support different API versions
- **Case handling**: lowercase vs camelCase

### 3. Type Handling:
```java
// LAK fees use String for precision
private String feeAmount;

// KHR fees use Double for calculations  
private Double feeAmount;

// Rates always String to preserve precision
private String rate;
```

## Usage Patterns

### 1. Internal MB Bank Verification:
```java
AccNumberVerifyDTO response = apiGeeService.verifyAccNumber(request);

// Simple domestic transaction
String accountName = response.getAccountName();
String currency = response.getAccountCurrency();
// No exchange rate, no original message
```

### 2. LAPNET Interbank Verification:
```java
AccNumberVerifyDTO response = apiGeeService.verifyAccNumber(request);

// Complex international transaction
String accountName = response.getAccountName();
CrossBorderExchangeRate rate = response.getCrossBorderExchangeRate();
OriginalMessage original = response.getOriginalMessage();
Fee fees = response.getFeeList();
```

### 3. Fee Calculation:
```java
Fee feeList = response.getFeeList();
List<Lak> lakFees = feeList.getLak();
List<Khr> khrFees = feeList.getKhr();

// Calculate total fee based on currency and amount
if ("LAK".equals(currency)) {
    // Use LAK fee structure
    for (Lak lakFee : lakFees) {
        if (amount >= Long.parseLong(lakFee.getFrom())) {
            fixedFee = Long.parseLong(lakFee.getFeeAmount());
            percentFee = amount * Double.parseDouble(lakFee.getFeePercent()) / 100;
            break;
        }
    }
}
```

## Business Rules Implementation

### 1. International Transaction Detection:
```java
boolean isInternational = "Y".equals(response.getInternationalQuery());
if (isInternational) {
    // Apply exchange rate
    // Use international fees
    // Check original message
}
```

### 2. Purpose Requirement:
```java
boolean purposeRequired = "Y".equals(response.getPurposerequired());
if (purposeRequired && Validator.isNull(request.getPurpose())) {
    throw new BadRequestAlertException("Purpose is required");
}
```

### 3. Fee Selection:
```java
String currency = response.getAccountCurrency();
Fee feeList = response.getFeeList();

if ("LAK".equals(currency)) {
    List<Lak> applicableFees = feeList.getLak();
} else if ("KHR".equals(currency)) {
    List<Khr> applicableFees = feeList.getKhr();
}
```

## Error Scenarios

### 1. Missing Required Fields:
```java
if (Validator.isNull(response.getAccountName())) {
    throw new BadRequestAlertException("Account not found");
}
```

### 2. Invalid Exchange Rate:
```java
if (isInternational && Validator.isNull(response.getCrossBorderExchangeRate())) {
    throw new BadRequestAlertException("Exchange rate not available");
}
```

### 3. Fee Calculation Errors:
```java
Fee feeList = response.getFeeList();
if (Validator.isNull(feeList) || 
    (Validator.isNull(feeList.getLak()) && Validator.isNull(feeList.getKhr()))) {
    throw new BadRequestAlertException("Fee information not available");
}
```

## Performance Considerations

### 1. Nested Object Creation:
- **Lazy initialization**: Nested objects chỉ tạo khi cần
- **Memory efficient**: @JsonInclude(NON_NULL) giảm memory usage

### 2. String vs Numeric Types:
- **Precision**: String cho rates và amounts để tránh floating point errors
- **Performance**: Double cho calculations khi cần speed

### 3. Serialization:
- **Gson compatibility**: @SerializedName cho Gson
- **Jackson compatibility**: @JsonInclude cho Jackson
- **Flexible mapping**: Support multiple JSON formats

Class `AccNumberVerifyDTO` là **data contract** quan trọng giữa MB App và T24/LAPNET, đảm bảo accurate và complete information cho transfer processing!
