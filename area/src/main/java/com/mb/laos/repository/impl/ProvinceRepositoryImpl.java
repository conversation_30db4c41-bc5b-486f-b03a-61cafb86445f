/**
 *
 */
package com.mb.laos.repository.impl;

import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import com.mb.laos.model.Province;
import com.mb.laos.repository.extend.ProvinceRepositoryExtend;

/**
 * <AUTHOR>
 *
 */
public class ProvinceRepositoryImpl implements ProvinceRepositoryExtend {
	@PersistenceContext
	private EntityManager entityManager;

	@Override
	public List<Province> findByHighlight() {
		StringBuilder sql = new StringBuilder();

		sql.append("SELECT e FROM Province e, HLProvince f WHERE e.provinceCode = f.provinceCode ");
		sql.append("AND e.status = 1 AND f.status = 1");

		Query query = this.entityManager.createQuery(sql.toString(), Province.class);

		return query.getResultList();
	}
}
