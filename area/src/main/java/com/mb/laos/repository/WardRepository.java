/**
 * 
 */
package com.mb.laos.repository;

import java.util.List;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.mb.laos.cache.util.AreaCacheConstants;
import com.mb.laos.model.Ward;
import com.mb.laos.repository.extend.WardRepositoryExtend;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface WardRepository extends JpaRepository<Ward, String>, WardRepositoryExtend {

	/**
	 * @param districtCode
	 * @param status
	 * @return
	 */
	@Cacheable(cacheNames = AreaCacheConstants.Ward.FIND_BY_DISTRICT, key = "#districtCode", unless = "#result == null")
	List<Ward> findByDistrictCodeAndStatus(String districtCode, int status);

	/**
	 * @param wardCode
	 * @return
	 */
	@Cacheable(cacheNames = AreaCacheConstants.Ward.FIND_BY_ID, key = "#wardCode", unless = "#result == null")
	default Ward findByWardCode(String wardCode) {
		return findById(wardCode).orElse(null);
	}

}
