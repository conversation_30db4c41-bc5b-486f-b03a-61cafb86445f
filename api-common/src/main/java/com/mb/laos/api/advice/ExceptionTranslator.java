package com.mb.laos.api.advice;

import java.io.IOException;
import java.io.Serializable;
import java.net.SocketTimeoutException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.crypto.BadPaddingException;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.tomcat.util.http.fileupload.impl.SizeLimitExceededException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.ConcurrencyFailureException;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.zalando.problem.DefaultProblem;
import org.zalando.problem.Problem;
import org.zalando.problem.ProblemBuilder;
import org.zalando.problem.Status;
import org.zalando.problem.spring.web.advice.ProblemHandling;
import org.zalando.problem.spring.web.advice.security.SecurityAdviceTrait;
import org.zalando.problem.spring.web.advice.validation.ConstraintViolationProblem;
import org.zalando.problem.spring.web.advice.validation.Violation;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.exc.MismatchedInputException;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.DecryptErrorException;
import com.mb.laos.api.exception.HttpResponseException;
import com.mb.laos.api.exception.InternalServerErrorException;
import com.mb.laos.api.exception.NoPermissionException;
import com.mb.laos.api.exception.ResourceNotFoundException;
import com.mb.laos.api.exception.UnauthorizedException;
import com.mb.laos.api.util.ApiConstants;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.api.util.HeaderUtil;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.Validator;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * Controller advice to translate the server side exceptions to client-friendly
 * json structures. The error response follows RFC7807 - Problem Details for
 * HTTP APIs (https://tools.ietf.org/html/rfc7807).
 */
@Slf4j
@RestControllerAdvice
public class ExceptionTranslator implements ProblemHandling, SecurityAdviceTrait {

    @Value("${spring.servlet.multipart.max-file-size}")
    private String maxFileSize;

    /**
     * Post-process the Problem payload to add the message key for the front-end if
     * needed.
     */
    @Override
    public ResponseEntity<Problem> process(@Nullable ResponseEntity<Problem> entity, NativeWebRequest request) {
        if (entity == null) {
            return null;
        }

        Problem problem = entity.getBody();

        if (!(problem instanceof ConstraintViolationProblem || problem instanceof DefaultProblem)) {
            return entity;
        }

        ProblemBuilder builder = Problem.builder()
                .withStatus(problem.getStatus()).withTitle(problem.getTitle())
                .with(ApiConstants.ErrorKey.PATH,
                        Objects.requireNonNull(request.getNativeRequest(HttpServletRequest.class)).getRequestURI());

        if (problem instanceof ConstraintViolationProblem) {
            ConstraintViolationProblem cvProblem = (ConstraintViolationProblem) problem;

            List<Violation> violations = cvProblem.getViolations();

            String message = violations.stream().map(Violation::getMessage)
                    .map(Labels::getLabels)
                    .collect(Collectors.joining(StringPool.COMMA));

            builder.with(ApiConstants.ErrorKey.VIOLATIONS, violations)
                    .withType(ApiConstants.ErrorType.CONSTRAINT_VIOLATION_TYPE)
                    .with(ApiConstants.ErrorKey.MESSAGE, message);
        } else {
            builder.withCause(((DefaultProblem) problem).getCause())
                    .withType(Problem.DEFAULT_TYPE.equals(problem.getType())
                            ? ApiConstants.ErrorType.DEFAULT_TYPE
                            : problem.getType())
                    .withInstance(problem.getInstance());

            problem.getParameters().forEach(builder::with);

            if (!problem.getParameters().containsKey(ApiConstants.ErrorKey.MESSAGE) && problem.getStatus() != null) {
                builder.with(ApiConstants.ErrorKey.MESSAGE,
                                Labels.getLabels(LabelKey.ERROR_AN_UNEXPECTED_ERROR_HAS_OCCURRED));
            }
        }

        return new ResponseEntity<>(builder.build(), entity.getHeaders(), entity.getStatusCode());
    }

//    @Override
//    public ResponseEntity<Problem> handleMethodArgumentNotValid(MethodArgumentNotValidException ex,
//                                                                @Nonnull NativeWebRequest request) {
//        BindingResult result = ex.getBindingResult();
//
//        List<FieldError> fieldErrors = result.getFieldErrors().stream()
//                .map(f -> new FieldError(f.getObjectName().replaceFirst("DTO$", ""), f.getField(), Labels.getLabels(f.getDefaultMessage())))
//                .collect(Collectors.toList());
//
//        _log.error("Method argument not valid: {}", StringUtil.join(fieldErrors, StringPool.PIPE));
//
//        Problem problem = Problem.builder().withType(ApiConstants.ErrorType.CONSTRAINT_VIOLATION_TYPE)
//                .withTitle(Labels.getLabels(LabelKey.ERROR_METHOD_ARGUMENT_NOT_VALID))
//                .withStatus(defaultConstraintViolationStatus())
//                .with(ApiConstants.ErrorKey.MESSAGE, Labels.getLabels(LabelKey.ERROR_METHOD_ARGUMENT_NOT_VALID))
//                // .with(ApiConstants.ErrorKey.FIELD_ERRORS, fieldErrors)
//                .build();
//
//        return create(ex, problem, request);
//    }

    @Override
    public ResponseEntity<Problem> handleMessageNotReadableException(HttpMessageNotReadableException e,
                    NativeWebRequest request) {
        String fieldName = null;
        Throwable cause = e.getCause();

        if (cause instanceof JsonParseException) {
            JsonParseException jpe = (JsonParseException) cause;
            fieldName = jpe.getOriginalMessage();
            _log.error("JsonParseException: {}", fieldName);
        }

        else if (cause instanceof MismatchedInputException) {
            MismatchedInputException mie = (MismatchedInputException) cause;
            if (Validator.isNotNull(mie.getPath())) {
                fieldName = mie.getPath().get(0).getFieldName();
            }

            _log.error("MismatchedInputException: {}", fieldName);
        }

        else if (cause instanceof JsonMappingException) {
            JsonMappingException jme = (JsonMappingException) cause;
            if (Validator.isNotNull(jme.getPath())) {
                fieldName = jme.getPath().get(0).getFieldName();
            }

            _log.error("JsonMappingException: {}", fieldName);
        }

        Problem problem = Problem.builder().withType(ApiConstants.ErrorType.CONSTRAINT_VIOLATION_TYPE)
                        .withTitle(Labels.getLabels(LabelKey.ERROR_METHOD_ARGUMENT_NOT_VALID))
                        .withStatus(defaultConstraintViolationStatus())
                        .with(ApiConstants.ErrorKey.MESSAGE, Labels.getLabels(LabelKey.ERROR_METHOD_ARGUMENT_NOT_VALID))
                        //.with(ApiConstants.ErrorKey.FIELD_ERRORS, fieldName)
                        .build();

        return create(e, problem, request);
    }

    @ExceptionHandler(BadRequestAlertException.class)
    public ResponseEntity<Problem> handleBadRequestAlertException(BadRequestAlertException ex,
                                                                  NativeWebRequest request) {
        return create(ex, request,
                HeaderUtil.createFailureAlert(true, ex.getErrorKey(), ex.getErrorParams(), ex.getMessage()));
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<Problem> handleResourceNotFoundException(ResourceNotFoundException ex,
                                                                   NativeWebRequest request) {
        return create(ex, request,
                HeaderUtil.createFailureAlert(true, ex.getErrorKey(), ex.getErrorParams(), ex.getMessage()));
    }

    @ExceptionHandler(UnauthorizedException.class)
    public ResponseEntity<Problem> handleUnauthorizedException(UnauthorizedException ex,
                                                               NativeWebRequest request) {
        return create(ex, request,
                HeaderUtil.createFailureAlert(true, ex.getErrorKey(), ex.getErrorParams(), ex.getMessage()));
    }

    @ExceptionHandler(NoPermissionException.class)
    public ResponseEntity<Problem> handleNoPermissionException(NoPermissionException ex, NativeWebRequest request) {
        Problem problem =
                Problem.builder().withStatus(Status.FORBIDDEN)
                        .with(ApiConstants.ErrorKey.MESSAGE,
                                Labels.getLabels(LabelKey.ERROR_YOU_MIGHT_NOT_HAVE_PERMISSION_TO_PERFORM_THIS_ACTION))
                        .build();

        return create(ex, problem, request);
    }

    @ExceptionHandler(DecryptErrorException.class)
    public ResponseEntity<Problem> handleDecryptErrorException(DecryptErrorException ex, NativeWebRequest request) {
        Problem problem =
                Problem.builder().withStatus(Status.BAD_REQUEST)
                        .with(ApiConstants.ErrorKey.MESSAGE,
                                Labels.getLabels(LabelKey.ERROR_CANNOT_DECRYPT_DATA))
                        .build();

        return create(ex, problem, request);
    }

    @ExceptionHandler(InternalServerErrorException.class)
    public ResponseEntity<Problem> handleInternalServerErrorException(InternalServerErrorException ex,
                    NativeWebRequest request) {
        return create(ex, request,
                        HeaderUtil.createFailureAlert(true, ex.getErrorKey(), ex.getErrorParams(), ex.getMessage()));
    }

    @ExceptionHandler(ConcurrencyFailureException.class)
    public ResponseEntity<Problem> handleConcurrencyFailure(ConcurrencyFailureException ex, NativeWebRequest request) {
        Problem problem = Problem.builder().withStatus(Status.CONFLICT)
                .with(ApiConstants.ErrorKey.MESSAGE, Labels.getLabels(LabelKey.ERROR_CONCURRENCY_FAILURE)).build();

        return create(ex, problem, request);
    }

    @ExceptionHandler(BadPaddingException.class)
    public ResponseEntity<Problem> handleBadPaddingException(BadPaddingException ex, NativeWebRequest request) {
        Problem problem = Problem.builder().withStatus(Status.BAD_REQUEST)
                        .with(ApiConstants.ErrorKey.MESSAGE, Labels.getLabels(LabelKey.ERROR_ENCRYPTED_DATA_IS_INVALID))
                        .build();

        return create(ex, problem, request);
    }

    @ExceptionHandler(HttpResponseException.class)
    public ResponseEntity<Problem> handleHttpResponseException(HttpResponseException ex, NativeWebRequest request) {
        Problem problem = Problem.builder().withStatus(Status.BAD_REQUEST)
                        .with(ApiConstants.ErrorKey.ERROR_CODE, ErrorCode.MSG1008.name())
                        .with(ApiConstants.ErrorKey.MESSAGE,
                                        Labels.getLabels(ErrorCode.MSG1008.getKey()))
                        .build();

        return create(ex, problem, request);
    }

    @ExceptionHandler(IOException.class)
    public ResponseEntity<Problem> handleIOException(IOException ex, NativeWebRequest request) {
        if (StringUtils.containsIgnoreCase(ExceptionUtils.getRootCauseMessage(ex), "Broken pipe")) {   //(2)
            return null;        //(2)	socket is closed, cannot return any response
        }

        Problem problem = Problem.builder().withStatus(Status.SERVICE_UNAVAILABLE)
                .with(ApiConstants.ErrorKey.MESSAGE, Labels.getLabels(LabelKey.ERROR_SERVICE_UNAVAILABLE)).build();

        return create(ex, problem, request);
    }

    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<Problem> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException ex,
                                                                        NativeWebRequest request) {
        _log.error("MaxUploadSizeExceededException: {}",
                Labels.getLabels(LabelKey.ERROR_THIS_FILE_IS_TOO_LARGE_TO_UPLOAD, new Object[]{this.maxFileSize}));

        Problem problem = Problem
                .builder().withStatus(Status.BAD_REQUEST).with(ApiConstants.ErrorKey.MESSAGE, Labels
                        .getLabels(LabelKey.ERROR_THIS_FILE_IS_TOO_LARGE_TO_UPLOAD, new Object[]{this.maxFileSize}))
                .build();

        return create(ex, problem, request);
    }

    @ExceptionHandler(SizeLimitExceededException.class)
    public ResponseEntity<Problem> handleSizeLimitExceededException(SizeLimitExceededException ex,
                                                                    NativeWebRequest request) {
        _log.error("SizeLimitExceededException: {}",
                Labels.getLabels(LabelKey.ERROR_THIS_FILE_IS_TOO_LARGE_TO_UPLOAD, new Object[]{this.maxFileSize}));

        Problem problem = Problem
                .builder().withStatus(Status.BAD_REQUEST).with(ApiConstants.ErrorKey.MESSAGE, Labels
                        .getLabels(LabelKey.ERROR_THIS_FILE_IS_TOO_LARGE_TO_UPLOAD, new Object[]{this.maxFileSize}))
                .build();

        return create(ex, problem, request);
    }

    @ExceptionHandler({SocketTimeoutException.class, ResourceAccessException.class})
    public ResponseEntity<Problem> handleSocketTimeoutException(SizeLimitExceededException ex,
                                                                    NativeWebRequest request) {
        _log.error("Socket timeout exception: {}",
                Labels.getLabels(LabelKey.ERROR_AN_UNEXPECTED_ERROR_HAS_OCCURRED));

        Problem problem = Problem
                .builder().withStatus(Status.BAD_REQUEST).with(ApiConstants.ErrorKey.MESSAGE, Labels
                        .getLabels(LabelKey.ERROR_AN_UNEXPECTED_ERROR_HAS_OCCURRED))
                .build();

        return create(ex, problem, request);
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public static class FieldError implements Serializable {
        private static final long serialVersionUID = -3173266024911499987L;

        private final String objectName;

        private final String field;

        private final String message;
    }
}
