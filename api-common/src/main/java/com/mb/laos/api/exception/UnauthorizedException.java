package com.mb.laos.api.exception;

import java.net.URI;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;
import com.mb.laos.api.util.ApiConstants;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.messages.Labels;
import lombok.Getter;

@Getter
public class UnauthorizedException extends AbstractThrowableProblem {

	private static final long serialVersionUID = 1L;

	private final String errorKey;

    private final Object[] errorParams;

    public UnauthorizedException(ErrorCode errorCode) {
    	this(Labels.getLabels(errorCode.getKey()), errorCode.name(), errorCode.getKey());
    }
    
    public UnauthorizedException(String defaultMessage, String errorCode, String errorKey) {
        this(ApiConstants.ErrorType.DEFAULT_TYPE, defaultMessage, errorCode, errorKey, new Object[0]);
    }

    public UnauthorizedException(String defaultMessage, String errorCode, String errorKey, Object[] errorParams) {
        this(ApiConstants.ErrorType.DEFAULT_TYPE, defaultMessage, errorCode, errorKey, errorParams);
    }
    
    public UnauthorizedException(String defaultMessage, String errorKey,
                    Map<String, Object> params, Map<String, Object> values) {
        super(ApiConstants.ErrorType.DEFAULT_TYPE, defaultMessage, Status.UNAUTHORIZED, null, null, null, params);

        params.put(ApiConstants.ErrorKey.MESSAGE, defaultMessage);

        if (values != null && !values.isEmpty()) {
            params.put(ApiConstants.ErrorKey.PARAMS, values);
        }

        this.errorKey = errorKey;
        this.errorParams = new Object[0];
    }

    public UnauthorizedException(String defaultMessage, String errorKey,
                    Map<String, Object> params) {
        super(ApiConstants.ErrorType.DEFAULT_TYPE, defaultMessage, Status.UNAUTHORIZED, null, null, null, params);

        params.put(ApiConstants.ErrorKey.MESSAGE, defaultMessage);

        this.errorKey = errorKey;
        this.errorParams = new Object[0];
    }

    public UnauthorizedException(URI type, String defaultMessage, String errorCode, String errorKey,
                    Object[] errorParams) {
        super(type, defaultMessage, Status.UNAUTHORIZED, null, null, null,
                        getAlertParameters(defaultMessage, errorCode, errorKey, errorParams));

        this.errorKey = errorKey;
        this.errorParams = errorParams;
    }

    private static Map<String, Object> getAlertParameters(String message, String errorCode, String errorKey,
                    Object[] errorParams) {
        Map<String, Object> parameters = new HashMap<>();

        parameters.put(ApiConstants.ErrorKey.MESSAGE, message);
        parameters.put(ApiConstants.ErrorKey.ERROR_CODE, errorCode);
        parameters.put(ApiConstants.ErrorKey.ERROR_KEY, errorKey);
        parameters.put(ApiConstants.ErrorKey.PARAMS, Arrays.asList(errorParams));

        return parameters;
    }

}
