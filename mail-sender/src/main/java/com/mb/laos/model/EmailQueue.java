
package com.mb.laos.model;

import java.io.Serializable;
import java.time.Instant;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "EMAIL_QUEUE")
public class EmailQueue extends AbstractAuditingEntity implements Serializable {

	private static final long serialVersionUID = 6923712186569694562L;

	@Id
	@Column(name = "EMAIL_QUEUE_ID", length = 19)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "EMAIL_QUEUE_SEQ")
	@SequenceGenerator(sequenceName = "EMAIL_QUEUE_SEQ", name = "EMAIL_QUEUE_SEQ", initialValue = 1, allocationSize = 1)
	private long emailQueueId;

	@Column(name = "STATUS", nullable = false, length = 1)
	private int status;

	@Column(name = "TO_", length = 255, nullable = false)
	private String to;

	@Column(name = "CC", length = 255)
	private String cc;

	@Column(name = "BCC", length = 255)
	private String bcc;

	@Column(name = "SUBJECT", nullable = false, length = 255)
	private String subject;

	@Column(name = "BODY", nullable = false)
	private String body;

	@Column(name = "SENT_TIME")
	private Instant sentTime;

	@Column(name = "SCHEDULE_TIME")
	private Instant scheduleTime;

	@Column(name = "SENT_COUNT", nullable = false)
	private int sentCount;
}
