package com.mb.laos.configuration;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mb.laos.util.StringPool;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "dynamic-link")
public class DynamicLinkProperties {
    private String subDomain;
    private String deepLink;
    private String packageName;

    @JsonIgnoreProperties
    private final String link = "link=";
    @JsonIgnoreProperties
    private final String apn = "apn=";
    @JsonIgnoreProperties
    private final String ibi = "ibi=";

    public String buildDynamicLink(String referralsCode) {
        StringBuilder builder = new StringBuilder();
        builder.append(this.subDomain);
        builder.append(StringPool.QUESTION);
        builder.append(apn);
        builder.append(this.packageName);
        builder.append(StringPool.AMPERSAND);
        builder.append(ibi);
        builder.append(this.packageName);
        builder.append(StringPool.AMPERSAND);
        builder.append(link);
        builder.append(this.deepLink);
        builder.append(referralsCode);

        return builder.toString();
    }
}
