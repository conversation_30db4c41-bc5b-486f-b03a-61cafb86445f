package com.mb.laos.model.search;

import com.mb.laos.enums.NationCode;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.enums.TransferTransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.messages.LabelKey;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
public class TransactionSearchRequest extends Parameter {
    private String keyword;

    @NotNull(message = LabelKey.ERROR_START_DATE_IS_REQUIRED)
    private LocalDate fromDate;

    @NotNull(message = LabelKey.ERROR_END_DATE_IS_REQUIRED)
    private LocalDate toDate;

    private Long transferTransactionId;

    private String transactionCode;

    private List<TransferType> transferTypes;

    private String bankCode;

    private List<TransactionStatus> transactionStatus;

    private String timeZoneStr;

    private boolean hasPageable;

    private List<TransferTransactionType> types;

    private String customerAccNumber;

    private List<String> customerAccNumbers;

    private boolean isHistory;

    private List<String> billingTypes;

    private List<String> savingAccountTypes;

    private TransferType transferType;

    private Long customerId;

    private List<String> nationCodes;

    private Boolean isIgnoreInternationalPayment;

    private String nation = NationCode.LA.name();
}
