package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import com.mb.laos.enums.MerchantCreatedOrigin;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.MasterMerchantType;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.validator.ValidationConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.Instant;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MerchantDTO extends Request implements Serializable {
    private static final long serialVersionUID = 2361109732412055517L;

    private Long merchantId;

    private Long parentId;

    @NotBlank(message = "error.merchant.name-required")
    @Size(max = ValidationConstraint.LENGTH.MERCHANT.MERCHANT_NAME_MAX_LENGTH,
            message = "error.merchant.name-max-length")
    private String merchantName;

    @Size(max = ValidationConstraint.LENGTH.MERCHANT.MERCHANT_CODE_MAX_LENGTH,
            message = "error.merchant.code-max-length")
    @Pattern(regexp = ValidationConstraint.PATTERN.CODE_REGEX,
            message = LabelKey.ERROR_MERCHANT_CODE_IS_INVALID)
    private String merchantCode;

    @NotBlank(message = "error.merchant.account-number-required")
    @Pattern(regexp = ValidationConstraint.PATTERN.MASTER_MERCHANT_ACCOUNT_NUMBER,
            message = LabelKey.ERROR_MERCHANT_ACCOUNT_NUMBER_IS_INVALID)
    private String merchantAccountNumber;

    private String merchantBankCode;

    private String merchantType;

    private String channel;

    @Size(max = ValidationConstraint.LENGTH.MERCHANT.MERCHANT_DESCRIPTION_MAX_LENGTH,
            message = "error.merchant.description-max-length")
    private String description;

    private Integer status;

    private String statusStr;

    private MerchantCreatedOrigin origin;

    private MasterMerchantType serviceType;

    private Long customerId;

    // Danh sách merchant con
    // List<MerchantDTO> child;

    QrCodeDTO qrCode;

    private String currency;

    public String getStatusStr() {
        return this.status == EntityStatus.ACTIVE.getStatus() ?
                Labels.getLabels(LabelKey.LABEL_ACTIVE) : Labels.getLabels(LabelKey.LABEL_INACTIVE);
    }

    private String merchantParentName;

    private String merchantParentCode;

    private String serviceTypeStr;

    private Long balance;

    private Instant createdDate;

    private String createdDateStr;

    private Instant lastModifiedDate;

    private String cif;

    private String phoneNumber;

    private Double totalAmount;

    private String totalAmountStr;

    private Long totalQuantity;

    private Long feeId;

    public MerchantDTO(String merchantName, String merchantCode, String merchantAccountNumber, Long parentId, int status, MerchantCreatedOrigin origin, Long customerId, String currency) {
        this.merchantName = merchantName;
        this.merchantCode = merchantCode;
        this.merchantAccountNumber = merchantAccountNumber;
        this.parentId = parentId;
        this.status = status;
        this.origin = origin;
        this.customerId = customerId;
        this.currency = currency;
    }
}
