package com.mb.laos.model;

import java.time.LocalDate;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.hibernate.search.annotations.Analyze;
import org.hibernate.search.annotations.Analyzer;
import org.hibernate.search.annotations.Field;
import org.hibernate.search.annotations.FieldBridge;
import org.hibernate.search.annotations.Index;
import org.hibernate.search.annotations.Indexed;
import org.hibernate.search.annotations.Normalizer;
import org.hibernate.search.annotations.SortableField;
import org.hibernate.search.annotations.Store;
import org.hibernate.search.annotations.TermVector;
import org.hibernate.search.bridge.builtin.LongBridge;
import com.mb.laos.util.Constants;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Indexed
@Entity
@Table(name = "LOAN_ONLINE")
@Data
@EqualsAndHashCode(callSuper = false)
public class LoanOnline extends AbstractAuditingEntity {
    /**
     *
     */
    private static final long serialVersionUID = 1257876475317306901L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LOAN_ONLINE_SEQ")
    @SequenceGenerator(sequenceName = "LOAN_ONLINE_SEQ", name = "LOAN_ONLINE_SEQ", allocationSize = 1)
	@Field(name = SortName.LOAN_ONLINE_ID_SORT, index = Index.YES, analyze = Analyze.NO, store = Store.YES)
    @SortableField(forField = SortName.LOAN_ONLINE_ID_SORT)
    @Column(name = "LOAN_ONLINE_ID")
    private long loanOnlineId;

    @Column(name = "LOAN_PRODUCT_ID")
    private long loanProductId;

	@Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES, 
			analyzer = @Analyzer(definition = Constants.AnalyzerDefName.EDGE_NGRAM), 
			store = Store.YES)
    @FieldBridge(impl = LongBridge.class)
    @Column(name = "LOAN_AMOUNT")
    private long loanAmount;

	@Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES, 
			analyzer = @Analyzer(definition = Constants.AnalyzerDefName.EDGE_NGRAM), 
			store = Store.YES)
    @FieldBridge(impl = LongBridge.class)
    @Column(name = "LOAN_TIME")
    private long loanTime;

    @Column(name = "COLLATERAL")
    private String collateral;

	@Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES, 
			analyzer = @Analyzer(definition = Constants.AnalyzerDefName.EDGE_NGRAM), 
			store = Store.YES)
	@Field(name = LoanOnline.SortName.FULL_NAME_SORT, termVector = TermVector.YES, index = Index.YES, 
			analyze = Analyze.NO, normalizer = @Normalizer(definition = Constants.AnalyzerDefName.LOWERCASE), 
			store = Store.YES)
    @SortableField(forField = LoanOnline.SortName.FULL_NAME_SORT)
    @Column(name = "FULLNAME")
    private String fullName;

    @Column(name = "DATE_OF_BIRTH")
    private LocalDate dateOfBirth;

    @Column(name = "GENDER", length = 1)
    private int gender;

    @Column(name = "MARITAL_STATUS")
    private Integer maritalStatus;

    @Column(name = "EMAIL")
    private String email;

	@Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES, 
			analyzer = @Analyzer(definition = Constants.AnalyzerDefName.EDGE_NGRAM), 
			store = Store.YES)
    @Column(name = "PHONE_NUMBER", length = 20)
//    @Type(type = Constants.HibernateDefName.ENCRYPTED_STRING)
    private String phoneNumber;

    @Column(name = "ADDRESS")
    private String address;

    @Column(name = "JOB")
    private String job;

    @Column(name = "POSITION")
    private String position;

    @Column(name = "WORKPLACE")
    private String workplace;

    @Column(name = "INCOME")
    private long income;

    @Field
    @Column(name = "STATUS")
    private Integer status;

	@Field(termVector = TermVector.YES, index = Index.YES, analyze = Analyze.YES, store = Store.YES)
	@Field(name = "createdDate", index = Index.YES, analyze = Analyze.NO, store = Store.YES)
    @SortableField(forField = "createdDate")
    @Column(name = "CREATED_DATE", updatable = false, insertable = false)
    private LocalDateTime localDateTime;

    @Transient
    private LoanProduct loanProduct;

    public interface FieldName {
        String STATUS = "status";
        String FULL_NAME = "fullName";
        String LOAN_AMOUNT = "loanAmount";
        String PHONE_NUMBER = "phoneNumber";
        String LOAN_TIME = "loanTime";
    }

    public interface SortName {
        String FULL_NAME_SORT = "fullNameSort";
        String CREATED_DATE_SORT = "createdDateSort";
        String LOAN_ONLINE_ID_SORT = "loanOnlineIdSort";
    }
}
