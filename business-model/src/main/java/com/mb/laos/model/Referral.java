package com.mb.laos.model;

import com.mb.laos.enums.ReferralType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Entity
@Table(name = "REFERRAL")
@Data
@EqualsAndHashCode(callSuper = false)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class Referral extends AbstractAuditingEntity {
    private static final long serialVersionUID = 7020271006196714571L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "REFERRAL_SEQ")
    @SequenceGenerator(sequenceName = "REFERRAL_SEQ", name = "REFERRAL_SEQ", initialValue = 1,
            allocationSize = 1)
    @Column(name = "REFERRAL_ID")
    private long referralId;

    @Column(name = "REFERRAL_CODE")
    private String referralCode;

    @Column(name = "USER_FULL_NAME")
    private String userFullName;

    @Column(name = "USER_CODE")
    private String userCode;

    @Column(name = "PHONE_NUMBER")
    private String phoneNumber;

    @Column(name = "RM_CODE")
    private String rmCode;

    @Column(name = "RM_LINK")
    private String rmLink;

    @Column(name = "TYPE")
    @Enumerated(EnumType.STRING)
    private ReferralType type;

	@Column(name = "STATUS")
	private Integer status;
}
