package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.Instant;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FeeScheduleDTO implements Serializable {
    private static final long serialVersionUID = -3174227228533667952L;

    private long feeScheduleId;
    private int status;
    private DocumentDTO icon;
    private FeeScheduleDetailDTO feeScheduleDetailDTO;
    private Instant createdDate;
    private Instant lastModifiedDate;
}
