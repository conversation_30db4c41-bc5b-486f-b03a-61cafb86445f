package com.mb.laos.model.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.TransferType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ServicePackSearch extends Parameter {

    /** The Constant serialVersionUID */
    private static final long serialVersionUID = 8449900647061223734L;

    private String type;

    private String clientId;

    private List<String> clientIds;

    private List<String> types;
}
