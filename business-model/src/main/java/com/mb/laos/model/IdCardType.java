package com.mb.laos.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "ID_CARD_TYPE")
@EqualsAndHashCode(callSuper = false)
public class IdCardType extends AbstractAuditingEntity {
    private static final long serialVersionUID = 7420188180749328275L;

    @Id
    @Column(name = "ID_CARD_TYPE_ID", length = 19, nullable = false)
    private long idCardTypeId;
    
    @Column(name = "NAME", length = 75)
    private String name;
    
    @Column(name = "CODE", length = 50)
    private String code;

    @Column(name = "EXPIRED_TIME_VALID")
    private Integer expiredTimeValid;

    @Column(name = "DESCRIPTION", length = 255)
    private String description;
    
    @Column(name = "STATUS", length = 2)
    private int status;
}
