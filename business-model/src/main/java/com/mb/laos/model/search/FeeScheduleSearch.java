package com.mb.laos.model.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.messages.LabelKey;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FeeScheduleSearch extends Parameter {

    private static final long serialVersionUID = -2379902830211422898L;

    private String title;

    private Integer status;

    @NotNull(message = LabelKey.ERROR_START_DATE_IS_REQUIRED)
    private LocalDate fromDate;

    @NotNull(message = LabelKey.ERROR_END_DATE_IS_REQUIRED)
    private LocalDate toDate;

    private String language;
}
