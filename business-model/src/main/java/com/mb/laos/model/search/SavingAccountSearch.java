package com.mb.laos.model.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.SavingAccountType;
import com.mb.laos.enums.TransferType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SavingAccountSearch extends Parameter {
    private String encryptedKeyword;

    private List<SavingAccountType> types;

    private LocalDate fromDate;

    private LocalDate toDate;

    private String keyword;

    private Integer status;

    private Long id;

    private TransferType transferType;

    private boolean hasPageable;
}
