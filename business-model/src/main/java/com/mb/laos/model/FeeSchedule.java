package com.mb.laos.model;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "FEE_SCHEDULE")
public class FeeSchedule extends AbstractAuditingEntity implements Serializable {
    private static final long serialVersionUID = 2334027358635553889L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "FEE_SCHEDULE_SEQ")
    @SequenceGenerator(sequenceName = "FEE_SCHEDULE_SEQ", name = "FEE_SCHEDULE_SEQ", initialValue = 1, allocationSize = 1)
    @Column(name = "FEE_SCHEDULE_ID")
    private long feeScheduleId;

    @Column(name = "STATUS")
    private int status;
}
