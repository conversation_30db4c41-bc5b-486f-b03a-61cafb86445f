package com.mb.laos.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@EqualsAndHashCode(callSuper = false)
@Table(name = "CASHOUT_ACCOUNT")
public class CashoutAccount extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CASHOUT_ACCOUNT_SEQ")
    @SequenceGenerator(sequenceName = "CASHOUT_ACCOUNT_SEQ", name = "CASHOUT_ACCOUNT_SEQ", initialValue = 1, allocationSize = 1)
    @Column(name = "ID", length = 19)
    private long id;

    @Column(name = "ACCOUNT_BANK", columnDefinition = "VARCHAR2(255)")
    private String accountBank;

    @Column(name = "ACCOUNT_NUMBER", columnDefinition = "VARCHAR2(255)")
    private String accountNumber;

    @Column(name = "ACCOUNT_NAME", columnDefinition = "VARCHAR2(255)")
    private String accountName;

    @Column(name = "ACCOUNT_TYPE", columnDefinition = "VARCHAR2(255)")
    private String accountType;

    @Column(name = "ACCOUNT_CURRENCY", columnDefinition = "VARCHAR2(255)")
    private String accountCurrency;

    @Column(name = "ACCOUNT_BALANCE")
    private Long accountBalance;

    @Column(name = "CONFIGURATION_TYPE", columnDefinition = "VARCHAR2(255)")
    private String configurationType;

    @Column(name = "STATUS", length = 1, nullable = false)
    private int status;

    @Column(name = "TRANSACTION_LIMIT", nullable = false)
    private Long transactionLimit;

    @Column(name = "AMOUNT_TRANSFERRED")
    private Long amountTransferred;

    @Column(name = "CIF", length = 8)
    private String cif;
}
