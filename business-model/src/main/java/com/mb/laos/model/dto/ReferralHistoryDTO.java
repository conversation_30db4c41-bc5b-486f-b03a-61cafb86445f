package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReferralHistoryDTO implements Serializable {
    private static final long serialVersionUID = 7020271006196714571L;

    private Long referralHistoryId;

    private Long referralId;

    private Long targetUserId;

    private String targetUserFullName;

    private String targetUserPhoneNumber;

    private Long quantity;

    private Integer status;

    private LocalDateTime createdDate;

    private LocalDateTime lastModifiedDate;



    public ReferralHistoryDTO(Long quantity, Long referralId) {
        this.quantity = quantity;
        this.referralId = referralId;
    }

}
