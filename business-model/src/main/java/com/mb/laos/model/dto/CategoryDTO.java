package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.CategoryType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class CategoryDTO implements Serializable {

    private static final long serialVersionUID = -5651507114929129680L;

    private Long categoryId;

    private String name;

    private CategoryType type;
}
