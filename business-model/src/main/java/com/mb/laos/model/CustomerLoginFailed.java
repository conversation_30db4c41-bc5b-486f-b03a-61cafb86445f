package com.mb.laos.model;

import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Entity
@Table(name = "CUSTOMER_LOGIN_FAILED")
@EqualsAndHashCode(callSuper = false)
public class CustomerLoginFailed extends AbstractAuditingEntity {

	private static final long serialVersionUID = 4482834111410590241L;

	/**
	 * @param customerId
	 */
	public CustomerLoginFailed(long customerId) {
		super();
		this.customerId = customerId;
	}

	@Id
	@Column(name = "CUSTOMER_ID", length = 19)
	private long customerId;

	@Column(name = "LOGIN_FAILED_ATTEMPTS", length = 2)
	private int loginFailedAttempts;

	@Column(name = "UNLOCK_TIME")
	private LocalDateTime unlockTime;
}
