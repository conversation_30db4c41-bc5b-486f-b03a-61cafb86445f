/**
 *
 */
package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerSyncInfoDTO extends Request implements Serializable {
    private List<CustomerDTO> customerSuccess;
    private List<CustomerDTO> customerFails;

}
