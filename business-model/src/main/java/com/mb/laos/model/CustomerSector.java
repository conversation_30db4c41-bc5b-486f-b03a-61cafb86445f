package com.mb.laos.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@EqualsAndHashCode(callSuper = false)
@Table(name = "CUSTOMER_SECTOR")
public class CustomerSector extends AbstractAuditingEntity {

    private static final long serialVersionUID = 2310820584041303268L;

    @Id
    @Column(name = "CUSTOMER_SECTOR_ID")
    private int customerSectorId;
    
    @Column(name = "LIMIT_PER_TRANSACTION", length = 19, nullable = false)
    private Long limitPerTransaction;
    
    @Column(name = "LIMIT_PER_DAY", length = 19, nullable = false)
    private Long limitPerDay;

    @Column(name = "LIMIT_PER_MONTH", length = 19, nullable = false)
    private Long limitPerMonth;
    
    @Column(name = "DESCRIPTION", length = 255)
    private String description;
}
