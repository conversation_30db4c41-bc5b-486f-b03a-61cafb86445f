package com.mb.laos.model;

import com.mb.laos.enums.TransactionFeeTypeCode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

/**
 * TransactionFeeType - Entity quản lý loại phí giao dịch
 *
 * Entity này định nghĩa các loại phí giao dịch trong hệ thống banking:
 * - <PERSON><PERSON> chuyển tiền nội bộ (INTERNAL_TRANSFER)
 * - <PERSON><PERSON> chuyển tiền liên ngân hàng (INTER_BANK_TRANSFER)
 * - <PERSON><PERSON> thanh toán QR Code (QR_PAYMENT)
 * - <PERSON><PERSON> nạp tiền điện thoại (TOPUP)
 * - <PERSON><PERSON> thanh toán hóa đơn (BILLING)
 * - Phí nạp ví điện tử (E_WALLET_RECHARGE)
 * - Phí rút tiền ATM (ATM_WITHDRAWAL)
 *
 * Mỗi loại phí có thể có nhiều mức phí khác nhau dựa trên:
 * - Số tiền giao dịch (amount ranges)
 * - Loại khách hàng (customer tier)
 * - Thời gian giao dịch (business hours vs after hours)
 * - Kênh giao dịch (mobile, internet, ATM)
 *
 * Entity này được cache để tối ưu performance vì:
 * - Dữ liệu ít thay đổi
 * - Được truy cập thường xuyên trong fee calculation
 * - Cần response time nhanh cho user experience
 *
 * <AUTHOR> Laos Development Team
 * @version 1.0
 * @since 2023
 */
@Entity                                                    // JPA: Đánh dấu đây là entity class
@Table(name = "T_TRANSACTION_FEE_TYPE")                   // JPA: Map với bảng T_TRANSACTION_FEE_TYPE trong database
@Data                                                     // Lombok: Tự động generate getter, setter, toString, equals, hashCode
@EqualsAndHashCode(callSuper = false)                     // Lombok: Generate equals/hashCode, không include parent class fields
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE) // Hibernate: Cache strategy cho performance optimization
public class TransactionFeeType extends AbstractAuditingEntity {

    /**
     * Serial version UID cho Java serialization
     * Đảm bảo compatibility khi class structure thay đổi
     */
    private static final long serialVersionUID = 7020271006196714891L;

    /**
     * ID duy nhất của loại phí giao dịch (Primary Key)
     *
     * Được generate tự động từ sequence TRANSACTION_FEE_TYPE_SEQ.
     * Sử dụng làm foreign key trong bảng Fee để liên kết với fee rates.
     *
     * Ví dụ:
     * - ID 1: INTERNAL_TRANSFER
     * - ID 2: INTER_BANK_TRANSFER
     * - ID 3: QR_PAYMENT
     * - ID 4: TOPUP
     */
    @Id                                                    // JPA: Đánh dấu đây là primary key
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TRANSACTION_FEE_TYPE_SEQ") // JPA: Auto-generate từ sequence
    @SequenceGenerator(sequenceName = "TRANSACTION_FEE_TYPE_SEQ", name = "TRANSACTION_FEE_TYPE_SEQ", initialValue = 1,
            allocationSize = 1)                           // JPA: Cấu hình sequence generator
    @Column(name = "TRANSACTION_FEE_TYPE_ID")             // JPA: Map với column TRANSACTION_FEE_TYPE_ID
    private long transactionFeeTypeId;

    /**
     * Tên hiển thị của loại phí giao dịch
     *
     * Tên dễ hiểu cho người dùng và admin, hỗ trợ đa ngôn ngữ.
     * Được sử dụng trong:
     * - Admin panel để quản lý fee configuration
     * - Reports và analytics
     * - Customer service support
     *
     * Ví dụ:
     * - "Phí chuyển tiền nội bộ"
     * - "Phí chuyển tiền liên ngân hàng"
     * - "Phí thanh toán QR Code"
     * - "Phí nạp tiền điện thoại"
     */
    @Column(name = "TRANSACTION_FEE_TYPE_NAME")           // JPA: Map với column TRANSACTION_FEE_TYPE_NAME
    private String transactionFeeTypeName;

    /**
     * Mã code của loại phí giao dịch (Business Key)
     *
     * Mã duy nhất để identify loại phí trong code logic.
     * Phải match với enum TransactionFeeTypeCode để type-safe.
     * Không được thay đổi sau khi đã deploy production.
     *
     * Các giá trị chuẩn:
     * - "INTERNAL_TRANSFER": Chuyển tiền nội bộ MB Bank
     * - "INTER_BANK_TRANSFER": Chuyển tiền liên ngân hàng qua LAPNET
     * - "INTERNAL_QR_BILLING": Thanh toán QR Code nội bộ
     * - "LAPNET_QR_BILLING": Thanh toán QR Code LAPNET
     * - "TOPUP": Nạp tiền điện thoại
     * - "BILLING": Thanh toán hóa đơn (điện, nước, internet...)
     * - "E_WALLET_RECHARGE": Nạp tiền ví điện tử (Umoney)
     * - "ATM_WITHDRAWAL": Rút tiền ATM
     * - "INTERNATIONAL_TRANSFER": Chuyển tiền quốc tế
     */
    @Column(name = "TRANSACTION_FEE_TYPE_CODE")           // JPA: Map với column TRANSACTION_FEE_TYPE_CODE
    private String transactionFeeTypeCode;

    /**
     * Mô tả chi tiết về loại phí giao dịch
     *
     * Thông tin bổ sung về:
     * - Phạm vi áp dụng của loại phí
     * - Điều kiện đặc biệt (nếu có)
     * - Business rules liên quan
     * - Integration notes cho developers
     *
     * Ví dụ:
     * - "Áp dụng cho chuyển tiền giữa các tài khoản MB Bank"
     * - "Phí LAPNET được lấy real-time từ network"
     * - "QR Code merchant có thể có fee riêng"
     */
    @Column(name = "DESCRIPTION")                         // JPA: Map với column DESCRIPTION
    private String description;

    /**
     * Loại cấu hình phí (Type Classification)
     *
     * Phân loại cách thức tính phí để system biết cách xử lý:
     *
     * Các giá trị:
     * - 1: STANDARD_FEE - Phí chuẩn theo bảng phí internal
     *   + Tính từ database fee configuration
     *   + Có thể có multiple tiers theo amount
     *   + Apply discount rules
     *
     * - 2: EXTERNAL_FEE - Phí từ hệ thống external
     *   + Lấy real-time từ LAPNET network
     *   + Không cache, luôn query fresh
     *   + Có thể có network timeout
     *
     * - 3: MERCHANT_SPECIFIC - Phí riêng cho merchant
     *   + Merchant có thể override general fee
     *   + Combine merchant fee + general fee
     *   + Business negotiation rates
     *
     * - 4: PROMOTIONAL_FEE - Phí khuyến mãi
     *   + Temporary fee reduction
     *   + Time-based promotion
     *   + Customer segment specific
     *
     * Business Logic:
     * ```java
     * if (type == 1) {
     *     return calculateStandardFee(amount, currency);
     * } else if (type == 2) {
     *     return getExternalFee(lapnetRequest);
     * } else if (type == 3) {
     *     return combineMerchantFee(merchantId, amount);
     * }
     * ```
     */
    @Column(name = "TYPE")                                // JPA: Map với column TYPE
    private int type;

    /**
     * Trạng thái của loại phí giao dịch
     *
     * Quản lý lifecycle của fee type configuration:
     *
     * Các giá trị (theo EntityStatus enum):
     * - 1: ACTIVE - Đang hoạt động
     *   + Fee type được sử dụng trong calculation
     *   + Hiển thị trong admin panel
     *   + Available cho new transactions
     *
     * - 2: INACTIVE - Tạm ngưng
     *   + Không áp dụng cho new transactions
     *   + Existing transactions vẫn valid
     *   + Có thể reactive sau này
     *
     * - 3: DELETED - Đã xóa (soft delete)
     *   + Không sử dụng trong bất kỳ calculation nào
     *   + Giữ lại cho audit trail
     *   + Không thể reactive
     *
     * - 4: PENDING - Chờ approval
     *   + Fee type mới chờ được approve
     *   + Chưa áp dụng trong production
     *   + Cần manager approval
     *
     * Business Impact:
     * - Chỉ ACTIVE fee types được sử dụng trong fee calculation
     * - INACTIVE types có thể cause fallback to default fee
     * - DELETED types sẽ throw exception nếu được reference
     */
    @Column(name = "STATUS")                              // JPA: Map với column STATUS
    private int status;
}
