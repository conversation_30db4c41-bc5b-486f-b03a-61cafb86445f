package com.mb.laos.model.dto;

import java.io.Serializable;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.validator.ValidationConstraint;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class BankDTO implements Serializable {
    private static final long serialVersionUID = 2361109732412055517L;

    private Long bankId;

    @NotBlank(message = "error.bank.name.required")
    @Size(max = ValidationConstraint.LENGTH.BANK_NAME_MAX, message = "error.bank.code-number.can.be.bigger")
    private String bankName;

    @NotBlank(message = "error.bank.code.required")
    @Size(max = ValidationConstraint.LENGTH.BANK_CODE_MAX, message = "error.bank.code-number.can.be.bigger")
    private String bankCode;

    private String branchCode;

    private Integer status;

    @Size(max = ValidationConstraint.LENGTH.MEDIUM_TEXT_MAX_LENGTH, message = "error.description.max-length")
    private String description;

    @NotBlank(message = "error.bank.code-number.required")
    @Size(max = ValidationConstraint.LENGTH.BANK_CODE_NUMBER_MAX, message = "error.bank.code-number.can.be.bigger")
    private String bankCodeNumber;

    @ApiModelProperty(required = true, notes = "Cấp độ của quyền, cấp độ giảm dần, cao nhất là quyền SupperAdmin có level = 999", example = "2")
    @Min(value = ValidationConstraint.LENGTH.BANK_ORDER_MIN, message = "error.bank.order.can.be.less")
    @Max(value = ValidationConstraint.LENGTH.BANK_ORDER_MAX, message = "error.bank.order.can.be.bigger")
    @NotNull(message = "error.bank.order.can.be.less")
    private Integer order;

    private String nation;

    private DocumentDTO icon;
}
