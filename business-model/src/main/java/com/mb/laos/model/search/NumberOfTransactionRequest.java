package com.mb.laos.model.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.DrCrType;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.validator.ValidationConstraint;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NumberOfTransactionRequest extends Parameter {

    private static final long serialVersionUID = -6915922021092686053L;

    private String keyword;

    private String encryptedKeyword;

    @NotNull(message = LabelKey.ERROR_START_DATE_IS_REQUIRED)
    private LocalDate fromDate;

    @NotNull(message = LabelKey.ERROR_END_DATE_IS_REQUIRED)
    private LocalDate toDate;

    private String timeZoneStr;

    @Min(value = ValidationConstraint.LENGTH.NUMBER_OF_TRANSACTION_MIN, message = LabelKey.ERROR_NUMBER_OF_TRANSACTION_MIN_IS_INVALID)
    private Long numberOfTransactionMin;

    @Min(value = ValidationConstraint.LENGTH.NUMBER_OF_TRANSACTION_MIN, message = LabelKey.ERROR_NUMBER_OF_TRANSACTION_MAX_IS_INVALID)
    private Long numberOfTransactionMax;

    private List<DrCrType> drCrs;

    private boolean hasPageable;
}
