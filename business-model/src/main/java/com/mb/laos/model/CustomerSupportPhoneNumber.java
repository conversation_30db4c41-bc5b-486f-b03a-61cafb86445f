package com.mb.laos.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.search.annotations.Indexed;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.io.Serializable;


@Indexed
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "CUSTOMER_SUPPORT_PHONE_NUMBER")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class CustomerSupportPhoneNumber extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 7020271006196714571L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CUSTOMER_SUPPORT_PHONE_NUMBER_SEQ")
    @SequenceGenerator(sequenceName = "CUSTOMER_SUPPORT_PHONE_NUMBER_SEQ", name = "CUSTOMER_SUPPORT_PHONE_NUMBER_SEQ", initialValue = 1, allocationSize = 1)
    @Column(name = "CUSTOMER_SUPPORT_PHONE_NUMBER_ID", length = 19)
    private long customerSupportPhoneNumberId;

    @Column(name = "CUSTOMER_SUPPORT_ID")
    private long customerSupportId;

    @Column(name = "PHONE_NUMBER")
    private String phoneNumber;

    @Column(name = "STATUS")
    private Integer status;

}
