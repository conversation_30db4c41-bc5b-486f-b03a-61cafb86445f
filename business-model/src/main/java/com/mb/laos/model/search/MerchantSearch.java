package com.mb.laos.model.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.MasterMerchantType;
import com.mb.laos.enums.MerchantCreatedOrigin;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MerchantSearch extends Parameter {

    /** The Constant serialVersionUID */
    private static final long serialVersionUID = 8449900647061223734L;

    private String merchantName;

    private String merchantCode;

    private String merchantBankCode;

    private String merchantAccountNumber;

    private MerchantCreatedOrigin origin;

    private Long parentId;

    private MasterMerchantType serviceType;

    private Boolean searchMaster = Boolean.TRUE;

    private LocalDate fromDate;

    private LocalDate toDate;

    private String timeZoneStr;
}
