package com.mb.laos.model;

import com.mb.laos.enums.ConfigurationFeeType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Entity
@Table(name = "CURRENCY")
@Data
@EqualsAndHashCode(callSuper = false)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class Currency extends AbstractAuditingEntity {
    private static final long serialVersionUID = 7020271006196714811L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CURRENCY_SEQ")
    @SequenceGenerator(sequenceName = "CURRENCY_SEQ", name = "CURRENCY_SEQ", initialValue = 1,
            allocationSize = 1)
    @Column(name = "CURRENCY_ID")
    private long currencyId;

    @Column(name = "NAME")
    private String name;

    @Column(name = "CODE")
    private String code;

    @Column(name = "VALUE")
    private String value;

    @Column(name = "STATUS")
    private int status;
}
