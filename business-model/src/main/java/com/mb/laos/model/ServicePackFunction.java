package com.mb.laos.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name ="SERVICE_PACK_FUNCTION")
public class ServicePackFunction extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 4638924756281720490L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SERVICE_PACK_FUNCTION_SEQ")
    @SequenceGenerator(name = "SERVICE_PACK_FUNCTION_SEQ", sequenceName = "SERVICE_PACK_FUNCTION_SEQ", initialValue = 1, allocationSize = 1)
    @Column(name="SERVICE_PACK_FUNCTION_ID")
    private long servicePackFunctionId;

    @Column(name = "NAME")
    private String name;

    @Column(name = "URL")
    private String url;

    @Column(name = "SERVICE_PACK_ID")
    private long servicePackId;

    @Column(name = "STATUS")
    private int status;
}
