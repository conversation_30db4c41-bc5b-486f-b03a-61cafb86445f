/**
 *
 */
package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;


@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerSupportDTO extends Request implements Serializable {

    private static final long serialVersionUID = 2361109732412055517L;

    public Long customerSupportId;

    public String address;

    private String email;

    private Integer status;

    private List<CustomerSupportPhoneNumberDTO> customerSupportPhoneNumbers;
}
