package com.mb.laos.model;

import com.mb.laos.enums.InsuranceContractType;
import com.mb.laos.enums.InsuranceType;
import com.mb.laos.enums.TransactionStatus;
import lombok.*;

import javax.persistence.*;
import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

@Entity
@Table(name = "T_TRANSACTION_INSURANCE")
@Data
@EqualsAndHashCode(callSuper = false)
public class TransactionInsurance extends AbstractAuditingEntity {
    private static final long serialVersionUID = 7020271006296714891L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TRANSACTION_INSURANCE_SEQ")
    @SequenceGenerator(sequenceName = "TRANSACTION_INSURANCE_SEQ", name = "TRANSACTION_INSURANCE_SEQ", initialValue = 1,
            allocationSize = 1)
    @Column(name = "TRANSACTION_INSURANCE_ID")
    private long transactionInsuranceId;

    @Column(name = "TRANSFER_TRANSACTION_ID")
    private long transferTransactionId;

    @Column(name = "ACCOUNT_NAME")
    private String accountName;

    @Column(name = "ACCOUNT_PHONE")
    private String accountPhone;

    @Column(name = "INSURED_NAME")
    private String insuredName;

    @Column(name = "INSURED_PHONE_NUMBER")
    private String insuredPhoneNumber;

    @Column(name = "INSURED_BIRTHDAY")
    private String insuredBirthday;

    @Column(name = "ID_CARD_TYPE_ID")
    private Long idCardTypeId;

    @Column(name = "INSURED_ID")
    private String insuredId;

    @Column(name = "CONTRACT_TYPE")
    @Enumerated(EnumType.STRING)
    private InsuranceContractType contractType;

    @Column(name = "TYPE")
    @Enumerated(EnumType.STRING)
    private InsuranceType type;

    @Column(name = "PACKAGE_CODE")
    private String packageCode;

    @Column(name = "VEHICLE_CODE")
    private String vehicleCode;

    @Column(name = "DELIVERY_CODE")
    private String deliveryCode;

    @Column(name = "PAY_TOTAL")
    private BigDecimal payTotal;

    @Column(name = "PREMIUM")
    private BigDecimal premium;

    @Column(name = "DISCOUNT")
    private BigDecimal discount;

    @Column(name = "PAY_CURRENCY")
    private String payCurrency;

    @Column(name = "IDENTIFICATION_NO")
    private String identificationNo;

    @Column(name = "CERTIFICATE_IMAGE")
    private String certificateImage;

    @Column(name = "CERTIFICATE")
    private String certificate;

    @Column(name = "CUSTOMER_ID")
    private Long customerId;

    @Column(name = "TRANSACTION_STATUS")
    @Enumerated(EnumType.STRING)
    private TransactionStatus transactionStatus;

    @Column(name = "TRANSACTION_CODE")
    private String transactionCode;

}
