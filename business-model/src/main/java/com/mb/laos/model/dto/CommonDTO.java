package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommonDTO implements Serializable {
    private static final long serialVersionUID = 5851422844257642453L;

    private Long commonId;

    private String category;

    private String code;

    private String value;

    private String description;

    private int status;
}
