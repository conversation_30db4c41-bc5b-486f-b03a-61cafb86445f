package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * DTO for {@link com.mb.laos.model.InterestRate}
 */
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class InterestRateInfoDTO implements Serializable {
    private static final long serialVersionUID = -8212927866139630113L;

    private long interestRateId;
    private int status;
    private List<InterestRateDetailDTO> details;
}
