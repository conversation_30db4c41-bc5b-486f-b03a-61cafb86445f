package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.model.AbstractAuditingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerDraftDTO extends AbstractAuditingEntity {

    private static final long serialVersionUID = 7819100550589159837L;

    private long customerDraftId;

    private Long referenceId;

    private String fullname;

    private LocalDate dateOfBirth;

    private String email;

    private String phoneNumber;

    private String phoneContact;

    private int status;

    private int gender;

    private String description;

    private Long idCardTypeId;

    private String idCardNumber;

    private LocalDate issueDate;

    private String nationCode;

    private String nationCodeOther;

    private String issuePlace;

    private String placeOfOrigin;

    private String placeOfResidence;

    private String placeOfResidenceOutCountry;

    private String currentAddress;

    private Integer customerSectorId;

    private Integer residentStatus;

    private Integer maritalStatus;

    private String staffCode;

    private String position;

    private String branchCode;

    private String cif;

    private String smsLanguage;

    private String job;

    private String transactionId;

    private String referralCode;

    private String password;

    private boolean hasAccountNumber;

}
