package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.NotificationHistoryEnum;
import com.mb.laos.enums.NotificationLimitType;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.enums.TransactionType;
import com.mb.laos.enums.TransferTransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.messages.Labels;
import com.mb.laos.util.InstantUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class NotificationLimitDTO {

    private long notificationLimitId;

    private long transferTransactionId;

    private TransferType transferType;

    private long customerId;

    private String customerAccNumber;

    private String target;

    private String bankCode;

    private String transactionId;

    private Long transactionAmount;

    private String message;

    private TransactionStatus transactionStatus;

    private String failureCause;

    private double transactionFee;

    private Instant transactionStartTime;

    private Instant transactionFinishTime;

    private String transactionCurrency;

    private String phoneNumber;

    private String branchCode;

    private int status;

    private TransactionType transactionType;

    private String transactionCode;

    private String beneficiaryCustomerName;

    private String beneficiaryAccountNumber;

    private String customerAccountName;

    private String customerCif;

    private String transactionDate;

    private double totalAmountTransaction;

    private String transactionStatusStr;

    private String transferTypeStr;

    private String transactionStartTimeStr;

    private String extraTransactionCode;

    private TransferTransactionType type;

    private String transferTransactionTypeStr;

    private String transactionAmountStr;

    private String transactionFeeStr;

    private String totalAmountTransactionStr;

    private double discount;

    private String clientMessageId;

    private String billingType;

    private String billingTypeStr;

    private String provinceCode;

    private String titleLa;

    private String accountName;

    private long discountFixed;

    private String discountFixedStr;

    private Double totalAmount;

    private String totalAmountStr;

    private double actualTransactionAmount;

    private Double limitPerTransaction;

    private Double limitPerDay;

    private Double limitPerMonth;

    private Double limitPerYear;

    private NotificationLimitType typeLimit;

    private String actualTransactionAmountStr;

    private String limitPerTransactionStr;

    private String limitPerDayStr;

    private String limitPerMonthStr;

    private String limitPerYearStr;

    private String statusStr;

    private String typeLimitStr;

    private NotificationHistoryEnum notificationType;

    private Instant createdDate;

    private Instant lastModifiedDate;

    private String createdDateStr;

    private String referralCode;

    public String formatTransactionStartTime() {
        return InstantUtil.formatStringDate(this.transactionFinishTime, Labels.getDefaultZoneId());
    }

}
