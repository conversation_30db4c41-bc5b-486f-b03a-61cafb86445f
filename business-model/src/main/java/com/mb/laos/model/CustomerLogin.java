package com.mb.laos.model;

import java.io.Serializable;
import java.time.Instant;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;
import com.mb.laos.util.Constants;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Entity
@Table(name = "CUSTOMER_LOGIN")
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerLogin extends AbstractAuditingEntity implements Serializable {

	private static final long serialVersionUID = 67775815282920687L;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CUSTOMER_LOGIN_SEQ")
	@SequenceGenerator(sequenceName = "CUSTOMER_LOGIN_SEQ", name = "CUSTOMER_LOGIN_SEQ", initialValue = 1,
			allocationSize = 1)
	@Column(name = "CUSTOMER_LOGIN_ID", length = 19)
	private long customerLoginId;
	
    @Column(name = "DEVICE_ID", length = 255)
    private String deviceId;
    
    @Column(name = "DEVICE_NAME", length = 255)
    private String deviceName;
    
    @Column(name = "PACKAGE_NAME", length = 255)
    private String packageName;
    
    @Column(name = "APP_VERSION", length = 10)
    private String appVersion;

	@Column(name = "LOGIN_TIME", updatable = false)
	private Instant loginTime;

    @Type(type = Constants.HibernateDefName.ENCRYPTED_STRING)
	@Column(name = "USERNAME", length = 75, nullable = false, updatable = false)
	private String username;
	
	@Column(name = "IP", length = 75)
	private String ip;

	@Column(name = "SUCCESS", length = 1)
	private boolean success;
	
	@Column(name = "DESCRIPTION", length = 255)
	private String description;
	
	@Column(name = "DEVICE_IMEI", length = 255)
	private String deviceImei;
	
	@Column(name = "DEVICE_OS", length = 255)
	private String deviceOs;

}
