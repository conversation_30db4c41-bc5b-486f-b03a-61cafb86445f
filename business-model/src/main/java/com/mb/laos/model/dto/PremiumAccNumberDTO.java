package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.model.Customer;
import com.mb.laos.model.PremiumAccountNumberStructure;
import com.mb.laos.model.Referral;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class PremiumAccNumberDTO implements Serializable {
    private static final long serialVersionUID = 4024884587362828786L;

    private Long premiumAccNumberId;

    private String premiumAccNumber;

    private Double discount;

    private Long originalPrice;

    private Long totalPrice;

    private Long referralId;

    private Long customerId;

    private int status;

    private String referralCode;


    private Customer customer;


    private String statusStr;

    private String createdDateStr;

    private Instant createdDate;


    private String originalPriceStr;


    private String totalPriceStr;


    private Instant paymentDate;


    private Instant paymentDueDate;

    private Long premiumAccNumberStructureId;


    private Long transactionId;

    private String cif;

    private String fullName;

    private String phoneNumber;

    private String numberStructure;

    private String rmCode;

    public PremiumAccNumberDTO(long premiumAccNumberId, Long premiumAccNumberStructureId, String premiumAccNumber,
                               double discount, Long originalPrice, Long totalPrice, Long referralId, long customerId,
                               int status, Instant createdDate, String cif, String fullName, String phoneNumber,
                               String rmCode, String numberStructure, String referralCode) {
        this.premiumAccNumberId = premiumAccNumberId;
        this.premiumAccNumberStructureId = premiumAccNumberStructureId;
        this.premiumAccNumber = premiumAccNumber;
        this.discount = discount;
        this.originalPrice = originalPrice;
        this.totalPrice = totalPrice;
        this.referralId = referralId;
        this.customerId = customerId;
        this.status = status;
        this.createdDate = createdDate;
        this.rmCode = rmCode;
        this.cif = cif;
        this.fullName = fullName;
        this.phoneNumber = phoneNumber;
        this.numberStructure = numberStructure;
        this.referralCode = referralCode;
    }
}
