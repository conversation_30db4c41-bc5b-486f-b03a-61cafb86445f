package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BeneficiaryBankDTO implements Serializable {

    private static final long serialVersionUID = 2361109732412055517L;

    private Long beneficiaryBankId;

    private String name;

    private String code;

    private Long codeNumber;

    private Integer status;

    private String title;
}
