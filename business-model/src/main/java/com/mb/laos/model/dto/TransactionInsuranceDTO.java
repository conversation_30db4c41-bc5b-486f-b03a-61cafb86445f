package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.InsuranceContractType;
import com.mb.laos.enums.InsuranceType;
import com.mb.laos.enums.TransactionStatus;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransactionInsuranceDTO implements Serializable {
    private String cif;
    private String insuredName;
    private String insuredPhone;
    private String identificationNo;
    private InsuranceType type;
    private String packageCode;
    private String vehicleCode;
    private BigDecimal payTotal;
    private BigDecimal mbPayTotal;
    private BigDecimal discount;
    private double mbDiscount;
    private double mbFee;
    private BigDecimal premium;
    private String payCurrency;
    private TransactionStatus transactionStatus;
    private String transactionCode;
    private Instant transactionTime;
    private String transactionTimeStr;
    private String payTotalCurrency;
    private String premiumCurrency;
    private String mbPayTotalCurrency;
    private String mbFeeCurrency;
    private String productTypeStr;
    private long transactionInsuranceId;
    private long transferTransactionId;
    private String accountName;
    private String accountPhone;
    private String insuredPhoneNumber;
    private String insuredBirthday;
    private Long idCardTypeId;
    private String insuredId;
    private InsuranceContractType contractType;
    private String deliveryCode;
    private String certificateImage;
    private String certificate;
    private Long customerId;
    private DocumentDTO icon;
    private String createdBy;
    private Instant createdDate;
    private String lastModifiedBy;
    private Instant lastModifiedDate;

    public TransactionInsuranceDTO(String cif, String insuredName, String insuredPhone, String identificationNo, InsuranceType type, String packageCode, String vehicleCode, BigDecimal payTotal, BigDecimal discount, BigDecimal premium, String payCurrency, TransactionStatus transactionStatus, String transactionCode, Instant transactionTime, long transferTransactionId) {
        this.cif = cif;
        this.insuredName = insuredName;
        this.insuredPhone = insuredPhone;
        this.identificationNo = identificationNo;
        this.type = type;
        this.packageCode = packageCode;
        this.vehicleCode = vehicleCode;
        this.payTotal = payTotal;
        this.discount = discount;
        this.premium = premium;
        this.payCurrency = payCurrency;
        this.transactionStatus = transactionStatus;
        this.transactionCode = transactionCode;
        this.transactionTime = transactionTime;
        this.transferTransactionId = transferTransactionId;

    }
}
