/**
 *
 */
package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.annotation.Exclude;
import com.mb.laos.annotation.ValueOfEnum;
import com.mb.laos.api.request.Request;
import com.mb.laos.enums.CustomerActiveStatus;
import com.mb.laos.enums.Gender;
import com.mb.laos.enums.MaritalStatus;
import com.mb.laos.enums.ResidentStatus;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.model.PremiumAccNumberCacheDTO;
import com.mb.laos.validator.ValidationConstraint;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Past;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
//@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerDTO extends Request implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 2361109732412055517L;

    @Exclude
    private Long customerId;

    @Exclude
    private String username;

    @ApiModelProperty(required = true)
    @NotBlank(message = LabelKey.ERROR_USERNAME_MUST_NOT_BE_EMPTY)
    @Size(max = ValidationConstraint.LENGTH.FULL_NAME_MAX_LENGTH, min = ValidationConstraint.LENGTH.FULL_NAME_MIN_LENGTH,
            message = LabelKey.ERROR_FULLNAME_IS_INVALID)
    private String fullname;

    @Exclude
    @ApiModelProperty(required = true, notes = "Số điện thoại đăng ký app")
    @NotBlank(message = LabelKey.ERROR_PHONE_NUMBER_IS_REQUIRED)
    @Pattern(regexp = ValidationConstraint.PATTERN.PHONE_NUMBER_LAOS_REGEX, message = LabelKey.ERROR_PHONE_NUMBER_IS_INVALID)
    private String phoneNumber;

    @ApiModelProperty(required = true, notes = "Số điện thoại liên hệ")
    @Pattern(regexp = ValidationConstraint.PATTERN.PHONE_NUMBER_REGEX, message = LabelKey.ERROR_PHONE_CONTACT_IS_INVALID)
    private String phoneContact;

    @ApiModelProperty(required = true, notes = "Ngày sinh")
    @NotNull(message = LabelKey.ERROR_DATE_OF_BIRTH_IS_REQUIRED)
    @Past(message = LabelKey.ERROR_DATE_OF_BIRTH_MUST_NOT_BE_GREATER_THAN_NOW)
    private LocalDate dateOfBirth;

    @Pattern(regexp = ValidationConstraint.PATTERN.EMAIL_REGEX, message = "error.email-is-invalid")
    private String email;

    @ApiModelProperty(notes = "1: Hoạt động, 0: Không hoạt động")
    private Integer status;

    @ApiModelProperty(notes = "Giới tính: 1 - Nam, 0 - Nữ, 2 - Khác", required = true)
    @NotNull(message = "error.gender-is-invalid")
    @ValueOfEnum(enumClass = Gender.class, message = "error.gender-is-invalid")
    private Integer gender;

    // Loại gttt
    @ApiModelProperty(notes = "Loại giấy tờ tùy thân", required = true)
    @NotNull(message = "error.customer.id-card-type-id.required")
    @Positive(message = "error.customer.id-card-type-id.invalid")
    private Long idCardTypeId;

    private String idCardTypeName;

    // Số gttt
    @Exclude
    @ApiModelProperty(notes = "Số giấy tờ tùy thân", required = true)
    @NotBlank(message = "error.customer.id-card-number.required")
    @Pattern(regexp = ValidationConstraint.PATTERN.ID_CARD_NUMBER, message = "error.customer.id-card-number.invalid")
    @Size(max = ValidationConstraint.LENGTH.ID_CARD_NUMBER_MAX_LENGTH, min = ValidationConstraint.LENGTH.ID_CARD_NUMBER_MIN_LENGTH, message = "error.customer.id-card-number-max-length")
    private String idCardNumber;

    // Ngày cấp
    @ApiModelProperty(notes = "Ngày cấp giấy tờ tùy thân", required = true)
    @NotNull(message = "error.customer.issue-date.required")
    @Past(message = "error.customer.issue-date-cannot-greater-than-now")
    private LocalDate issueDate;

    // Nơi cấp
    @ApiModelProperty(notes = "Nơi cấp giấy tờ tùy thân", required = true)
    @NotBlank(message = "error.issue-place-is-required")
    @Size(max = ValidationConstraint.LENGTH.VERY_SMALL_TEXT_MAX_LENGTH, message = "error.issue-place-is-max-length")
    private String issuePlace;

    // Quốc tịch
    @ApiModelProperty(notes = "Quốc tịch", required = true)
    @NotBlank(message = "error.nation-code-is-required")
    @Size(max = ValidationConstraint.LENGTH.NATION_CODE_MAX_LENGTH, message = "error.nation-code-is-max-length")
    private String nationCode;

    private String nationName;

    // Quốc tịch khác
    @Size(max = ValidationConstraint.LENGTH.NATION_CODE_MAX_LENGTH, message = "error.nation-code-is-max-length")
    private String nationCodeOther;

    private String nationOtherName;

    @Size(max = ValidationConstraint.LENGTH.SMALL_TEXT_MAX_LENGTH, message = "error.customer.device-token-is-invalid")
    private String deviceToken;

    // Nơi sinh
    @ApiModelProperty(notes = "Nơi sinh", required = true)
    @NotBlank(message = "error.place-of-origin-is-empty")
    @Size(max = ValidationConstraint.LENGTH.PLACE_ORIGIN_MAX_LENGTH, message = "error.place-of-origin.max-length")
    private String placeOfOrigin;

    // Tình trạng cư trú
    @ApiModelProperty(notes = "Tình trạng cư trú: 0 - tạm trú, 1 - thường trú")
    @ValueOfEnum(enumClass = ResidentStatus.class, message = "error.customer.resident-status.invalid")
    private Integer residentStatus;

    // Địa chỉ liên hệ hiện tại
    @ApiModelProperty(notes = "Địa chỉ liên hệ hiện tại", required = true)
    @Size(max = ValidationConstraint.LENGTH.SMALL_TEXT_MAX_LENGTH, message = "error.current-address-is-max-length")
    private String currentAddress;

    // Địa chỉ thường trú
    @ApiModelProperty(notes = "Địa chỉ thường trú", required = true)
    @NotBlank(message = "error.place-of-residence-is-empty")
    @Size(max = ValidationConstraint.LENGTH.SMALL_TEXT_MAX_LENGTH, message = "error.place-of-residence.max-length")
    private String placeOfResidence;

    // Địa chỉ thường trú ở nước ngoài
    @ApiModelProperty(notes = "Địa chỉ thường trú ở nước ngoài")
    @Size(max = ValidationConstraint.LENGTH.SMALL_TEXT_MAX_LENGTH, message = "error.place-of-residence-out-country.max-length")
    private String placeOfResidenceOutCountry;

    // Nghề nghiệp
    @ApiModelProperty(notes = "Nghề nghiệp")
    @Size(max = ValidationConstraint.LENGTH.JOB_MAX_LENGTH, message = "error.job-max-length")
    private String job;

    // Vị trí - chức vụ
    @ApiModelProperty(notes = "Vị trí - chức vụ")
    @Size(max = ValidationConstraint.LENGTH.JOB_MAX_LENGTH, message = "error.position.max-length")
    private String position;

    //    @ApiModelProperty(required = true)
    //    @NotBlank(message = "error.customer.cifNumber-is-required")
    //    @Size(max = ValidationConstraint.LENGTH.CIF_MAX_LENGTH, message = "error.customer.cif-max-length")
    // Chỉ để hiển thị khi thông tin T24 có trả về
    private String cif;

    private Integer customerOldSectorId;

    // Trạng thái hồ sơ định danh khách hàng
//    @ApiModelProperty(notes = "Trạng thái hồ sơ định danh khách hàng", required = true)
//    @NotNull(message = "error.customer.sector-id-required")
//    @ValueOfEnum(enumClass = Sector.class, message = "error.customer.sector-id-invalid")
    private Integer customerSectorId;

    // Tình trạng hôn nhân
    @ApiModelProperty(notes = "1: married, 0: single, 2: other")
    @ValueOfEnum(enumClass = MaritalStatus.class, message = "error.customer.marital-status.invalid")
    private Integer maritalStatus;

    // Mã nhân viên
    @Pattern(regexp = ValidationConstraint.PATTERN.PHONE_NUMBER_REGEX, message = "error.customer.staff-code.invalid")
    private String staffCode;

    @ApiModelProperty(notes = "1: Đăng ký khách hàng mới, 2: Cập nhật thông tin khách hàng, 3: Tạm khóa, 4: Mở khóa, 5: Đóng")
    private Integer approvalType;

    @ApiModelProperty(notes = "0: Draft, 1: Chờ phê duyệt, 2: Từ chối phê duyệt, 3: Đồng ý phê duyệt, 4: Phê duyệt lỗi")
    private Integer approvalStatus;

    @ApiModelProperty(notes = "0: Chưa kích hoạt, 1: Đã kích hoạt")
    private Integer activeStatus = CustomerActiveStatus.NOT_ACTIVATED.getStatus();

    @Size(max = ValidationConstraint.LENGTH.SMALL_TEXT_MAX_LENGTH, message = "error.customer.reason-max-length")
    private String reason;

    private String branchCode;

    private String smsLanguage;

    private Long referenceId;

    private Long customerDraftId;

    private LocalDateTime createdDate;

    private LocalDateTime lastModifiedDate;

    private List<DocumentDTO> idCardFiles;

    private List<DocumentDTO> signatureFiles;

    private boolean revertPremiumAccNumber;

    private List<DocumentDTO> ekycFiles;

    @Size(max = ValidationConstraint.LENGTH.ID_CARD_MAX_FILE_UPLOAD, message = "error.customer.id-card-max-file-upload")
    private List<Long> idCardFileDelete;

    private List<CustomerDTO> oldVersions;

    private String notificationStatus;

    private Boolean ekycRequired;

    private List<MoneyAccountDTO> moneyAccounts;

    private String statusStr;

    private String activeStatusStr;

    private String dateOfBirthStr;

    private String genderStr;

    @Size(max = ValidationConstraint.LENGTH.SMALL_TEXT_MAX_LENGTH, message = LabelKey.ERROR_CUSTOMER_WORK_PLACE_MAX_LENGTH)
    private String workplace;

    private String referralCode;

    private String rmCode;

    private String userFullNameReferral;

    private String phoneNumberReferral;

    private Integer accountType;

    private String accountTypeStr;

    private MultipartFile file;

    private Long totalTransactionDebit;

    private Long totalTransactionCredit;

    private Instant lastTransaction;

    private Instant lastCloseAccountNotification;

    private String approvalStatusStr;

    private Instant manualActivationTime;

    private String premiumAccountNumber;

    private String otp;

    private String transactionId;

    private PremiumAccNumberCacheDTO premiumAccountNumberCache;

    private boolean hasAccountNumber;

    public CustomerDTO(Long customerId, String phoneNumber, String idCardNumber, String cif) {
        this.customerId = customerId;
        this.phoneNumber = phoneNumber;
        this.idCardNumber = idCardNumber;
        this.cif = cif;
    }

    public CustomerDTO(Long totalTransactionDebit, Long totalTransactionCredit, Long customerId,
                       String cif, String fullname) {
        this.totalTransactionDebit = totalTransactionDebit;
        this.totalTransactionCredit = totalTransactionCredit;
        this.customerId = customerId;
        this.cif = cif;
        this.fullname = fullname;
    }

    public CustomerDTO(Instant lastTransaction, Long customerId) {
        this.customerId = customerId;
        this.lastTransaction = lastTransaction;
    }

    public CustomerDTO(String cif, String fullname) {
        this.cif = cif;
        this.fullname = fullname;
    }
}
