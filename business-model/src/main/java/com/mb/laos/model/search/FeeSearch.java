package com.mb.laos.model.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.ConfigurationFeeType;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FeeSearch extends Parameter {
    private static final long serialVersionUID = 5741356875920735417L;

    private ConfigurationFeeType configurationFeeType;

    private Long transactionFeeTypeId;

    private Long ignoredFeeId;

    private Long feeId;

    private Long merchantId;

    private Integer status;

    private Integer isSystem;
}
