package com.mb.laos.model.dto;

import com.mb.laos.enums.CurrencyType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
public class TotalMoneySavingAccountDTO implements Serializable {
    private static final long serialVersionUID = 6940911306334011053L;

    private boolean isBranch;
    private Long total;
    private Integer totalSavingAccount;
    private CurrencyType currency;
}
