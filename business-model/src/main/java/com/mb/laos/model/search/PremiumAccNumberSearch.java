package com.mb.laos.model.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PremiumAccNumberSearch extends Parameter {

    /** The Constant serialVersionUID */
    private static final long serialVersionUID = 8449900647061223734L;

    private Long premiumAccNumberId;

    private Long customerId;

    private List<Long> referralIds;

    private String timeZoneStr;

    private String encryptedKeyword;

    private boolean hasPageable;

//    @NotNull(message = "error.created-start-date.required")
    private LocalDate startDate;

//    @NotNull(message = "error.created-end-date.required")
    private LocalDate endDate;

    private Long numberRecord;

    private String expectedString;

    private List<String> accountLengths;

    private List<String> hideAccountNumber;

    private List<Integer> numberTypes;
}
