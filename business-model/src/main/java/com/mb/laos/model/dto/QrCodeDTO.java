package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.QrLapnetType;
import com.mb.laos.enums.QrType;
import com.mb.laos.response.QrCodeResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QrCodeDTO implements Serializable {
    private static final long serialVersionUID = 2361109732412055518L;

    private long qrCodeId;

    private QrType qrType;

    private String accountNumber;

    private String merchantId;

    private long customerId;

    private String description;

    private String qrCodeValue;

    private QrLapnetType type;

    private QrCodeResponse qrCodeResponse;

    private int status;
}
