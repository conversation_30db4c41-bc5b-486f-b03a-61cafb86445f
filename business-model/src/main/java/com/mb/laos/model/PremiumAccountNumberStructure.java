package com.mb.laos.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.search.annotations.Indexed;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Indexed
@EqualsAndHashCode(callSuper = false)
@Table(name = "PREMIUM_ACCOUNT_NUMBER_STRUCTURE")
public class PremiumAccountNumberStructure extends AbstractAuditingEntity {
    private static final long serialVersionUID = -6647510161936049454L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PREMIUM_ACCOUNT_NUMBER_STRUCTURE_SEQ")
    @SequenceGenerator(sequenceName = "PREMIUM_ACCOUNT_NUMBER_STRUCTURE_SEQ", name = "PREMIUM_ACCOUNT_NUMBER_STRUCTURE_SEQ", initialValue = 1, allocationSize = 1)
    @Column(name = "PREMIUM_ACCOUNT_NUMBER_STRUCTURE_ID", length = 19)
    private long premiumAccountNumberStructureId;

    @Column(name = "NAME")
    private String name;

    @Column(name = "NUMBER_GROUP_ID")
    private Long numberGroupId;

    @Column(name = "LENGTH")
    private Integer length;

    @Column(name = "NUMBER_STRUCTURE")
    private String numberStructure;

    @Column(name = "PATTERN")
    private String pattern;

    @Column(name = "PRICE")
    private Long price;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "TOTAL_PRICE")
    private Long totalPrice;

    @Column(name = "STATUS")
    private Integer status;

    @Column(name = "DISCOUNT")
    private Double discount;

}
