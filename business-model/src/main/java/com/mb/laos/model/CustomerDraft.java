package com.mb.laos.model;

import com.mb.laos.util.Constants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "CUSTOMER_DRAFT")
public class CustomerDraft extends AbstractAuditingEntity {

    private static final long serialVersionUID = 7819100550589159837L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CUSTOMER_DRAFT_SEQ")
    @SequenceGenerator(sequenceName = "CUSTOMER_DRAFT_SEQ", name = "CUSTOMER_DRAFT_SEQ", initialValue = 1, allocationSize = 1)
    @Column(name = "CUSTOMER_DRAFT_ID", length = 19)
    private long customerDraftId;

    @Column(name = "REFERENCE_ID")
    private Long referenceId;

    @Column(name = "FULLNAME", length = 255)
    private String fullname;

    @Column(name = "DATE_OF_BIRTH")
    private LocalDate dateOfBirth;

    @Column(name = "EMAIL", length = 255)
    private String email;

    @Column(name = "PHONE_NUMBER", length = 20)
    @Type(type = Constants.HibernateDefName.ENCRYPTED_STRING)
    private String phoneNumber;

    @Column(name = "PHONE_CONTACT", length = 20)
    @Type(type = Constants.HibernateDefName.ENCRYPTED_STRING)
    private String phoneContact;

    @Column(name = "STATUS", nullable = false, length = 2)
    private int status;

    @Column(name = "GENDER", length = 1)
    private int gender;

    @Column(name = "DESCRIPTION", length = 255)
    private String description;

    @Column(name = "ID_CARD_TYPE_ID")
    private Long idCardTypeId;

    @Column(name = "ID_CARD_NUMBER", length = 19)
    @Type(type = Constants.HibernateDefName.ENCRYPTED_STRING)
    private String idCardNumber;

    @Column(name = "ISSUE_DATE")
    private LocalDate issueDate;

    @Column(name = "NATION_CODE", length = 5)
    private String nationCode;

    @Column(name = "NATION_CODE_OTHER", length = 5)
    private String nationCodeOther;

    @Column(name = "ISSUE_PLACE", length = 500)
    private String issuePlace;

    @Column(name = "PLACE_OF_ORIGIN", length = 500)
    private String placeOfOrigin;

    @Column(name = "PLACE_OF_RESIDENCE", length = 500)
    private String placeOfResidence;

    @Column(name = "PLACE_OF_RESIDENCE_OUT_COUNTRY", length = 500)
    private String placeOfResidenceOutCountry;

    @Column(name = "CURRENT_ADDRESS", length = 500)
    private String currentAddress;

    @Column(name = "CUSTOMER_SECTOR_ID", length = 10)
    private Integer customerSectorId;

    @Column(name = "RESIDENT_STATUS")
    private Integer residentStatus;

    @Column(name = "MARITAL_STATUS")
    private Integer maritalStatus;

    @Column(name = "STAFF_CODE")
    private String staffCode;

    @Column(name = "POSITION")
    private String position;

    @Column(name = "BRANCH_CODE")
    private String branchCode;

    @Column(name = "CIF", length = 8, nullable = false)
    @Type(type = Constants.HibernateDefName.ENCRYPTED_STRING)
    private String cif;

    @Column(name = "SMS_LANGUAGE", length = 5)
    private String smsLanguage;

    @Column(name = "JOB", length = 500)
    private String job;

    @Column(name = "TRANSACTION_ID", unique = true, nullable = false)
    private String transactionId;

    @Column(name = "REFERRAL_CODE")
    private String referralCode;

    @Column(name = "PASSWORD")
    private String password;

}
