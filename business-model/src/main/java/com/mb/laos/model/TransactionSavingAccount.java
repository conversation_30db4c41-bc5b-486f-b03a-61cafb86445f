package com.mb.laos.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Table(name = "T_TRANSACTION_SAVING_ACCOUNT")
@Data
@EqualsAndHashCode(callSuper = false)
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
public class TransactionSavingAccount extends AbstractAuditingEntity {

    private static final long serialVersionUID = -9088035587264973730L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TRANSACTION_SAVING_ACCOUNT_SEQ")
    @SequenceGenerator(sequenceName = "TRANSACTION_SAVING_ACCOUNT_SEQ", name = "TRANSACTION_SAVING_ACCOUNT_SEQ", initialValue = 1,
            allocationSize = 1)
    @Column(name = "TRANSACTION_SAVING_ACCOUNT_ID")
    private long transactionSavingAccountId;

    @Column(name = "TRANSFER_TRANSACTION_ID")
    private long transferTransactionId;

    @Column(name = "SAVING_ACCOUNT_ID")
    private long savingAccountId;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "STATUS")
    private int status;
}
