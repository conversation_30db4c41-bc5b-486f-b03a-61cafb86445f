package com.mb.laos.model.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 
 * 07/07/2022 - LinhLH: Create new
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CampaignSearch extends Parameter {
    /** The Constant serialVersionUID */
    private static final long serialVersionUID = -6263588561054384548L;
    
    private String campaignName;

    private LocalDate startDateFrom;
    
    private LocalDate startDateTo;

    private boolean hasPageable;
}
