/**
 *
 */
package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;


@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerSupportPhoneNumberDTO extends Request implements Serializable {
    private static final long serialVersionUID = 2361109732412055517L;

    public Long customerSupportPhoneNumberId;

    private Long customerSupportId;

    public String phoneNumber;

    private Integer status;

    public CustomerSupportPhoneNumberDTO(String phoneNumber, Long customerSupportId, Integer status) {
        this.phoneNumber = phoneNumber;
        this.status = status;
        this.customerSupportId = customerSupportId;
    }
}
