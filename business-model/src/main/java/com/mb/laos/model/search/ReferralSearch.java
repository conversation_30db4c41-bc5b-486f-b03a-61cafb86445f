package com.mb.laos.model.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.ReferralType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReferralSearch extends Parameter{

    private String fullName;

    private String phoneNumber;

    private String rmCode;

    private String referralCode;

    private String userCode;

    private List<ReferralType> types;

    private boolean hasPageable;
}
