package com.mb.laos.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.mb.laos.enums.ConfigurationFeeType;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Fee - Entity quản lý cấu hình phí giao dịch
 *
 * Entity này lưu trữ cấu hình phí cho các loại giao dịch khác nhau trong hệ thống banking.
 * Mỗi Fee record định nghĩa một cấu hình phí cụ thể với:
 * - <PERSON><PERSON><PERSON> giao dịch (TransactionFeeType)
 * - <PERSON><PERSON><PERSON> tính phí (ConfigurationFeeType)
 * - Merchant áp dụng (nếu có)
 * - <PERSON><PERSON><PERSON> mứ<PERSON> phí chi tiết (FeeRate)
 *
 * Hệ thống phí hoạt động theo mô hình:
 * 1. TransactionFeeType: Định nghĩa loại giao dịch (TRANSFER, QR_PAYMENT...)
 * 2. Fee: Cấu hình phí cho loại giao dịch (có thể có nhiều config khác nhau)
 * 3. FeeRate: Chi tiết mức phí theo amount ranges
 *
 * Ví dụ cấu trúc:
 * - TransactionFeeType: "INTERNAL_TRANSFER"
 *   + Fee 1: General fee (cho tất cả customers)
 *   + Fee 2: Premium customer fee (discount)
 *   + Fee 3: Merchant-specific fee (cho merchant ABC)
 *
 * Mỗi Fee có thể có nhiều FeeRate:
 * - 0 - 1M LAK: 5,000 LAK
 * - 1M - 5M LAK: 10,000 LAK
 * - >5M LAK: 15,000 LAK
 *
 * Entity được cache để optimize performance vì:
 * - Được truy cập thường xuyên trong fee calculation
 * - Dữ liệu ít thay đổi
 * - Cần response time nhanh cho real-time transactions
 *
 * <AUTHOR> Laos Development Team
 * @version 1.0
 * @since 2023
 */
@Entity                                                    // JPA: Đánh dấu đây là entity class
@Table(name = "T_FEE")                                    // JPA: Map với bảng T_FEE trong database
@Data                                                     // Lombok: Tự động generate getter, setter, toString, equals, hashCode
@EqualsAndHashCode(callSuper = false)                     // Lombok: Generate equals/hashCode, không include parent class fields
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE) // Hibernate: Cache strategy cho performance optimization
public class Fee extends AbstractAuditingEntity {

    /**
     * Serial version UID cho Java serialization
     * Đảm bảo compatibility khi class structure thay đổi
     */
    private static final long serialVersionUID = 7020271006196714811L;

    /**
     * ID duy nhất của cấu hình phí (Primary Key)
     *
     * Được generate tự động từ sequence FEE_SEQ.
     * Sử dụng làm foreign key trong bảng FeeRate để liên kết với fee rates.
     *
     * Ví dụ:
     * - ID 1: General internal transfer fee
     * - ID 2: Premium customer internal transfer fee
     * - ID 3: Merchant ABC QR payment fee
     * - ID 4: LAPNET inter-bank transfer fee
     *
     * Relationship:
     * - One Fee -> Many FeeRate (1:N)
     * - Many Fee -> One TransactionFeeType (N:1)
     * - Many Fee -> One Merchant (N:1, optional)
     */
    @Id                                                    // JPA: Đánh dấu đây là primary key
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "FEE_SEQ") // JPA: Auto-generate từ sequence
    @SequenceGenerator(sequenceName = "FEE_SEQ", name = "FEE_SEQ", initialValue = 1,
            allocationSize = 1)                           // JPA: Cấu hình sequence generator
    @Column(name = "FEE_ID")                              // JPA: Map với column FEE_ID
    private long feeId;

    /**
     * Tên hiển thị của cấu hình phí
     *
     * Tên dễ hiểu cho admin và business users để quản lý fee configuration.
     * Được sử dụng trong:
     * - Admin panel để hiển thị danh sách fees
     * - Reports và analytics
     * - Audit logs và tracking
     * - Customer service support
     *
     * Naming convention:
     * - "[Transaction Type] - [Customer Segment] - [Special Condition]"
     *
     * Ví dụ:
     * - "Phí chuyển tiền nội bộ - Khách hàng thường"
     * - "Phí chuyển tiền nội bộ - Khách hàng VIP"
     * - "Phí QR Code - Merchant ABC"
     * - "Phí LAPNET - Giờ hành chính"
     * - "Phí nạp tiền điện thoại - Khuyến mãi"
     */
    @Column(name = "FEE_NAME")                            // JPA: Map với column FEE_NAME
    private String feeName;

    /**
     * Loại cấu hình phí (Configuration Type)
     *
     * Enum định nghĩa cách thức tính phí và apply business rules.
     * Quyết định logic xử lý trong fee calculation engine.
     *
     * Các giá trị (ConfigurationFeeType enum):
     *
     * 1. FIXED_AMOUNT:
     *    - Phí cố định không phụ thuộc vào số tiền giao dịch
     *    - Ví dụ: Phí SMS OTP = 500 LAK (fixed)
     *    - Logic: return fixedAmount
     *
     * 2. PERCENTAGE:
     *    - Phí theo tỷ lệ % của số tiền giao dịch
     *    - Ví dụ: Phí chuyển tiền = 0.1% amount
     *    - Logic: return amount * percentage / 100
     *
     * 3. TIERED:
     *    - Phí theo bậc thang dựa trên amount ranges
     *    - Ví dụ: 0-1M = 5K, 1M-5M = 10K, >5M = 15K
     *    - Logic: find matching tier based on amount
     *
     * 4. COMBINED:
     *    - Kết hợp fixed + percentage
     *    - Ví dụ: 2,000 LAK + 0.05% amount
     *    - Logic: return fixedAmount + (amount * percentage / 100)
     *
     * 5. EXTERNAL:
     *    - Phí được lấy từ external system (LAPNET)
     *    - Real-time query, không cache
     *    - Logic: call external API
     *
     * Business Impact:
     * - Quyết định algorithm tính phí
     * - Ảnh hưởng đến performance (external calls)
     * - Validation rules khác nhau
     */
    @Column(name = "CONFIGURATION_FEE_TYPE")              // JPA: Map với column CONFIGURATION_FEE_TYPE
    @Enumerated(EnumType.STRING)                          // JPA: Store enum as string value trong database
    private ConfigurationFeeType configurationFeeType;

    /**
     * ID của loại phí giao dịch (Foreign Key)
     *
     * Liên kết với bảng TransactionFeeType để xác định loại giao dịch.
     * Một TransactionFeeType có thể có nhiều Fee configurations khác nhau.
     *
     * Ví dụ mapping:
     * - transactionFeeTypeId = 1 (INTERNAL_TRANSFER):
     *   + Fee ID 1: General customer fee
     *   + Fee ID 2: VIP customer fee
     *   + Fee ID 3: Corporate customer fee
     *
     * - transactionFeeTypeId = 2 (QR_PAYMENT):
     *   + Fee ID 4: General QR fee
     *   + Fee ID 5: Merchant ABC specific fee
     *   + Fee ID 6: Promotional QR fee
     *
     * Business Logic:
     * ```java
     * // Tìm fee configuration cho transaction type
     * List<Fee> fees = feeRepository.findByTransactionFeeTypeIdAndStatus(
     *     transactionFeeTypeId, EntityStatus.ACTIVE);
     *
     * // Apply business rules để chọn fee phù hợp
     * Fee applicableFee = selectApplicableFee(fees, customer, merchant, amount);
     * ```
     *
     * Database Constraint:
     * - NOT NULL (bắt buộc phải có)
     * - Foreign Key reference đến T_TRANSACTION_FEE_TYPE.TRANSACTION_FEE_TYPE_ID
     */
    @Column(name = "TRANSACTION_FEE_TYPE_ID")             // JPA: Map với column TRANSACTION_FEE_TYPE_ID
    private long transactionFeeTypeId;

    /**
     * Trạng thái của cấu hình phí
     *
     * Quản lý lifecycle của fee configuration để control việc áp dụng phí.
     *
     * Các giá trị (theo EntityStatus enum):
     *
     * 1. ACTIVE (1) - Đang hoạt động:
     *    - Fee configuration được sử dụng trong calculation
     *    - Hiển thị trong admin panel
     *    - Available cho new transactions
     *    - Được include trong fee selection logic
     *
     * 2. INACTIVE (2) - Tạm ngưng:
     *    - Không áp dụng cho new transactions
     *    - Existing transactions vẫn valid (cho reconciliation)
     *    - Có thể reactive sau này
     *    - Ẩn khỏi admin panel selection
     *
     * 3. DELETED (3) - Đã xóa (soft delete):
     *    - Không sử dụng trong bất kỳ calculation nào
     *    - Giữ lại cho audit trail và historical data
     *    - Không thể reactive
     *    - Chỉ hiển thị trong audit reports
     *
     * 4. PENDING (4) - Chờ approval:
     *    - Fee configuration mới chờ được approve
     *    - Chưa áp dụng trong production
     *    - Cần manager/admin approval
     *    - Test mode only
     *
     * 5. SCHEDULED (5) - Đã lên lịch:
     *    - Fee sẽ active vào thời điểm trong tương lai
     *    - Promotional fees với start date
     *    - System tự động activate khi đến thời gian
     *
     * Business Logic:
     * ```java
     * // Chỉ lấy active fees cho calculation
     * List<Fee> activeFees = feeRepository.findByTransactionFeeTypeIdAndStatus(
     *     typeId, EntityStatus.ACTIVE.getStatus());
     *
     * if (activeFees.isEmpty()) {
     *     // Fallback to default fee hoặc throw exception
     *     throw new NoActiveFeeException();
     * }
     * ```
     *
     * Admin Operations:
     * - CREATE: status = PENDING
     * - APPROVE: PENDING -> ACTIVE
     * - SUSPEND: ACTIVE -> INACTIVE
     * - DELETE: ANY -> DELETED
     * - SCHEDULE: PENDING -> SCHEDULED
     */
    @Column(name = "STATUS")                              // JPA: Map với column STATUS
    private int status;

    /**
     * Loại phí bổ sung (Additional Fee Classification)
     *
     * Phân loại bổ sung để hỗ trợ business logic phức tạp và reporting.
     * Khác với configurationFeeType (technical), đây là business classification.
     *
     * Các giá trị thường dùng:
     *
     * 1. "STANDARD" - Phí chuẩn:
     *    - Phí thông thường theo bảng phí chính thức
     *    - Áp dụng cho majority customers
     *    - Stable, ít thay đổi
     *
     * 2. "PROMOTIONAL" - Phí khuyến mãi:
     *    - Phí giảm giá trong thời gian limited
     *    - Marketing campaigns
     *    - Có expiry date
     *
     * 3. "VIP" - Phí ưu đãi:
     *    - Dành cho premium customers
     *    - Lower fees hoặc free transactions
     *    - Based on customer tier/relationship
     *
     * 4. "CORPORATE" - Phí doanh nghiệp:
     *    - Negotiated rates cho business customers
     *    - Volume-based discounts
     *    - Contract-based pricing
     *
     * 5. "PENALTY" - Phí phạt:
     *    - Additional charges cho violations
     *    - Overdraft fees, late payment fees
     *    - Compliance-related charges
     *
     * 6. "EMERGENCY" - Phí khẩn cấp:
     *    - Higher fees cho after-hours transactions
     *    - Weekend/holiday surcharges
     *    - Express processing fees
     *
     * Usage trong Business Logic:
     * ```java
     * // Reporting by fee type
     * Map<String, BigDecimal> feeRevenue = calculateRevenueByFeeType();
     *
     * // Customer segment analysis
     * if ("VIP".equals(fee.getFeeType())) {
     *     applyVipBenefits(customer);
     * }
     *
     * // Promotional tracking
     * if ("PROMOTIONAL".equals(fee.getFeeType())) {
     *     trackPromotionalUsage(fee, customer);
     * }
     * ```
     *
     * Reporting Applications:
     * - Revenue analysis by fee type
     * - Customer segment profitability
     * - Promotional campaign effectiveness
     * - Compliance reporting
     */
    @Column(name = "FEE_TYPE")                            // JPA: Map với column FEE_TYPE
    private String feeType;

    /**
     * ID của merchant (Foreign Key, Optional)
     *
     * Liên kết với bảng Merchant để tạo merchant-specific fee configuration.
     * NULL = General fee áp dụng cho tất cả merchants/customers.
     * NON-NULL = Fee riêng cho merchant cụ thể.
     *
     * Use Cases:
     *
     * 1. MERCHANT-SPECIFIC FEES:
     *    - Merchant lớn negotiate special rates
     *    - Volume-based discounts
     *    - Partnership agreements
     *    - Competitive pricing
     *
     * 2. QR CODE MERCHANT FEES:
     *    - Merchant có thể có fee structure riêng
     *    - Different rates cho different merchant categories
     *    - Promotional rates cho new merchants
     *
     * 3. BILLING MERCHANT FEES:
     *    - Utility companies có rates khác nhau
     *    - Government services có special rates
     *    - Telecom operators có negotiated fees
     *
     * Business Logic Priority:
     * ```java
     * // 1. Tìm merchant-specific fee trước
     * Fee merchantFee = feeRepository.findByTransactionFeeTypeIdAndMerchantIdAndStatus(
     *     typeId, merchantId, EntityStatus.ACTIVE);
     *
     * if (merchantFee != null) {
     *     return calculateFee(merchantFee, amount);
     * }
     *
     * // 2. Fallback to general fee
     * Fee generalFee = feeRepository.findByTransactionFeeTypeIdAndMerchantIdIsNullAndStatus(
     *     typeId, EntityStatus.ACTIVE);
     *
     * return calculateFee(generalFee, amount);
     * ```
     *
     * Examples:
     * - merchantId = NULL: General QR payment fee (5,000 LAK)
     * - merchantId = 123: Merchant ABC QR fee (3,000 LAK - discounted)
     * - merchantId = 456: Merchant XYZ QR fee (0 LAK - promotional)
     *
     * Database Constraints:
     * - NULLABLE (optional field)
     * - Foreign Key reference đến T_MERCHANT.MERCHANT_ID
     * - Index on (TRANSACTION_FEE_TYPE_ID, MERCHANT_ID) cho performance
     */
    @Column(name = "MERCHANT_ID")                         // JPA: Map với column MERCHANT_ID
    private Long merchantId;

    /**
     * Flag đánh dấu fee configuration hệ thống
     *
     * Phân biệt giữa system-generated fees và user-configured fees.
     * Ảnh hưởng đến quyền chỉnh sửa và xóa trong admin panel.
     *
     * Các giá trị:
     *
     * 1. IS_SYSTEM = 1 (TRUE) - System Fee:
     *    - Fee được tạo bởi system/migration scripts
     *    - Core business fees không được phép xóa
     *    - Chỉ admin cấp cao mới được modify
     *    - Protected khỏi accidental deletion
     *    - Required cho system operation
     *
     * 2. IS_SYSTEM = 0 (FALSE) - User Fee:
     *    - Fee được tạo bởi admin users
     *    - Có thể freely modify/delete
     *    - Custom business requirements
     *    - Promotional/temporary fees
     *    - Merchant-specific configurations
     *
     * System Fees Examples:
     * - Default internal transfer fee
     * - Standard LAPNET inter-bank fee
     * - Basic QR payment fee
     * - ATM withdrawal fee
     * - SMS OTP fee
     *
     * User Fees Examples:
     * - Promotional discount fees
     * - Merchant-specific rates
     * - Seasonal campaign fees
     * - VIP customer fees
     * - Corporate negotiated rates
     *
     * Business Logic:
     * ```java
     * // Admin panel validation
     * if (fee.getIsSystem() == 1) {
     *     if (!user.hasRole("SUPER_ADMIN")) {
     *         throw new InsufficientPermissionException();
     *     }
     * }
     *
     * // Deletion protection
     * public void deleteFee(Long feeId) {
     *     Fee fee = feeRepository.findById(feeId);
     *     if (fee.getIsSystem() == 1) {
     *         throw new SystemFeeCannotBeDeletedExceptio();
     *     }
     *     fee.setStatus(EntityStatus.DELETED.getStatus());
     * }
     * ```
     *
     * Migration Strategy:
     * - All initial fees: isSystem = 1
     * - New admin-created fees: isSystem = 0
     * - System upgrades: preserve isSystem flag
     *
     * Security Implications:
     * - Prevents accidental deletion of critical fees
     * - Audit trail cho system vs user changes
     * - Role-based access control
     * - Data integrity protection
     */
    @Column(name = "IS_SYSTEM")                           // JPA: Map với column IS_SYSTEM
    private int isSystem;
}
