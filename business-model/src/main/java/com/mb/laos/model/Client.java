package com.mb.laos.model;

import com.mb.laos.enums.ClientType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name ="CLIENT")
public class Client extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 4638924756281720490L;

    @Id
    @Column(name="CLIENT_ID")
    private String clientId;

    @Column(name = "CLIENT_SECRET")
    private String clientSecret;

    @Column(name = "TYPE")
    @Enumerated(EnumType.STRING)
    private ClientType type;

    @Column(name = "NAME")
    private String name;

    @Column(name = "STATUS")
    private int status;
}
