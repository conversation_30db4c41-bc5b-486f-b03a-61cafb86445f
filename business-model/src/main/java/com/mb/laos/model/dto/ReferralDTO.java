package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.ReferralType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReferralDTO implements Serializable {
    private static final long serialVersionUID = 7020271006196714571L;

    private Long referralId;

    private String referralCode;

    private String userFullName;

    private String userCode;

    private String phoneNumber;

    private String rmCode;

    private String rmLink;

    private ReferralType type;

    private Integer status;

    // Số lượng khách hàng đã giới thiếu
    private Long quantity;

    private String statusStr;

    private String typeStr;

    private QrCodeGeneralDTO qrCode;

    public ReferralDTO(int status) {
        this.status = status;
    }
}
