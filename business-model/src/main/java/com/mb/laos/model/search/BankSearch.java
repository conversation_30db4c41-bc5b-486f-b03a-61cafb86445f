package com.mb.laos.model.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BankSearch extends Parameter {

    /** The Constant serialVersionUID */
    private static final long serialVersionUID = 8449900647061223734L;
    private String bankCode;

    private String name;

    private String code;

    private String nation;

    private String bankCodeNumber;
}
