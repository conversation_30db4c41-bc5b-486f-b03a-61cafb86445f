package com.mb.laos.repository.impl;

import com.mb.laos.enums.EntityStatus;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.SavingAccount;
import com.mb.laos.model.dto.SavingAccountDTO;
import com.mb.laos.model.search.SavingAccountSearch;
import com.mb.laos.repository.extend.SavingAccountRepositoryExtend;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;
import org.springframework.data.domain.Pageable;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SavingAccountRepositoryImpl implements SavingAccountRepositoryExtend {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<SavingAccountDTO> search(SavingAccountSearch savingAccountSearch, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        sql.append("select new com.mb.laos.model.dto.SavingAccountDTO" +
                "(e.savingAccountNumber, e.receivingAccountNumber, e.type, e.status, c.cif, c.fullname, " +
                "c.idCardNumber, c.phoneNumber, e.startTime, e.settlementDueTime, " +
                "e.savingAmount, e.finalizationMethod, e.interestRate, e.tenor, e.savingAccountId, " +
                "e.amtAfterSettlement, e.interestAmtEnd, e.rmCode) " +
                "from SavingAccount e " +
                "inner join Customer c on e.customerId = c.customerId ");

        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(savingAccountSearch, values));
        sql.append(QueryUtil.createOrderQuery(SavingAccount.class, savingAccountSearch.getOrderByType(),
                savingAccountSearch.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), SavingAccountDTO.class);

        values.forEach(query::setParameter);

        if (savingAccountSearch.isHasPageable()) {
            if (pageable != null) {
                query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

                query.setMaxResults(pageable.getPageSize());
            } else {
                query.setFirstResult(QueryUtil.FIRST_INDEX);

                query.setMaxResults(QueryUtil.MAX_RESULT);
            }
        }
        return query.getResultList();
    }

    @Override
    public Long count(SavingAccountSearch request) {
        StringBuilder sql = new StringBuilder();

        Map<String, Object> values = new HashMap<>();

        sql.append("select count(*) " +
                "from SavingAccount e " +
                "inner join Customer c on e.customerId = c.customerId ");

        sql.append(this.createWhereQuery(request, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    @Override
    public List<SavingAccountDTO> search(SavingAccountSearch savingAccountSearch) {
        return this.search(savingAccountSearch, null);
    }

    private String createWhereQuery(SavingAccountSearch request, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE e.status != :deletedStatus ");

        values.put("deletedStatus", EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(request.getId())) {
            sql.append(" AND e.savingAccountId = :id ");
            values.put("id", request.getId());
        } else {
            if (Validator.isNotNull(request.getKeyword())) {
                sql.append(" AND (lower(e.savingAccountNumber) LIKE lower(:keyword)");
                sql.append(" OR lower(c.fullname) LIKE lower(:keyword)");
                sql.append(" OR lower(c.cif) = lower(:keywordEncrypt)");
                sql.append(" OR lower(c.idCardNumber) = lower(:keywordEncrypt)");
                sql.append(" OR lower(c.phoneNumber) = lower(:keywordEncrypt))");

                values.put("keyword", QueryUtil.getFullStringParam(request.getKeyword()));
                values.put("keywordEncrypt", request.getEncryptedKeyword());
            }

            if (Validator.isNotNull(request.getFromDate())) {
                sql.append(" AND e.startTime >= :transactionTimeStart ");

                values.put("transactionTimeStart", InstantUtil.getInstantFromLocalDateTime(
                        LocalDateUtil.getStartOfDay(request.getFromDate()), Labels.getDefaultZoneId()));
            }

            if (Validator.isNotNull(request.getToDate())) {
                sql.append(" AND e.startTime <= :transactionTimeEnd ");

                values.put("transactionTimeEnd", InstantUtil.getInstantFromLocalDateTime(
                        LocalDateUtil.getEndOfDay(request.getToDate()), Labels.getDefaultZoneId()));
            }

            if (Validator.isNotNull(request.getStatus())) {
                sql.append(" AND e.status = :status ");
                values.put("status", request.getStatus());
            }

            if (Validator.isNotNull(request.getTypes())) {
                sql.append(" AND e.type in :types ");
                values.put("types", request.getTypes());
            }
        }
        return sql.toString();
    }
}
