package com.mb.laos.repository.extend;

import com.mb.laos.model.PremiumAccountNumberStructure;
import com.mb.laos.model.dto.PremiumAccountNumberStructureDTO;
import com.mb.laos.model.search.PremiumAccountNumberStructureSearch;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface PremiumAccountNumberStructureRepositoryExtend {

    long count(PremiumAccountNumberStructureSearch search);

    List<PremiumAccountNumberStructure> search(PremiumAccountNumberStructureSearch search, Pageable pageable);
}
