package com.mb.laos.repository.impl;

import com.mb.laos.messages.Labels;
import com.mb.laos.model.FeeSchedule;
import com.mb.laos.model.search.FeeScheduleSearch;
import com.mb.laos.repository.extend.FeeScheduleRepositoryExtend;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;
import org.springframework.data.domain.Pageable;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.time.Instant;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FeeScheduleRepositoryImpl implements FeeScheduleRepositoryExtend {
    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public long count(FeeScheduleSearch search) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT COUNT(e) FROM FeeSchedule e inner join FeeScheduleDetail c on e.feeScheduleId = c.feeScheduleId ");
        sql.append(createWhereQuery(search, values));

        Query query = this.entityManager.createQuery(sql.toString(), Long.class);
        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    @Override
    public List<FeeSchedule> search(FeeScheduleSearch search, Pageable pageable) {
        StringBuilder sql = new StringBuilder(1);
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT distinct(e) FROM FeeSchedule e inner join FeeScheduleDetail c on e.feeScheduleId = c.feeScheduleId ");
        sql.append(this.createWhereQuery(search, values));
        sql.append(QueryUtil.createOrderQuery(FeeSchedule.class, search.getOrderByType(), search.getOrderByColumn()));

        Query query = this.entityManager.createQuery(sql.toString(), FeeSchedule.class);
        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());
            query.setMaxResults(pageable.getPageSize());
        } else {
            query.setFirstResult(QueryUtil.FIRST_INDEX);
            query.setMaxResults(QueryUtil.MAX_RESULT);
        }

        return query.getResultList();
    }
    private String createWhereQuery(FeeScheduleSearch search, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();
        String sourceZone = Labels.getTimeZoneFromRequest();
        String language = Labels.getLanguageFromRequest();

        sql.append(" WHERE 1=1 AND e.status != -1 ");

        sql.append(" AND c.language = :language");
        values.put("language", language);

        if (Validator.isNotNull(search.getTitle())) {
            sql.append(" AND c.title LIKE :title ");
            values.put("title", QueryUtil.getFullStringParam(search.getTitle()));
        }

        if (Validator.isNotNull(search.getStatus())) {
            sql.append(" AND e.status = :status ");
            values.put("status", search.getStatus());
        }

        if (Validator.isNotNull(search.getFromDate())) {
            sql.append(" AND c.createdDate >= :fromDate ");

            Instant fromDate = InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getStartOfDay(search.getFromDate()), ZoneId.of(sourceZone));
            values.put("fromDate", fromDate);
        }

        if (Validator.isNotNull(search.getToDate())) {
            sql.append(" AND c.createdDate <= :toDate ");

            Instant toDate = InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getEndOfDay(search.getToDate()), ZoneId.of(sourceZone));
            values.put("toDate", toDate);
        }

        return sql.toString();
    }
}
