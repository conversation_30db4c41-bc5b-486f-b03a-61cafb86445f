package com.mb.laos.repository;

import com.mb.laos.cache.util.BusinessCacheConstants;
import com.mb.laos.enums.CampaignPosition;
import com.mb.laos.model.Campaign;
import com.mb.laos.repository.extend.CampaignRepositoryExtend;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 
 * 07/07/2022 - LinhLH: Create new
 *
 * <AUTHOR>
 */

@Repository
public interface CampaignRepository extends JpaRepository<Campaign, Long>, CampaignRepositoryExtend {

    @Cacheable(cacheNames = BusinessCacheConstants.Campaign.FIND_BY_ID, key = "#campaignId", unless = "#result == null")
    default Campaign findByCampaignId(Long campaignId) {
        return this.findById(campaignId).orElse(null);
    }

    /**
     * @param campaign
     */
    @Caching(put = {
                    @CachePut(cacheNames = {BusinessCacheConstants.Campaign.FIND_BY_ID}, key = "#entity.campaignId")
    },
            evict = {
    		@CacheEvict(cacheNames = {BusinessCacheConstants.Campaign.FIND_BY_STATUS}, allEntries = true),
            @CacheEvict(cacheNames = {BusinessCacheConstants.Campaign.FIND_BY_POSITION_AND_STATUS}, allEntries = true)
    })
    default Campaign save_(Campaign entity) {
        return this.save(entity);
    }
    
    /**
     * 
     * @param startDateBefore
     * @param endDateAfter
     * @param status
     * @return
     */
    @Cacheable(cacheNames = BusinessCacheConstants.Campaign.FIND_BY_STATUS, key = "#status",
                    unless = "#result.size() < 1")
    List<Campaign> findAllByStatus(int status);


    @Cacheable(cacheNames = BusinessCacheConstants.Campaign.FIND_BY_POSITION_AND_STATUS, key = "{#position, #status}",
            unless = "#result.size() < 1")
    List<Campaign> findAllByPositionAndStatus(CampaignPosition position, int status);


}
