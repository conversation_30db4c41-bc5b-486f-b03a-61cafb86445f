package com.mb.laos.repository.extend;

import com.mb.laos.model.dto.NewsDTO;
import com.mb.laos.model.search.NewsSearch;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface NewsRepositoryExtend {
    /**
     * search bank
     *
     * @param newsSearch
     * @param pageable
     * @return
     */
    List<NewsDTO> search(NewsSearch newsSearch, Pageable pageable);

    /**
     * count bank
     *
     * @param newsSearch
     * @return
     */
    Long count(NewsSearch newsSearch);

}
