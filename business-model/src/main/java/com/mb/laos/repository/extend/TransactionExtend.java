package com.mb.laos.repository.extend;

import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.enums.TransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.model.Transaction;
import com.mb.laos.model.TransactionClient;
import com.mb.laos.model.dto.CustomerDTO;
import com.mb.laos.model.dto.InternationalPaymentDTO;
import com.mb.laos.model.dto.TransactionInsuranceDTO;
import com.mb.laos.model.search.BillingHistoryV2Search;
import com.mb.laos.model.search.CashInHistorySearch;
import com.mb.laos.model.search.MerchantTransactionHistorySearch;
import com.mb.laos.model.search.NumberOfTransactionRequest;
import com.mb.laos.model.search.TransactionInsuranceSearchRequest;
import com.mb.laos.model.search.TransactionSearchRequest;
import com.mb.laos.request.TransLimitRecordRequest;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.List;

public interface TransactionExtend {
	/**
	 * get all transaction in day of customer
	 *
	 * @param customerId
	 * @param transactionStatus
	 * @return
	 */
	List<Transaction> getListTransactionBetweenTime(long customerId, Instant startTime, Instant endTime,
													List<TransactionType> transactionType, List<TransactionStatus> transactionStatus, List<TransferType> transferTypes);

	List<Transaction> getListTransactionBetweenTimeAndCurrency(TransLimitRecordRequest request);

	/**
	 * get all transaction in day of an account
	 *
	 * @param customerAccountNumber
	 * @param transactionStatus
	 * @return
	 */
	List<Transaction> getListTransactionBetweenTime(String customerAccountNumber, Instant startTime, Instant endTime,
													List<TransactionType> transactionType, List<TransactionStatus> transactionStatus);
	/**
	 * searchHistoryByMerchant
	 *
	 * @param search   MerchantTransactionHistorySearch
	 * @param pageable Pageable
	 * @return List<Transaction>
	 */
	List<Transaction> searchTransactionByMerchant(MerchantTransactionHistorySearch search, Pageable pageable);

	/**
	 * exportTransactionByMerchant
	 *
	 * @param search MerchantTransactionHistorySearch
	 * @return List<Transaction>
	 */
	List<Transaction> exportTransactionByMerchant(MerchantTransactionHistorySearch search);

	/**
	 * countTransactionByMerchant
	 *
	 * @param search MerchantTransactionHistorySearch
	 * @return Long
	 */
	Long countTransactionByMerchant(MerchantTransactionHistorySearch search);

	List<Transaction> getBillingHistories(Long customerId, Pageable pageable);

	List<Transaction> getBillingHistoriesV2(BillingHistoryV2Search billingHistoryV2Search, Pageable pageable, Long customerId);

	/**
	 * Search transaction
	 *
	 * @param search   TransactionSearchRequest
	 * @param pageable Pageable
	 * @return List<Transaction>
	 */
	List<Transaction> searchTransaction(TransactionSearchRequest search, Pageable pageable);

	/**
	 * Count transaction
	 *
	 * @param search: TransactionSearchRequest
	 * @return Long
	 */
	Long countTransaction(TransactionSearchRequest search);

	Long countTransactionInsurance(TransactionInsuranceSearchRequest request);

	List<TransactionInsuranceDTO> searchTransactionInsurance(TransactionInsuranceSearchRequest request, Pageable pageable);

	List<Transaction> umoneySearch(TransactionSearchRequest search);

	List<TransactionClient> debitDepositSearch(TransactionSearchRequest search);

	/**
	 * searchNumberOfTransaction
	 *
	 * @param request: NumberOfTransactionRequest
	 * @return List<CustomerDTO>
	 */
	List<CustomerDTO> searchNumberOfTransaction(NumberOfTransactionRequest request, Pageable pageable);

	/**
	 * Count number of transaction
	 *
	 * @param request: NumberOfTransactionRequest
	 * @return List<CustomerDTO>
	 */
	List<CustomerDTO> countNumberOfTransaction(NumberOfTransactionRequest request);


	/**
	 * get topup history
	 *
	 * @param pageable
	 * @param customerId
	 * @return
	 */
	List<Transaction> getTopupHistory(Pageable pageable, long customerId);

	/**
	 *
	 * @param request
	 * @param pageable
	 * @param customerId
	 * @return
	 */
	List<Transaction> getCashInHistories(CashInHistorySearch request, Pageable pageable, Long customerId);

	Long countInternationalTransaction(TransactionSearchRequest search);

	List<InternationalPaymentDTO> searchInternationalTransaction(TransactionSearchRequest request, Pageable pageable);
}
