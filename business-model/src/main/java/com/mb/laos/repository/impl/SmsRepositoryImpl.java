package com.mb.laos.repository.impl;

import com.mb.laos.enums.EntityStatus;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.SmsLog;
import com.mb.laos.model.search.SmsSearch;
import com.mb.laos.repository.extend.SmsRepositoryExtend;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SmsRepositoryImpl implements SmsRepositoryExtend {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<SmsLog> search(SmsSearch smsSearch) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT e from SmsLog e ");

        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(smsSearch, values));
        sql.append(QueryUtil.createOrderQuery(SmsLog.class, smsSearch.getOrderByType(),
                smsSearch.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), SmsLog.class);

        values.forEach(query::setParameter);

        return query.getResultList();
    }

    private String createWhereQuery(SmsSearch params, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE e.status != :deletedStatus ");

        values.put("deletedStatus", EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(params.getTypes())) {
            sql.append(" AND e.type IN :type ");

            values.put("type", params.getTypes());
        }

        if (Validator.isNotNull(params.getFromDate())) {
            sql.append(" AND e.createdDate >= :fromDate ");

            values.put("fromDate", InstantUtil.getInstantFromLocalDateTime(
                    LocalDateUtil.getStartOfDay(params.getFromDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(params.getToDate())) {
            sql.append(" AND e.createdDate <= :toDate ");

            values.put("toDate", InstantUtil.getInstantFromLocalDateTime(
                    LocalDateUtil.getEndOfDay(params.getToDate()), Labels.getDefaultZoneId()));
        }

        return sql.toString();
    }
}
