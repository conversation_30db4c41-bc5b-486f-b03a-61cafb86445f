package com.mb.laos.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.mb.laos.model.CustomerLogin;

import java.util.List;

@Repository
public interface CustomerLoginRepository extends JpaRepository<CustomerLogin, Long> {

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    default CustomerLogin save_(CustomerLogin entity) {
        return save(entity);
    }

    /**
     * findFirstByUsernameAndSuccessOrderByCreatedDateAsc
     *
     * @param username String
     * @param success boolean
     * @return CustomerLogin
     */
    CustomerLogin findFirstByUsernameAndSuccessOrderByCreatedDateAsc(String username, boolean success);

    /**
     * findFirstByUsernameAndSuccessOrderByLastModifiedDateDesc
     *
     * @param username
     * @param success
     * @return
     */
    CustomerLogin findFirstByUsernameAndSuccessOrderByLastModifiedDateDesc(String username, boolean success);

    /**
     * findFirstByCustomerActivationAndSuccessOrderByCreatedDateAsc
     *
     * @param username List<String>
     * @param success boolean
     * @return List<String>
     */
    @Query("SELECT DISTINCT cus.username FROM CustomerLogin cus WHERE cus.username IN :username AND cus.success = :success")
    List<String> findFirstByCustomerActivationAndSuccessOrderByCreatedDateAsc(List<String> username, boolean success);
}
