package com.mb.laos.repository;

import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.model.TransactionInsurance;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface TransactionInsuranceRepository extends JpaRepository<TransactionInsurance, Long> {
    Optional<TransactionInsurance> findByTransferTransactionId(Long transferTransactionId);

    List<TransactionInsurance> findAllByCustomerIdAndTransactionStatusOrderByCreatedDateDesc(Long customerId, TransactionStatus status);

    Optional<TransactionInsurance> findFirstByIdentificationNo(String identificationNo);

}
