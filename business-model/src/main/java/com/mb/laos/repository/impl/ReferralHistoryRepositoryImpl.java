package com.mb.laos.repository.impl;

import com.mb.laos.enums.EntityStatus;
import com.mb.laos.model.ReferralHistory;
import com.mb.laos.model.dto.ReferralHistoryDTO;
import com.mb.laos.model.search.ReferralHistorySearch;
import com.mb.laos.repository.extend.ReferralHistoryRepositoryExtend;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;
import org.springframework.data.domain.Pageable;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ReferralHistoryRepositoryImpl implements ReferralHistoryRepositoryExtend {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Long count(ReferralHistorySearch referralHistorySearch) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT COUNT(1) FROM ReferralHistory e ");

        sql.append(createWhereQuery(referralHistorySearch, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    @Override
    public List<ReferralHistory> search(ReferralHistorySearch search, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT e FROM ReferralHistory e ");

        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(search, values));
        sql.append(QueryUtil.createOrderQuery(ReferralHistory.class, search.getOrderByType(), search.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), ReferralHistory.class);

        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

            query.setMaxResults(pageable.getPageSize());
        }

        return query.getResultList();
    }

    @Override
    public List<ReferralHistoryDTO> countReferralHistoriesByReferralIds(List<Long> referralIds) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT new com.mb.laos.model.dto.ReferralHistoryDTO(COUNT(e.referralId) as quantity, e.referralId) FROM ReferralHistory e ");
        sql.append("WHERE e.referralId in :referralIds and e.status != :deletedStatus ");
        sql.append("GROUP BY e.referralId");

        Map<String, Object> values = new HashMap<>();
        values.put("referralIds", referralIds);
        values.put("deletedStatus", EntityStatus.DELETED.getStatus());

        Query query = entityManager.createQuery(sql.toString(), ReferralHistoryDTO.class);
        values.forEach(query::setParameter);

        return query.getResultList();
    }

    private String createWhereQuery(ReferralHistorySearch params, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE e.status != :deletedStatus ");

        values.put("deletedStatus", EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(params.getReferralId())) {

            sql.append(" AND e.referralId = :referralId ");

            values.put("referralId", params.getReferralId());
        }

        return sql.toString();
    }
}
