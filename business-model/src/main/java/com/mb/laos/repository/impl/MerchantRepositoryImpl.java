package com.mb.laos.repository.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import com.mb.laos.messages.Labels;
import com.mb.laos.model.LoanOnline;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.StringNormalizer;
import org.apache.lucene.search.Sort;
import org.hibernate.search.jpa.FullTextEntityManager;
import org.hibernate.search.jpa.FullTextQuery;
import org.hibernate.search.jpa.Search;
import org.hibernate.search.query.dsl.BooleanJunction;
import org.hibernate.search.query.dsl.QueryBuilder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.model.Merchant;
import com.mb.laos.model.ResultSet;
import com.mb.laos.model.search.MerchantSearch;
import com.mb.laos.repository.extend.MerchantRepositoryExtend;
import com.mb.laos.util.Constants;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;

public class MerchantRepositoryImpl implements MerchantRepositoryExtend {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Long count(MerchantSearch merchantSearch) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT COUNT(1) FROM Merchant e ");

        sql.append(createWhereQuery(merchantSearch, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    @Override
    public List<Merchant> search(MerchantSearch merchantSearch, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT e FROM Merchant e ");

        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(merchantSearch, values));
        sql.append(QueryUtil.createOrderQuery(Merchant.class, merchantSearch.getOrderByType(), merchantSearch.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), Merchant.class);

        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

            query.setMaxResults(pageable.getPageSize());
        }

        return query.getResultList();
    }

    @Override
    public ResultSet<Merchant> searchByKeyword(MerchantSearch params, Pageable pageable) {
        FullTextEntityManager fullTextEntityManager = Search.getFullTextEntityManager(this.entityManager);

        QueryBuilder queryBuilder = fullTextEntityManager.getSearchFactory().buildQueryBuilder()
                        .forEntity(MerchantSearch.class)
                        .overridesForField(Merchant.FieldName.MERCHANT_NAME, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                        .overridesForField(Merchant.FieldName.MERCHANT_CODE, Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                        .overridesForField(Merchant.FieldName.MERCHANT_ACCOUNT_NUMBER,
                                        Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                        .overridesForField(Merchant.FieldName.MERCHANT_BANK_CODE,
                                        Constants.AnalyzerDefName.EDGE_NGRAM_QUERY)
                        .get();

        String keyword = params.getKeyword();

        BooleanJunction<?> mustJunc = queryBuilder.bool();

        mustJunc = mustJunc.must(queryBuilder.keyword().onField(Merchant.FieldName.STATUS)
                        .matching(EntityStatus.DELETED.getStatus()).createQuery()).not();

        if (Validator.isNotNull(params.getStatus())) {
            mustJunc = mustJunc
                            .must(queryBuilder.keyword().onField(Merchant.FieldName.STATUS).matching(params.getStatus())
                                            .createQuery());
        }

        if (Validator.isNotNull(params.getMerchantCode())) {
            mustJunc = mustJunc.must(queryBuilder.keyword().wildcard().onField(Merchant.FieldName.MERCHANT_CODE)
                            .matching(QueryUtil.getFullWildcardParam(params.getMerchantCode()))
                            .createQuery());
        }

        if (Validator.isNotNull(params.getMerchantName())) {
            mustJunc = mustJunc.must(queryBuilder.keyword().onField(Merchant.FieldName.MERCHANT_NAME)
                            .matching(params.getMerchantName().toLowerCase())
                            .createQuery());
        }

        if (Validator.isNotNull(params.getMerchantBankCode())) {
            mustJunc = mustJunc.must(queryBuilder.keyword().wildcard().onField(Merchant.FieldName.MERCHANT_BANK_CODE)
                            .matching(QueryUtil.getFullWildcardParam(params.getMerchantBankCode()))
                            .createQuery());
        }

        if (Validator.isNotNull(params.getMerchantAccountNumber())) {
            mustJunc = mustJunc
                            .must(queryBuilder.keyword().wildcard().onField(Merchant.FieldName.MERCHANT_ACCOUNT_NUMBER)
                                            .matching(QueryUtil.getFullWildcardParam(params.getMerchantAccountNumber()))
                                            .createQuery());
        }

        if (Validator.isNotNull(keyword)) {
            BooleanJunction<?> shouldJunc = queryBuilder.bool();
            
            shouldJunc = shouldJunc
                            .should(queryBuilder
                                            .keyword()
                                            .wildcard()
                                            .onFields(Merchant.FieldName.MERCHANT_CODE,
                                                            Merchant.FieldName.MERCHANT_BANK_CODE,
                                                            Merchant.FieldName.MERCHANT_ACCOUNT_NUMBER)
                                            .matching(QueryUtil.getFullWildcardParam(keyword)).createQuery())
                            .should(queryBuilder
                                            .keyword()
                                            .onFields(Merchant.FieldName.MERCHANT_NAME)
                                            .matching(keyword.toLowerCase()).createQuery());
            
            mustJunc = mustJunc.must(shouldJunc.createQuery());
        }

        org.apache.lucene.search.Query query = mustJunc.createQuery();

        FullTextQuery jpaQuery = fullTextEntityManager.createFullTextQuery(query, Merchant.class);

        Sort sort = queryBuilder.sort()//
                        .byScore().desc()//
                        .andByField(Merchant.SortName.MERCHANT_NAME_SORT).asc()//
                        .createSort();

        jpaQuery.setSort(sort);

        int count = jpaQuery.getResultSize();

        if (pageable != null) {
            jpaQuery.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());
            jpaQuery.setMaxResults(pageable.getPageSize());
        } else {
            jpaQuery.setFirstResult(QueryUtil.FIRST_INDEX);
            jpaQuery.setMaxResults(QueryUtil.MAX_RESULT);
        }

        return new ResultSet<>(jpaQuery.getResultList(), count);
    }


    private String createWhereQuery(MerchantSearch params, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE e.status <> :statusDelete ");
        values.put("statusDelete", EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(params.getMerchantName())) {
            sql.append(" AND e.merchantName LIKE :name ");

            values.put("name", QueryUtil.getFullStringParam(params.getMerchantName()));
        }

        if (Validator.isNotNull(params.getMerchantCode())) {
            sql.append(" AND e.merchantCode LIKE :code ");

            values.put("code", QueryUtil.getFullStringParam(params.getMerchantCode()));
        }

        if (Validator.isNotNull(params.getServiceType())) {
            sql.append(" AND e.serviceType LIKE :serviceType");

            values.put("serviceType", QueryUtil.getFullStringParam(String.valueOf(params.getServiceType())));
        }

        if (Validator.isNotNull(params.getParentId())) {
            sql.append(" AND e.parentId = :parentId ");

            values.put("parentId", params.getParentId());
        }

        if (Validator.isNotNull(params.getOrigin())) {
            sql.append(" AND e.origin = :origin ");
            values.put("origin", params.getOrigin());
        }

        if (Validator.isNotNull(params.getKeyword())) {
            sql.append(" AND (e.merchantName LIKE :keyword OR e.merchantCode LIKE :keyword ");
            sql.append(" OR e.merchantAccountNumber LIKE :keyword ) ");

            values.put("keyword", QueryUtil.getFullStringParam(params.getKeyword()));
        }

        if (Validator.equals(params.getSearchMaster(), Boolean.TRUE)) {
            sql.append(" AND e.parentId IS NULL ");
        } else {
            sql.append(" AND e.parentId IS NOT NULL ");
        }

        if (Validator.isNotNull(params.getFromDate())) {
            sql.append(" AND e.createdDate >= :fromDate ");

            values.put("fromDate", InstantUtil.getInstantFromLocalDateTime(
                    LocalDateUtil.getStartOfDay(params.getFromDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(params.getToDate())) {
            sql.append(" AND e.createdDate <= :toDate ");

            values.put("toDate", InstantUtil.getInstantFromLocalDateTime(
                    LocalDateUtil.getEndOfDay(params.getToDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(params.getStatus())) {
            sql.append(" AND e.status = :status ");

            values.put("status", params.getStatus());
        }

        return sql.toString();
    }
}
