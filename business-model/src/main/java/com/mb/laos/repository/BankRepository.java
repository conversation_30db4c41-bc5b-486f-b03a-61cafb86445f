package com.mb.laos.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.mb.laos.cache.util.BusinessCacheConstants;
import com.mb.laos.model.Bank;
import com.mb.laos.repository.extend.BankRepositoryExtend;

@Repository
public interface BankRepository extends JpaRepository<Bank, Long>, BankRepositoryExtend {
    List<Bank> findAllByStatus(int status);

    List<Bank> findAllByNationAndStatus(String nation, int status);

    /**
     * findByBankCode
     *
     * @param code
     * @return Bank
     */
    @Cacheable(cacheNames = BusinessCacheConstants.Bank.FIND_BY_CODE,
            key = "{#code}", unless = "#result == null")
    Bank findByBankCode(String code);

    /**
     * findByBankCodeNumber
     *
     * @param bankCodeNumber
     * @return Bank
     */
    @Cacheable(cacheNames = BusinessCacheConstants.Bank.FIND_BY_BANK_NUMBER_CODE,
            key = "{#bankCodeNumber}", unless = "#result == null")
    Bank findByBankCodeNumber(String bankCodeNumber);

    @Cacheable(cacheNames = BusinessCacheConstants.Bank.FIND_BY_ID, key = "{#bankId}", unless = "#result == null")
    default Bank findByBankId(Long bankId) {
        return findById(bankId).orElse(null);
    }

    /**
     * find bank by id and status
     *
     * @param bankId
     * @param status
     * @return Optional<Bank>
     */
    @Cacheable(cacheNames = BusinessCacheConstants.Bank.FIND_BY_ID_AND_STATUS_NOT, key = "{#bankId, #status}", unless = "#result == null")
    Bank findByBankIdAndStatusNot(Long bankId, Integer status);

    @Caching(put = {
            @CachePut(cacheNames = {BusinessCacheConstants.Bank.FIND_BY_CODE}, key = "#entity.bankCode"),
            @CachePut(cacheNames = {BusinessCacheConstants.Bank.FIND_BY_ID}, key = "{#entity.bankId}")
    }, evict = {
            @CacheEvict(cacheNames = {BusinessCacheConstants.Bank.FIND_BY_ID_AND_STATUS_NOT}, allEntries = true)
    })
    default Bank save_(Bank entity) {
        return save(entity);
    }

    List<Bank> findAllByBankCodeInAndStatus(List<String> bankCodes, Integer status);

    List<Bank> findAllByBankCodeInAndStatusNot(List<String> bankCodes, Integer status);

    /**
     * existsByBankIdAndStatusNot
     *
     * @param bankId Long
     * @param status Integer
     * @return boolean
     */
    boolean existsByBankIdAndStatusNot(Long bankId, Integer status);

    List<Bank> findByBankCodeAndStatus(String bankCode, int status);

    Optional<Bank> findByBankCodeAndStatusAndNation(String bankCode, int status, String nation);

    /**
     * findByBankCodeAndStatusNot
     *
     * @param code
     * @param status
     * @return Bank
     */
//    @Cacheable(cacheNames = BusinessCacheConstants.Bank.FIND_BY_CODE_AND_STATUS_NOT,
//            key = "{#code, #status}", unless = "#result == null")
    Bank findByBankCodeAndStatusNot(String code, Integer status);

    /**
     * findByBankCodeNumberAndStatusNot
     *
     * @param bankCodeNumber
     * @param status
     * @return Bank
     */
//    @Cacheable(cacheNames = BusinessCacheConstants.Bank.FIND_BY_BANK_NUMBER_CODE_AND_STATUS_NOT,
//            key = "{#bankCodeNumber, #status}", unless = "#result == null")
    Bank findByBankCodeNumberAndStatusNot(String bankCodeNumber, Integer status);

    Bank findByBankCodeAndNationAndStatusNot(String code, String nation, int status);
}
