
package com.mb.laos.repository;

import com.mb.laos.enums.TransferType;
import com.mb.laos.model.ServicePack;
import com.mb.laos.repository.extend.ServicePackRepositoryExtend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ServicePackRepository extends JpaRepository<ServicePack, Long>, ServicePackRepositoryExtend {

    List<ServicePack> findAllByClientIdAndStatusNot(String clientId, int status);

    List<ServicePack> findAllByClientIdAndStatus(String clientId, int status);

    List<ServicePack> findAllByClientIdInAndStatusNot(List<String> clientId, int status);

    List<ServicePack> findAllByClientIdInAndStatus(List<String> clientId, int status);

    List<ServicePack> findAllByTypeAndStatusNot(String type, int status);

}
