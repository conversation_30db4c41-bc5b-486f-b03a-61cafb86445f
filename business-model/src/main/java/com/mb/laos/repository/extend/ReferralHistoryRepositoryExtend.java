package com.mb.laos.repository.extend;

import com.mb.laos.model.ReferralHistory;
import com.mb.laos.model.dto.ReferralHistoryDTO;
import com.mb.laos.model.search.ReferralHistorySearch;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ReferralHistoryRepositoryExtend {

    Long count(ReferralHistorySearch merchantSearch);

    List<ReferralHistory> search(ReferralHistorySearch referralHistorySearch, Pageable pageable);

    List<ReferralHistoryDTO> countReferralHistoriesByReferralIds(List<Long> referralIds);
}
