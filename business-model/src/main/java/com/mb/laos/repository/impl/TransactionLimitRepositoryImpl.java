package com.mb.laos.repository.impl;

import com.mb.laos.enums.EntityStatus;
import com.mb.laos.model.TransactionLimit;
import com.mb.laos.model.search.TransactionLimitSearch;
import com.mb.laos.repository.extend.TransactionLimitRepositoryExtend;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.Validator;
import org.springframework.data.domain.Pageable;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.time.Instant;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TransactionLimitRepositoryImpl implements TransactionLimitRepositoryExtend {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<TransactionLimit> search(TransactionLimitSearch request, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT e FROM TransactionLimit e ");
        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(request, values));
        sql.append(QueryUtil.createOrderQuery(TransactionLimit.class, request.getOrderByType(), request.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), TransactionLimit.class);
        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

            query.setMaxResults(pageable.getPageSize());
        } else {
            query.setFirstResult(QueryUtil.FIRST_INDEX);
            query.setMaxResults(QueryUtil.MAX_RESULT);
        }
        return query.getResultList();
    }

    @Override
    public Long count(TransactionLimitSearch params) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT COUNT(1) FROM TransactionLimit e ");

        sql.append(createWhereQuery(params, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    private String createWhereQuery(TransactionLimitSearch request, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();
        ZoneId sourceZone = request.getSourceZone();

        sql.append(" WHERE e.status != :deletedStatus ");
        values.put("deletedStatus", EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(request.getStatus())) {
            sql.append(" AND e.status = :status ");
            values.put("status", request.getStatus());
        }

        if (Validator.isNotNull(request.getKeyword())) {
            sql.append(" AND (e.serviceName LIKE :keyword OR e.sectorId LIKE :keyword OR e.currency LIKE :keyword) ");
            values.put("keyword", QueryUtil.getFullStringParam(request.getKeyword()));
        }

        if (Validator.isNotNull(request.getFromDate())) {
            Instant fromDate = Validator.isNull(sourceZone) ?
                    InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getStartOfDay(request.getFromDate()), ZoneId.of(StringPool.UTC)) :
                    InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getStartOfDay(request.getFromDate()), sourceZone);
            sql.append(" AND e.startDate >= :fromDate ");

            values.put("fromDate", fromDate);
        }

        if (Validator.isNotNull(request.getToDate())) {
            Instant toDate = Validator.isNull(sourceZone) ?
                    InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getEndOfDay(request.getToDate()), ZoneId.of(StringPool.UTC)) :
                    InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getEndOfDay(request.getToDate()), sourceZone);
            sql.append(" AND e.endDate <= :toDate ");

            values.put("toDate", toDate);
        }

        return sql.toString();
    }
}
