package com.mb.laos.repository;

import com.mb.laos.enums.QrLapnetType;
import com.mb.laos.enums.QrType;
import com.mb.laos.model.QrCode;
import com.mb.laos.repository.extend.QrCodeRepositoryExtend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface QrCodeRepository extends JpaRepository<QrCode, Long>, QrCodeRepositoryExtend {
    Optional<QrCode> findByAccountNumberAndStatusAndQrType(String accountNumber, int status, QrType qrType);

    List<QrCode> findAllByQrCodeValueAndStatus(String qrCodeValue, int status);

    QrCode findByQrCodeValueAndStatus(String qrCodeValue, int status);

    /**
     * findByMerchantIdAndStatus
     *
     * @param merchantId String
     * @param status     Integer
     * @return QrCode
     */
    QrCode findByMerchantIdAndStatus(String merchantId, Integer status);

    /**
     * findByMerchantIdAndStatusNot
     *
     * @param merchantId String
     * @param status     Integer
     * @return QrCode
     */
    QrCode findByMerchantIdAndStatusNot(String merchantId, Integer status);

    List<QrCode> findByAccountNumberAndCustomerIdAndStatusAndQrTypeAndType(String accountNumber, Long customerId, int status, QrType qrType, QrLapnetType type);

    Optional<QrCode> findByQrCodeIdAndCustomerIdAndTypeAndStatus(Long qrCodeId, Long customerId, QrLapnetType type, Integer status);

    Optional<QrCode> findByQrCodeIdAndCustomerIdAndStatus(Long qrCodeId, Long customerId, Integer status);

    QrCode findByQrCodeValueAndCustomerIdAndStatus(String qrCodeValue, Long customerId, int status);
}
