package com.mb.laos.repository;

import com.mb.laos.model.SpecialPremiumAccountNumber;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SpecialPremiumAccountNumberRepository extends JpaRepository<SpecialPremiumAccountNumber, Long> {
    List<SpecialPremiumAccountNumber> findAllBySpecialPremiumAccountNumberAndStatus(String premiumAccountNumber, int status);

    List<SpecialPremiumAccountNumber> findAllBySpecialPremiumAccountNumberInAndStatus(List<String> premiumAccountNumbers, int status);

    List<SpecialPremiumAccountNumber> findAllBySpecialPremiumAccountNumberInAndHidedAndStatus(List<String> premiumAccountNumbers, boolean hided, int status);

    SpecialPremiumAccountNumber findBySpecialPremiumAccountNumberAndStatus(String premiumAccNumber, int status);

    SpecialPremiumAccountNumber findBySpecialPremiumAccountNumberAndHidedAndStatus(String premiumAccNumber, boolean hidded, int status);
}
