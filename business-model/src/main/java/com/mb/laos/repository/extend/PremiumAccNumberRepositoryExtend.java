package com.mb.laos.repository.extend;

import com.mb.laos.model.PremiumAccNumber;
import com.mb.laos.model.dto.NewsDTO;
import com.mb.laos.model.dto.PremiumAccNumberDTO;
import com.mb.laos.model.search.NewsSearch;
import com.mb.laos.model.search.PremiumAccNumberSearch;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface PremiumAccNumberRepositoryExtend {
    /**
     * search
     *
     * @param search
     * @param pageable
     * @return
     */
    List<PremiumAccNumberDTO> search(PremiumAccNumberSearch search, Pageable pageable);

    /**
     * count
     *
     * @param search
     * @return
     */
    Long count(PremiumAccNumberSearch search);

}
