package com.mb.laos.repository.impl;

import com.mb.laos.model.ServicePack;
import com.mb.laos.model.search.ServicePackSearch;
import com.mb.laos.repository.extend.ServicePackRepositoryExtend;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;
import org.springframework.data.domain.Pageable;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ServicePackRepositoryImpl implements ServicePackRepositoryExtend {

    @PersistenceContext
    private EntityManager entityManager;


    @Override
    public List<ServicePack> search(ServicePackSearch search, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT e FROM ServicePack e ");

        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(search, values));
        sql.append(QueryUtil.createOrderQuery(ServicePack.class, search.getOrderByType(), search.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), ServicePack.class);

        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

            query.setMaxResults(pageable.getPageSize());
        } else {
            query.setFirstResult(QueryUtil.FIRST_INDEX);

            query.setMaxResults(QueryUtil.MAX_RESULT);
        }

        return query.getResultList();
    }

    @Override
    public Long count(ServicePackSearch search) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT COUNT(1) FROM ServicePack e ");

        sql.append(createWhereQuery(search, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    private String createWhereQuery(ServicePackSearch params, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE 1 = 1 AND e.status != -1 ");

        if (Validator.isNotNull(params.getKeyword())) {
            sql.append(" AND (e.clientId LIKE :keyword OR e.code LIKE :keyword OR e.name LIKE :keyword )");

            values.put("keyword", QueryUtil.getFullStringParam(params.getKeyword()));
        }

        if (Validator.isNotNull(params.getTypes())) {
            sql.append(" AND e.type IN :type ");

            values.put("type", params.getTypes());
        }

        if (Validator.isNotNull(params.getClientIds())) {
            sql.append(" AND e.clientId IN :clientId ");

            values.put("clientId", params.getClientIds());
        }

        if (Validator.isNotNull(params.getType())) {
            sql.append(" AND e.type LIKE :type ");

            values.put("type", QueryUtil.getFullStringParam(params.getType()));
        }

        if (Validator.isNotNull(params.getStatus())) {
            sql.append(" AND e.status = :status ");

            values.put("status", params.getStatus());
        }

        return sql.toString();
    }
}
