package com.mb.laos.repository;

import com.mb.laos.model.SmsBalance;
import com.mb.laos.repository.extend.SmsBalanceRepositoryExtend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SmsBalanceRepository extends JpaRepository<SmsBalance, Long>, SmsBalanceRepositoryExtend {
    SmsBalance findByPhoneNumberAndCustomerAccountNumberAndStatus(String phoneNumber, String customerAccountNumber, int status);

    List<SmsBalance> findAllByCustomerIdAndStatus(Long customerId, int status);
}
