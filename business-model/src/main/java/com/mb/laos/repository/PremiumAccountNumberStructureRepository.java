package com.mb.laos.repository;

import com.mb.laos.cache.util.BusinessCacheConstants;
import com.mb.laos.model.PremiumAccountNumberStructure;
import com.mb.laos.repository.extend.PremiumAccountNumberStructureRepositoryExtend;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PremiumAccountNumberStructureRepository extends JpaRepository<PremiumAccountNumberStructure, Long>, PremiumAccountNumberStructureRepositoryExtend {
    @Cacheable(cacheNames = {BusinessCacheConstants.Structure.FIND_BY_LENGTH}, key = "#numberGroup", unless = "#result == null")
    List<PremiumAccountNumberStructure> findAllByLengthAndStatus(int numberGroup, int status);

    @CacheEvict(cacheNames = BusinessCacheConstants.Structure.FIND_BY_LENGTH, key = "#numberGroup")
    default void evictFindAllByLengthAndStatus(Integer numberGroup) {
    }

    boolean existsByNumberStructureAndStatusNot(String numberStructure, int status);

    boolean existsByPatternAndLengthAndStatusNot(String pattern, int Length, int status);

    List<PremiumAccountNumberStructure> findAllByStatus(int status);

    List<PremiumAccountNumberStructure> findAllByStatusNot(int status);

    List<PremiumAccountNumberStructure> findAllByNumberGroupIdAndStatusNot(long numberGroupId, int status);

    List<PremiumAccountNumberStructure> findAllByLengthAndStatusNot(int length, int status);

    PremiumAccountNumberStructure findByPremiumAccountNumberStructureIdAndStatusNot(Long premiumAccountNumberStructureId, int status);

    PremiumAccountNumberStructure findByNumberStructureAndStatusNot(String numberStructure, int status);

    boolean existsByPatternAndStatusNot(String pattern, int status);

    List<PremiumAccountNumberStructure> findAllByLengthInAndStatusNot(List<Integer> lengths, int status);

    List<PremiumAccountNumberStructure> findByNumberGroupIdInAndStatusNot(List<Long> numberGroupIds, int status);

    List<PremiumAccountNumberStructure> findAllByPremiumAccountNumberStructureIdInAndStatusNot(List<Long> premiumAccountNumberStructureIds, int status);
}
