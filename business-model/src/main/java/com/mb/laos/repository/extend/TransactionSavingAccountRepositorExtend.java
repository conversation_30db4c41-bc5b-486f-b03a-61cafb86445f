package com.mb.laos.repository.extend;

import com.mb.laos.model.dto.TransactionSavingAccountDTO;
import com.mb.laos.model.search.SavingAccountHistorySearch;
import com.mb.laos.model.search.SavingAccountSearch;
import org.springframework.data.domain.Pageable;


import java.util.List;

public interface TransactionSavingAccountRepositorExtend {
    List<TransactionSavingAccountDTO> search(SavingAccountSearch savingAccountSearch, Pageable pageable);

    Long count(SavingAccountSearch savingAccountSearch);

    Long count(SavingAccountHistorySearch savingAccountHistorySearch);

    List<TransactionSavingAccountDTO> searchHistory(SavingAccountHistorySearch savingAccountHistorySearch, Pageable pageable);
}
