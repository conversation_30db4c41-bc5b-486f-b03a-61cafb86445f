package com.mb.laos.repository;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.ListUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.mb.laos.cache.util.BusinessCacheConstants;
import com.mb.laos.model.LoanProduct;

@Repository
public interface LoanProductRepository extends JpaRepository<LoanProduct, Long> {

    /**
     * find all loan product by status
     *
     * @param status
     * @return list LoanProduct
     */
    @Cacheable(cacheNames = BusinessCacheConstants.LoanProduct.FIND_ALL,
            key = "T(com.mb.laos.cache.util.CacheConstants).KEY", unless = "#result.size() < 1")
    List<LoanProduct> findAllByStatus(int status);

    /**
     * Check loan product exist
     *
     * @param loanProductId long
     * @param status        int
     * @return boolean
     */
    @Cacheable(cacheNames = BusinessCacheConstants.LoanProduct.EXITED_BY_ID_AND_STATUS, key = "{#loanProductId, #status}"
            , unless = "#result == false")
    boolean existsByLoanProductIdAndStatus(long loanProductId, int status);

    List<LoanProduct> findAllByLoanProductIdIn(List<Long> listId);

    /**
     * find loan product by loanProductId and status
     *
     * @param loanProductId
     * @param status
     * @return LoanProduct
     */
    @Cacheable(cacheNames = BusinessCacheConstants.LoanProduct.FIND_BY_ID,
            key = "{#loanProductId, #status}", unless = "#result == null")
    LoanProduct findByLoanProductIdAndStatus(long loanProductId, int status);

    default List<LoanProduct> findAllByLoanProductIdInList(List<Long> listId){
        List<LoanProduct> result = new ArrayList<>();
        //tach ra vi oracle chi cho phep find in toi da 1000 phan tu
        List<List<Long>> productIdSublist = ListUtils.partition(listId, 900);
        for (List<Long> ids : productIdSublist) {
            result.addAll(findAllByLoanProductIdIn(ids));
        }
        return result;
    }
}
