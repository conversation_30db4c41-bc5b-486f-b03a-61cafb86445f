package com.mb.laos.repository.impl;

import com.mb.laos.enums.EntityStatus;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.dto.SmsBalanceDTO;
import com.mb.laos.model.search.SmsBalanceSearch;
import com.mb.laos.repository.extend.SmsBalanceRepositoryExtend;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;
import org.springframework.data.domain.Pageable;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.time.Instant;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SmsBalanceRepositoryImpl implements SmsBalanceRepositoryExtend {
    @PersistenceContext
    private EntityManager entityManager;


    @Override
    public Long count(SmsBalanceSearch request) {
        StringBuilder sql = new StringBuilder();

        Map<String, Object> values = new HashMap<>();

        sql.append("select count(*) " +
                "from SmsBalance e " +
                "inner join Customer c on e.customerId = c.customerId ");

        sql.append(this.createWhereQuery(request, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    @Override
    public List<SmsBalanceDTO> search(SmsBalanceSearch search, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT new com.mb.laos.model.dto.SmsBalanceDTO(e.smsBalanceId, c.fullname, c.cif, e.phoneNumber, e.customerAccountNumber, e.registrationDate, e.cancellationDate, e.currency, e.status) " +
                "FROM SmsBalance e inner join Customer c on e.customerId = c.customerId ");

        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(search, values));
        sql.append(QueryUtil.createOrderQuery(SmsBalanceDTO.class, search.getOrderByType(), search.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), SmsBalanceDTO.class);

        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

            query.setMaxResults(pageable.getPageSize());
        }

        return query.getResultList();
    }

    private String createWhereQuery(SmsBalanceSearch request, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();
        String sourceZone = Labels.getTimeZoneFromRequest();

        sql.append(" WHERE e.status != :deletedStatus ");

        values.put("deletedStatus", EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(request.getStatus())) {
            sql.append(" AND e.status = :status ");
            values.put("status", request.getStatus());
        }

        if (Validator.isNotNull(request.getKeyword())) {
            sql.append(" AND (e.customerAccountNumber LIKE :keyword OR e.phoneNumber LIKE :keyword OR lower(c.cif) = lower(:keywordEncrypt)) ");
            values.put("keywordEncrypt", request.getEncryptedKeyword());
            values.put("keyword", QueryUtil.getFullStringParam(request.getKeyword()));
        }

        if (Validator.isNotNull(request.getCustomerName())) {
            sql.append(" AND c.fullname LIKE :fullname ");
            values.put("fullname", QueryUtil.getFullStringParam(request.getCustomerName()));
        }

        if (Validator.isNotNull(request.getFromDate())) {
            sql.append(" AND e.registrationDate >= :fromDate ");

            Instant fromDate = InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getStartOfDay(request.getFromDate()), ZoneId.of(sourceZone));
            values.put("fromDate", fromDate);
        }

        if (Validator.isNotNull(request.getToDate())) {
            sql.append(" AND e.registrationDate <= :toDate ");

            Instant toDate = InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getEndOfDay(request.getToDate()), ZoneId.of(sourceZone));
            values.put("toDate", toDate);
        }

        return sql.toString();
    }
}


