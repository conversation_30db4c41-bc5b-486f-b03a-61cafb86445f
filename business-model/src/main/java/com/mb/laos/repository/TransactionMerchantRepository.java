package com.mb.laos.repository;

import com.mb.laos.model.TransactionMerchant;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import java.util.Optional;

@Repository
public interface TransactionMerchantRepository extends JpaRepository<TransactionMerchant, Long> {

    /**
     * findAllByMerchantIdInAndStatus
     *
     * @param merchantIds List<Long>
     * @param status Integer
     * @return List<TransactionMerchant>
     */
    List<TransactionMerchant> findAllByMerchantIdInAndStatus(List<Long> merchantIds, Integer status);

    Optional<TransactionMerchant> findByTransferTransactionId(Long transferTransactionId);

    /**
     * findAllByMerchantIdInAndStatusNot
     *
     * @param merchantIds List<Long>
     * @param status Integer
     * @return List<TransactionMerchant>
     */
    List<TransactionMerchant> findAllByMerchantIdInAndStatusNot(List<Long> merchantIds, Integer status);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    default TransactionMerchant saveAnyway(TransactionMerchant entity){
        return save(entity);
    }
}
