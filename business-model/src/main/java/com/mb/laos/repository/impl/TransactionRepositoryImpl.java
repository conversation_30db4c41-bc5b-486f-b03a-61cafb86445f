package com.mb.laos.repository.impl;

import com.mb.laos.enums.AccountTypeCif;
import com.mb.laos.enums.BillingType;
import com.mb.laos.enums.DrCrType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.enums.TransactionType;
import com.mb.laos.enums.TransferTransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Customer;
import com.mb.laos.model.Transaction;
import com.mb.laos.model.TransactionClient;
import com.mb.laos.model.dto.CustomerDTO;
import com.mb.laos.model.dto.InternationalPaymentDTO;
import com.mb.laos.model.dto.TransactionInsuranceDTO;
import com.mb.laos.model.search.BillingHistoryV2Search;
import com.mb.laos.model.search.CashInHistorySearch;
import com.mb.laos.model.search.MerchantTransactionHistorySearch;
import com.mb.laos.model.search.NumberOfTransactionRequest;
import com.mb.laos.model.search.TransactionInsuranceSearchRequest;
import com.mb.laos.model.search.TransactionSearchRequest;
import com.mb.laos.repository.extend.TransactionExtend;
import com.mb.laos.request.TransLimitRecordRequest;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import org.jasypt.encryption.pbe.PBEStringEncryptor;
import org.springframework.data.domain.Pageable;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RequiredArgsConstructor
public class TransactionRepositoryImpl implements TransactionExtend {

    private static final String BANK_CODE_INTERNAL = "MB";

    private final PBEStringEncryptor encryptor;

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<Transaction> getListTransactionBetweenTime(long customerId, Instant startTime, Instant endTime, List<TransactionType> transactionType, List<TransactionStatus> transactionStatus, List<TransferType> transferTypes) {
        StringBuilder sql = new StringBuilder();

        Map<String, Object> values = new HashMap<>();

        sql.append(" FROM Transaction t");
        sql.append(" WHERE 1 = 1 AND t.customerId = :customerId ");
        sql.append(" AND t.transactionStatus in :transactionStatus ");
        sql.append(" AND t.transactionType in :transactionType ");
        sql.append(" AND t.transactionStartTime BETWEEN :startTime AND :endTime ");

        if (Validator.isNotNull(transferTypes)) {
            sql.append(" AND t.transferType in :transferType ");
            values.put("transferType", transferTypes);
        }

        // map value
        values.put("customerId", customerId);
        values.put("transactionStatus", transactionStatus);
        values.put("transactionType", transactionType);
        values.put("startTime", startTime);
        values.put("endTime", endTime);
        Query query = entityManager.createQuery(sql.toString(), Transaction.class);

        values.forEach(query::setParameter);

        return query.getResultList();
    }

    @Override
    public List<Transaction> getListTransactionBetweenTimeAndCurrency(TransLimitRecordRequest request) {
        StringBuilder sql = new StringBuilder();

        Map<String, Object> values = new HashMap<>();

        sql.append(" FROM Transaction t");
        sql.append(" WHERE 1 = 1 AND t.customerId = :customerId ");
        sql.append(" AND t.transactionStatus in :transactionStatus ");
        sql.append(" AND t.transactionType in :transactionType ");
        sql.append(" AND t.transactionStartTime BETWEEN :startTime AND :endTime ");

        if (Validator.isNotNull(request.getTransferTypes())) {
            sql.append(" AND t.transferType in :transferType ");
            values.put("transferType", request.getTransferTypes());
        }

        if (Validator.isNotNull(request.getCurrency())) {
            sql.append(" AND t.transactionCurrency like :currency ");
            values.put("currency", request.getCurrency());
        }

        if (Validator.isNotNull(request.getBankCode())) {
            sql.append(" AND t.foreignAmount > 0 ");
        }

        // map value
        values.put("customerId", request.getCustomerId());
        values.put("transactionStatus", request.getTransactionStatus());
        values.put("transactionType", request.getTransactionTypes());
        values.put("startTime", request.getStartTime());
        values.put("endTime", request.getEndTime());
        Query query = entityManager.createQuery(sql.toString(), Transaction.class);

        values.forEach(query::setParameter);

        return query.getResultList();
    }

    @Override
    public List<Transaction> getListTransactionBetweenTime(String customerAccountNumber, Instant startTime, Instant endTime, List<TransactionType> transactionType, List<TransactionStatus> transactionStatus) {
        StringBuilder sql = new StringBuilder();

        Map<String, Object> values = new HashMap<>();

        sql.append(" FROM Transaction t");
        sql.append(" WHERE 1 = 1 AND t.customerAccNumber = :customerAccountNumber ");
        sql.append(" AND t.transactionStatus in :transactionStatus ");
        sql.append(" AND t.transactionType in :transactionType ");
        sql.append(" AND t.transactionStartTime BETWEEN :startTime AND :endTime ");

        // map value
        values.put("customerAccountNumber", customerAccountNumber);
        values.put("transactionStatus", transactionStatus);
        values.put("transactionType", transactionType);
        values.put("startTime", startTime);
        values.put("endTime", endTime);

        Query query = entityManager.createQuery(sql.toString(), Transaction.class);

        values.forEach(query::setParameter);

        return query.getResultList();
    }

    @Override
    public List<Transaction> searchTransactionByMerchant(MerchantTransactionHistorySearch search, Pageable pageable) {
        StringBuilder sql = new StringBuilder(2);
        Map<String, Object> values = new HashMap<>();

        sql.append(" SELECT e FROM Transaction e LEFT JOIN TransactionMerchant tm ");
        sql.append(" ON tm.transferTransactionId = e.transferTransactionId ");

//        if (Validator.isNotNull(search.getCustomerName())) {
//            sql.append(" LEFT JOIN Customer c ON c.customerId = e.customerId ");
//        }

        sql.append(this.createWhereQuery(search, values));

        sql.append(QueryUtil.createOrderQuery(Transaction.class, search.getOrderByType(), search.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), Transaction.class);

        values.forEach(query::setParameter);


        if (search.isHasPageable()) {
            if (pageable != null) {
                query.setFirstResult(pageable.getPageNumber() * search.getPageSize());

                query.setMaxResults(search.getPageSize());
            } else {
                query.setFirstResult(QueryUtil.FIRST_INDEX);

                query.setMaxResults(QueryUtil.MAX_RESULT);
            }
        }

        return query.getResultList();
    }

    @Override
    public List<Transaction> exportTransactionByMerchant(MerchantTransactionHistorySearch search) {
        StringBuilder sql = new StringBuilder(2);
        Map<String, Object> values = new HashMap<>();

        sql.append(" SELECT e FROM Transaction e LEFT JOIN TransactionMerchant tm ");
        sql.append(" ON tm.transferTransactionId = e.transferTransactionId ");

//        if (Validator.isNotNull(search.getCustomerName())) {
//            sql.append(" LEFT JOIN Customer c ON c.customerId = e.customerId ");
//        }

        sql.append(this.createWhereQuery(search, values));

        sql.append(QueryUtil.createOrderQuery(Transaction.class, search.getOrderByType(), search.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), Transaction.class);

        values.forEach(query::setParameter);

        return query.getResultList();
    }

    @Override
    public Long countTransactionByMerchant(MerchantTransactionHistorySearch search) {
        StringBuilder sql = new StringBuilder(2);

        Map<String, Object> values = new HashMap<>();

        sql.append(" SELECT COUNT(1) FROM Transaction e LEFT JOIN TransactionMerchant tm ");
        sql.append(" ON tm.transferTransactionId = e.transferTransactionId ");

//        if (Validator.isNotNull(search.getCustomerName())) {
//            sql.append(" LEFT JOIN Customer c ON c.customerId = e.customerId ");
//        }

        sql.append(this.createWhereQuery(search, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    @Override
    public List<Transaction> getBillingHistories(Long customerId, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        Map<String, Object> values = new HashMap<>();

        sql.append(" FROM Transaction t");
        sql.append(" WHERE 1 = 1 AND t.customerId = :customerId ");
        sql.append(" AND t.transactionStatus = :transactionStatus ");
        sql.append(" AND t.transferType = :transactionType ");
        sql.append(" AND status = :status ");

        // map value
        values.put("customerId", customerId);
        values.put("transactionStatus", TransactionStatus.SUCCESS);
        values.put("transactionType", TransferType.BILLING);
        values.put("status", EntityStatus.ACTIVE.getStatus());

        sql.append(" order by t.transactionFinishTime desc");

        Query query = entityManager.createQuery(sql.toString(), Transaction.class);

        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

            query.setMaxResults(pageable.getPageSize());
        } else {
            query.setFirstResult(QueryUtil.FIRST_INDEX);

            query.setMaxResults(QueryUtil.MAX_RESULT);
        }

        return query.getResultList();
    }

    @Override
    public List<Transaction> getTopupHistory(Pageable pageable, long customerId) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT t1 FROM Transaction t1 ");
        Map<String, Object> values = new HashMap<>();

        sql.append(createTopupWhereQuery(customerId, values));

        sql.append(" order by t1.transactionFinishTime desc");

        Query query = entityManager.createQuery(sql.toString(), Transaction.class);

        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

            query.setMaxResults(pageable.getPageSize());
        } else {
            query.setFirstResult(QueryUtil.FIRST_INDEX);

            query.setMaxResults(QueryUtil.MAX_RESULT);
        }

        return query.getResultList();
    }

    @Override
    public List<Transaction> getBillingHistoriesV2(BillingHistoryV2Search request, Pageable pageable, Long customerId) {

        StringBuilder sql = new StringBuilder();

        sql.append("SELECT t1 FROM Transaction t1 ");
        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(request, customerId, values));

        sql.append(" order by t1.transactionFinishTime desc");

        Query query = entityManager.createQuery(sql.toString(), Transaction.class);

        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

            query.setMaxResults(pageable.getPageSize());
        } else {
            query.setFirstResult(QueryUtil.FIRST_INDEX);

            query.setMaxResults(QueryUtil.MAX_RESULT);
        }

        return query.getResultList();

    }

    @Override
    public List<Transaction> searchTransaction(TransactionSearchRequest search, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        Map<String, Object> values = new HashMap<>();

        sql.append(" SELECT distinct(e) FROM Transaction e inner join Customer c on c.customerId = e.customerId ");

        if (search.isHistory()) {
            sql.append(" left join Bank b on b.bankCode = e.bankCode ");
            sql.append(" left join Merchant m on m.merchantCode = e.target ");
        }

        sql.append(this.createWhereQuery(search, values));

        sql.append(QueryUtil.createOrderQuery(Transaction.class, search.getOrderByType(), search.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), Transaction.class);

        values.forEach(query::setParameter);

        if (search.isHasPageable()) {
            if (pageable != null) {
                query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

                query.setMaxResults(pageable.getPageSize());
            } else {
                query.setFirstResult(QueryUtil.FIRST_INDEX);

                query.setMaxResults(QueryUtil.MAX_RESULT);
            }
        }

        return query.getResultList();
    }

    @Override
    public Long countTransaction(TransactionSearchRequest search) {
        StringBuilder sql = new StringBuilder(1);
        Map<String, Object> values = new HashMap<>();

        sql.append(" SELECT COUNT(e) FROM Transaction e inner join Customer c on c.customerId = e.customerId ");

        if (search.isHistory()) {
            sql.append(" left join Bank b on b.bankCode = e.bankCode ");
            sql.append(" left join Merchant m on m.merchantCode = e.target ");
        }

        sql.append(this.createWhereQuery(search, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);
        return (Long) query.getSingleResult();
    }

    @Override
    public List<Transaction> getCashInHistories(CashInHistorySearch request, Pageable pageable, Long customerId) {

        StringBuilder sql = new StringBuilder();

        sql.append("SELECT t1 FROM Transaction t1 ");
        Map<String, Object> values = new HashMap<>();

        sql.append(createCashInWhereQuery(customerId, values));

        sql.append(" order by t1.transactionFinishTime desc");

        Query query = entityManager.createQuery(sql.toString(), Transaction.class);

        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

            query.setMaxResults(pageable.getPageSize());
        } else {
            query.setFirstResult(QueryUtil.FIRST_INDEX);

            query.setMaxResults(QueryUtil.MAX_RESULT);
        }

        return query.getResultList();

    }

    private String createWhereQuery(MerchantTransactionHistorySearch search, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE e.status <> :statusDelete ");

        values.put("statusDelete", EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(search.getMerchantIds())) {
            sql.append(" AND tm.merchantId IN(:merchantIds) ");
            values.put("merchantIds", search.getMerchantIds());
        }

        if (Validator.isNotNull(search.getCustomerAccountNumber())) {
            sql.append(" AND e.customerAccNumber LIKE :customerAccNumber ");
            values.put("customerAccNumber", QueryUtil.getFullStringParam(search.getCustomerAccountNumber()));
        }

        if (Validator.isNotNull(search.getTransactionId())) {
            sql.append(" AND e.transactionId LIKE :transactionId ");
            values.put("transactionId", QueryUtil.getFullStringParam(search.getTransactionId()));
        }

        if (Validator.isNotNull(search.getKeyword())) {
            sql.append(" AND (e.customerAccNumber LIKE :keyword OR e.transactionId LIKE :keyword ) ");
            values.put("keyword", QueryUtil.getFullStringParam(search.getKeyword()));
        }

        if (Validator.isNotNull(search.getTransactionStatuses())) {
            sql.append(" AND e.transactionStatus IN (:transactionStatuses) ");
            values.put("transactionStatuses", search.getTransactionStatuses());
        }

        if (Validator.isNotNull(search.getTransactionDateStart())) {
            sql.append(" AND e.createdDate >= :transactionTimeStart ");

            values.put("transactionTimeStart", InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getStartOfDay(search.getTransactionDateStart()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(search.getTransactionDateEnd())) {
            sql.append(" AND e.createdDate <= :transactionTimeEnd ");

            values.put("transactionTimeEnd", InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getEndOfDay(search.getTransactionDateEnd()), Labels.getDefaultZoneId()));
        }

        return sql.toString();
    }

    private String createWhereQuery(TransactionSearchRequest search, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE e.status <> :statusDelete");

        values.put("statusDelete", EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(search.getTransactionCode())) {
            sql.append(" AND e.transactionCode LIKE :transactionCode ");

            values.put("transactionCode", QueryUtil.getFullStringParam(search.getTransactionCode()));
        }

        if (Validator.isNotNull(search.getKeyword())) {
            sql.append(" AND (lower(e.beneficiaryCustomerName) LIKE lower(:keyword) " + " OR lower(e.target) LIKE lower(:keyword) ");
            if (search.isHistory()) {
                sql.append(" OR lower(m.merchantName) LIKE lower(:keyword) ");
                sql.append(" OR lower(b.bankName) LIKE lower(:keyword) ");
                sql.append(" OR lower(m.merchantName) LIKE lower(:keyword) OR lower(m.merchantAccountNumber) LIKE lower(:keyword) ");
            } else {
                sql.append(" OR lower(c.fullname) LIKE lower(:keyword) ");
                sql.append(" OR lower(e.customerAccNumber) LIKE lower(:keyword) ");
                sql.append(" OR lower(e.transactionId) LIKE :keyword OR lower(e.transactionCode) LIKE lower(:keyword) ");
            }
            sql.append(" OR lower(e.transactionAmount) LIKE lower(:keyword)) ");
            values.put("keyword", QueryUtil.getFullStringParam(search.getKeyword()));
        }

        if (Validator.isNotNull(search.getFromDate())) {
            sql.append(" AND e.createdDate >= :transactionTimeStart ");

            values.put("transactionTimeStart", InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getStartOfDay(search.getFromDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(search.getToDate())) {
            sql.append(" AND e.createdDate <= :transactionTimeEnd ");

            values.put("transactionTimeEnd", InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getEndOfDay(search.getToDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(search.getTransactionStatus())) {
            sql.append(" AND e.transactionStatus in :transactionStatus ");
            values.put("transactionStatus", search.getTransactionStatus());
        }

        if (Validator.isNotNull(search.getCustomerAccNumber())) {
            sql.append(" AND e.customerAccNumber = :customerAccNumber ");
            values.put("customerAccNumber", search.getCustomerAccNumber());
        }

        if (Validator.isNotNull(search.getCustomerAccNumbers())) {
            sql.append(" AND e.customerAccNumber in :customerAccNumbers ");
            values.put("customerAccNumbers", search.getCustomerAccNumbers());
        }

        if (Boolean.TRUE.equals(search.getIsIgnoreInternationalPayment())) {
            sql.append(" AND (e.type is null or e.type <> :internationalPaymentType) ");
            values.put("internationalPaymentType", TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT);
        }

        if (Validator.isNotNull(search.getTransferTypes())) {
            sql.append(" AND (e.transferType in :transferTypes ");
            values.put("transferTypes", search.getTransferTypes());

            if (search.getTransferTypes().stream().anyMatch(Arrays.asList(TransferType.INTER_BANK, TransferType.INTERNAL_BANK, TransferType.INTERNATIONAL_BANK)::contains)) {
                sql.append(" OR (e.transferType = :transferType AND (1 = 0 ");
                values.put("transferType", TransferType.TRANSFER_MONEY);

                if (search.getTransferTypes().contains(TransferType.INTERNAL_BANK)) {
                    sql.append(" OR (e.bankCode = :bankCode) ");
                }

                if (search.getTransferTypes().contains(TransferType.INTER_BANK)) {
                    sql.append(" OR (e.bankCode <> :bankCode AND e.type <> :type) ");
                    values.put("type", TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT);
                }

                if (search.getTransferTypes().contains(TransferType.INTERNATIONAL_BANK)) {
                    sql.append(" OR (e.bankCode <> :bankCode AND e.type = :type) ");
                    values.put("type", TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT);
                }
                sql.append(" ))) ");

                values.put("bankCode", BANK_CODE_INTERNAL);

                return sql.toString();
            }
            sql.append(" ) ");
        } else {
            sql.append(" AND e.transferType in :transferTypes ");
            values.put("transferTypes", TransferType.getOtherTransferType());
        }

        if (Validator.isNotNull(search.getTypes())) {
            sql.append(" AND e.type IN :type ");

            values.put("type", search.getTypes());
        }

        if (Validator.isNotNull(search.getBillingTypes())) {
            sql.append(" AND e.billingType in :billingTypes ");
            values.put("billingTypes", search.getBillingTypes());
        }

        return sql.toString();
    }

    @Override
    public Long countTransactionInsurance(TransactionInsuranceSearchRequest request) {
        StringBuilder sql = new StringBuilder();

        Map<String, Object> values = new HashMap<>();

        sql.append("select count(*) " + "from TransactionInsurance e " + "inner join Customer c on e.customerId = c.customerId");

        sql.append(this.createWhereQuery(request, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    @Override
    public List<TransactionInsuranceDTO> searchTransactionInsurance(TransactionInsuranceSearchRequest request, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        Map<String, Object> values = new HashMap<>();

        sql.append("select new com.mb.laos.model.dto.TransactionInsuranceDTO(c.cif,e.insuredName,e.insuredPhoneNumber,e.identificationNo,e.type,e.packageCode,e.vehicleCode,e.payTotal,e.discount,e.premium,e.payCurrency,e.transactionStatus,e.transactionCode,e.createdDate,e.transferTransactionId) " + "from TransactionInsurance e " + "inner join Customer c on e.customerId = c.customerId ");

        sql.append(this.createWhereQuery(request, values));

        sql.append(QueryUtil.createOrderQuery(Transaction.class, request.getOrderByType(), request.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), TransactionInsuranceDTO.class);

        values.forEach(query::setParameter);

        if (request.isHasPageable()) {
            if (pageable != null) {
                query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

                query.setMaxResults(pageable.getPageSize());
            } else {
                query.setFirstResult(QueryUtil.FIRST_INDEX);

                query.setMaxResults(QueryUtil.MAX_RESULT);
            }
        }

        return query.getResultList();
    }

    @Override
    public List<Transaction> umoneySearch(TransactionSearchRequest search) {

        if (Validator.isNull(search.getTransferTypes())) {
            search.setTransferTypes(TransferType.getUmoneyTransferType());
        }

        StringBuilder sql = new StringBuilder();

        List<Transaction> transactions = new ArrayList<>();

        List<TransferType> transferTypes = search.getTransferTypes();

        if (transferTypes.contains(TransferType.CASH_OUT)) {
            Map<String, Object> values = new HashMap<>();

            sql.append(createUmoneySearchTransactionQuery());

            if (search.isHistory()) {
                sql.append(" left join Bank b on b.bankCode = e.bankCode ");
            }

            sql.append(createUmoneyWhereQuery(search, values));

            Query query = entityManager.createQuery(sql.toString(), Transaction.class);

            values.forEach(query::setParameter);

            transactions.addAll(query.getResultList());
        }

        if (transferTypes.contains(TransferType.CASH_IN)) {
            search.setTransferTypes(TransferType.getCashInTransferType());
            transactions.addAll(searchTransaction(search, null));
        }
        return transactions;
    }

    @Override
    public List<TransactionClient> debitDepositSearch(TransactionSearchRequest search) {

        if (Validator.isNull(search.getTransferTypes())) {
            search.setTransferTypes(TransferType.getDebitDepositUmoneyTransferType());
        }

        StringBuilder sql = new StringBuilder();

        List<TransactionClient> transactions = new ArrayList<>();

        Map<String, Object> values = new HashMap<>();

        sql.append(createDebitDepositUmoneySearchTransactionQuery());

        if (search.isHistory()) {
            sql.append(" left join Bank b on b.bankCode = e.bankCode ");
        }

        sql.append(createUmoneyWhereQuery(search, values));

        Query query = entityManager.createQuery(sql.toString(), TransactionClient.class);

        values.forEach(query::setParameter);

        transactions.addAll(query.getResultList());

        return transactions;
    }

    @Override
    public List<CustomerDTO> searchNumberOfTransaction(NumberOfTransactionRequest search, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT " +
                "       COALESCE(debitA.debitCount, 0) + COALESCE(debitB.debitCount, 0) AS totalTransactionDebit, " +
                "       COALESCE(credit.creditCount, 0) AS totalTransactionCredit, " +
                "       e.CUSTOMER_ID, e.CIF , e.FULLNAME " +
                "FROM CUSTOMER  e ");

        sql.append(this.createWhereQueryNumberTransaction(search, values));
        sql.append(this.createOrderQuery(Customer.class, search.getOrderByType(), search.getOrderByColumn()));

        Query query = entityManager.createNativeQuery(sql.toString());
        values.forEach(query::setParameter);

        if (search.isHasPageable()) {
            if (pageable != null) {
                query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());
                query.setMaxResults(pageable.getPageSize());
            } else {
                query.setFirstResult(QueryUtil.FIRST_INDEX);

                query.setMaxResults(QueryUtil.MAX_RESULT);
            }
        }

        List<Object[]> results = query.getResultList();
        List<CustomerDTO> customerDTOS = new ArrayList<>();

        for (Object[] row : results) {
            CustomerDTO dto = new CustomerDTO(
                    (Long.valueOf(row[0].toString())),
                    (Long.valueOf(row[1].toString())),
                    (Long.valueOf(row[2].toString())),
                    (this.encryptor.decrypt(row[3].toString())),
                    ((String) row[4])
            );
            customerDTOS.add(dto);
        }

        return customerDTOS;
    }

    @Override
    public List<CustomerDTO> countNumberOfTransaction(NumberOfTransactionRequest search) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT " +
                "       COALESCE(debitA.debitCount, 0) + COALESCE(debitB.debitCount, 0) AS totalTransactionDebit, " +
                "       COALESCE(credit.creditCount, 0) AS totalTransactionCredit, " +
                "       e.CUSTOMER_ID, e.CIF , e.FULLNAME " +
                "FROM CUSTOMER  e ");

        sql.append(this.createWhereQueryNumberTransaction(search, values));
        Query query = entityManager.createNativeQuery(sql.toString());
        values.forEach(query::setParameter);

        List<Object[]> results = query.getResultList();
        List<CustomerDTO> customerDTOS = new ArrayList<>();

        for (Object[] row : results) {
            CustomerDTO dto = new CustomerDTO(
                    (Long.valueOf(row[0].toString())),
                    (Long.valueOf(row[1].toString())),
                    (Long.valueOf(row[2].toString())),
                    (this.encryptor.decrypt(row[3].toString())),
                    ((String) row[4])
            );
            customerDTOS.add(dto);
        }

        return customerDTOS;
    }

    private String createUmoneySearchTransactionQuery() {

        StringBuilder sql = new StringBuilder();

        sql.append("SELECT new com.mb.laos.model.Transaction" + "(e.transferType,e.clientAccountNumber,e.target,e.transactionId,"
                + "e.transactionAmount,e.transactionStatus,e.transactionFee,"
                + "e.transactionCurrency,e.beneficiaryCustomerName,e.beneficiaryAccountNumber,e.transactionStartTime,"
                + "e.discount, e.lastModifiedDate, e.bankCode, e.transactionCode, e.type, e.clientMessageId) "
                + "from TransactionClient e");

        return sql.toString();
    }

    private String createDebitDepositUmoneySearchTransactionQuery() {

        StringBuilder sql = new StringBuilder();

        sql.append("SELECT new com.mb.laos.model.TransactionClient" + "(e.transferType, e.clientAccountName, e.target, e.transactionId,"
                + "e.transactionAmount,e.transactionStatus,e.transactionFee,"
                + "e.transactionCurrency,e.beneficiaryCustomerName,e.beneficiaryAccountNumber,e.transactionStartTime,"
                + "e.discount, e.lastModifiedDate, e.bankCode, e.bankName, e.transactionCode, e.type, e.clientMessageId, "
                + "e.fromMember,e.fromUser,e.fromUserFullName,e.fromAccount, e.message, e.description, e.clientAccountNumber) "
                + "from TransactionClient e");

        return sql.toString();
    }

    private String createWhereQuery(TransactionInsuranceSearchRequest request, Map<String, Object> values) {

        StringBuilder sql = new StringBuilder();

        if (Validator.isNotNull(request.getKeyword())) {
            sql.append(" AND (lower(c.cif) = lower(:keywordEncrypt)");
            sql.append(" OR lower(e.identificationNo) LIKE lower(:keyword) ");
            sql.append(" OR lower(e.insuredName) LIKE lower(:keyword) ");
            sql.append(" OR lower(e.insuredPhoneNumber) LIKE lower(:keyword) ");
            sql.append(" OR lower(e.transactionCode) LIKE lower(:keyword)) ");
            values.put("keyword", QueryUtil.getFullStringParam(request.getKeyword()));
            values.put("keywordEncrypt", request.getEncryptedKeyword());
        }

        if (Validator.isNotNull(request.getFromDate())) {
            sql.append(" AND e.createdDate >= :transactionTimeStart ");

            values.put("transactionTimeStart", InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getStartOfDay(request.getFromDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(request.getToDate())) {
            sql.append(" AND e.createdDate <= :transactionTimeEnd ");

            values.put("transactionTimeEnd", InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getEndOfDay(request.getToDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(request.getTransactionStatus())) {
            sql.append(" AND e.transactionStatus in :transactionStatus ");
            values.put("transactionStatus", request.getTransactionStatus());
        }

        if (Validator.isNotNull(request.getInsuranceType())) {
            sql.append(" AND e.type = :type ");
            values.put("type", request.getInsuranceType());
        }

        if (Validator.isNotNull(request.getPackageCode())) {
            sql.append(" AND e.packageCode = :packageCode ");
            values.put("packageCode", request.getPackageCode());
        }

        if (Validator.isNotNull(request.getVehicleCode())) {
            sql.append(" AND e.vehicleCode = :vehicleCode ");
            values.put("vehicleCode", request.getVehicleCode());
        }

        return sql.toString();
    }

    private String createWhereQuery(BillingHistoryV2Search request, Long customerId, Map<String, Object> values) {

        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE t1.transactionFinishTime = (SELECT MAX(t2.transactionFinishTime) FROM Transaction t2  WHERE t2.target = t1.target ");

        sql.append(" AND t2.status = :status ");
        values.put("status", EntityStatus.ACTIVE.getStatus());

        sql.append(" AND t2.transactionStatus = :transactionStatus ");
        values.put("transactionStatus", TransactionStatus.SUCCESS);

        sql.append(" AND t2.transferType =: transferType ");
        values.put("transferType", TransferType.BILLING);

        sql.append(" AND t2.billingType =: billingType ");
        values.put("billingType", BillingType.getBillingType(request.getBillingType()).name());

        sql.append(" AND t2.customerId =: customerId) ");
        values.put("customerId", customerId);

        return sql.toString();
    }

    public String createCashInWhereQuery(Long customerId, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE t1.transactionFinishTime = (SELECT MAX(t2.transactionFinishTime) FROM Transaction t2  WHERE t2.target = t1.target ");

        sql.append(" AND t2.status = :status ");
        values.put("status", EntityStatus.ACTIVE.getStatus());

        sql.append(" AND t2.transactionStatus = :transactionStatus ");
        values.put("transactionStatus", TransactionStatus.SUCCESS);

        sql.append(" AND t2.transferType =: transferType ");
        values.put("transferType", TransferType.CASH_IN);

        sql.append(" AND t2.customerId =: customerId) ");
        values.put("customerId", customerId);

        return sql.toString();
    }

    public String createTopupWhereQuery(Long customerId, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE t1.transactionFinishTime = (SELECT MAX(t2.transactionFinishTime) FROM Transaction t2  WHERE t2.target = t1.target ");

        sql.append(" AND t2.status = :status ");
        values.put("status", EntityStatus.ACTIVE.getStatus());

        sql.append(" AND t2.transactionStatus = :transactionStatus ");
        values.put("transactionStatus", TransactionStatus.SUCCESS);

        sql.append(" AND t2.transferType =: transferType ");
        values.put("transferType", TransferType.TOPUP);

        sql.append(" AND t2.customerId =: customerId) ");
        values.put("customerId", customerId);

        return sql.toString();
    }

    private String createUmoneyWhereQuery(TransactionSearchRequest search, Map<String, Object> values) {

        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE e.status <> :statusDelete");

        values.put("statusDelete", EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(search.getTransactionCode())) {
            sql.append(" AND e.transactionCode LIKE :transactionCode ");

            values.put("transactionCode", QueryUtil.getFullStringParam(search.getTransactionCode()));
        }

        if (Validator.isNotNull(search.getKeyword())) {
            if (search.getTransferTypes().contains(TransferType.DEBIT) || search.getTransferTypes().contains(TransferType.DEPOSIT)) {
                sql.append(" AND (lower(e.clientAccountName) LIKE lower(:keyword) ");
            } else {
                sql.append(" AND (lower(e.beneficiaryCustomerName) LIKE lower(:keyword) " + " OR lower(e.target) LIKE lower(:keyword) ");
            }

            if (search.isHistory()) {
                sql.append(" OR lower(b.bankName) LIKE lower(:keyword) ");
            } else {
                sql.append(" OR lower(e.bankCode) LIKE lower(:keyword) ");
                sql.append(" OR lower(e.clientMessageId) LIKE lower(:keyword) ");
                sql.append(" OR lower(e.transactionCode) LIKE lower(:keyword) ");
                sql.append(" OR lower(e.fromMember) LIKE lower(:keyword) ");
                sql.append(" OR lower(e.fromUserFullName) LIKE lower(:keyword) ");
                sql.append(" OR lower(e.bankName) LIKE lower(:keyword) ");
                sql.append(" OR lower(e.clientAccountNumber) LIKE lower(:keyword) ");
                sql.append(" OR lower(e.transactionId) LIKE :keyword OR lower(e.transactionCode) LIKE lower(:keyword) ");
            }
            sql.append(" OR lower(e.transactionAmount) LIKE lower(:keyword)) ");
            values.put("keyword", QueryUtil.getFullStringParam(search.getKeyword()));
        }

        if (Validator.isNotNull(search.getFromDate())) {
            sql.append(" AND e.createdDate >= :transactionTimeStart ");

            values.put("transactionTimeStart", InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getStartOfDay(search.getFromDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(search.getToDate())) {
            sql.append(" AND e.createdDate <= :transactionTimeEnd ");

            values.put("transactionTimeEnd", InstantUtil.getInstantFromLocalDateTime(LocalDateUtil.getEndOfDay(search.getToDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(search.getTransactionStatus())) {
            sql.append(" AND e.transactionStatus in :transactionStatus ");
            values.put("transactionStatus", search.getTransactionStatus());
        }

        if (Validator.isNotNull(search.getCustomerAccNumber())) {
            sql.append(" AND e.customerAccNumber = :customerAccNumber ");
            values.put("customerAccNumber", search.getCustomerAccNumber());
        }

        if (Validator.isNotNull(search.getCustomerAccNumbers())) {
            sql.append(" AND e.customerAccNumber in :customerAccNumbers ");
            values.put("customerAccNumbers", search.getCustomerAccNumbers());
        }

        if (Validator.isNotNull(search.getTypes())) {
            sql.append(" AND e.type IN :type ");

            values.put("type", search.getTypes());
        }

        if (Validator.isNotNull(search.getTransferTypes())) {
            sql.append(" AND e.transferType IN :transferTypes ");
            values.put("transferTypes", search.getTransferTypes());
        }

        return sql.toString();
    }


    private String createWhereQueryNumberTransaction(NumberOfTransactionRequest search, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append("LEFT JOIN ( " +
                "    SELECT t.CUSTOMER_ID, COUNT(*) AS debitCount " +
                "    FROM T_TRANSACTION t " +
                "    WHERE t.status = :status AND t.TRANSACTION_STATUS = :transactionStatus " +
                "    AND t.TRANSACTION_FINISH_TIME BETWEEN :transactionTimeStart AND :transactionTimeEnd " +
                "    GROUP BY t.CUSTOMER_ID " +
                ") debitA ON e.CUSTOMER_ID = debitA.CUSTOMER_ID ");

        sql.append("LEFT JOIN ( " +
                "    SELECT dr.CUSTOMER_ID, COUNT(*) AS debitCount " +
                "    FROM T_RECEIVE_TRANSFER_MONEY dr WHERE dr.DR_CR = :debitDrCr " +
                "    AND (dr.ACCOUNT_TYPE <> :accountType OR dr.ACCOUNT_TYPE IS NULL) " +
                "    AND dr.TRANSACTION_FINISH_TIME BETWEEN :transactionTimeStart AND :transactionTimeEnd " +
                "    GROUP BY dr.CUSTOMER_ID " +
                ") debitB ON e.CUSTOMER_ID = debitB.CUSTOMER_ID ");

        sql.append("LEFT JOIN ( " +
                "    SELECT cr.CUSTOMER_ID, COUNT(*) AS creditCount " +
                "    FROM T_RECEIVE_TRANSFER_MONEY cr " +
                "    WHERE cr.DR_CR = :creditDrCr " +
                "    AND (cr.ACCOUNT_TYPE <> :accountType OR cr.ACCOUNT_TYPE IS NULL) " +
                "    AND cr.TRANSACTION_FINISH_TIME BETWEEN :transactionTimeStart AND :transactionTimeEnd " +
                "    GROUP BY cr.CUSTOMER_ID " +
                ") credit ON e.CUSTOMER_ID = credit.CUSTOMER_ID ");

        sql.append("WHERE e.STATUS <> :deleteStatus AND e.CIF IS NOT NULL AND e.REFERENCE_ID IS NULL ");

        if (Validator.isNotNull(search.getKeyword())) {
            sql.append("AND (lower(e.CIF) = lower(:keywordEncrypt) ");
            sql.append("OR lower(e.FULLNAME) LIKE lower(:keyword)) ");
            values.put("keyword", QueryUtil.getFullStringParam(search.getKeyword()));
            values.put("keywordEncrypt", search.getEncryptedKeyword());
        }

        if (Validator.isNotNull(search.getDrCrs())) {
            if (Validator.isNull(search.getNumberOfTransactionMin())) {
                if (search.getDrCrs().contains(DrCrType.D)) {
                    sql.append("AND (COALESCE(debitA.debitCount, 0) + COALESCE(debitB.debitCount, 0)) > 0 ");
                }
                if (search.getDrCrs().contains(DrCrType.C)) {
                    sql.append("AND COALESCE(credit.creditCount, 0) > 0 ");
                }
            } else {
                if (search.getDrCrs().contains(DrCrType.D)) {
                    sql.append("AND (COALESCE(debitA.debitCount, 0) + COALESCE(debitB.debitCount, 0)) >= :numberOfTransactionMin ");
                }

                if (search.getDrCrs().contains(DrCrType.C)) {
                    sql.append("AND COALESCE(credit.creditCount, 0) >= :numberOfTransactionMin ");
                }

                values.put("numberOfTransactionMin", search.getNumberOfTransactionMin());
            }

            if (Validator.isNotNull(search.getNumberOfTransactionMax())) {
                if (search.getDrCrs().contains(DrCrType.D)) {
                    sql.append("AND (COALESCE(debitA.debitCount, 0) + COALESCE(debitB.debitCount, 0)) <= :numberOfTransactionMax ");
                }

                if (search.getDrCrs().contains(DrCrType.C)) {
                    sql.append("AND COALESCE(credit.creditCount, 0) <= :numberOfTransactionMax ");
                }
                values.put("numberOfTransactionMax", search.getNumberOfTransactionMax());
            }
        }

        if (Validator.isNull(search.getDrCrs())
                || (Validator.isNotNull(search.getDrCrs()) && Validator.isNull(search.getNumberOfTransactionMin())
                && Validator.isNull(search.getNumberOfTransactionMax()))) {
            sql.append("AND ((COALESCE(debitA.debitCount, 0) + COALESCE(debitB.debitCount, 0)) > 0 " +
                    "OR COALESCE(credit.creditCount, 0) > 0) ");
        }

        values.put("deleteStatus", EntityStatus.DELETED.getStatus());
        values.put("transactionStatus", TransactionStatus.SUCCESS.name());
        values.put("creditDrCr", DrCrType.C.name());
        values.put("debitDrCr", DrCrType.D.name());
        values.put("status", EntityStatus.ACTIVE.getStatus());
        values.put("accountType", AccountTypeCif.SAVING.getStatus());
        values.put("transactionTimeStart", InstantUtil.getInstantFromLocalDateTime(
                LocalDateUtil.getStartOfDay(search.getFromDate()), Labels.getDefaultZoneId()));
        values.put("transactionTimeEnd", InstantUtil.getInstantFromLocalDateTime(
                LocalDateUtil.getEndOfDay(search.getToDate()), Labels.getDefaultZoneId()));

        return sql.toString();
    }

    private String createOrderQuery(Class<?> entityClass, String orderByType, String orderByColumn) {
        if (Validator.isNull(orderByType) || Validator.isNull(orderByColumn)) {
            return "order by e.fullname asc";
        }

        StringBuilder sql = new StringBuilder();

        orderByType = QueryUtil.ASC.equalsIgnoreCase(orderByType) ? QueryUtil.ASC : QueryUtil.DESC;

        sql.append(QueryUtil.addOrder(entityClass, orderByColumn, orderByType, "e"));

        if (sql.length() <= 0) {
            sql.append(" order by e.fullname asc");
        } else if (orderByColumn.indexOf("fullname") < 0) {
            sql.append(", e.fullname asc");
        }

        return sql.toString();

    }

    @Override
    public Long countInternationalTransaction(TransactionSearchRequest search) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> values = new HashMap<>();

        sql.append(" SELECT COUNT(e) FROM InternationalPayment e " +
                "join Customer c on c.customerId = e.customerId " +
                "join Transaction t on e.transferTransactionId = t.transferTransactionId ");

        sql.append(this.createInternationalQuery(search, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);
        return (Long) query.getSingleResult();
    }

    @Override
    public List<InternationalPaymentDTO> searchInternationalTransaction(TransactionSearchRequest request, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        Map<String, Object> values = new HashMap<>();

        sql.append("select new com.mb.laos.model.dto.InternationalPaymentDTO(" +
                "t.customerAccNumber,c.fullname,t.transactionStartTime,t.qrCodeValue, t.beneficiaryCustomerName,c.cif,t.transactionCurrency,e.beneficiaryCurrency,t.transferType,t.transactionAmount,t.transactionFee,t.discount,t.transactionCode,t.transactionId,t.transactionStatus, e.exchangeRate, e.nationCode, e.foreignAmount, e.foreignFee) " +
                "from InternationalPayment e " +
                "join Customer c on c.customerId = e.customerId " +
                "join Transaction t on e.transferTransactionId = t.transferTransactionId ");

        sql.append(this.createInternationalQuery(request, values));

        sql.append(QueryUtil.createOrderQuery(Transaction.class, request.getOrderByType(), request.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), InternationalPaymentDTO.class);

        values.forEach(query::setParameter);

        if (request.isHasPageable()) {
            if (pageable != null) {
                query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

                query.setMaxResults(pageable.getPageSize());
            } else {
                query.setFirstResult(QueryUtil.FIRST_INDEX);

                query.setMaxResults(QueryUtil.MAX_RESULT);
            }
        }

        return query.getResultList();
    }

    private String createInternationalQuery(TransactionSearchRequest search, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE e.status <> :statusDelete");

        values.put("statusDelete", EntityStatus.DELETED.getStatus());

        if (Validator.isNotNull(search.getKeyword())) {
            sql.append(" AND (lower(t.beneficiaryCustomerName) LIKE lower(:keyword) ");
            sql.append(" OR lower(c.fullname) LIKE lower(:keyword) ");
            sql.append(" OR lower(e.customerAccNumber) LIKE lower(:keyword) ");
            sql.append(" OR lower(e.transactionId) LIKE :keyword OR lower(e.transactionCode) LIKE lower(:keyword) ");
            sql.append(" OR lower(e.transactionAmount) LIKE lower(:keyword)) ");
            values.put("keyword", QueryUtil.getFullStringParam(search.getKeyword()));
        }

        if (Validator.isNotNull(search.getFromDate())) {
            sql.append(" AND e.createdDate >= :transactionTimeStart ");

            values.put("transactionTimeStart", InstantUtil.getInstantFromLocalDateTime(
                    LocalDateUtil.getStartOfDay(search.getFromDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(search.getToDate())) {
            sql.append(" AND e.createdDate <= :transactionTimeEnd ");

            values.put("transactionTimeEnd", InstantUtil.getInstantFromLocalDateTime(
                    LocalDateUtil.getEndOfDay(search.getToDate()), Labels.getDefaultZoneId()));
        }

        if (Validator.isNotNull(search.getTransactionStatus())) {
            sql.append(" AND e.transactionStatus in :transactionStatus ");
            values.put("transactionStatus", search.getTransactionStatus());
        }

        if (Validator.isNotNull(search.getNationCodes())) {
            sql.append(" AND e.nationCode in :nationCodes ");
            values.put("nationCodes", search.getNationCodes());
        }

        return sql.toString();
    }
}
