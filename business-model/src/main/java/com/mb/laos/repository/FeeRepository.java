package com.mb.laos.repository;

import com.mb.laos.model.Fee;
import com.mb.laos.repository.extend.FeeExtend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FeeRepository extends JpaRepository<Fee, Long>, FeeExtend {
    Optional<Fee> findByFeeId(Long id);

    @Query("SELECT f FROM Fee f WHERE f.transactionFeeTypeId = :transactionFeeTypeId AND f.status = :status AND f.isSystem = 0")
    List<Fee> findByTransactionFeeTypeIdAndStatus(Long transactionFeeTypeId, Integer status);

    List<Fee> findByTransactionFeeTypeIdAndStatusAndMerchantId(Long transactionFeeTypeId, Integer status, Long merchantId);
}
