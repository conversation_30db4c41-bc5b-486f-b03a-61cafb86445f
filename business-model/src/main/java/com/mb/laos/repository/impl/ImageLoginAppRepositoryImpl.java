package com.mb.laos.repository.impl;

import com.mb.laos.model.ImageLoginApp;
import com.mb.laos.model.search.ImageLoginAppSearch;
import com.mb.laos.repository.extend.ImageLoginAppRepositoryExtend;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;
import org.springframework.data.domain.Pageable;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ImageLoginAppRepositoryImpl implements ImageLoginAppRepositoryExtend {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Long count(ImageLoginAppSearch search) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT COUNT(1) FROM ImageLoginApp e ");

        sql.append(createWhereQuery(search, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<ImageLoginApp> search(ImageLoginAppSearch search, Pageable pageable) {
        StringBuilder sql = new StringBuilder();
        
        sql.append("SELECT e FROM ImageLoginApp e ");

        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(search, values));
        sql.append(QueryUtil.createOrderQuery(ImageLoginApp.class, search.getOrderByType(), search.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), ImageLoginApp.class);

        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

            query.setMaxResults(pageable.getPageSize());
        } else {
            query.setFirstResult(QueryUtil.FIRST_INDEX);

            query.setMaxResults(QueryUtil.MAX_RESULT);
        }

        return query.getResultList();
    }

    private String createWhereQuery(ImageLoginAppSearch params, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();
        
        sql.append(" WHERE 1 = 1 AND e.status != -1 ");

        if (Validator.isNotNull(params.getColorCode())) {
            sql.append(" AND e.colorCode LIKE :colorCode ");
            
            values.put("colorCode", QueryUtil.getFullStringParam(params.getColorCode()));
        }

        if (Validator.isNotNull(params.getKeyword())) {
            sql.append(" AND (e.colorCode LIKE :keyword) ");
            
            values.put("keyword", QueryUtil.getFullStringParam(params.getKeyword()));
        }

        if (Validator.isNotNull(params.getStatus())) {
            sql.append(" AND e.status = :status ");

            values.put("status", params.getStatus());
        }

        return sql.toString();
    }
}
