package com.mb.laos.repository;

import com.mb.laos.model.FeeRate;
import com.mb.laos.repository.extend.FeeRateExtend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface FeeRateRepository extends JpaRepository<FeeRate, Long>, FeeRateExtend {
    Optional<FeeRate> findByFeeRateId(Long feeRateId);

    @Query("from FeeRate fr where fr.status = 1 and fr.feeId = :feeId and fr.feeRateId <> :feeRateId and ( ( fr.effectiveAt <= :effectiveAt and fr.expiredAt >= :expiredAt) or" +
            " (fr.effectiveAt >= :effectiveAt and fr.effectiveAt <= :expiredAt) or (fr.expiredAt <= :expiredAt and fr.expiredAt >= :expiredAt) ) ")
    List<FeeRate> findFeeRateActiveByConflictDatetimeAndFeeId(Instant expiredAt, Instant effectiveAt, Long feeId, Long feeRateId);

    @Query("from FeeRate fr where fr.status = 1 and fr.feeId = :feeId and ( ( fr.effectiveAt <= :effectiveAt and fr.expiredAt >= :expiredAt) or" +
            " (fr.effectiveAt >= :effectiveAt and fr.effectiveAt <= :expiredAt) or (fr.expiredAt <= :expiredAt and fr.expiredAt >= :expiredAt) ) ")
    List<FeeRate> findFeeRateActiveByConflictDatetimeAndFeeId(Instant expiredAt, Instant effectiveAt, Long feeId);

    List<FeeRate> findAllByFeeIdAndStatus(Long feeId, Integer status);

    @Query("from FeeRate fr where fr.status = 1 and fr.feeId = :feeId and (fr.effectiveAt < :dateTime and fr.expiredAt > :dateTime)")
    List<FeeRate> findAllFeeRateActiveAndHasEffective(Long feeId, Instant dateTime);

    List<FeeRate> findAllByFeeIdInAndStatus(List<Long> feeIds, Integer status);

    boolean existsAllByFeeIdAndStatus(Long feeId, Integer status);
}
