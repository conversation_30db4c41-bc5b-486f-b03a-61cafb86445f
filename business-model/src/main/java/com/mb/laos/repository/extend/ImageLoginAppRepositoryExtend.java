package com.mb.laos.repository.extend;

import com.mb.laos.model.ImageLoginApp;
import com.mb.laos.model.search.ImageLoginAppSearch;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ImageLoginAppRepositoryExtend {
    /**
     * search
     *
     * @param search
     * @param pageable
     * @return
     */
    List<ImageLoginApp> search(ImageLoginAppSearch search, Pageable pageable);

    /**
     * count
     *
     * @param search
     * @return
     */
    Long count(ImageLoginAppSearch search);
}
