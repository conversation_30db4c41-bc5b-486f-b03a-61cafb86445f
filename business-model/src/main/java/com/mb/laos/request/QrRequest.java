package com.mb.laos.request;

import com.mb.laos.api.request.Request;
import com.mb.laos.enums.QrLapnetType;
import com.mb.laos.enums.QrType;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.validator.ValidationConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Getter
@Setter
@NoArgsConstructor
public class QrRequest extends Request {
    private static final long serialVersionUID = 2168493463179924986L;

    private QrType qrType;

    private String accountNumber;

    private String merchantCode;

    private QrLapnetType qrLapnetType = QrLapnetType.STATIC;

    @Min(value = ValidationConstraint.LENGTH.TRANSACTION_AMOUNT_VALUE_MIN, message = LabelKey.ERROR_TRANSACTION_AMOUNT_MIN)
    @Max( value = ValidationConstraint.LENGTH.TRANSACTION_AMOUNT_MAX, message = LabelKey.ERROR_TRANSACTION_AMOUNT_MAX_LENGTH)
    private Double amount;

    @Pattern(regexp = ValidationConstraint.PATTERN.CONTENT, message = "error.qr.qr-content-is-invalid")
    @Size(max = ValidationConstraint.LENGTH.QR_CONTENT_MAX_LENGTH, message = LabelKey.ERROR_QR_CONTENT_INVALID)
    private String content;

    private Long qrCodeId;
}
