package com.mb.laos.request;

import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.annotation.Exclude;
import com.mb.laos.api.request.Request;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IdentityVerifyRequest extends Request {
    private static final long serialVersionUID = 2657298440497443513L;

    private String fullname;

    private LocalDate dateOfBirth;

    private Long idCardTypeId;

    private String idCardCode;
    
    @Exclude
    private String idCardNumber;

    private LocalDate issueDate;

    @Exclude
    private String phoneNumber;

    private String transactionId;
}
