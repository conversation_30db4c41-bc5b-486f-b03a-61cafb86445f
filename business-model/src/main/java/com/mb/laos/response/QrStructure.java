package com.mb.laos.response;

import lombok.Data;

@Data
public class QrStructure {

    private static final Integer ID_SUBSTRING_QR_LENGTH = 4;

    private int id;

    private int length;

    private String data;

    public QrStructure(int id, int length, String qrCode) {
        this.id = id;
        this.length = length;
        this.data = qrCode.substring(ID_SUBSTRING_QR_LENGTH, ID_SUBSTRING_QR_LENGTH + this.length);
    }
}
