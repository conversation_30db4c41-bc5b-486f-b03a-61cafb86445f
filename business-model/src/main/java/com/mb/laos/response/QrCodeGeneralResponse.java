package com.mb.laos.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.response.Response;
import com.mb.laos.enums.QrType;
import com.mb.laos.enums.QrTypeGeneral;
import com.mb.laos.model.dto.BankDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QrCodeGeneralResponse extends Response {

    /**
	 * 
	 */
	private static final long serialVersionUID = 7398467522898327798L;

    private String payloadFormatIndicator;

    private String pointInitMethod;

    private DataObject dataObject;

    private QrTypeGeneral qrType;

    @Getter
    @Setter
    public static class DataObject implements Serializable {
    	
        /**
		 * 
		 */
		private static final long serialVersionUID = 7320724727301640951L;

		private String billNumber;

        private String phoneNumber;

        private String storeLabel;

        private String loyaltyNumber;

        private String referenceLabel;

        private String customerLabel;

        private String terminalLabel;

        private String purposeTransaction;
    }
}
