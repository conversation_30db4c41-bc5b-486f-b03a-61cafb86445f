package com.mb.laos.controller;

import com.mb.laos.model.dto.CategoryDTO;
import com.mb.laos.service.CategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/category")
public class CategoryController {
    private final CategoryService categoryService;

    @PostMapping("/all")
    public ResponseEntity<List<CategoryDTO>> getIdCardTypes() {
        return new ResponseEntity<>(this.categoryService.getAll(), HttpStatus.OK);
    }
}
