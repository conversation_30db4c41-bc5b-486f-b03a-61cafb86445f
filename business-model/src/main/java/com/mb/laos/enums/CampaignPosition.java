package com.mb.laos.enums;

import com.mb.laos.util.Validator;

import java.util.Arrays;
import java.util.List;

public enum CampaignPosition {
    HOME,
    LOGIN,
    OTHER;

    public static boolean isPublicPosition(CampaignPosition position) {
        if (Validator.isNull(position)) {
            return false;
        }

        List<CampaignPosition> publicPositions = Arrays.asList(LOGIN, OTHER);
        if (publicPositions.contains(position)) {
            return true;
        } else {
            return false;
        }
    }
}
