package com.mb.laos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum IdCard {
	IDCARD_OLD(0),
	IDCARD(1),
    PASSPORT(2);

    private long idCardTypeId;
    
    public static IdCard of(long id) {
        for (IdCard idCard: IdCard.values()) {
            if (idCard.getIdCardTypeId() == id) {
                return idCard;
            }
        }
        
        return null;
    }
}
