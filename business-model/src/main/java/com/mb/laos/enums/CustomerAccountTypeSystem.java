package com.mb.laos.enums;

import com.mb.laos.messages.LabelKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CustomerAccountTypeSystem {
    // tài khoản MB
    ACCOUNT_MB(0, LabelKey.LABEL_ACCOUNT_MB),

    // tài khoản umoney
    ACCOUNT_UMONEY(1, LabelKey.LABEL_ACCOUNT_UMONEY),
    ;
    private int status;

    private String key;

    /**
     * Dùng làm input cho annotation ValueOfEnum
     *
     * @return List<Integer>
     */
    public static List<Integer> getValues() {
        return Stream.of(values()).map(e -> e.status).collect(Collectors.toList());
    }

    public static CustomerAccountTypeSystem valueOfStatus(int status) {
        for (CustomerAccountTypeSystem e : values()) {
            if (e.getStatus() == status) {
                return e;
            }
        }

        return null;
    }
}
