package com.mb.laos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum MaturityInstructionType {
    ROTATION_OF_PRINCIPAL_AND_INTEREST,
    ROOT_ROTATION,
    NO_ROTATION;


    public static Map<MaturityInstructionType, Integer> getMaturityInstructionType() {
        Map<MaturityInstructionType, Integer> result = new HashMap<>();
        result.put(ROTATION_OF_PRINCIPAL_AND_INTEREST, 1);
        result.put(ROOT_ROTATION, 2);
        result.put(NO_ROTATION, 3);
        return result;
    }
}
