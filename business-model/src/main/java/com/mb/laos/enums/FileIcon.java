/**
 * 
 */
package com.mb.laos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 *
 */
@Getter
@AllArgsConstructor
public enum FileIcon {
	BANK("com.mb.laos.model.Bank"),

	NEWS("com.mb.laos.model.News"),

	NEWS_CONTENT("com.mb.laos.model.NewsContent"),

	INTEREST_RATE("com.mb.laos.model.InterestRateDetail"),

	FEE_SCHEDULE("com.mb.laos.model.FeeScheduleDetail")

	;

	private String className;

	/**
	 * Dùng làm input cho annotation ValueOfEnum
	 *
	 * @return List<Integer>
	 */
	public static List<String> getValues() {
		return Stream.of(values()).map(e -> e.className).collect(Collectors.toList());
	}

	public static FileIcon valueOfStatus(String className) {
		for (FileIcon e : values()) {
			if (e.name().equals(className)) {
				return e;
			}
		}

		return null;
	}
}
