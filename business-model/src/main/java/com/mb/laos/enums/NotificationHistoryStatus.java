/**
 * 
 */
package com.mb.laos.enums;

import com.mb.laos.messages.LabelKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 *
 */
@Getter
@AllArgsConstructor
public enum NotificationHistoryStatus {
	DELETED(-1, LabelKey.LABEL_DELETED),

	NO_CONTACT_YET(0, LabelKey.LABEL_NOT_CONTACT_YET),
	
	CONTACTED(1, LabelKey.LABEL_CONTACTED);


	

	private int status;

	private String key;

	/**
	 * Dùng làm input cho annotation ValueOfEnum
	 *
	 * @return List<Integer>
	 */
	public static List<Integer> getValues() {
		return Stream.of(values()).map(e -> e.status).collect(Collectors.toList());
	}

	public static NotificationHistoryStatus valueOfStatus(int status) {
		for (NotificationHistoryStatus e : values()) {
			if (e.getStatus() == status) {
				return e;
			}
		}

		return null;
	}
}
