package com.mb.laos.enums;

import com.mb.laos.messages.LabelKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CustomerApprovalStatus {
    // nháp
    DRAFT(0, LabelKey.LABEL_DRAFT),

    // chờ duyệt
    PENDING(1, LabelKey.LABEL_PENDING),

    // từ chối
    REJECT(2, LabelKey.LABEL_REJECT),

    // phê duyệt
    APPROVAL(3, LabelKey.LABEL_APPROVAL),

    // hủy phê duyệt
    CANCEL(4, LabelKey.LABEL_CANCEL),

    // hết hạn
    EXPIRED(5, LabelKey.LABEL_EXPIRED),

    // phê duyệt lỗi
    APPROVAL_ERROR(6, LabelKey.LABEL_APPROVAL_ERROR)
    ;
    private int status;

    private String key;

    /**
     * Dùng làm input cho annotation ValueOfEnum
     *
     * @return List<Integer>
     */
    public static List<Integer> getValues() {
        return Stream.of(values()).map(e -> e.status).collect(Collectors.toList());
    }

    public static CustomerApprovalStatus valueOfStatus(int status) {
        for (CustomerApprovalStatus e : values()) {
            if (e.getStatus() == status) {
                return e;
            }
        }

        return null;
    }
}
