package com.mb.laos.enums;

import java.util.ArrayList;
import java.util.List;

public enum TransactionFeeTypeCode {
    // Chuyển khoản nội bộ
    TRANSFER_INTERNAL_BANK,

    // Chuyển khoản liên ngân hàng
    TRANSFER_INTER_BANK,

    // Nạp tiền ví điện tử
    E_WALLET_RECHARGE,

    // Thanh toán hóa đơn internet
    INTERNET_BILLING,

    // Thanh toán hóa đơn nước
    WATER_BILLING,

    // Thanh toán hóa đơn điện
    ELECTRIC_BILLING,

    // Nạp tiền điện thoại các nhà mạng
    TOPUP,

    // Thanh toán QR
    QR_BILLING,

    //Thanh toán merchant nội bộ
    INTERNAL_QR_BILLING,

    // Thanh toán merchant Lapnet
    LAPNET_QR_BILLING,

    // Thanh toán điện thoại trả sau
    POST_PAID_BILLING,

    // <PERSON>h toán điện thoại cố định
    PSTN_BILLING,

    // Thanh toán bảo hiểm
    INSURANCE;

    public static List<String> getTypeCodeAvoid() {
        List<String> result = new ArrayList();
        result.add(LAPNET_QR_BILLING.name());
        result.add(TRANSFER_INTERNAL_BANK.name());
        result.add(TRANSFER_INTER_BANK.name());
        return result;
    }

}
