package com.mb.laos.service.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import com.mb.laos.model.MoneyAccount;
import com.mb.laos.model.dto.MoneyAccountDTO;
import com.mb.laos.util.Validator;

@Mapper(componentModel = "spring")
public interface MoneyAccountMapper extends EntityMapper<MoneyAccountDTO, MoneyAccount> {
	@Mapping(source = "availableAmount", target = "availableAmount", qualifiedByName = "castDoubleToDouble")
	MoneyAccount toEntity(MoneyAccountDTO dto);

	@Named("castDoubleToDouble")
	default Double castDoubleToDouble(String doubleNum) {
		return Validator.isNotNull(doubleNum) ? Double.valueOf(doubleNum) : null;
	}
}
