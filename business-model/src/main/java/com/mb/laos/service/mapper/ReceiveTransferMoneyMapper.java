package com.mb.laos.service.mapper;

import com.mb.laos.model.ReceiveTransferMoney;
import com.mb.laos.model.SavingAccount;
import com.mb.laos.model.dto.ReceiveTransferMoneyDTO;
import com.mb.laos.model.dto.SavingAccountDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ReceiveTransferMoneyMapper extends EntityMapper<ReceiveTransferMoneyDTO, ReceiveTransferMoney> {

}
