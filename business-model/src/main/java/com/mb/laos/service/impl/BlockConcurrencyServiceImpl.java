package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.service.BlockConcurrencyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j // Annotation để sử dụng logger từ Lombok
@RequiredArgsConstructor // Annotation tự động tạo constructor với các final fields
@Service // Annotation đánh dấu đây là Spring Service component
public class BlockConcurrencyServiceImpl implements BlockConcurrencyService { // Class triển khai interface BlockConcurrencyService để xử lý chặn concurrency và spam

    private final RedissonClient redissonClient; // Dependency injection cho Redis client để xử lý distributed lock và cache

    @Override // Override method từ interface BlockConcurrencyService
    public void blockConcurrency(String key, int maxAttempt, String cacheConstant, ErrorCode errorCode, int time) { // Method chặn concurrency với các tham số tùy chỉnh

        RMapCache<String, Integer> map = redissonClient.getMapCache(cacheConstant); // Lấy map cache từ Redis với tên cache constant
        RReadWriteLock rwLock = map.getReadWriteLock(key); // Lấy read-write lock cho key cụ thể
        rwLock.writeLock().lock(); // Khóa write lock để đảm bảo thread-safe
        try { // Try block để đảm bảo unlock trong finally
            int attempt = map.getOrDefault(key, 0); // Lấy số lần attempt hiện tại từ cache, mặc định là 0
            attempt++; // Tăng số lần attempt lên 1
            _log.info("number of attempt : {}", attempt); // Log số lần attempt hiện tại
            if (attempt > maxAttempt) { // Nếu số lần attempt vượt quá giới hạn cho phép
                throw new BadRequestAlertException(errorCode); // Ném exception với error code tương ứng
            }

            map.put(key, attempt, time, TimeUnit.SECONDS); // Lưu số lần attempt vào cache với TTL (time to live) tính bằng giây

        } finally { // Finally block để đảm bảo unlock
            rwLock.writeLock().unlock(); // Mở khóa write lock
        }
    }

    @Override // Override method từ interface BlockConcurrencyService
    public void resetSpamCount(String key, String cacheConstant) { // Method reset spam count cho một key cụ thể
        RMapCache<String, Integer> map = redissonClient.getMapCache(cacheConstant); // Lấy map cache từ Redis với tên cache constant
        map.remove(key); // Xóa key khỏi cache để reset spam count
    }
}
