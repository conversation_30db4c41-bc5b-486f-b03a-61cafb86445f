package com.mb.laos.service;

import com.mb.laos.model.dto.LoanOnlineDTO;
import com.mb.laos.model.search.LoanOnlineSearch;
import org.springframework.data.domain.Page;

public interface LoanOnlineBusinessService {

    /**
     * Create loan online
     *
     * @param loanOnlineDTO LoanOnlineDTO
     * @return LoanOnlineDTO
     */
    LoanOnlineDTO create(LoanOnlineDTO loanOnlineDTO);

    /**
     * Search by keyword
     *
     * @param params   LoanOnlineSearch
     * @return Page<LoanOnlineDTO>
     */
    Page<LoanOnlineDTO> searchByKeyword(LoanOnlineSearch params);

    /**
     * Get detail
     *
     * @param loanId Long
     * @return LoanOnlineDTO
     */
    LoanOnlineDTO detail(Long loanId);
}
