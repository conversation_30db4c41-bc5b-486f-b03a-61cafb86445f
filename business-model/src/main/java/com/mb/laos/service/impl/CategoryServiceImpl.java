package com.mb.laos.service.impl;

import com.mb.laos.enums.CategoryType;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Category;
import com.mb.laos.model.dto.CategoryDTO;
import com.mb.laos.repository.CategoryRepository;
import com.mb.laos.service.CategoryService;
import com.mb.laos.service.mapper.CategoryMapper;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service // Annotation đánh dấu đây là Spring Service component
@RequiredArgsConstructor // Annotation tự động tạo constructor với các final fields
public class CategoryServiceImpl implements CategoryService { // Class triển khai interface CategoryService để xử lý các thao tác với Category
    private final CategoryRepository categoryRepository; // Dependency injection cho repository thao tác với bảng Category

    private final CategoryMapper categoryMapper; // Dependency injection cho mapper chuyển đổi giữa Category entity và CategoryDTO

    @Override // Override method từ interface CategoryService
    public List<CategoryDTO> getAll() { // Method lấy tất cả categories và chuyển đổi thành DTO
        List<Category> results = this.categoryRepository.findAll(); // Lấy tất cả Category entities từ database

        List<CategoryDTO> categories = this.categoryMapper.toDto(results); // Chuyển đổi list Category entities thành list CategoryDTO

        categories.forEach(item -> { // Lặp qua từng CategoryDTO để xử lý thêm thông tin
            if (Validator.equals(item.getCategoryId(), CategoryType.NEWS_INTERNAL.getIdCategoryType())) { // Nếu category ID khớp với NEWS_INTERNAL type
                item.setType(CategoryType.NEWS_INTERNAL); // Set type là NEWS_INTERNAL
            } else { // Nếu không phải NEWS_INTERNAL
                item.setType(CategoryType.NEWS_INTER); // Set type là NEWS_INTER (international)
            }
            item.setName(Labels.getLabels(item.getName())); // Lấy tên category đã được localize từ Labels
        });

        return categories; // Trả về list CategoryDTO đã được xử lý
    }

}
