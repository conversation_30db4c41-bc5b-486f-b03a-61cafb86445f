package com.mb.laos.cache.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

import org.springframework.stereotype.Component;

import com.mb.laos.configuration.ConsumerProperties;
import com.mb.laos.util.StringPool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class LtcEncryptUtil {

	private final ConsumerProperties consumerProperties;

	private String privateKey;

	private String algorithm;

	private String cipherAlgorithm;

	private SecretKeySpec secretKey;

	private byte[] key;

	@PostConstruct
	void init() {
		try {
			this.privateKey = this.consumerProperties.getApiLtc().getPrivateKey();

			this.algorithm = this.consumerProperties.getApiLtc().getAlgorithm();

			this.cipherAlgorithm = this.consumerProperties.getApiLtc().getCipherAlgorithm();

			byte[] byteKey = this.privateKey.getBytes(StandardCharsets.UTF_8);

			MessageDigest sha = MessageDigest.getInstance(this.algorithm);

			byteKey = sha.digest(byteKey);

			this.key = Arrays.copyOf(byteKey, 16);

			this.secretKey = new SecretKeySpec(this.key, "AES");
		} catch (NoSuchAlgorithmException e) {
			_log.error("No such algorithm exception!: {}", e);
		}
	}

	private String encrypt(String dataString) {
		String encryptedStr = StringPool.NULL;

		try {
			Cipher cipher = Cipher.getInstance(this.cipherAlgorithm);

			cipher.init(1, this.secretKey);

			byte[] encryptedByte = cipher.doFinal(dataString.getBytes(StandardCharsets.UTF_8));

			encryptedStr = Base64.getEncoder().encodeToString(encryptedByte);
		} catch (Exception e) {
			_log.error("Error while encrypting: {}", e);
		}

		return encryptedStr;
	}

	public String generateKey(String transactionId) {
		StringBuilder sb = new StringBuilder(2);

		sb.append(this.consumerProperties.getApiLtc().getUserId());
		sb.append(transactionId);

		return this.encrypt(sb.toString());
	}

}
