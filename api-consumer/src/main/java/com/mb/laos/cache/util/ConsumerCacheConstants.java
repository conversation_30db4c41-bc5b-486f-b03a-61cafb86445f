/**
 *
 */
package com.mb.laos.cache.util;

/**
 * <AUTHOR>
 *
 */
public interface ConsumerCacheConstants extends CacheConstants {
	public interface ApiGee {
		public static final String TOKEN = "api-gee-token";
		public static final String LOAN_ACCOUNT = "loan-account";
		public static final String SAVING_ACCOUNT = "saving-account";

        public static final String CURRENCY_ACCOUNT = "currency-account";
	}

    public interface ApiUmoney {
        public static final String TOKEN = "api-umoney-token";
    }

    public interface ApiUmoneyReport {
        public static final String TOKEN = "api-umoney-report-token";
    }

    public interface ApiMmoney {
        public static final String TOKEN = "api-mmoney-token";
    }

    public interface TransactionCache {
        public static final String TRANSACTION_ID_CACHE = "transactionId_findByCustomerId";
    }

    public interface LviCache {
        public static final String VEHICLE_PACKAGE = "lvi_vehicle_package";
        public static final String VEHICLE_TYPE = "lvi_vehicle_type";
        public static final String VEHICLE_PACKAGE_FEE = "lvi_vehicle_package_fee";
        public static final String HEALTH_PACKAGE = "lvi_health_package";
        public static final String HEALTH_PACKAGE_FEE = "lvi_health_package_fee";
        public static final String DELIVERY = "lvi_delivery";
    }

}
