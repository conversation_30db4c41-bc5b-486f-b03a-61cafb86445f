package com.mb.laos.cache.util;

import com.mb.laos.configuration.ConsumerProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.EncodedKeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;

@Component
@RequiredArgsConstructor
@Slf4j
public class UnitelEncryptUtil {

    private final ConsumerProperties consumerProperties;

    private PrivateKey privateKey;

    private PrivateKey getPrivateKeyFromString(String key) {
        try {
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(
                    key));
            PrivateKey privateKeyVpg = keyFactory.generatePrivate(privateKeySpec);
            return privateKeyVpg;
        } catch (Exception e) {
            _log.error("Error while generate Unitel private key {}",e);
            return null;
        }
    }

    private String generateVpgMacBase64(String data) {
        String encryptData = "";
        try {
            this.privateKey = this.getPrivateKeyFromString(this.consumerProperties.getApiUnitel().getSecretKey());
            java.security.Signature sig = java.security.Signature.getInstance(
                    "SHA1withRSA");
            sig.initSign(this.privateKey);
            sig.update(data.getBytes());
            byte[] signature = sig.sign();
            encryptData = new String(Base64.getEncoder().encode(signature));
        } catch (Exception e) {
            _log.error("Error while generate Unitel signature {}",e);
        }
        return encryptData;
    }

    public String genSignature(Map<String, Object> data){
        Set<String> fieldsWithSignature = new LinkedHashSet<>(Arrays.asList("MSISDN","PROCESS_CODE","TRANS_AMOUNT","TRANS_TIME","SYSTEM_TRACE","CUST_CODE","CLIENT_ID"));
        StringBuilder signature = new StringBuilder();
        fieldsWithSignature.stream().filter(v-> data.containsKey(v)).forEach(v-> signature.append(data.get(v)));
        return generateVpgMacBase64(signature.toString());
    }
}
