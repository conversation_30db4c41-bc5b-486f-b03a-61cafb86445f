package com.mb.laos.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class DeviceInfoDTO implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 5936803091989851824L;

    private String token;
    private String deviceId;

    public DeviceInfoDTO(String deviceId, String token) {
        this.deviceId = deviceId;
        this.token = token;
    }

}
