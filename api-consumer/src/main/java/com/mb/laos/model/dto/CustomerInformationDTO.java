package com.mb.laos.model.dto;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import com.google.gson.annotations.SerializedName;
import com.mb.laos.annotation.Exclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CustomerInformationDTO implements Serializable {
	
    /**
	 * 
	 */
	private static final long serialVersionUID = 3059409453366726034L;

	@Exclude
    private String customerId;
    
    private String englishName;

    @Exclude
    private String phoneNo;
    
    private LocalDate dateOfBirth;

    private Integer status;

    private String gender;

    private String kycComplete;
    
    private String smsLanguage;
    
    private Integer sector;
    
    private String accountNumber;
    
    @SerializedName("personalID")
    private List<PersonalID> personalIDs = new ArrayList<>();
    
    private List<Account> accountList = new ArrayList<>();
    
    @Getter
    @Setter
    public static class PersonalID implements Serializable {
    	
        /**
		 * 
		 */
		private static final long serialVersionUID = 1635833121544976572L;

		private String idType;
        
        @Exclude
        private String idCode;
        
        private String issuePlace;
        
        private LocalDate idIssueDate;
    }
    
    @Getter
    @Setter
    public static class Account implements Serializable {
    	
        /**
		 * 
		 */
		private static final long serialVersionUID = -5560877268003445504L;

		private String customerID;
        
        private String accountName;
        
        private String accountNumber;
        
        private String accountType;
        
        private Balance balance;
        
        private String currency;
        
        private LocalDate opendate;
    }
    
    @Getter
    @Setter
    public static class Balance implements Serializable {
    	
        /**
		 * 
		 */
		private static final long serialVersionUID = 4363245210599568590L;

		private Long currentBalance;
        
        private Long availableBalance;
        
        private Long blockedAmount;
    }
}
