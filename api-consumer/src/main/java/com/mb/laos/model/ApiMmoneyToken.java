package com.mb.laos.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
public class ApiMmoneyToken implements Serializable {
    private static final long serialVersionUID = -8804815625683100592L;

    @SerializedName(value = "token")
    private String token;

    @SerializedName(value = "resultCode")
    private String resultCode;

    @JsonIgnore
    @SerializedName(value = "username")
    private String username;

    @SerializedName(value = "resultDesc")
    private String resultDesc;
}
