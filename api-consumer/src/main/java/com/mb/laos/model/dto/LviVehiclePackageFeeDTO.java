package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LviVehiclePackageFeeDTO extends InsuranceFeeDTO {
    private Integer id;

    private String packageCode;

    private String vehicleCode;

    private BigDecimal premium;

    private BigDecimal paid;

    private BigDecimal discount;

    private String currency;

    private String brochure;

    private String note;
}
