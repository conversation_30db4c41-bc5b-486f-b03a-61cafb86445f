package com.mb.laos.model.dto;

// Import các thư viện cần thiết cho JSON processing và Lombok
import com.fasterxml.jackson.annotation.JsonInclude;  // Annotation để control JSON serialization
import com.google.gson.annotations.SerializedName;    // Gson annotation để map JSON field names
import lombok.AllArgsConstructor;                     // Lombok: tạo constructor với tất cả fields
import lombok.Data;                                   // Lombok: tạo getter/setter/toString/equals/hashCode
import lombok.EqualsAndHashCode;                      // Lombok: control equals và hashCode generation
import lombok.Getter;                                 // Lombok: tạo getter methods
import lombok.NoArgsConstructor;                      // Lombok: tạo default constructor
import lombok.Setter;                                 // Lombok: tạo setter methods

import java.io.Serializable;                          // Interface để support serialization
import java.util.List;                                // Java List interface

/**
 * AccNumberVerifyDTO - Response DTO từ T24/LAPNET account verification
 *
 * Class này chứa tất cả thông tin trả về từ T24/LAPNET khi verify account:
 * - Account information: name, currency, location, phone
 * - Fee information: phí giao dịch theo từng currency (LAK, KHR)
 * - Exchange rate: tỷ giá cho cross-border transactions
 * - Original message: thông tin request gốc từ LAPNET
 * - Reference tracking: reference number để trace giao dịch
 *
 * Được sử dụng bởi:
 * - ApiGeeTransferService.verifyAccNumber(): Parse response từ T24/LAPNET
 * - TransferService: Lấy account info và fee để tính toán
 * - QrCodeService: Verify QR Code account information
 *
 * JSON mapping:
 * - Sử dụng @SerializedName để map với T24/LAPNET field names
 * - Support cả Jackson và Gson serialization
 * - Nested classes cho complex objects (Fee, ExchangeRate...)
 *
 * <AUTHOR> Laos Development Team
 * @version 1.0
 * @since 2023
 */
@Data                                    // Lombok: tạo getter/setter/toString/equals/hashCode
@NoArgsConstructor                       // Lombok: tạo default constructor
@AllArgsConstructor                      // Lombok: tạo constructor với tất cả parameters
@EqualsAndHashCode(callSuper = false)    // Lombok: tạo equals/hashCode, không include parent class
public class AccNumberVerifyDTO implements Serializable {

    /**
     * Serial version UID cho Java serialization
     * Đảm bảo compatibility khi class structure thay đổi
     */
    private static final long serialVersionUID = 5936803091989851824L;

    /**
     * Tên chủ tài khoản
     *
     * Tên đầy đủ của chủ tài khoản được verify:
     * - Từ T24: Tên khách hàng trong core banking
     * - Từ LAPNET: Tên từ ngân hàng đích
     * - Hiển thị cho user để confirm trước khi transfer
     *
     * JSON mapping: "accountname" hoặc "accountName"
     */
    @SerializedName(value = "accountname", alternate = {"accountName"})
    private String accountName;

    /**
     * Số thẻ tín dụng (nếu có)
     *
     * Thông tin thẻ tín dụng liên kết với tài khoản:
     * - Chỉ có khi account là credit card account
     * - Được mask để bảo mật (ví dụ: ****1234)
     * - Hiện tại chưa được sử dụng nhiều trong MB Laos
     */
    private String creditCard;

    /**
     * Reference number để tracking giao dịch
     *
     * Unique identifier từ T24/LAPNET:
     * - T24: Transaction reference từ core banking
     * - LAPNET: Message reference từ LAPNET network
     * - Được sử dụng để trace và reconcile giao dịch
     * - Lưu vào database để audit trail
     */
    private String referenceNumber;

    /**
     * Danh sách phí giao dịch theo currency
     *
     * Chứa fee information cho từng loại tiền tệ:
     * - LAK: Phí cho giao dịch nội địa
     * - KHR: Phí cho giao dịch Cambodia
     * - Bao gồm cả fixed fee và percentage fee
     * - Được sử dụng để tính total transaction cost
     */
    @SerializedName("feelist")
    private Fee feeList;

    /**
     * Loại tiền tệ của tài khoản đích
     *
     * Currency của account được verify:
     * - LAK: Kip Lào (nội địa)
     * - KHR: Riel Cambodia (quốc tế)
     * - USD: Đô la Mỹ
     * - Quyết định có cần tỷ giá hay không
     */
    private String accountCurrency;

    /**
     * Flag cho international query
     *
     * Xác định loại giao dịch:
     * - "Y": International transaction (cần tỷ giá)
     * - "N": Domestic transaction (không cần tỷ giá)
     * - Được set từ request và confirm bởi T24/LAPNET
     */
    private String internationalQuery;

    /**
     * Thông tin tỷ giá xuyên biên giới
     *
     * Exchange rate cho international transactions:
     * - Rate: Tỷ giá LAK/KHR, LAK/USD...
     * - Last update: Thời gian cập nhật tỷ giá
     * - Chỉ có khi internationalQuery = "Y"
     * - Được sử dụng để convert amount
     */
    @SerializedName("crossborderexchangerate")
    private CrossBorderExchangeRate crossBorderExchangeRate;

    /**
     * Timestamp của response
     *
     * Thời gian T24/LAPNET xử lý request:
     * - Format: ISO datetime string
     * - Được sử dụng để check response freshness
     * - Audit trail cho troubleshooting
     */
    private String time;

    /**
     * Địa chỉ/vị trí của tài khoản
     *
     * Location information của account holder:
     * - Branch location nơi mở tài khoản
     * - Address của khách hàng
     * - Thông tin bổ sung để verify
     */
    @SerializedName("accountlocation")
    private String accountLocation;

    /**
     * Terminal ID xử lý giao dịch
     *
     * Identifier của terminal/system xử lý:
     * - T24 terminal ID
     * - LAPNET node ID
     * - Được sử dụng cho technical troubleshooting
     */
    private String terminal;

    /**
     * Số điện thoại liên kết với tài khoản
     *
     * Phone number của account holder:
     * - Được mask để bảo mật (ví dụ: ***1234)
     * - Sử dụng để verify identity
     * - Có thể dùng cho SMS notification
     */
    @SerializedName("accountphone")
    private String accountPhone;

    /**
     * Invoice/bill information (nếu có)
     *
     * Thông tin hóa đơn liên quan:
     * - Bill payment scenarios
     * - Invoice number reference
     * - Hiện tại chưa được implement đầy đủ
     */
    private String invoice;

    /**
     * Flag yêu cầu purpose cho giao dịch
     *
     * Xác định có cần nhập purpose hay không:
     * - "Y": Bắt buộc nhập purpose
     * - "N": Không cần purpose
     * - Tuân thủ regulatory requirements
     */
    @SerializedName("purposerequired")
    private String purposerequired;

    /**
     * Mục đích giao dịch
     *
     * Purpose/reason cho transfer:
     * - Được nhập bởi user nếu purposerequired = "Y"
     * - Compliance với anti-money laundering
     * - Audit trail cho regulatory reporting
     */
    private String purpose;

    /**
     * Thông tin message gốc từ LAPNET
     *
     * Original request information từ LAPNET:
     * - From/To member, account, user info
     * - Reference và timestamp
     * - Được sử dụng cho reconciliation và audit
     */
    @SerializedName("originalmessage")
    private OriginalMessage originalMessage;

    /**
     * Fee - Nested class chứa thông tin phí giao dịch theo currency
     *
     * Class này organize fee information theo từng loại tiền tệ:
     * - LAK: Phí cho giao dịch nội địa (Kip Lào)
     * - KHR: Phí cho giao dịch Cambodia (Riel Cambodia)
     * - Mỗi currency có thể có multiple fee tiers
     * - Support cả fixed amount và percentage fee
     *
     * Structure từ T24/LAPNET response:
     * {
     *   "feelist": {
     *     "LAK": [{"feeamount": "5000", "feepercent": "0.1", "from": "0"}],
     *     "KHR": [{"feeamount": 1250.0, "feepercent": "0.1", "from": "0"}]
     *   }
     * }
     */
    @Getter  // Lombok: tạo getter methods
    @Setter  // Lombok: tạo setter methods
    public static class Fee implements Serializable {
        /**
         * Serial version UID cho nested class serialization
         */
		private static final long serialVersionUID = 4024996892118766367L;

        /**
         * Danh sách phí cho giao dịch LAK (Kip Lào)
         *
         * Fee tiers cho domestic transactions:
         * - Có thể có multiple tiers dựa vào amount ranges
         * - Mỗi tier có fixed fee và percentage fee
         * - "from" field xác định amount threshold
         */
        @SerializedName("LAK")
        private List<Lak> lak;

        /**
         * Danh sách phí cho giao dịch KHR (Riel Cambodia)
         *
         * Fee tiers cho Cambodia transactions:
         * - International fee structure
         * - Có thể khác với LAK fee
         * - Include cross-border processing fee
         */
        @SerializedName("KHR")
        private List<Khr> khr;
    }

    /**
     * Lak - Fee structure cho giao dịch LAK (Kip Lào)
     *
     * Class này chứa fee information cho domestic transactions:
     * - Fixed fee amount (feeAmount)
     * - Percentage fee (feePercent)
     * - Amount threshold (from)
     *
     * Example:
     * {"feeamount": "5000", "feepercent": "0.1", "from": "0"}
     * → Fixed fee 5000 LAK + 0.1% cho amount >= 0
     */
    @Getter                                      // Lombok: tạo getter methods
    @Setter                                      // Lombok: tạo setter methods
    @JsonInclude(JsonInclude.Include.NON_NULL)   // Jackson: chỉ include non-null fields
    public static class Lak implements Serializable {
        /**
         * Serial version UID cho Lak class serialization
         */
        private static final long serialVersionUID = -3843351383930633234L;

        /**
         * Fixed fee amount cho LAK transactions
         *
         * Phí cố định tính bằng LAK:
         * - String type để preserve precision
         * - Ví dụ: "5000" = 5,000 LAK
         * - Được add vào total transaction cost
         */
        @SerializedName("feeamount")
		private String feeAmount;

        /**
         * Percentage fee cho LAK transactions
         *
         * Phí theo phần trăm của transaction amount:
         * - String type để preserve precision
         * - Ví dụ: "0.1" = 0.1% của amount
         * - Calculated fee = amount * (feePercent / 100)
         */
        @SerializedName("feepercent")
        private String feePercent;

        /**
         * Amount threshold cho fee tier này
         *
         * Ngưỡng amount để áp dụng fee tier:
         * - "0": Áp dụng từ 0 LAK trở lên
         * - "100000": Áp dụng từ 100,000 LAK trở lên
         * - Fee tiers được sort theo "from" value
         */
        private String from;
    }

    /**
     * Khr - Fee structure cho giao dịch KHR (Riel Cambodia)
     *
     * Class này chứa fee information cho Cambodia transactions:
     * - Tương tự Lak nhưng cho international transactions
     * - feeAmount là Double type (khác với Lak)
     * - Include cross-border processing costs
     *
     * Example:
     * {"feeamount": 1250.0, "feepercent": "0.1", "from": "0"}
     * → Fixed fee 1,250 KHR + 0.1% cho amount >= 0
     */
    @Getter                                      // Lombok: tạo getter methods
    @Setter                                      // Lombok: tạo setter methods
    @JsonInclude(JsonInclude.Include.NON_NULL)   // Jackson: chỉ include non-null fields
    public static class Khr implements Serializable {
        /**
         * Serial version UID cho Khr class serialization
         */
        private static final long serialVersionUID = 2296671344965989540L;

        /**
         * Fixed fee amount cho KHR transactions
         *
         * Phí cố định tính bằng KHR:
         * - Double type để support decimal values
         * - Ví dụ: 1250.0 = 1,250 KHR
         * - International fee thường cao hơn domestic
         */
        @SerializedName("feeamount")
        private Double feeAmount;

        /**
         * Percentage fee cho KHR transactions
         *
         * Phí theo phần trăm của transaction amount:
         * - String type để preserve precision
         * - Ví dụ: "0.1" = 0.1% của amount
         * - Có thể khác với LAK percentage
         */
        @SerializedName("feepercent")
        private String feePercent;

        /**
         * Amount threshold cho fee tier này
         *
         * Ngưỡng amount để áp dụng fee tier:
         * - Tính bằng KHR
         * - "0": Áp dụng từ 0 KHR trở lên
         * - Multiple tiers cho different amount ranges
         */
        private String from;
    }

    /**
     * CrossBorderExchangeRate - Thông tin tỷ giá xuyên biên giới
     *
     * Class này chứa exchange rate information cho international transactions:
     * - Rate: Tỷ giá conversion giữa currencies
     * - Currency: Loại tiền tệ đích
     * - Last update: Thời gian cập nhật tỷ giá
     * - ID: Unique identifier cho rate record
     *
     * Được sử dụng khi:
     * - internationalQuery = "Y"
     * - Transaction có currency khác LAK
     * - Cần convert amount cho cross-border payments
     *
     * Example:
     * {
     *   "id": "LAK_KHR_001",
     *   "ccy": "KHR",
     *   "rate": "0.2",
     *   "lastupdate": "2023-12-01T10:30:00"
     * }
     * → 1 LAK = 0.2 KHR
     */
    @Getter                                      // Lombok: tạo getter methods
    @Setter                                      // Lombok: tạo setter methods
    @JsonInclude(JsonInclude.Include.NON_NULL)   // Jackson: chỉ include non-null fields
    public static class CrossBorderExchangeRate implements Serializable{
        /**
         * Serial version UID cho CrossBorderExchangeRate serialization
         */
        private static final long serialVersionUID = -2198990412473133350L;

        /**
         * Unique identifier cho exchange rate record
         *
         * ID để identify rate record trong T24:
         * - Format: "LAK_KHR_001", "LAK_USD_002"...
         * - Được sử dụng để track rate changes
         * - Reference cho audit và reconciliation
         */
        private String id;

        /**
         * Currency code của tiền tệ đích
         *
         * ISO 4217 currency code:
         * - "KHR": Riel Cambodia
         * - "USD": Đô la Mỹ
         * - "THB": Baht Thái Lan
         * - Xác định loại tiền tệ được convert đến
         */
        private String ccy;

        /**
         * Thời gian cập nhật tỷ giá cuối cùng
         *
         * Timestamp khi rate được update:
         * - ISO datetime format
         * - Được sử dụng để check rate freshness
         * - Compliance với regulatory requirements
         * - Audit trail cho rate changes
         */
        @SerializedName("lastupdate")
        private String lastUpdate;

        /**
         * Tỷ giá conversion
         *
         * Exchange rate từ LAK sang target currency:
         * - String type để preserve precision
         * - Ví dụ: "0.2" = 1 LAK = 0.2 KHR
         * - Ví dụ: "0.000048" = 1 LAK = 0.000048 USD
         * - Được sử dụng: target_amount = lak_amount * rate
         */
        private String rate;
    }

    /**
     * OriginalMessage - Thông tin message gốc từ LAPNET
     *
     * Class này chứa original request information từ LAPNET network:
     * - From/To member và account information
     * - User information và account types
     * - Reference và timestamp cho tracking
     * - Được sử dụng cho reconciliation và audit
     *
     * Chỉ có trong LAPNET responses (interbank transactions):
     * - Internal MB transactions không có OriginalMessage
     * - LAPNET cần preserve original request info
     * - Compliance với LAPNET protocol requirements
     *
     * Example:
     * {
     *   "frommember": "MBLAOS",
     *   "fromuser": "USER123",
     *   "fromaccount": "**********",
     *   "tomember": "BCEL",
     *   "toaccount": "**********",
     *   "reference": "REF123456",
     *   "time": "2023-12-01T10:30:00"
     * }
     */
    @Getter                                      // Lombok: tạo getter methods
    @Setter                                      // Lombok: tạo setter methods
    @JsonInclude(JsonInclude.Include.NON_NULL)   // Jackson: chỉ include non-null fields
    public static class OriginalMessage implements Serializable{
        /**
         * Serial version UID cho OriginalMessage serialization
         */
        private static final long serialVersionUID = 796699998583960554L;

        /**
         * Member code của ngân hàng gửi
         *
         * LAPNET member identifier của sender:
         * - "MBLAOS": MB Bank Laos
         * - "BCEL": Banque pour le Commerce Extérieur Lao
         * - "LDB": Lao Development Bank
         * - Được sử dụng để identify source bank
         */
        @SerializedName("frommember")
        private String fromMember;

        /**
         * User ID của người gửi
         *
         * Identifier của user thực hiện giao dịch:
         * - Customer ID hoặc username
         * - Được sử dụng cho audit trail
         * - Tracking user activity across banks
         */
        @SerializedName("fromuser")
        private String fromUser;

        /**
         * Tên đầy đủ của người gửi
         *
         * Full name của account holder:
         * - Tên chính thức trong hệ thống ngân hàng
         * - Được sử dụng để verify identity
         * - Compliance với KYC requirements
         */
        @SerializedName("fromuserfullname")
        private String fromUserFullName;

        /**
         * Loại tài khoản nguồn
         *
         * Type của from account:
         * - "SAVINGS": Tài khoản tiết kiệm
         * - "CURRENT": Tài khoản vãng lai
         * - "CREDIT": Tài khoản tín dụng
         * - Được sử dụng cho business rules
         */
        @SerializedName("fromaccounttype")
        private String fromAccountType;

        /**
         * Số tài khoản nguồn
         *
         * Account number của sender:
         * - Full account number (không mask)
         * - Được sử dụng để debit amount
         * - Reference cho reconciliation
         */
        @SerializedName("fromaccount")
        private String fromAccount;

        /**
         * Loại tài khoản đích
         *
         * Type của destination account:
         * - Tương tự fromAccountType
         * - Có thể khác với source account type
         * - Ảnh hưởng đến processing rules
         */
        @SerializedName("totype")
        private String toType;

        /**
         * Số tài khoản đích
         *
         * Account number của receiver:
         * - Account được verify trong request này
         * - Destination cho credit amount
         * - Must match với request.toAccount
         */
        @SerializedName("toaccount")
        private String toAccount;

        /**
         * Member code của ngân hàng nhận
         *
         * LAPNET member identifier của receiver:
         * - Destination bank trong LAPNET network
         * - Được sử dụng để route message
         * - Must match với request.toMember
         */
        @SerializedName("tomember")
        private String toMember;

        /**
         * Reference number của original request
         *
         * Unique reference từ original LAPNET message:
         * - Được sử dụng để correlate messages
         * - Tracking end-to-end transaction flow
         * - Reconciliation giữa các banks
         */
        private String reference;

        /**
         * Timestamp của original message
         *
         * Thời gian original request được tạo:
         * - ISO datetime format
         * - Được sử dụng để check message age
         * - Audit trail cho transaction timeline
         */
        private String time;
    }
}
