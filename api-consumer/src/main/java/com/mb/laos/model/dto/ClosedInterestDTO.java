package com.mb.laos.model.dto;

import com.mb.laos.enums.T24ErrorCode;
import com.mb.laos.enums.TransactionStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClosedInterestDTO implements Serializable {
    private static final long serialVersionUID = -3422488722205183528L;

    private String principalAmount;
    private String interestAmount;
    private TransactionStatus transactionStatus;
    private T24ErrorCode t24ErrorCode;
}
