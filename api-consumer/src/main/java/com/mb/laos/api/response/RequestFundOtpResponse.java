package com.mb.laos.api.response;

import com.mb.laos.model.dto.ReqFundTransferDTO;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class RequestFundOtpResponse extends ApiGeeResponse {
    private static final long serialVersionUID = 950404015436319823L;

    private ReqFundTransferDTO data;
    private List<String> errorDesc = new ArrayList<>();
    @Override
    public String getErrorDesccription() {
        return StringUtil.join(this.errorDesc, StringPool.COMMA);
    }
}
