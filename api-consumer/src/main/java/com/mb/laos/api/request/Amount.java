// Package chứa các request classes cho API consumer module
package com.mb.laos.api.request;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * Amount - Class đại diện cho thông tin số tiền trong giao dịch
 *
 * Class này được sử dụng để encapsulate thông tin về số tiền và currency
 * trong các API requests, đặc biệt cho:
 * 1. Domestic transactions (LAK only)
 * 2. International transactions (LAK → Foreign currency)
 * 3. Multi-currency transactions với exchange rate
 * 4. Cross-border payments qua LAPNET
 *
 * Design Pattern:
 * - Composite pattern với nested InternationalPaymentInfo
 * - Null object pattern (internationalPaymentInfo = null cho domestic)
 * - Value object pattern (immutable after creation)
 *
 * Usage Scenarios:
 * - Transfer requests (internal/external)
 * - QR payment requests
 * - Bill payment requests
 * - International payment requests
 * - Fee calculation requests
 *
 * Validation Strategy:
 * - Amount format validation (numeric, positive)
 * - Currency code validation (ISO 4217)
 * - Exchange rate validation (for international)
 * - Business rule validation (limits, restrictions)
 *
 * <AUTHOR> Laos Development Team
 * @version 1.0
 * @since 2023
 */
@Getter                    // Lombok: Tự động generate getter methods cho tất cả fields
@Setter                    // Lombok: Tự động generate setter methods cho tất cả fields
public class Amount implements Serializable {

    /**
     * Serial version UID cho Java serialization
     * Đảm bảo compatibility khi class structure thay đổi
     */
    private static final long serialVersionUID = -2340283143967599470L;

    /**
     * Số tiền giao dịch (Transaction Amount)
     *
     * Đây là số tiền chính của giao dịch, được represent dưới dạng String
     * để tránh floating point precision issues trong financial calculations.
     *
     * Format Rules:
     * - String representation của số nguyên (không có decimal)
     * - Đơn vị: Minor unit của currency (ví dụ: cents cho USD, LAK cho LAK)
     * - Không có thousand separators (commas)
     * - Chỉ chứa digits (0-9)
     *
     * Examples:
     * - "100000" = 100,000 LAK
     * - "50000" = 50,000 LAK
     * - "1000000" = 1,000,000 LAK
     * - "25000" = 25,000 LAK
     *
     * Business Rules:
     * - Minimum amount: 1,000 LAK (configurable)
     * - Maximum amount: 50,000,000 LAK per transaction
     * - Must be positive (> 0)
     * - Must be integer (no fractional amounts)
     *
     * Validation Logic:
     * ```java
     * // Numeric validation
     * if (!amount.matches("\\d+")) {
     *     throw new InvalidAmountFormatException();
     * }
     *
     * // Range validation
     * long amountValue = Long.parseLong(amount);
     * if (amountValue < MIN_AMOUNT || amountValue > MAX_AMOUNT) {
     *     throw new AmountOutOfRangeException();
     * }
     * ```
     *
     * Usage Context:
     * - Domestic transfers: Amount trong LAK
     * - International transfers: Amount trong LAK (source currency)
     * - QR payments: Amount từ QR Code hoặc user input
     * - Bill payments: Bill amount trong LAK
     * - Fee calculations: Base amount cho fee calculation
     *
     * Security Considerations:
     * - Input sanitization để prevent injection
     * - Range validation để prevent overflow
     * - Business rule validation để prevent fraud
     * - Audit logging cho compliance
     */
    private String amount;

    /**
     * Mã tiền tệ (Currency Code)
     *
     * ISO 4217 currency code định nghĩa loại tiền tệ của amount.
     * Được sử dụng để xác định business rules và processing logic.
     *
     * Supported Currencies:
     *
     * 1. "LAK" - Lao Kip (Primary Currency):
     *    - Default currency cho tất cả domestic transactions
     *    - Base currency cho exchange rate calculations
     *    - Minor unit: 1 LAK = 1 LAK (no subdivision)
     *    - Symbol: ₭
     *
     * 2. "KHR" - Cambodian Riel (International):
     *    - Destination currency cho Cambodia payments
     *    - Exchange rate: LAK/KHR (variable)
     *    - Minor unit: 1 KHR = 1 KHR
     *    - Symbol: ៛
     *
     * 3. "THB" - Thai Baht (Planned):
     *    - Destination currency cho Thailand payments
     *    - Exchange rate: LAK/THB (variable)
     *    - Minor unit: 1 THB = 100 satang
     *    - Symbol: ฿
     *
     * 4. "USD" - US Dollar (Planned):
     *    - International wire transfers
     *    - Exchange rate: LAK/USD (variable)
     *    - Minor unit: 1 USD = 100 cents
     *    - Symbol: $
     *
     * Business Logic:
     * ```java
     * switch (currency) {
     *     case "LAK":
     *         return processDomesticTransaction(amount);
     *     case "KHR":
     *         return processInternationalTransaction(amount, "KH");
     *     case "THB":
     *         return processInternationalTransaction(amount, "TH");
     *     default:
     *         throw new UnsupportedCurrencyException();
     * }
     * ```
     *
     * Validation Rules:
     * - Must be valid ISO 4217 code
     * - Must be supported by system
     * - Must match với destination country (for international)
     * - Must have available exchange rate (for non-LAK)
     *
     * Integration Impact:
     * - Fee calculation: Different fee structures per currency
     * - Exchange rate: Required cho non-LAK currencies
     * - Routing: Different processing paths per currency
     * - Compliance: Different regulations per currency
     * - Settlement: Different settlement networks per currency
     */
    private String currency;

    /**
     * Thông tin thanh toán quốc tế (International Payment Information)
     *
     * Nested object chứa thông tin bổ sung cho cross-border transactions.
     * Chỉ được populate khi đây là international payment.
     *
     * Null Object Pattern:
     * - NULL: Domestic transaction (LAK only)
     * - NON-NULL: International transaction với exchange rate
     *
     * Business Logic:
     * ```java
     * if (internationalPaymentInfo != null) {
     *     // Process as international transaction
     *     return processInternationalPayment(amount, internationalPaymentInfo);
     * } else {
     *     // Process as domestic transaction
     *     return processDomesticPayment(amount, currency);
     * }
     * ```
     *
     * Use Cases:
     * - Cross-border QR payments (LAK → KHR)
     * - International money transfers
     * - Multi-currency transactions
     * - Exchange rate locked transactions
     *
     * Validation Dependencies:
     * - Nếu NON-NULL: currency phải khác "LAK"
     * - Nếu NULL: currency phải là "LAK"
     * - Exchange rate phải valid và current
     * - Destination country phải supported
     */
    private InternationalPaymentInfo internationalPaymentInfo;

    /**
     * InternationalPaymentInfo - Nested class cho thông tin thanh toán quốc tế
     *
     * Class này chứa chi tiết về exchange rate và destination information
     * cho cross-border transactions. Được design như một value object
     * để encapsulate tất cả international payment data.
     *
     * Design Principles:
     * - Immutable after creation (builder pattern)
     * - Self-contained validation
     * - Clear separation of concerns
     * - Type-safe currency handling
     *
     * Lifecycle:
     * 1. Created during beneficiary verification
     * 2. Populated với exchange rate từ LAPNET
     * 3. Validated trước khi fee calculation
     * 4. Used trong transaction execution
     *
     * <AUTHOR> Laos Development Team
     * @version 1.0
     * @since 2023
     */
    @Getter                    // Lombok: Tự động generate getter methods
    @Setter                    // Lombok: Tự động generate setter methods
    @Builder                   // Lombok: Tạo builder pattern cho object construction
    public static class InternationalPaymentInfo implements Serializable {

        /**
         * Serial version UID cho nested class serialization
         * Đảm bảo compatibility cho nested object
         */
        private static final long serialVersionUID = 8083413971285594516L;

        /**
         * Mã quốc gia đích (Destination Country Code)
         *
         * ISO 3166-1 alpha-2 country code của quốc gia nhận tiền.
         * Được sử dụng để xác định routing và compliance requirements.
         *
         * Supported Countries:
         *
         * 1. "KH" - Cambodia (Kingdom of Cambodia):
         *    - Currency: KHR (Cambodian Riel)
         *    - Network: LAPNET → Cambodia National Bank
         *    - Settlement: Real-time via LAPNET
         *    - Compliance: FATF standards
         *
         * 2. "TH" - Thailand (Kingdom of Thailand) - Planned:
         *    - Currency: THB (Thai Baht)
         *    - Network: LAPNET → PromptPay
         *    - Settlement: Real-time via PromptPay
         *    - Compliance: Bank of Thailand regulations
         *
         * 3. "VN" - Vietnam (Socialist Republic of Vietnam) - Planned:
         *    - Currency: VND (Vietnamese Dong)
         *    - Network: LAPNET → NAPAS
         *    - Settlement: Real-time via NAPAS
         *    - Compliance: State Bank of Vietnam regulations
         *
         * Business Rules:
         * - Must be supported destination country
         * - Must have active corridor với Laos
         * - Must comply với bilateral agreements
         * - Must have available exchange rates
         *
         * Routing Logic:
         * ```java
         * switch (toNation) {
         *     case "KH":
         *         return routeToCambodiaViaLAPNET(paymentInfo);
         *     case "TH":
         *         return routeToThailandViaPromptPay(paymentInfo);
         *     case "VN":
         *         return routeToVietnamViaNAPAS(paymentInfo);
         *     default:
         *         throw new UnsupportedDestinationException();
         * }
         * ```
         *
         * Compliance Impact:
         * - AML/CFT screening rules per country
         * - Transaction limits per corridor
         * - Reporting requirements per jurisdiction
         * - Sanctions checking per destination
         */
        private String toNation;

        /**
         * Số tiền gốc trước khi convert (Original Amount in LAK)
         *
         * Số tiền ban đầu mà customer muốn gửi, tính bằng LAK.
         * Được giữ nguyên để audit trail và reconciliation.
         *
         * Purpose:
         * - Audit trail: Track original customer intent
         * - Reconciliation: Match với customer account debit
         * - Fee calculation: Base amount cho fee calculation
         * - Compliance: Original transaction amount reporting
         *
         * Relationship với parent amount:
         * - Thường giống với parent.amount
         * - Có thể khác trong trường hợp amount adjustment
         * - Always trong LAK currency
         *
         * Examples:
         * - Customer input: 100,000 LAK
         * - originalAmount: "100000"
         * - After conversion: 400,000 KHR (với rate 4.0)
         *
         * Business Logic:
         * ```java
         * // Validation consistency
         * if (!originalAmount.equals(parentAmount.amount)) {
         *     logAmountAdjustment(originalAmount, parentAmount.amount);
         * }
         *
         * // Fee calculation base
         * double feeAmount = calculateInternationalFee(
         *     Long.parseLong(originalAmount),
         *     exchangeCurrency
         * );
         * ```
         *
         * Audit Requirements:
         * - Log original amount cho compliance
         * - Track any adjustments made
         * - Reconcile với customer account
         * - Report to regulatory authorities
         */
        private String originalAmount;

        /**
         * Tỷ giá hối đoái (Exchange Rate)
         *
         * Rate được sử dụng để convert từ LAK sang destination currency.
         * Được lấy real-time từ LAPNET hoặc exchange rate providers.
         *
         * Format:
         * - String representation của decimal number
         * - Precision: 4 decimal places
         * - Base currency: LAK (1 LAK = X destination currency)
         *
         * Examples:
         * - LAK/KHR: "4.0000" (1 LAK = 4 KHR)
         * - LAK/THB: "0.0015" (1 LAK = 0.0015 THB)
         * - LAK/USD: "0.000048" (1 LAK = 0.000048 USD)
         *
         * Rate Sources:
         * 1. LAPNET Network (Primary):
         *    - Real-time rates từ partner banks
         *    - Updated every 15 minutes
         *    - Includes spread và fees
         *
         * 2. Central Bank Rates (Backup):
         *    - Official rates từ Bank of Lao PDR
         *    - Updated daily
         *    - Used khi LAPNET unavailable
         *
         * 3. Commercial Rates (Fallback):
         *    - Market rates từ financial data providers
         *    - Updated hourly
         *    - Higher spread
         *
         * Calculation Logic:
         * ```java
         * // Convert LAK to destination currency
         * double rate = Double.parseDouble(exchangeRate);
         * double lakAmount = Double.parseDouble(originalAmount);
         * double foreignAmount = lakAmount * rate;
         *
         * // Round according to destination currency rules
         * if ("KHR".equals(exchangeCurrency)) {
         *     foreignAmount = Math.round(foreignAmount); // No decimals
         * } else if ("THB".equals(exchangeCurrency)) {
         *     foreignAmount = Math.round(foreignAmount * 100) / 100.0; // 2 decimals
         * }
         * ```
         *
         * Business Rules:
         * - Rate must be positive (> 0)
         * - Rate must be current (not older than 30 minutes)
         * - Rate must include all applicable spreads
         * - Rate must be locked during transaction
         *
         * Risk Management:
         * - Rate validation against market bounds
         * - Stale rate detection và refresh
         * - Rate lock mechanism during processing
         * - Fallback rate sources
         */
        private String exchangeRate;

        /**
         * Loại tiền tệ đích sau convert (Destination Currency)
         *
         * Currency mà beneficiary sẽ nhận được sau khi convert từ LAK.
         * Phải match với currency của destination country.
         *
         * Currency Mapping:
         * - "KH" (Cambodia) → "KHR" (Cambodian Riel)
         * - "TH" (Thailand) → "THB" (Thai Baht)
         * - "VN" (Vietnam) → "VND" (Vietnamese Dong)
         * - "US" (USA) → "USD" (US Dollar)
         *
         * Validation Rules:
         * ```java
         * // Country-currency consistency
         * Map<String, String> countryCurrencyMap = Map.of(
         *     "KH", "KHR",
         *     "TH", "THB",
         *     "VN", "VND",
         *     "US", "USD"
         * );
         *
         * if (!exchangeCurrency.equals(countryCurrencyMap.get(toNation))) {
         *     throw new CurrencyCountryMismatchException();
         * }
         * ```
         *
         * Business Impact:
         * - Determines final amount calculation
         * - Affects fee structure selection
         * - Influences settlement network routing
         * - Impacts compliance requirements
         *
         * Settlement Considerations:
         * - Different settlement networks per currency
         * - Different cut-off times per currency
         * - Different processing fees per currency
         * - Different regulatory requirements per currency
         *
         * Customer Experience:
         * - Display currency symbol correctly
         * - Format amount according to currency rules
         * - Show exchange rate transparency
         * - Provide rate lock guarantee
         *
         * Examples:
         * - Send 100,000 LAK → Receive 400,000 KHR
         * - Send 100,000 LAK → Receive 150 THB
         * - Send 100,000 LAK → Receive 4.8 USD
         */
        private String exchangeCurrency;
    }
}
