package com.mb.laos.api.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@XmlAccessorType(XmlAccessType.FIELD)
public class EtlSoapResponse implements Serializable {

    private static final long serialVersionUID = -598031529712134039L;

    @XmlElement
    private SoapResponse.Header header;

    @Getter
    @Setter
    @NoArgsConstructor
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class Header implements Serializable {

        /**
         *
         */
        private static final long serialVersionUID = -2423392393549159358L;

        @XmlElement(name = "ax23:resultcode")
        private String resultCode;

        @XmlElement(name = "ax23:resultDes")
        private String resultDes;

        @XmlElement(name = "ax23:transaction")
        private String transaction;
    }
}
