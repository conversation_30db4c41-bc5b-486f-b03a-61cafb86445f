package com.mb.laos.api.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class UmoneyResponse implements ConsumerResponse, Serializable {

    private static final long serialVersionUID = -5673034766260165765L;

    private String clientMessageId;

    @SerializedName("responseCode")
    private String errorCode;

    @SerializedName("message")
    private String errorDetail;

    @Override
    public String getErrorDesccription() {
        return null;
    }

    @Override
    public List<String> getErrorDesc() {
        return null;
    }
}

