package com.mb.laos.api.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
public class TopupUnitelResponse extends SoapResponse{
    /** The Constant serialVersionUID */
    private static final long serialVersionUID = -4932232910175191556L;

    private static final String SUCCESS_CODE = "00";

    @XmlElement(name = "MTI")
    private String mti;

    @XmlElement(name = "MSISDN")
    private String msisdn;

    @XmlElement(name = "PROCESS_CODE")
    private String processCode;

    @XmlElement(name = "TRANS_TIME")
    private String transTime;

    @XmlElement(name = "RESPONSE_CODE")
    private String responseCode;

    @XmlElement(name = "SYSTEM_TRACE")
    private String systemTrace;

    @XmlElement(name = "CLIENT_ID")
    private String clientId;

    @XmlElement(name = "SIGNATURE")
    private String signature;

    @XmlElement(name = "ADD_DATA")
    private String addData;

    @XmlElement(name = "TRANS_AMOUNT")
    private String transAmount;

    public boolean isSuccess(){
        return SUCCESS_CODE.equals(responseCode);
    }
}
