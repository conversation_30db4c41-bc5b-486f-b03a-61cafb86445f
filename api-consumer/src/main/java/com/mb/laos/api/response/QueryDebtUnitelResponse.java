package com.mb.laos.api.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.util.Validator;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Getter
@Setter
@NoArgsConstructor
@XmlRootElement
@JsonInclude(JsonInclude.Include.NON_NULL)
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryDebtUnitelResponse extends SoapResponse {

    private static final String SUCCESS_CODE = "00";

    /**
     * The Constant serialVersionUID
     */
    private static final long serialVersionUID = -8979907997260189373L;

    @XmlElement(name = "RESPONSE_CODE")
    private String responseCode;

    @XmlElement(name = "ADD_DATA")
    private String addData;

    @XmlElement(name = "PROCESS_CODE")
    private String processCode;

    public boolean isSuccess() {
        return SUCCESS_CODE.equals(responseCode);
    }

    public Long getDebt() {
        if (Validator.isNull(addData) || addData.indexOf("|") == -1) {
            return 0L;
        } else {
            return Long.parseLong(addData.substring(0, addData.indexOf("|")));
        }
    }
}

