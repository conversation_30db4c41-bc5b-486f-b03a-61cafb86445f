package com.mb.laos.api.response.lvi;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
public class LviDeliveryResponse extends LviSoapResponse {
    @XmlElement(name = "table")
    private LviTable table;

    @Getter
    @Setter
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement
    public static class LviTable implements Serializable {
        @XmlElement(name = "diffgr:diffgram")
        private LviDiffGr diffGr;
    }

    @Getter
    @Setter
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement
    public static class LviDiffGr implements Serializable {
        @XmlElement(name = "DocumentElement")
        private LviDocumentElement documentElement;
    }

    @Getter
    @Setter
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement
    public static class LviDocumentElement implements Serializable {
        @XmlElement(name = "tblAPI_Delevery")
        private List<Delivery> delivery;
    }

    @Getter
    @Setter
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlRootElement
    public static class Delivery implements Serializable {
        @XmlElement(name = "id")
        private Integer id;

        @XmlElement(name = "code")
        private String code;

        @XmlElement(name = "partner")
        private String partner;

        @XmlElement(name = "name_la")
        private String nameLa;

        @XmlElement(name = "name_en")
        private String nameEn;

        @XmlElement(name = "name_vn")
        private String nameVn;

        @XmlElement(name = "name_cn")
        private String nameCn;
    }

    public List<Delivery> getDelivery() {
        return table.diffGr.documentElement.delivery;
    }
}
