package com.mb.laos.api.request;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ChargeInfo implements Serializable {
    private static final long serialVersionUID = 176416113880250276L;

    private String accountName;

    private String accountNumber;

    private String code;

    private String amount;

    private String currency;

    private String description;

    private InternationalPaymentFeeInfo internationalPaymentFeeInfo;

    @Getter
    @Setter
    @Builder
    public static class InternationalPaymentFeeInfo implements Serializable {
        private static final long serialVersionUID = -1033874544239434644L;

        private String toNation;
        private String originalFeeAmount;
        private String exchangeRate;
        private String exchangeCurrency;
    }
}
