package com.mb.laos.api.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.OtpConfirmType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AuthenMethod implements Serializable {
    private OtpConfirmType type;
    private String otpValue;
    private String deviceId;
    private String token;
    private String transData;
    private String userId;
}
