package com.mb.laos.api.consumer;

import java.net.URI;

import com.mb.laos.messages.Labels;
import com.mb.laos.service.ApiUmoneyReportTokenService;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import com.google.gson.Gson;
import com.mb.laos.annotation.OutboundRequestLog;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.HttpResponseException;
import com.mb.laos.api.exception.UnauthorizedException;
import com.mb.laos.api.request.Request;
import com.mb.laos.api.response.UmoneyResponse;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.api.util.HeaderUtil;
import com.mb.laos.enums.UmoneyErrorCode;
import com.mb.laos.model.ApiUmoneyToken;
import com.mb.laos.security.util.SecurityConstants;
import com.mb.laos.service.ApiUmoneyTokenService;
import com.mb.laos.util.GsonUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class ApiUmoneySender {
    private final RestTemplate restTemplate;

    private final Gson gson;

    private final ApiUmoneyTokenService apiUmoneyTokenService;

    private final ApiUmoneyReportTokenService apiUmoneyReportTokenService;

    @OutboundRequestLog
    @Retryable(value = UnauthorizedException.class, maxAttemptsExpression = "${consumer.api-umoney.retry.max-attempts:2}",
            backoff = @Backoff(delayExpression = "${consumer.api-umoney.retry.max-delay:100}"))
    public <T extends UmoneyResponse, V extends Request> T sendToUmoneyApi(V req, Class<T> clazz,
                                                                           MultiValueMap<String, String> params, boolean isUmoneyReport) {
        HttpHeaders headers = getHttpHeaders(isUmoneyReport);

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(req.getUrl());

        HttpEntity<?> entity = null;

        if (Validator.isNotNull(params) && !params.isEmpty()) {
            builder = builder.queryParams(params);

            entity = new HttpEntity<>(headers);
        } else {
            entity = new HttpEntity<>(req, headers);
        }

        URI uri = builder.build(false).toUri();

        _log.info("Start call sendToUmoneyApi: {} {}", uri.toString(), gson.toJson(req));

        ResponseEntity<String> response = restTemplate.exchange(uri, req.getMethod(), entity, String.class);

        String body = response.getBody();

        _log.info("sendToUmoneyApi umoney body: {} ", body);

        T apiUmoneyResponse = GsonUtil.canJsonParse(response) ? gson.fromJson(body, clazz) : null;

        if (response.getStatusCode().equals(HttpStatus.OK)) {

            if (Validator.isNull(apiUmoneyResponse)) {
                _log.error("An unexpected error has occurred when sendToUmoneyApi: apiUmoneyResponse is null ");

                throw new BadRequestAlertException(ErrorCode.MSG1023);
            }

            return apiUmoneyResponse;
        } else {
            _log.error("An unexpected error has occurred when send to api umoney: {} ", body);

            if (Validator.isNotNull(apiUmoneyResponse)) {
                if (Validator.equals(apiUmoneyResponse.getErrorCode(), UmoneyErrorCode.TOKEN_IS_INVALID.getCode())) {
                    _log.error("Umoney token is invalid or expired: {}",
                            apiUmoneyResponse.getErrorDetail());

                    // refresh api token
                    if (isUmoneyReport) {
                        this.apiUmoneyReportTokenService.refreshApiUmoneyToken();
                    } else {
                        this.apiUmoneyTokenService.refreshApiUmoneyToken();
                    }

                    throw new UnauthorizedException(UmoneyErrorCode.TOKEN_IS_INVALID.getErrorCode());
                }

                throw new HttpResponseException(response.getStatusCodeValue(),
                        apiUmoneyResponse.getErrorCode(),
                        apiUmoneyResponse.getErrorDetail());

            }

            throw new BadRequestAlertException(ErrorCode.MSG1037);
        }
    }

    private HttpHeaders getHttpHeaders(boolean isUmoneyReport) {
        HttpHeaders headers = HeaderUtil.getTypeJsonHeaders();

        // NOTE: Phục vụ luồng báo cáo lapnet Umoney
        if (isUmoneyReport) {
            headers.set(HttpHeaders.ACCEPT_LANGUAGE, Labels.Language.EN);
        }

        ApiUmoneyToken token = null;

        try {
            token = isUmoneyReport ? this.apiUmoneyReportTokenService.getApiUmoneyToken() : this.apiUmoneyTokenService.getApiUmoneyToken();
        } catch (Exception e) {
            _log.error("Cannot get umoneyToken {}", e);

            if (isUmoneyReport) {
                this.apiUmoneyReportTokenService.refreshApiUmoneyToken();
            } else {
                this.apiUmoneyTokenService.refreshApiUmoneyToken();
            }
        }

        if (Validator.isNotNull(token)) {
            _log.info("get header with Token : {}", gson.toJson(token));

            headers.set(SecurityConstants.Header.AUTHORIZATION_HEADER,
                    HeaderUtil.getAuthorization(token.getTokenType(), token.getAccessToken()));
        }

        return headers;
    }

}
