package com.mb.laos.api.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class ApiGeeResponse implements Serializable, ConsumerResponse {

	private static final long serialVersionUID = 5831861618277831329L;

	private String clientMessageId;

	private String errorCode;

	private String errorDetail;
	
	public abstract Object getData();

	public abstract List<String> getErrorDesc();
	
	@Override
	public abstract String getErrorDesccription();
}
