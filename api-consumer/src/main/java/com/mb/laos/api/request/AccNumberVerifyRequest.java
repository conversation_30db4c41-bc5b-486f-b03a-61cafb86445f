// Package chứa các request classes cho API consumer module
package com.mb.laos.api.request;

// Import Lombok annotations để tự động generate code
import lombok.Builder; // Tạo builder pattern cho object construction
import lombok.Getter;  // Tự động tạo getter methods cho tất cả fields
import lombok.Setter;  // Tự động tạo setter methods cho tất cả fields

/**
 * AccNumberVerifyRequest - Request để verify thông tin số tài khoản
 *
 * Class này được sử dụng để gửi request verify thông tin tài khoản trong các scenarios:
 * 1. Verify tài khoản người nhận trước khi chuyển tiền
 * 2. Kiểm tra thông tin tài khoản nội bộ MB Bank
 * 3. Verify tài khoản liên ngân hàng qua LAPNET
 * 4. Kiểm tra tài khoản quốc tế (Cambodia, Thailand)
 * 5. Validate merchant account cho QR payments
 *
 * Request này được gửi đến:
 * - T24 Core Banking System (cho MB Bank accounts)
 * - LAPNET Network (cho inter-bank accounts)
 * - International payment gateways (cho cross-border)
 *
 * Response sẽ chứa:
 * - Tên chủ tài khoản
 * - Trạng thái tài khoản (active/inactive/blocked)
 * - Thông tin ngân hàng
 * - Phí giao dịch applicable
 *
 * <AUTHOR> Laos Development Team
 * @version 1.0
 * @since 2023
 */
@Getter  // Lombok: tự động tạo getter methods cho tất cả fields
@Setter  // Lombok: tự động tạo setter methods cho tất cả fields
@Builder // Lombok: tạo builder pattern để khởi tạo object dễ dàng
public class AccNumberVerifyRequest extends Request {

    /**
     * Serial version UID cho Java serialization
     * Đảm bảo compatibility khi class structure thay đổi
     */
    private static final long serialVersionUID = 4990000980614746607L;

    /**
     * Số tài khoản cần verify
     *
     * Đây là field chính chứa số tài khoản người nhận cần kiểm tra.
     * Format tùy theo loại tài khoản:
     * - MB Bank: 10-16 digits (ví dụ: "****************")
     * - LAPNET banks: theo chuẩn của từng ngân hàng
     * - International: theo format quốc tế
     *
     * Validation:
     * - Không được null hoặc empty
     * - Phải match regex pattern của bank tương ứng
     * - Luhn algorithm check (nếu applicable)
     */
    private String accountNumber;

    /**
     * Tài khoản nguồn (người gửi)
     *
     * Số tài khoản của khách hàng MB Bank thực hiện giao dịch.
     * Được sử dụng để:
     * - Kiểm tra quyền thực hiện giao dịch
     * - Validate transaction limits
     * - Calculate applicable fees
     * - Audit trail và compliance
     *
     * Format: MB Bank account number (10-16 digits)
     * Ví dụ: "****************"
     */
    private String fromAccount;

    /**
     * Tên đầy đủ của người gửi
     *
     * Họ tên chủ tài khoản nguồn, được sử dụng để:
     * - Compliance checking (AML/CFT)
     * - Transaction description
     * - Audit trail
     * - Customer identification
     *
     * Format: Tên đầy đủ theo CMND/CCCD
     * Ví dụ: "NGUYEN VAN A", "PHOMSAVANH SISAVATH"
     */
    private String fromUserFullName;

    /**
     * Loại tài khoản nguồn
     *
     * Phân loại tài khoản người gửi để xác định:
     * - Business rules applicable
     * - Fee structure
     * - Transaction limits
     * - Compliance requirements
     *
     * Các giá trị có thể:
     * - "SAVINGS": Tài khoản tiết kiệm
     * - "CURRENT": Tài khoản vãng lai
     * - "BUSINESS": Tài khoản doanh nghiệp
     * - "PREMIUM": Tài khoản cao cấp
     */
    private String fromAccountType;

    /**
     * User ID của người gửi
     *
     * Identifier duy nhất của user trong hệ thống, được sử dụng để:
     * - Authentication và authorization
     * - User session management
     * - Activity tracking
     * - Security audit
     *
     * Format: UUID hoặc numeric ID
     * Ví dụ: "user123456", "550e8400-e29b-41d4-a716-************"
     */
    private String fromUser;

    /**
     * Tài khoản đích (người nhận)
     *
     * Số tài khoản đích cần verify, thường giống với accountNumber
     * nhưng có thể khác trong một số trường hợp đặc biệt:
     * - Proxy account verification
     * - Batch verification requests
     * - Multi-step verification process
     *
     * Format: Tùy theo ngân hàng đích
     */
    private String toAccount;

    /**
     * Member ID của ngân hàng đích trong LAPNET Network
     *
     * LAPNET (Lao Payment Network) là hệ thống thanh toán liên ngân hàng của Lào,
     * kết nối tất cả các ngân hàng thành viên để thực hiện giao dịch chuyển tiền.
     *
     * Chức năng của toMember:
     * 1. ROUTING LOGIC:
     *    - Xác định ngân hàng đích để route request verification
     *    - LAPNET gateway sử dụng field này để forward request đến đúng bank
     *    - Đảm bảo request không bị gửi nhầm ngân hàng
     *
     * 2. FEE CALCULATION:
     *    - Mỗi member bank có fee structure riêng
     *    - MB Bank có thỏa thuận fee khác nhau với từng bank
     *    - Fee inter-bank khác với fee intra-bank (nội bộ MB)
     *
     * 3. PROTOCOL COMPLIANCE:
     *    - LAPNET có protocol chuẩn cho message format
     *    - Member ID phải đúng format theo LAPNET specification
     *    - Validation theo danh sách member banks được approve
     *
     * 4. BUSINESS RULES:
     *    - Một số member bank có giờ hoạt động khác nhau
     *    - Limit amount khác nhau cho từng member
     *    - Currency support khác nhau (một số chỉ hỗ trợ LAK)
     *
     * Danh sách Member Banks chính trong LAPNET:
     *
     * TIER 1 BANKS (Ngân hàng lớn):
     * - "BCEL": Banque pour le Commerce Extérieur Lao (BCEL Bank)
     *   + Ngân hàng thương mại lớn nhất Lào
     *   + Hỗ trợ đầy đủ các loại giao dịch
     *   + 24/7 availability
     *
     * - "LDB": Lao Development Bank
     *   + Ngân hàng phát triển quốc gia
     *   + Focus vào development projects
     *   + Business hours: 8:00-16:00
     *
     * - "APB": Agricultural Promotion Bank
     *   + Ngân hàng nông nghiệp
     *   + Chuyên về rural banking
     *   + Limit amount thấp hơn
     *
     * TIER 2 BANKS (Ngân hàng vừa):
     * - "JDB": Joint Development Bank
     * - "INDOCHINA": Indochina Bank
     * - "SACOMBANK": Sacombank Lao
     * - "PHONGSAVANH": Phongsavanh Bank
     *
     * TIER 3 BANKS (Ngân hàng nhỏ):
     * - "MARUHAN": Maruhan Japan Bank
     * - "ST": ST Bank
     * - "BRED": BRED Bank
     *
     * Technical Implementation:
     *
     * 1. VALIDATION LOGIC:
     *    ```java
     *    if (toMember != null) {
     *        // Validate member exists in LAPNET
     *        if (!lapnetMemberService.isValidMember(toMember)) {
     *            throw new InvalidMemberException();
     *        }
     *
     *        // Check member availability
     *        if (!lapnetMemberService.isAvailable(toMember)) {
     *            throw new MemberUnavailableException();
     *        }
     *    }
     *    ```
     *
     * 2. ROUTING LOGIC:
     *    ```java
     *    if ("BCEL".equals(toMember)) {
     *        return bcelGateway.verifyAccount(request);
     *    } else if ("LDB".equals(toMember)) {
     *        return ldbGateway.verifyAccount(request);
     *    } else {
     *        return lapnetGateway.verifyAccount(request);
     *    }
     *    ```
     *
     * 3. FEE CALCULATION:
     *    ```java
     *    Fee fee = feeService.calculateInterBankFee(
     *        fromMember: "MB",
     *        toMember: toMember,
     *        amount: amount,
     *        currency: "LAK"
     *    );
     *    ```
     *
     * Business Impact:
     * - NULL value: Giao dịch nội bộ MB Bank (không qua LAPNET)
     * - NON-NULL value: Giao dịch liên ngân hàng qua LAPNET
     * - Invalid value: Request sẽ bị reject với error code
     * - Unavailable member: Request sẽ timeout hoặc fail
     *
     * Error Scenarios:
     * - Member không tồn tại: "INVALID_MEMBER_CODE"
     * - Member offline: "MEMBER_UNAVAILABLE"
     * - Member maintenance: "MEMBER_UNDER_MAINTENANCE"
     * - Network timeout: "LAPNET_TIMEOUT"
     */
    private String toMember;

    /**
     * Loại tài khoản đích
     *
     * Phân loại tài khoản người nhận để:
     * - Validate compatibility với transaction type
     * - Apply correct business rules
     * - Calculate appropriate fees
     * - Compliance checking
     *
     * Các giá trị có thể:
     * - "ACCOUNT": Tài khoản thông thường
     * - "QR": QR Code account
     * - "MERCHANT": Merchant account
     * - "WALLET": E-wallet account
     */
    private String toType;

    /**
     * Flag cho international query
     *
     * Indicator để xác định đây có phải là query quốc tế không.
     * Ảnh hưởng đến:
     * - Routing logic (domestic vs international)
     * - Fee calculation (higher fees cho international)
     * - Compliance requirements (AML/CFT enhanced)
     * - Exchange rate application
     *
     * Các giá trị:
     * - "true": International query
     * - "false" hoặc null: Domestic query
     */
    private String internationalQuery;

    /**
     * Mã quốc gia đích (cho international transfers)
     *
     * ISO country code của quốc gia đích, được sử dụng khi:
     * - Cross-border payment verification
     * - Apply country-specific regulations
     * - Calculate international fees
     * - Compliance với international standards
     *
     * Các giá trị hỗ trợ:
     * - "KH": Cambodia (Campuchia)
     * - "TH": Thailand (Thái Lan)
     * - "VN": Vietnam (Việt Nam)
     * - "MM": Myanmar
     *
     * Null nếu là domestic transaction
     */
    private String toNation;
}
