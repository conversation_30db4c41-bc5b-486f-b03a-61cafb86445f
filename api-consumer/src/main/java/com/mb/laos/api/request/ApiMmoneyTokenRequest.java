package com.mb.laos.api.request;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.http.HttpMethod;

@Getter
@Setter
@NoArgsConstructor
public class ApiMmoneyTokenRequest extends Request {

    private static final long serialVersionUID = 3277830987214494191L;

    public ApiMmoneyTokenRequest(String url, String path, HttpMethod method) {
        super();

        super.setUrl(url + path);
        super.setMethod(method);
    }
}
