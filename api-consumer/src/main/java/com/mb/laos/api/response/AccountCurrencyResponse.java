package com.mb.laos.api.response;

import com.mb.laos.model.dto.AccountCurrencyDTO;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class AccountCurrencyResponse extends ApiGeeResponse {
    /**
     * The Constant serialVersionUID
     */
    private static final long serialVersionUID = 8578796631104631003L;
    private List<String> errorDesc = new ArrayList<>();

    private List<AccountCurrencyDTO> data;

    @Override
    public String getErrorDesccription() {
        return StringUtil.join(this.errorDesc, StringPool.COMMA);
    }
}
