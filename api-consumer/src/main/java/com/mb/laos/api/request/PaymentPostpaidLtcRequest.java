package com.mb.laos.api.request;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentPostpaidLtcRequest extends Request {
    private static final long serialVersionUID = -9068521719311245166L;

    @JsonProperty("ApiKey")
    private String apiKey;

    @JsonProperty("TransactionID")
    private String transactionId;

    @JsonProperty("Msisdn")
    private String msisdn;

    @JsonProperty("Operator")
    private String operator;

    @JsonProperty("Type")
    private String type;

    @JsonProperty("Amount")
    private String amount;

    @JsonProperty("PhoneUser")
    private String phoneUser;
}
