package com.mb.laos.api.consumer;

import java.io.IOException;
import java.io.StringReader;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.dom.DOMSource;

import org.apache.commons.text.StringEscapeUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import com.google.gson.Gson;
import com.mb.laos.annotation.OutboundRequestLog;
import com.mb.laos.api.request.SoapRequest;
import com.mb.laos.api.response.SoapResponse;
import com.mb.laos.util.XMLUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Component
public class ApiSoapSender {
    private final RestTemplate restTemplate;

    private final Gson gson;

    @OutboundRequestLog
    public <T extends SoapResponse, V extends SoapRequest> T sendToSoapService(V request, Class<T> clazz) {
        T response = null;

        try {
            HttpHeaders headers = new HttpHeaders();

            headers.setContentType(MediaType.valueOf(MediaType.TEXT_XML_VALUE));
            headers.set("SOAPAction", request.getSoapAction());

            _log.info("sendToSoapService request: {}", request.getRequestBody());

            ResponseEntity<String> exchange = restTemplate.exchange(request.getUrl(), request.getMethod(),
                            new HttpEntity<>(request.getRequestBody(), headers),
                            String.class);
            _log.info("sendToSoapService response : {}", exchange.getBody());

            JAXBContext jc = JAXBContext.newInstance(clazz);

            Unmarshaller unmarshaller = jc.createUnmarshaller();



            JAXBElement<T> je =
                            unmarshaller.unmarshal(this.getDOMSource(StringEscapeUtils.unescapeXml(exchange.getBody()), request.getResultNodeName()),
                                            clazz);

            response = je.getValue();

            _log.info("sendToSoapService response unmarshaled: {}", this.gson.toJson(response));
        } catch (ResourceAccessException ex) {
            _log.error("Time out when sendToSoapService: {}", ex);
            throw ex;

        }
        catch (Exception e) {
            _log.error("Error occured when sendToSoapService: {}", e);
        }

        return response;
    }

    private DOMSource getDOMSource(String body, String resultNodeName)
                    throws ParserConfigurationException, SAXException, IOException {
        DocumentBuilder db = XMLUtil.createDocumentBuilder(false);

        Document doc = db.parse(new InputSource(new StringReader(body)));

        Node resultNode = doc.getElementsByTagName(resultNodeName).item(0);

        return new DOMSource(resultNode);
    }
}
