package com.mb.laos.api.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentLeasingMmoneyRequest extends Request {
    private static final long serialVersionUID = -7013796848869401226L;

    @JsonProperty("TransactionID")
    private String transactionId;

    @JsonProperty("LeasID")
    private Integer leasId;

    @JsonProperty("AccName")
    private String accName;

    @JsonProperty("AccNo")
    private String accNo;

    @JsonProperty("Amount")
    private Long amount;

    @JsonProperty("ProviderID")
    private Integer providerId;

    @JsonProperty("PhoneUser")
    private String phoneUser;

    @JsonProperty("Name_Code")
    private String nameCode;

    @JsonProperty("Fee")
    private String fee;

    @JsonProperty("Remark")
    private String remark;
}
