package com.mb.laos.api.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public abstract class ListMmoneyResponse {
    private static final long serialVersionUID = -6617157567351535161L;

    @JsonProperty("Title_LA")
    private String titleLA;

    @JsonProperty("Title_EN")
    private String titleEN;

    @JsonProperty("Logo")
    private String logo;
}
