package com.mb.laos.api.response;

import com.mb.laos.model.dto.VerifyFundTransferDTO;
import com.mb.laos.util.StringPool;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class VerifyFundTransferResponse extends ApiGeeResponse {
    private static final long serialVersionUID = 950404015436319823L;

    private VerifyFundTransferDTO data;
    private List<String> errorDesc = new ArrayList<>();

    @Override
    public List<String> getErrorDesc() {
        return this.errorDesc;
    }

    @Override
    public String getErrorDesccription() {
        return StringPool.BLANK;
    }
}
