package com.mb.laos.api.consumer;

import com.google.gson.Gson;
import com.mb.laos.annotation.OutboundRequestLog;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.UnauthorizedException;
import com.mb.laos.api.request.Request;
import com.mb.laos.api.response.BestTelecomResponse;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.api.util.HeaderUtil;
import com.mb.laos.configuration.ConsumerProperties;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.security.util.SecurityConstants;
import com.mb.laos.util.GsonUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ApiBestTelecomSender {
    private final RestTemplate restTemplate;

    private final ConsumerProperties consumerProperties;

    private final Gson gson;

    /**
     * @param _req
     * @return
     */
    @Retryable(value = UnauthorizedException.class, maxAttemptsExpression = "${consumer.api-best-telecom.retry.max-attempts:2}",
            backoff = @Backoff(delayExpression = "${consumer.api-best-telecom.retry.max-delay:100}"))
    @OutboundRequestLog
    public <T extends BestTelecomResponse, V extends Request> T sendToApiBestTelecom(V request, Class<T> clazz,
                                                                                     MultiValueMap<String, String> params) {
        HttpHeaders headers = this.getHttpHeaders();

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(request.getUrl());

        HttpEntity<?> entity = null;

        if (Validator.isNotNull(params) && !params.isEmpty()) {
            builder = builder.queryParams(params);

            entity = new HttpEntity<>(headers);
        } else {
            entity = new HttpEntity<>(request, headers);
        }

        URI uri = builder.build(false).toUri();

        _log.info("Start call sendToApiBestTelecom with request: {}, {}", uri.toString(), this.gson.toJson(request));

        ResponseEntity<String> response = this.restTemplate.exchange(uri, request.getMethod(), entity, String.class);

        String body = response.getBody();

        _log.info("sendToApiBestTelecom response body: {} ", body);

        T apiBestTelecomResponse = GsonUtil.canJsonParse(response) ? this.gson.fromJson(body, clazz) : null;

        if (response.getStatusCode().equals(HttpStatus.OK)) {

            if (Validator.isNull(apiBestTelecomResponse)) {
                _log.error("An unexpected error has occurred when send to ApiBestTelecom: ccqResponse is null ");

                throw new BadRequestAlertException(ErrorCode.MSG1023);
            }

            return apiBestTelecomResponse;
        } else {
            _log.error("An unexpected error has occurred when send to ApiBestTelecom: status: {}, body: {} ",
                    response.getStatusCode().value(), body);

            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_AN_UNEXPECTED_ERROR_HAS_OCCURRED),
                    ErrorCode.MSG1037.name(), LabelKey.ERROR_AN_UNEXPECTED_ERROR_HAS_OCCURRED);
        }
    }

    private HttpHeaders getHttpHeaders() {
        HttpHeaders headers = HeaderUtil.getTypeJsonHeaders();

        headers.set(SecurityConstants.Header.AUTHORIZATION_HEADER,
                HeaderUtil.getBasicAuthorization(this.consumerProperties.getApiBestTelecom().getToken()));

        return headers;
    }
}
