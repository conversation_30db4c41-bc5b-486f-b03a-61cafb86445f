package com.mb.laos.api.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class PaymentMmoneyResponse extends MmoneyResponse {
    private static final long serialVersionUID = -8603345258757335773L;

    @JsonProperty("TransactionID")
    private String transactionId;

    @JsonProperty("PhoneUser")
    private String phoneUser;

    @JsonProperty("AccName")
    private String accName;

    @JsonProperty("AccNo")
    private String accNo;

    @JsonProperty("EWid")
    private Long ewId;

    @JsonProperty("Debit")
    private Long debit;
}
