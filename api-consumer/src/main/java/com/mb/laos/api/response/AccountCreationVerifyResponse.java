package com.mb.laos.api.response;

import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class AccountCreationVerifyResponse extends ApiGeeResponse {

    private static final long serialVersionUID = -4601849045039996079L;
    private List<String> errorDesc = new ArrayList<>();

    @Override
    public Object getData() {
        return null;
    }

    @Override
    public String getErrorDesccription() {
        return StringUtil.join(this.errorDesc, StringPool.COMMA);
    }

}
