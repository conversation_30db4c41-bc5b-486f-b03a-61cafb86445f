package com.mb.laos.api.request;

import com.mb.laos.messages.LabelKey;
import com.mb.laos.validator.ValidationConstraint;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Pattern;

@Getter
@Setter
@Builder
public class TransferAccountRequest extends Request {
    private static final long serialVersionUID = 5657304430458324957L;

    private String accountNumber;

    private String bankAccount;

    @Pattern(regexp = ValidationConstraint.PATTERN.NUMBER_OF_TRANSACTION_EWALLET, message = LabelKey.ERROR_AMOUNT_IS_INVALID)
    private String amount;

    private String bankTransId;

//    @Pattern(regexp = ValidationConstraint.PATTERN.CONTENT_LAOS_EWALLET, message = LabelKey.ERROR_QR_CONTENT_INVALID)
    private String content;

    private String merchantService;

    private String partnerId;

    private String phoneNumber;
}
