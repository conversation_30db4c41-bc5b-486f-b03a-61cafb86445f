package com.mb.laos.api.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.gson.annotations.SerializedName;
import com.mb.laos.model.dto.AddOnDTO;
import com.mb.laos.model.dto.BillOutStandingDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
@NoArgsConstructor
public class BestTelecomResponse implements Serializable {

    private static final long serialVersionUID = -8115338770958917755L;

    @SerializedName("bill_outstanding")
    private List<BillOutStandingDTO> billOutStandings;

    @SerializedName("add_on_list")
    private List<AddOnDTO> addOns;

    @SerializedName("validity")
    private String validity;

    @SerializedName("mobile_number")
    private String mobileNumber;

    @SerializedName("trans_id")
    private String transId;

    @SerializedName("return_code")
    private String returnCode;

    @SerializedName("receipt_no")
    private String receiptNo;
}

