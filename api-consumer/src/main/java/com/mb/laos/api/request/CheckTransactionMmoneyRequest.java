package com.mb.laos.api.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckTransactionMmoneyRequest extends Request {
    private static final long serialVersionUID = 1920263457086485219L;

    @JsonProperty("TransactionID")
    private String transactionId;

    @JsonProperty("ServiceName")
    private String serviceName;
}
