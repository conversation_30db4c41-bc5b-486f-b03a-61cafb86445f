package com.mb.laos.api.request.lvi;

import com.mb.laos.api.request.SoapRequest;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class LviVehicleInsuranceBuyRequest extends SoapRequest {
    private String accountName;

    private String accountPhone;

    private String vehicleCode;

    private String packageCode;

    private String registrationImage;

    private String deliveryCode;

    private String payCurrency;

    private String payTotal;

    private String payLvi;

    private String certificate;

    private String transactionRef;

    private String transactionDate;


}
