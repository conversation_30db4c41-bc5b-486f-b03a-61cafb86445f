package com.mb.laos.api.request;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class DebitMoneyTransferRequest extends Request {
    private static final long serialVersionUID = 4990000980614746607L;

    private String transferType;

    /**
     * Mặc định MB
     */
    private String channel;

    /**
     * Giá trị mặc định ACCOUNT or QR
     */
    private String serviceType;

    private String serviceIndicator;

    private String t24TransactionType;

    private String remark;

    private String branchCode;

    private AccountMoney debitAccount;

    private AccountMoney creditAccount;

    private Amount amount;

    private CreditBank creditBank;

    private ChargeInfo chargeInfo;

    private List<AddInfoList> addInfoList;
}
