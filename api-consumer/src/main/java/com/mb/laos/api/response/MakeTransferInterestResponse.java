package com.mb.laos.api.response;

import com.mb.laos.model.dto.MakeTransferInterestDTO;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class MakeTransferInterestResponse extends ApiGeeResponse {
    private static final long serialVersionUID = -3863610300020783435L;
    private MakeTransferInterestDTO data;
    private List<String> errorDesc = new ArrayList<>();
    @Override
    public String getErrorDesccription() {
        return StringUtil.join(this.errorDesc, StringPool.COMMA);
    }
}
