package com.mb.laos.api.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
public class QuerySubscriberTypeResponse extends SoapResponse{
    /** The Constant serialVersionUID */
    private static final long serialVersionUID = -6510045180816370867L;

    @XmlElement(name = "resultCode")
    private String resultCode;

    @XmlElement(name = "resultDesc")
    private String resultDesc;

    @XmlElement(name = "transaction")
    private String transaction;

    @XmlElement(name = "subscriberType")
    private String subscriberType;
}
