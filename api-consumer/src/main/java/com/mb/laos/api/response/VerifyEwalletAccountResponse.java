package com.mb.laos.api.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class VerifyEwalletAccountResponse extends UmoneyResponse {

    private static final long serialVersionUID = 7076844435984277402L;
    
    private String accountName;

    @JsonIgnore
    private String accountStatus;

    @JsonIgnore
    private String tier;

//    @JsonIgnore
    private Double minAmount;

//    @JsonIgnore
    private Double maxAmount;

}
