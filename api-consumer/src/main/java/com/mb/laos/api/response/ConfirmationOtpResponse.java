package com.mb.laos.api.response;

import com.mb.laos.model.dto.CustomerInformationDTO;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
public class ConfirmationOtpResponse extends ApiGeeResponse {

    private static final long serialVersionUID = -7486605044204015193L;
    private List<String> errorDesc = new ArrayList<>();

    private CustomerInformationDTO data;

    @Override
    public String getErrorDesccription() {
        return StringUtil.join(this.errorDesc, StringPool.COMMA);
    }
}
