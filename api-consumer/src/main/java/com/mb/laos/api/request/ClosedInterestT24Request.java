package com.mb.laos.api.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClosedInterestT24Request extends Request {
    private static final long serialVersionUID = 2168718144738102723L;

    private String chanel;
    private String savingAccountNumber;
    private String receivePrincipalAccount;
    private Integer category;
    private Integer subProduct;
    private String cifT24;
    private AuthenMethod authenMethod;
}
