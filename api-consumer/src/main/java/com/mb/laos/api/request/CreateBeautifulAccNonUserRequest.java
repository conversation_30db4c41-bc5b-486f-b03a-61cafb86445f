package com.mb.laos.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CreateBeautifulAccNonUserRequest extends Request {
    private static final long serialVersionUID = -69507723020452961L;

    private String mobile;
    private String fullName;
    private String dateOfBirth;
    private String birthPlace;
    private String idNumber;
    private String dateIssue;
    private String placeIssue;
    private String idType;
    private String gender;
    private String residentialAddress;
    private String currentAddress;
    private String nationality;
    private String email;
    private String smsLanguage;
    private String contactPhone1;
    private String contactPhone2;
    private String maritalStatus;
    private String career;
    private String duty;
    private String staffCode;
    private String shopCode;
    private String branchCode;
    private String beautifulAccountNo;
    private String subProduct;
    private String feeCode;
    private String feeAmount;
    private String currency;
    private String channel;
    private String sector;
    private String otpValue;
}
