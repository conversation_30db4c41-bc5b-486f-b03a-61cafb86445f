package com.mb.laos.service.impl;

import com.mb.laos.api.consumer.ApiBestTelecomSender;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.HttpResponseException;
import com.mb.laos.api.request.MobilePaymentRequest;
import com.mb.laos.api.request.ValidateMobileNumberRequest;
import com.mb.laos.api.response.BestTelecomResponse;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.ConsumerProperties;
import com.mb.laos.enums.BestTelecomErrorCode;
import com.mb.laos.service.ApiBestTelecomService;
import com.mb.laos.util.ReflectionUtil;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;

@Slf4j
@Service
@RequiredArgsConstructor
public class ApiBestTelecomImpl implements ApiBestTelecomService {
    private final ApiBestTelecomSender apiBestTelecomSender;

    private final ConsumerProperties consumerProperties;

    @Override
    public BestTelecomResponse validateMobileNumber(ValidateMobileNumberRequest request) {
        try {
            ValidateMobileNumberRequest req = ReflectionUtil.clone(request);

            StringBuilder url = new StringBuilder();

            url.append(consumerProperties.getApiBestTelecom().getUrl().getValidate()).append(StringPool.QUESTION)
                    .append("mobile_number").append(StringPool.EQUAL).append(this.formatPhoneNumber(request.getPhoneNumber()))
                    .append("&display_addons").append(StringPool.EQUAL).append(request.getDisplayAddon());

            req.setUrl(url.toString());
            req.setMethod(HttpMethod.GET);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            return apiBestTelecomSender.sendToApiBestTelecom(req, BestTelecomResponse.class, params);
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (Exception ex) {
            _log.error("Best Telecom validate Mobile Number has error : {}", ex);

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public BestTelecomResponse mobilePayment(MobilePaymentRequest request) {
        try {
            MobilePaymentRequest req = ReflectionUtil.clone(request);

            StringBuilder url = new StringBuilder();

            url.append(consumerProperties.getApiBestTelecom().getUrl().getPayment()).append(StringPool.QUESTION)
                    .append("mobile_number").append(StringPool.EQUAL).append(this.formatPhoneNumber(request.getPhoneNumber()))
                    .append("&trans_id").append(StringPool.EQUAL).append(request.getTransId())
                    .append("&amount").append(StringPool.EQUAL).append(100000)
                    .append("&service_type").append(StringPool.EQUAL).append(request.getServiceType())
                    .append("&add_on_code");

            req.setUrl(url.toString());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            BestTelecomResponse bestTelecomResponse = apiBestTelecomSender.sendToApiBestTelecom(req, BestTelecomResponse.class, params);

            BestTelecomErrorCode errorCode = BestTelecomErrorCode.valueOfCode(bestTelecomResponse.getReturnCode());

            if (Validator.isNotNull(bestTelecomResponse.getReturnCode()) && Validator.isNotNull(errorCode)) {
                throw new BadRequestAlertException(errorCode.getErrorCode());
            }

            return bestTelecomResponse;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            BestTelecomErrorCode errorCode = BestTelecomErrorCode.valueOfCode(ex.getErrorCode());

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("Best Telecom mobile payment has error : {}", ex);

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    private String formatPhoneNumber(String phoneNumber) {

        // 0 là đại diện cho đầu số điện thoại theo quy định bên LAOS
        // đối với thanh toán cố định không cần bỏ số 0
        if (phoneNumber.charAt(0) == '0') {
            phoneNumber = phoneNumber.substring(1);
        }

        // 856 là đại diện cho đầu số điện thoại theo quy định bên LAOS
        if (phoneNumber.startsWith("856")) {
            phoneNumber = phoneNumber.substring(3);
        }

        return phoneNumber;
    }

}

