package com.mb.laos.service.impl;

import com.mb.laos.api.consumer.ApiUmoneySender;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.HttpResponseException;
import com.mb.laos.api.request.LapnetReportRequest;
import com.mb.laos.api.request.TransferAccountRequest;
import com.mb.laos.api.request.VerifyUmoneyAccountRequest;
import com.mb.laos.api.response.LapnetSettlementReportResponse;
import com.mb.laos.api.response.TransferAccountResponse;
import com.mb.laos.api.response.VerifyEwalletAccountResponse;
import com.mb.laos.api.response.VerifyUmoneyAccountResponse;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.ConsumerProperties;
import com.mb.laos.configuration.ConsumerProperties.ApiUmoney;
import com.mb.laos.enums.EwalletErrorCode;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.enums.UmoneyErrorCode;
import com.mb.laos.service.ApiUmoneyService;
import com.mb.laos.util.ReflectionUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;

import javax.annotation.PostConstruct;

/**
 * Service Implementation tích hợp với Umoney E-Wallet
 *
 * Umoney là ví điện tử phổ biến tại Lào, service này cung cấp:
 *
 * 💰 CHỨC NĂNG CHÍNH:
 * - Xác thực tài khoản Umoney (verify account)
 * - Nạp tiền từ MB Bank vào ví Umoney
 * - Chuyển tiền giữa các ví Umoney
 * - Báo cáo settlement hàng ngày
 *
 * 🔗 TÍCH HỢP:
 * - API Umoney: REST API với OAuth2 authentication
 * - Token management: Tự động refresh token khi hết hạn
 * - Error handling: Xử lý các mã lỗi từ Umoney
 * - Retry mechanism: Tự động retry khi có lỗi network
 *
 * 📊 BUSINESS FLOW:
 * 1. Verify account: Kiểm tra tài khoản Umoney có tồn tại
 * 2. Transfer money: Nạp tiền từ MB Bank → Umoney
 * 3. Settlement: Đối soát giao dịch cuối ngày
 *
 * 🔒 BẢO MẬT:
 * - OAuth2 client credentials
 * - Request signing với client secret
 * - HTTPS encryption
 * - Transaction ID để tránh duplicate
 *
 * <AUTHOR> Development Team
 * @version 2.0
 * @since 2023
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiUmoneyServiceImpl implements ApiUmoneyService {

    /** Sender để gửi HTTP request đến Umoney API */
    private final ApiUmoneySender apiUmoneySender;

    /** Properties chứa cấu hình consumer */
    private final ConsumerProperties consumerProperties;

    /** Cấu hình Umoney được extract từ properties */
    private ApiUmoney apiUmoney;

    /**
     * Khởi tạo cấu hình Umoney sau khi inject dependencies
     */
    @PostConstruct
    protected void init() {
        this.apiUmoney = consumerProperties.getApiUmoney();
    }

    /**
     * Xác thực tài khoản Umoney
     *
     * API này kiểm tra tài khoản Umoney có tồn tại và hoạt động không:
     * - Gọi API checkEwalletAccount của Umoney
     * - Validate số điện thoại/account ID
     * - Trả về thông tin tài khoản (tên, trạng thái...)
     *
     * Error handling:
     * - ACCOUNT_NOT_FOUND: Tài khoản không tồn tại
     * - Network error: Retry với exponential backoff
     * - Invalid format: Báo lỗi format số điện thoại
     *
     * @param request Chứa thông tin tài khoản cần verify
     * @return VerifyUmoneyAccountResponse Thông tin tài khoản Umoney
     * @throws BadRequestAlertException Khi tài khoản không tồn tại hoặc có lỗi
     */
    @Override
    public VerifyUmoneyAccountResponse verifyAccount(VerifyUmoneyAccountRequest request) {
        try {
            VerifyUmoneyAccountRequest req = ReflectionUtil.clone(request);

            req.setUrl(apiUmoney.getBaseUrl() + apiUmoney.getUri().getCheckEwalletAccount());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            VerifyUmoneyAccountResponse reponse =
                    apiUmoneySender.sendToUmoneyApi(req, VerifyUmoneyAccountResponse.class, params, Boolean.FALSE);

            UmoneyErrorCode error = UmoneyErrorCode.valueOfCode(reponse.getErrorCode());

            if (Validator.equals(error, UmoneyErrorCode.ACCOUNT_NOT_FOUND)) {
                throw new BadRequestAlertException(error.getErrorCode());
            }

            return reponse;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            UmoneyErrorCode errorCode = UmoneyErrorCode.valueOfCode(ex.getErrorCode());

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("umoney verifyAccount has error : {}", ex);

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public TransferAccountResponse transferUmoney(TransferAccountRequest request) {
        try {
            TransferAccountRequest req = ReflectionUtil.clone(request);

            req.setUrl(apiUmoney.getBaseUrl() + apiUmoney.getUri().getTransferMoney());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            TransferAccountResponse reponse =
                    apiUmoneySender.sendToUmoneyApi(req, TransferAccountResponse.class, params, Boolean.FALSE);

            UmoneyErrorCode error = UmoneyErrorCode.valueOfCode(reponse.getErrorCode());

            if (Validator.equals(error, UmoneyErrorCode.ACCOUNT_NOT_FOUND)) {
                throw new BadRequestAlertException(error.getErrorCode());
            }

            return reponse;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            UmoneyErrorCode errorCode = UmoneyErrorCode.valueOfCode(ex.getErrorCode());

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("Transfer money to umoney account has error : {}", ex);

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    /**
     * Nạp tiền vào tài khoản Umoney với error handling nâng cao
     *
     * Đây là method chính để nạp tiền từ MB Bank vào ví Umoney:
     * - Gọi API transferMoney của Umoney
     * - Xử lý tất cả các trường hợp lỗi có thể xảy ra
     * - Trả về trạng thái giao dịch rõ ràng (SUCCESS/FAILED/TIMEOUT)
     * - Không throw exception để caller có thể xử lý
     *
     * Transaction status:
     * - SUCCESS: Nạp tiền thành công
     * - FAILED: Nạp tiền thất bại (lỗi business logic)
     * - TIMEOUT: Timeout khi gọi API (cần check manual)
     *
     * Error cases:
     * - ACCOUNT_NOT_FOUND: Tài khoản Umoney không tồn tại
     * - TRANSACTION_ID_DUPLICATED: Transaction ID đã được sử dụng
     * - INSUFFICIENT_BALANCE: Số dư MB Bank không đủ
     * - NETWORK_TIMEOUT: Timeout khi gọi API
     *
     * @param request Thông tin nạp tiền (account, amount, transaction ID)
     * @return TransferAccountResponse Kết quả nạp tiền với status rõ ràng
     */
    @Override
    public TransferAccountResponse transferAccountUmoney(TransferAccountRequest request) {
        TransferAccountResponse data = new TransferAccountResponse();
        try {
            TransferAccountRequest req = ReflectionUtil.clone(request);

            req.setUrl(apiUmoney.getBaseUrl() + apiUmoney.getUri().getTransferMoney());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            TransferAccountResponse reponse =
                    apiUmoneySender.sendToUmoneyApi(req, TransferAccountResponse.class, params, Boolean.FALSE);

            data = reponse;

            UmoneyErrorCode error = UmoneyErrorCode.valueOfCode(reponse.getErrorCode());

            if (Validator.equals(error, UmoneyErrorCode.ACCOUNT_NOT_FOUND)
                    || Validator.equals(error, UmoneyErrorCode.TRANSACTION_ID_DUPLICATED)
                    || EwalletErrorCode.isPaymentFail(data.getErrorCode())) {
                data.setTransactionStatus(TransactionStatus.FAILED);
                return data;
            }

            data.setTransactionStatus(TransactionStatus.SUCCESS);

            return data;
        } catch (ResourceAccessException e) {
            _log.error("Credit account Umoney has timeout : {}", e);
            // timeout
            data.setTransactionStatus(TransactionStatus.TIMEOUT);
            return data;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            data.setTransactionStatus(TransactionStatus.FAILED);

            _log.info("Transaction transfer Umoney {} has been failed cause {}", request.getBankTransId(), ex);
            return data;
        } catch (HttpResponseException ex) {
            _log.info("Umoney transfer has been failed cause {}", ex);

            data.setTransactionStatus(TransactionStatus.FAILED);
            data.setErrorCode(ex.getErrorCode());
            data.setErrorDetail(ex.getErrorMessage());

            return data;
        } catch (Exception ex) {
            _log.error("Transfer money to umoney account has error : {}", ex);

            data.setTransactionStatus(TransactionStatus.FAILED);
            return data;
        }
    }

    @Override
    public VerifyEwalletAccountResponse verifyEwalletAccount(VerifyUmoneyAccountRequest request) {
        try {
            VerifyUmoneyAccountRequest req = ReflectionUtil.clone(request);

            req.setUrl(apiUmoney.getBaseUrl() + apiUmoney.getUri().getCheckEwalletAccount());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            VerifyEwalletAccountResponse reponse =
                    apiUmoneySender.sendToUmoneyApi(req, VerifyEwalletAccountResponse.class, params, Boolean.FALSE);

            UmoneyErrorCode error = UmoneyErrorCode.valueOfCode(reponse.getErrorCode());

            if (Validator.equals(error, UmoneyErrorCode.ACCOUNT_NOT_FOUND)) {
                throw new BadRequestAlertException(ErrorCode.MSG100013);
            }

            return reponse;
        } catch (ResourceAccessException e) {
            _log.error("Credit verify Ewallet account has timeout : {}", e);
            // timeout
            throw new BadRequestAlertException(ErrorCode.MSG101162);
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            _log.error("Credit verify Ewallet account has error HttpClientErrorException and BadRequestAlertException : {}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG100013);
        } catch (HttpResponseException ex) {
            _log.error("umoney verifyAccount has error HttpResponseException with log : {}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG100013);
        } catch (Exception ex) {
            _log.error("umoney verifyAccount has error : {}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG100013);
        }
    }

    @Override
    public LapnetSettlementReportResponse settlementReport(LapnetReportRequest request) {
        try {
            LapnetReportRequest req = ReflectionUtil.clone(request);

            req.setUrl(apiUmoney.getBaseUrlSettlementReport() + apiUmoney.getUri().getSettlementReport());
            req.setMethod(HttpMethod.GET);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("date", String.valueOf(request.getDate()));

            LapnetSettlementReportResponse reponse =
                    apiUmoneySender.sendToUmoneyApi(req, LapnetSettlementReportResponse.class, params, Boolean.TRUE);

            return reponse;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            UmoneyErrorCode errorCode = UmoneyErrorCode.valueOfCode(ex.getErrorCode());

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("settlement report umoney has error : {}", ex);

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }
}

