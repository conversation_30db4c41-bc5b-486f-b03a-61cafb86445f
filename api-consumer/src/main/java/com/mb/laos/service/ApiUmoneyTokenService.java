package com.mb.laos.service;

import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import com.mb.laos.cache.util.ConsumerCacheConstants;
import com.mb.laos.model.ApiUmoneyToken;

public interface ApiUmoneyTokenService {
    @Cacheable(value = ConsumerCacheConstants.ApiUmoney.TOKEN, key = "T(com.mb.laos.cache.util.CacheConstants).KEY",
                    unless = "#result == null")
    public ApiUmoneyToken getApiUmoneyToken();
    
    @CachePut(value = ConsumerCacheConstants.ApiUmoney.TOKEN,
                    key = "T(com.mb.laos.cache.util.CacheConstants).KEY", unless = "#result == null")
    public ApiUmoneyToken refreshApiUmoneyToken();
}

