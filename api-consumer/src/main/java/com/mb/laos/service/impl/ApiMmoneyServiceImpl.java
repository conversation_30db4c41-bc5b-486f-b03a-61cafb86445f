package com.mb.laos.service.impl;

import com.mb.laos.api.consumer.ApiMmoneySender;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.HttpResponseException;
import com.mb.laos.api.request.CheckTransactionMmoneyRequest;
import com.mb.laos.api.request.ListElectricMmoneyRequest;
import com.mb.laos.api.request.ListLeasingMmoneyRequest;
import com.mb.laos.api.request.ListWaterMmoneyRequest;
import com.mb.laos.api.request.PaymentElectricRequest;
import com.mb.laos.api.request.PaymentInternetLtcRequest;
import com.mb.laos.api.request.PaymentLeasingMmoneyRequest;
import com.mb.laos.api.request.PaymentPostpaidLtcRequest;
import com.mb.laos.api.request.PaymentPstnLtcRequest;
import com.mb.laos.api.request.PaymentWaterMmoneyRequest;
import com.mb.laos.api.request.VerifyElectricMmoneyRequest;
import com.mb.laos.api.request.VerifyInternetLtcMmoneyRequest;
import com.mb.laos.api.request.VerifyLeasingMmoneyRequest;
import com.mb.laos.api.request.VerifyPostPaidLtcMmoneyRequest;
import com.mb.laos.api.request.VerifyPstnMmoneyPstnRequest;
import com.mb.laos.api.request.VerifyWaterMmoneyRequest;
import com.mb.laos.api.response.CheckTransactionMmoneyResponse;
import com.mb.laos.api.response.ListElectricMmoneyResponse;
import com.mb.laos.api.response.ListLeasingMmoneyResponse;
import com.mb.laos.api.response.ListWaterMmoneyResponse;
import com.mb.laos.api.response.PaymentElectricMmoneyResponse;
import com.mb.laos.api.response.PaymentInternetLtcResponse;
import com.mb.laos.api.response.PaymentLeasingMmoneyResponse;
import com.mb.laos.api.response.PaymentPostPaidLtcResponse;
import com.mb.laos.api.response.PaymentPstnLtcResponse;
import com.mb.laos.api.response.PaymentWaterMmoneyResponse;
import com.mb.laos.api.response.VerifyElectricMmoneyResponse;
import com.mb.laos.api.response.VerifyInternetLtcResponse;
import com.mb.laos.api.response.VerifyLeasingMmoneyResponse;
import com.mb.laos.api.response.VerifyPostPaidLtcResponse;
import com.mb.laos.api.response.VerifyPstnLtcResponse;
import com.mb.laos.api.response.VerifyWaterMmoneyResponse;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.ConsumerProperties;
import com.mb.laos.configuration.ConsumerProperties.ApiMmoney;
import com.mb.laos.enums.MmoneyErrorCode;
import com.mb.laos.service.ApiMmoneyService;
import com.mb.laos.util.ReflectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;

import javax.annotation.PostConstruct;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class ApiMmoneyServiceImpl implements ApiMmoneyService {

    private final ConsumerProperties consumerProperties;

    private final ApiMmoneySender apiMmoneySender;

    private ApiMmoney apiMmoney;

    @PostConstruct
    protected void init() {
        this.apiMmoney = this.consumerProperties.getApiMmoney();
    }

    @Override
    public List<ListElectricMmoneyResponse> listElectric(ListElectricMmoneyRequest request) {
        try {
            ListElectricMmoneyRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiMmoney.getBaseUrlElectric() + this.apiMmoney.getUri().getListElectric());
            req.setMethod(HttpMethod.GET);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            return this.apiMmoneySender.sendToListMmoneyApi(req, ListElectricMmoneyResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("List electrict has error: {0}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public VerifyElectricMmoneyResponse verifyElectric(VerifyElectricMmoneyRequest request) {
        try {
            VerifyElectricMmoneyRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiMmoney.getBaseUrlElectric() + this.apiMmoney.getUri().getVerifyElectric());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            return this.apiMmoneySender.sendToMmoneyApi(req, VerifyElectricMmoneyResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("Verify electric has error: {}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public List<ListLeasingMmoneyResponse> listLeasing(ListLeasingMmoneyRequest request) {
        try {
            ListLeasingMmoneyRequest req = ReflectionUtil.clone(request);

//            req.setUrl(this.apiMmoney.getBaseUrl() + this.apiMmoney.getUri().getListLeasing());
            req.setMethod(HttpMethod.GET);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            return this.apiMmoneySender.sendToListMmoneyApi(req, ListLeasingMmoneyResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("List leasing has error: {0}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public PaymentWaterMmoneyResponse paymentWater(PaymentWaterMmoneyRequest request) {
        try {
            PaymentWaterMmoneyRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiMmoney.getBaseUrlWater() + this.apiMmoney.getUri().getPaymentWater());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            return this.apiMmoneySender.sendToMmoneyApi(req, PaymentWaterMmoneyResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("payment water has error: {0}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public PaymentElectricMmoneyResponse paymentElectric(PaymentElectricRequest request) {
        try {
            PaymentElectricRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiMmoney.getBaseUrlElectric() + this.apiMmoney.getUri().getPaymentElectric());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            return this.apiMmoneySender.sendToMmoneyApi(req, PaymentElectricMmoneyResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("payment electric has error: {0}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public VerifyPostPaidLtcResponse verifyPostPaid(VerifyPostPaidLtcMmoneyRequest request) {
        try {
            VerifyPostPaidLtcMmoneyRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiMmoney.getBaseUrlMmoney() + this.apiMmoney.getUri().getVerifyPostpaid());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            return this.apiMmoneySender.sendToMmoneyApi(req, VerifyPostPaidLtcResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("verify postpaid has error: {0}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public VerifyInternetLtcResponse verifyInternet(VerifyInternetLtcMmoneyRequest request) {
        try {
            VerifyInternetLtcMmoneyRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiMmoney.getBaseUrlMmoney() + this.apiMmoney.getUri().getVerifyInternet());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            return this.apiMmoneySender.sendToMmoneyApi(req, VerifyInternetLtcResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("verify internet has error: {0}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public VerifyPstnLtcResponse verifyPstn(VerifyPstnMmoneyPstnRequest request) {
        try {
            VerifyPstnMmoneyPstnRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiMmoney.getBaseUrlMmoney() + this.apiMmoney.getUri().getVerifyPstn());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            return this.apiMmoneySender.sendToMmoneyApi(req, VerifyPstnLtcResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("verify pstn has error: {0}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public PaymentPostPaidLtcResponse paymentPostPaid(PaymentPostpaidLtcRequest request) {
        try {
            PaymentPostpaidLtcRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiMmoney.getBaseUrlMmoney() + this.apiMmoney.getUri().getPaymentPostpaid());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            return this.apiMmoneySender.sendToMmoneyApi(req, PaymentPostPaidLtcResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("payment postpaid has error: {0}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public PaymentInternetLtcResponse paymentInternet(PaymentInternetLtcRequest request) {
        try {
            PaymentInternetLtcRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiMmoney.getBaseUrlMmoney() + this.apiMmoney.getUri().getPaymentInternet());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            return this.apiMmoneySender.sendToMmoneyApi(req, PaymentInternetLtcResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("payment internet has error: {0}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public PaymentPstnLtcResponse paymentPstn(PaymentPstnLtcRequest request) {
        try {
            PaymentPstnLtcRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiMmoney.getBaseUrlMmoney() + this.apiMmoney.getUri().getPaymentPstn());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            return this.apiMmoneySender.sendToMmoneyApi(req, PaymentPstnLtcResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("payment pstn has error: {0}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public PaymentLeasingMmoneyResponse paymentLeasing(PaymentLeasingMmoneyRequest request) {
        try {
            PaymentLeasingMmoneyRequest req = ReflectionUtil.clone(request);

//            req.setUrl(this.apiMmoney.getBaseUrl() + this.apiMmoney.getUri().getPaymentLeasing());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            return this.apiMmoneySender.sendToMmoneyApi(req, PaymentLeasingMmoneyResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("payment leasing has error: {0}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public VerifyLeasingMmoneyResponse verifyLeasing(VerifyLeasingMmoneyRequest request) {
        try {
            VerifyLeasingMmoneyRequest req = ReflectionUtil.clone(request);

//            req.setUrl(this.apiMmoney.getBaseUrl() + this.apiMmoney.getUri().getVerifyLeasing());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            return this.apiMmoneySender.sendToMmoneyApi(req, VerifyLeasingMmoneyResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("Verify leasing has error: {}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public List<ListWaterMmoneyResponse> listWater(ListWaterMmoneyRequest request) {
        try {
            ListWaterMmoneyRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiMmoney.getBaseUrlWater() + this.apiMmoney.getUri().getListWater());
            req.setMethod(HttpMethod.GET);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            return this.apiMmoneySender.sendToListMmoneyApi(req, ListWaterMmoneyResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("List electric has error: {0}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public VerifyWaterMmoneyResponse verifyWater(VerifyWaterMmoneyRequest request) {
        try {
            VerifyWaterMmoneyRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiMmoney.getBaseUrlWater() + this.apiMmoney.getUri().getVerifyWater());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            return this.apiMmoneySender.sendToMmoneyApi(req, VerifyWaterMmoneyResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("Verify water has error: {}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public CheckTransactionMmoneyResponse checkTransaction(CheckTransactionMmoneyRequest request) {
        try {
            CheckTransactionMmoneyRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiMmoney.getBaseUrlMmoney() + this.apiMmoney.getUri().getCheckTransaction());
            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            return this.apiMmoneySender.sendToMmoneyApi(req, CheckTransactionMmoneyResponse.class, params);

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            MmoneyErrorCode errorCode = MmoneyErrorCode.valueOfCode(ex.getErrorCode());
            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("check transaction has error: {}", ex);
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }
}

