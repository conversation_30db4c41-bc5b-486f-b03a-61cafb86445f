package com.mb.laos.service.impl;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.mb.laos.api.consumer.ApiGeeSender;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.HttpResponseException;
import com.mb.laos.api.request.*;
import com.mb.laos.api.response.AccNumberVerifyResponse;
import com.mb.laos.api.response.AccountBalanceResponse;
import com.mb.laos.api.response.AccountCurrencyResponse;
import com.mb.laos.api.response.AccountLoanResponse;
import com.mb.laos.api.response.AccountSavingResponse;
import com.mb.laos.api.response.InquiryTransactionStatusResponse;
import com.mb.laos.api.response.RequestFundOtpResponse;
import com.mb.laos.api.response.RevertTransactionResponse;
import com.mb.laos.api.response.TransactionHistoryResponse;
import com.mb.laos.api.response.VerifyFundTransferResponse;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.ConsumerProperties;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.T24ErrorCode;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.PremiumAccNumber;
import com.mb.laos.model.TransactionLock;
import com.mb.laos.model.dto.AccNumberVerifyDTO;
import com.mb.laos.model.dto.AccountBalanceDTO;
import com.mb.laos.model.dto.AccountCurrencyDTO;
import com.mb.laos.model.dto.AccountLoanDTO;
import com.mb.laos.model.dto.AccountSavingDTO;
import com.mb.laos.model.dto.InquiryTransactionStatusDTO;
import com.mb.laos.model.dto.ReqFundTransferDTO;
import com.mb.laos.model.dto.ReqSavingAccountDTO;
import com.mb.laos.model.dto.RevertDTO;
import com.mb.laos.model.dto.TransactionHistoryDTO;
import com.mb.laos.model.dto.VerifyFundTransferDTO;
import com.mb.laos.repository.PremiumAccNumberRepository;
import com.mb.laos.repository.TransactionLockRepository;
import com.mb.laos.service.ApiGeeTransferService;
import com.mb.laos.util.ReflectionUtil;
import com.mb.laos.util.UUIDUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class ApiGeeTransferServiceImpl implements ApiGeeTransferService {

    private final ApiGeeSender apiGeeSender;

    private final ConsumerProperties consumerProperties;

    private final TransactionLockRepository transactionLockRepository;

    private final PremiumAccNumberRepository premiumAccNumberRepository;

    private ConsumerProperties.ApiGee apiGee;

    @PostConstruct
    protected void init() {
        this.apiGee = this.consumerProperties.getApiGee();
    }

    /**
     * Verify thông tin tài khoản người nhận qua T24/LAPNET
     *
     * Method này là gateway chính để verify account information:
     * 1. Clone request để tránh modify object gốc
     * 2. Generate unique transaction ID cho tracking
     * 3. Routing: Internal MB vs External LAPNET dựa vào ToMember
     * 4. Gọi T24/LAPNET API qua ApiGee gateway
     * 5. Handle response và error cases
     * 6. Return account info đã verify
     *
     * Routing logic:
     * - ToMember = null → Internal MB Bank (verifyAccNumberMB)
     * - ToMember != null → External LAPNET (verifyAccNumberLapnet)
     *
     * @param request AccNumberVerifyRequest chứa thông tin cần verify
     * @return AccNumberVerifyDTO Thông tin tài khoản đã verify từ T24/LAPNET
     * @throws BadRequestAlertException Khi account không tồn tại hoặc có lỗi T24
     */
    @Override
    public AccNumberVerifyDTO verifyAccNumber(AccNumberVerifyRequest request) {
        try {
            // Clone request để tránh modify object gốc
            // Đảm bảo thread-safe và không ảnh hưởng đến caller
            AccNumberVerifyRequest req = ReflectionUtil.clone(request);

            // Generate unique transaction ID cho tracking và audit
            // 20 ký tự UUID để đảm bảo uniqueness across system
            req.setApiGeeTransactionId(UUIDUtil.generateUUID(20));

            // Routing logic: Internal vs External dựa vào ToMember
            if (Validator.isNull(request.getToMember())) {

                // INTERNAL MB BANK - ToMember = null
                // Verify account trong cùng MB Bank qua T24 trực tiếp
                // URL: /api/v1/verify-acc-number-mb
                req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getVerifyAccNumberMB());

            } else {

                // EXTERNAL LAPNET - ToMember != null (BCEL, LDB, APB...)
                // Verify account ở ngân hàng khác qua LAPNET network
                // URL: /api/v1/verify-acc-number-lapnet
                req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getVerifyAccNumberLapnet());
            }

            // Set HTTP method là POST cho tất cả verify requests
            req.setMethod(HttpMethod.POST);

            // Tạo empty params map cho ApiGee sender
            // Params sẽ được extract từ request object
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            // Gọi T24/LAPNET API qua ApiGee gateway
            // ApiGeeSender sẽ handle authentication, retry, timeout...
            AccNumberVerifyResponse response = this.apiGeeSender
                    .sendToApiGee(req, AccNumberVerifyResponse.class, params);

            // Debug logging để troubleshoot khi cần
            if (_log.isDebugEnabled()) {
                _log.debug("response: {}", response);
            }

            // Fallback: Set reference number nếu T24/LAPNET không trả về
            // Đảm bảo luôn có reference number để tracking
            if (response.getData().getReferenceNumber() == null) {
                response.getData().setReferenceNumber(req.getApiGeeTransactionId());
            }

            // Return data portion của response (không cần wrapper)
            return response.getData();

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            // Re-throw business exceptions và HTTP client errors
            // Đây là expected errors từ T24/LAPNET
            throw ex;

        } catch (HttpResponseException ex) {
            // Handle T24 specific error codes
            // Convert T24 error codes sang MB error codes
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            // Throw với error code đã được map
            throw new BadRequestAlertException(errorCode.getErrorCode());

        } catch (Exception ex) {
            // Handle unexpected errors (network, parsing, etc.)
            _log.error("getCcqSeBalances has error : {}", ex);

            // Throw generic error với user-friendly message
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_AN_UNEXPECTED_ERROR_HAS_OCCURRED),
                    ErrorCode.MSG1023.name(), LabelKey.ERROR_AN_UNEXPECTED_ERROR_HAS_OCCURRED);
        }

    }

    @Override
    public ReqFundTransferDTO requestFundTransfer(OtpTransferRequest request) {
        try {
            OtpTransferRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getRequestTransfer());
            req.setMethod(HttpMethod.POST);
            req.setApiGeeTransactionId(request.getApiGeeTransactionId());

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            RequestFundOtpResponse response = this.apiGeeSender
                    .sendToApiGee(req, RequestFundOtpResponse.class, params);

            return response.getData();

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("getCcqSeBalances has error : {}", ex);

            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_AN_UNEXPECTED_ERROR_HAS_OCCURRED),
                    ErrorCode.MSG1023.name(), LabelKey.ERROR_AN_UNEXPECTED_ERROR_HAS_OCCURRED);
        }
    }

    @Override
    public VerifyFundTransferDTO verifyFundTransfer(OtpVerifyTransferRequest request) {
        VerifyFundTransferDTO data = new VerifyFundTransferDTO();

        TransactionLock transactionLock = new TransactionLock(request.getRequestId());

        try {
            // insert to transaction lock
            this.transactionLockRepository.saveAndFlush(transactionLock);

            OtpVerifyTransferRequest req = ReflectionUtil.clone(request);
            req.setApiGeeTransactionId(req.getRequestId());

            req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getVerifyTransfer());

            req.setMethod(HttpMethod.POST);

            data.setTransactionId(req.getApiGeeTransactionId());

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            VerifyFundTransferResponse response = this.apiGeeSender
                    .sendToApiGee(req, VerifyFundTransferResponse.class, params);

            data = response.getData();

            data.setTransactionStatus(TransactionStatus.SUCCESS);

            return data;
        } catch (ResourceAccessException e) {
            _log.error("verifyFundTransfer has timeout : {}", e);
            // timeout
            data.setTransactionStatus(TransactionStatus.TIMEOUT);

            return data;

        } catch (BadRequestAlertException | HttpClientErrorException e) {
            _log.info("Transaction with id {} has been failed cause {}", transactionLock.getTransactionId(),
                    e);

            data.setTransactionStatus(TransactionStatus.FAILED);

            this.transactionLockRepository.deleteInBatch(Collections.singleton(transactionLock));

            return data;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            if (Validator.equals(errorCode, T24ErrorCode.OTP_VALUE_IS_INVALID)
                    || Validator.equals(errorCode, T24ErrorCode.OTP_IS_INVALID)
                    || Validator.equals(errorCode, T24ErrorCode.AUTHENTICATION_UNSUCCESSFUL)) {
                _log.info("Transaction with id {} has been failed cause OTP invalid",
                        transactionLock.getTransactionId());

                this.transactionLockRepository.deleteInBatch(Collections.singleton(transactionLock));
                return null;
            }

            data.setTransactionStatus(TransactionStatus.FAILED);
            data.setT24ErrorCode(errorCode);

            return data;

        } catch (HttpServerErrorException ex) {
            String responseBody = ex.getResponseBodyAsString();
            try {
                Gson gson = new Gson();
                VerifyFundTransferResponse errorResponse = gson.fromJson(responseBody, VerifyFundTransferResponse.class);
                T24ErrorCode errorCode = T24ErrorCode.valueOfCode(errorResponse.getErrorCode());
                data.setTransactionStatus(TransactionStatus.FAILED);

                if (Validator.equals(errorResponse.getErrorCode(), T24ErrorCode.INVALID_INFORMATION.getCode())
                        && Validator.isNotNull(errorResponse.getErrorDesc())) {
                    if (Validator.equals(errorResponse.getErrorDesc().get(0), "NO,Tai khoan la TKSD chua thu phi! De nghi kiem tra lai!")) {
                        PremiumAccNumber premiumAccNumber = this.premiumAccNumberRepository
                                .findByPremiumAccNumberAndStatus(request.getDebitAccount().getAccountNumber(), EntityStatus.PENDING.getStatus());

                        if (Validator.isNotNull(premiumAccNumber)) {
                            data.setT24ErrorCode(T24ErrorCode.ERROR_PREMIUM_NUMBER_ACCOUNT_NOT_YET_CHARGED_V2);
                        } else {
                            data.setT24ErrorCode(T24ErrorCode.ERROR_PREMIUM_NUMBER_ACCOUNT_NOT_YET_CHARGED);
                        }
                    } else {
                        data.setT24ErrorCode(errorCode);
                    }
                } else {
                    throw new BadRequestAlertException(ErrorCode.MSG1023);
                }
            } catch (JsonSyntaxException e) {
                System.out.println("Fail parse response: " + e.getMessage());
                throw new BadRequestAlertException(ErrorCode.MSG1023);
            }

            return data;
        } catch (Exception ex) {
            _log.error("verifyFundTransfer has error : {}", ex);

            if (ex.getMessage().contains(ConstraintViolationException.class.getName())) {
                _log.error("Transaction with id {} and otp {} have been processed", request.getRequestId(),
                        request.getOtpValue());

                throw new BadRequestAlertException(ErrorCode.MSG1165);
            }

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public VerifyFundTransferDTO verifyFundTransferOtherCurrency(OtpVerifyTransferRequest request) {
        VerifyFundTransferDTO data = new VerifyFundTransferDTO();

        TransactionLock transactionLock = new TransactionLock(request.getRequestId());

        try {
            // insert to transaction lock
            this.transactionLockRepository.saveAndFlush(transactionLock);

            OtpVerifyTransferRequest req = ReflectionUtil.clone(request);
            req.setApiGeeTransactionId(req.getRequestId());

            req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getVerifyTransferOtherCurrency());

            req.setMethod(HttpMethod.POST);

            data.setTransactionId(req.getApiGeeTransactionId());

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            VerifyFundTransferResponse response = this.apiGeeSender
                    .sendToApiGee(req, VerifyFundTransferResponse.class, params);

            data = response.getData();

            data.setTransactionStatus(TransactionStatus.SUCCESS);

            return data;
        } catch (ResourceAccessException e) {
            _log.error("verifyFundTransfer Other Currency has timeout : {}", e);
            // timeout
            data.setTransactionStatus(TransactionStatus.TIMEOUT);

            return data;

        } catch (BadRequestAlertException | HttpClientErrorException e) {
            _log.info("Transaction Other Currency with id {} has been failed cause {}", transactionLock.getTransactionId(),
                    e);

            data.setTransactionStatus(TransactionStatus.FAILED);

            this.transactionLockRepository.deleteInBatch(Collections.singleton(transactionLock));

            return data;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            if (Validator.equals(errorCode, T24ErrorCode.OTP_VALUE_IS_INVALID)
                    || Validator.equals(errorCode, T24ErrorCode.OTP_IS_INVALID)
                    || Validator.equals(errorCode, T24ErrorCode.AUTHENTICATION_UNSUCCESSFUL)) {
                _log.info("Transaction Other Currency with id {} has been failed cause OTP invalid",
                        transactionLock.getTransactionId());

                this.transactionLockRepository.deleteInBatch(Collections.singleton(transactionLock));
                return null;
            }

            data.setTransactionStatus(TransactionStatus.FAILED);
            data.setT24ErrorCode(errorCode);

            return data;

        } catch (HttpServerErrorException ex) {
            String responseBody = ex.getResponseBodyAsString();

            try {
                Gson gson = new Gson();
                VerifyFundTransferResponse errorResponse = gson.fromJson(responseBody, VerifyFundTransferResponse.class);

                if (Validator.equals(errorResponse.getErrorCode(), T24ErrorCode.UNKNOW_L.getCode())) {
                    data.setTransactionStatus(TransactionStatus.FAILED);
                    data.setT24ErrorCode(T24ErrorCode.UNKNOW_L);
                    return data;
                }
            } catch (JsonSyntaxException e) {
                _log.error("Fail parse response: {}", e.getMessage());
                throw new BadRequestAlertException(ErrorCode.MSG1023);
            }
            throw new BadRequestAlertException(ErrorCode.MSG1023);
        } catch (Exception ex) {

            if (ex.getMessage().contains(ConstraintViolationException.class.getName())) {
                _log.error("Transaction with Other Currency id {} and otp {} have been processed", request.getRequestId(),
                        request.getOtpValue());

                throw new BadRequestAlertException(ErrorCode.MSG1165);
            }

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public List<AccountBalanceDTO> checkAccountBalance(AccountBalanceRequest request) {
        try {
            AccountBalanceRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getAccountBalance());

            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            AccountBalanceResponse response = this.apiGeeSender
                    .sendToApiGee(req, AccountBalanceResponse.class, params);

            List<AccountBalanceDTO> data = response.getData();

            if (Validator.isNull(data)) {
                throw new BadRequestAlertException(ErrorCode.MSG1091);
            }

            return data;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            if (Validator.equals(errorCode, T24ErrorCode.CUSTOMER_IS_NOT_ATTACHED_TO_ANY_ACCOUNT)) {
                _log.error("Customer is not attachted to any accounts");
                return null;
            }

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("getCcqSeBalances has error : {}", ex);

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public List<AccountBalanceDTO> checkAccountBalanceCustom(AccountBalanceRequest request) {
        try {
            AccountBalanceRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getAccountBalance());

            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            AccountBalanceResponse response = this.apiGeeSender
                    .sendToApiGee(req, AccountBalanceResponse.class, params);

            List<AccountBalanceDTO> data = response.getData();

            if (Validator.isNull(data)) {
                throw new BadRequestAlertException(ErrorCode.MSG1091);
            }

            return data;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            if (Validator.equals(errorCode, T24ErrorCode.CUSTOMER_ID_DOES_NOT_EXIST)) {
                _log.error("Customer is not attachted to any accounts");
                throw new BadRequestAlertException(ErrorCode.MSG101098);
            }

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("getCcqSeBalances has error : {}", ex);

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public List<AccountBalanceDTO> checkAccountBalanceCashout(AccountBalanceRequest request) {
        try {
            AccountBalanceRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getAccountBalance());

            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            AccountBalanceResponse response = this.apiGeeSender
                    .sendToApiGee(req, AccountBalanceResponse.class, params);

            List<AccountBalanceDTO> data = response.getData();

            if (Validator.isNull(data)) {
                return new ArrayList<>();
            }

            return data;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            return new ArrayList<>();
        } catch (HttpResponseException ex) {
            return new ArrayList<>();
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    @Override
    public List<TransactionHistoryDTO> getTransactionHistory(TransactionHistoryMBRequest request) {
        try {
            TransactionHistoryMBRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getTransactionHistory());

            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            TransactionHistoryResponse response = this.apiGeeSender
                    .sendToApiGee(req, TransactionHistoryResponse.class, params);

            List<TransactionHistoryDTO> data = response.getData();

            if (Objects.isNull(data)) {
                throw new BadRequestAlertException(ErrorCode.MSG1091);
            }

            return data;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("getCcqSeBalances has error : {}", ex);

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public RevertDTO revert(RevertTransactionRequest request) {
        try {
            RevertTransactionRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getRevert());

            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            RevertTransactionResponse response = this.apiGeeSender
                    .sendToApiGee(req, RevertTransactionResponse.class, params);

            RevertDTO data = response.getData();

            if (Objects.isNull(data)) {
                throw new BadRequestAlertException(ErrorCode.MSG1091);
            }

            return data;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("Response of revert transaction has error : {}", ex);

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public InquiryTransactionStatusDTO inquiryTransactionStatus(InquiryTransactionStatusRequest request) {
        try {
            InquiryTransactionStatusRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getInquiryTransactionStatus());

            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            InquiryTransactionStatusResponse response = this.apiGeeSender
                    .sendToApiGee(req, InquiryTransactionStatusResponse.class, params);

            InquiryTransactionStatusDTO data = response.getData();

            if (Objects.isNull(data)) {
                throw new BadRequestAlertException(ErrorCode.MSG1091);
            }

            return data;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("Response of inquiry transaction status has error : {}", ex);

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public List<AccountCurrencyDTO> checkAccountCurrency(AccountCurrencyRequest request) {
        try {
            AccountCurrencyRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getAccountCurrency());

            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            AccountCurrencyResponse response = this.apiGeeSender
                    .sendToApiGee(req, AccountCurrencyResponse.class, params);

            List<AccountCurrencyDTO> data = response.getData();

            if (data == null) {
                return new ArrayList<>();
            }

            return data;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("get Account currency has error : {}", ex);

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public List<AccountSavingDTO> checkAccountSaving(AccountSavingRequest request) {
        try {
            AccountSavingRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getAccountSaving());

            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            AccountSavingResponse response = this.apiGeeSender
                    .sendToApiGee(req, AccountSavingResponse.class, params);

            List<AccountSavingDTO> data = response.getData();

            if (data == null) {
                return new ArrayList<>();
            }

            return data;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("get Account saving has error : {}", ex);

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public List<AccountLoanDTO> checkAccountLoan(AccountLoanRequest request) {
        try {
            AccountLoanRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getAccountLoan());

            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            AccountLoanResponse response = this.apiGeeSender
                    .sendToApiGee(req, AccountLoanResponse.class, params);

            List<AccountLoanDTO> data = response.getData();

            if (data == null) {
                return new ArrayList<>();
            }

            return data;
        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error("get Account loan has error : {}", ex);

            throw new BadRequestAlertException(ErrorCode.MSG1023);
        }
    }

    @Override
    public VerifyFundTransferDTO cashout(CashoutTransferRequest request) {
        VerifyFundTransferDTO data = new VerifyFundTransferDTO();
        try {
            _log.info("cash out request: {}", request);
//            req.setApiGeeTransactionId(req.getRequestId());

            request.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getCashout());

            request.setMethod(HttpMethod.POST);

            data.setTransactionId(request.getApiGeeTransactionId());

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            VerifyFundTransferResponse response = this.apiGeeSender
                    .sendToApiGee(request, VerifyFundTransferResponse.class, params);

            data = response.getData();

            data.setTransactionStatus(TransactionStatus.SUCCESS);

            return data;
        } catch (ResourceAccessException e) {
            _log.error("cashout has timeout : {}", e);
            // timeout
            data.setTransactionStatus(TransactionStatus.TIMEOUT);

            return data;

        } catch (BadRequestAlertException | HttpClientErrorException e) {
            _log.error("cashout error: {}", e);

            data.setTransactionStatus(TransactionStatus.FAILED);

            return data;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            data.setTransactionStatus(TransactionStatus.FAILED);
            data.setT24ErrorCode(errorCode);

            return data;

        } catch (Exception ex) {
            _log.error("cashout has error : {}", ex);
            data.setTransactionStatus(TransactionStatus.FAILED);
            return data;
        }
    }

    @Override
    public VerifyFundTransferDTO debitMoney(DebitMoneyTransferRequest request) {
        VerifyFundTransferDTO data = new VerifyFundTransferDTO();
        try {
            _log.info("cash out request: {}", request);
//            request.setApiGeeTransactionId(UUIDUtil.generateUUID(20));

            request.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getDebitMoney());

            request.setMethod(HttpMethod.POST);

            data.setTransactionId(request.getApiGeeTransactionId());

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            VerifyFundTransferResponse response = this.apiGeeSender
                    .sendToApiGee(request, VerifyFundTransferResponse.class, params);

            data = response.getData();

            data.setTransactionStatus(TransactionStatus.SUCCESS);

            return data;
        } catch (ResourceAccessException e) {
            _log.error("cashout has timeout : {}", e);
            // timeout
            data.setTransactionStatus(TransactionStatus.TIMEOUT);

            return data;

        } catch (BadRequestAlertException | HttpClientErrorException e) {
            _log.error("cashout error: {}", e);

            data.setTransactionStatus(TransactionStatus.FAILED);

            return data;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            data.setTransactionStatus(TransactionStatus.FAILED);
            data.setT24ErrorCode(errorCode);

            return data;

        } catch (Exception ex) {
            _log.error("cashout has error : {}", ex);
            data.setTransactionStatus(TransactionStatus.FAILED);
            return data;
        }
    }

    @Override
    public VerifyFundTransferDTO depositMoney(DepositMoneyTransferRequest request) {
        VerifyFundTransferDTO data = new VerifyFundTransferDTO();
        try {
            _log.info("cash out request: {}", request);
            request.setApiGeeTransactionId(UUIDUtil.generateUUID(20));

            request.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getDebitMoney());

            request.setMethod(HttpMethod.POST);

            data.setTransactionId(request.getApiGeeTransactionId());

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            VerifyFundTransferResponse response = this.apiGeeSender
                    .sendToApiGee(request, VerifyFundTransferResponse.class, params);

            data = response.getData();

            data.setTransactionStatus(TransactionStatus.SUCCESS);

            return data;
        } catch (ResourceAccessException e) {
            _log.error("cashout has timeout : {}", e);
            // timeout
            data.setTransactionStatus(TransactionStatus.TIMEOUT);

            return data;

        } catch (BadRequestAlertException | HttpClientErrorException e) {
            _log.error("cashout error: {}", e);

            data.setTransactionStatus(TransactionStatus.FAILED);

            return data;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            data.setTransactionStatus(TransactionStatus.FAILED);
            data.setT24ErrorCode(errorCode);

            return data;

        } catch (Exception ex) {
            _log.error("cashout has error : {}", ex);
            data.setTransactionStatus(TransactionStatus.FAILED);
            return data;
        }
    }

    @Override
    public ReqSavingAccountDTO requestSavingAccount(OtpAccountSavingRequest request) {
        return null;
    }
}
