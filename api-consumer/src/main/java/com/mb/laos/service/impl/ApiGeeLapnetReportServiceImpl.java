package com.mb.laos.service.impl;

import com.mb.laos.api.consumer.ApiGeeSender;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.HttpResponseException;
import com.mb.laos.api.request.LapnetReportRequest;
import com.mb.laos.api.response.LapnetReportResponse;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.ConsumerProperties;
import com.mb.laos.enums.T24ErrorCode;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.service.ApiGeeLapnetReportService;
import com.mb.laos.util.ReflectionUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;

import javax.annotation.PostConstruct;

@Service
@RequiredArgsConstructor
@Slf4j
public class ApiGeeLapnetReportServiceImpl implements ApiGeeLapnetReportService {

    private final ApiGeeSender apiGeeSender;

    private final ConsumerProperties consumerProperties;

    private ConsumerProperties.ApiGee apiGee;

    @PostConstruct
    protected void init() {
        this.apiGee = this.consumerProperties.getApiGee();
    }

    @Override
    @Retryable(value = Exception.class, maxAttemptsExpression = "${scheduling.lapnet-sync-report.retry-quota:5}",
            backoff = @Backoff(delayExpression = "${scheduling.lapnet-sync-report.max-delay:100}"))
    public LapnetReportResponse getReport(LapnetReportRequest request) {
        try {
            LapnetReportRequest req = ReflectionUtil.clone(request);

            req.setUrl(this.apiGee.getBaseUrl() + this.apiGee.getUri().getLapnetReport());

            req.setMethod(HttpMethod.POST);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

            LapnetReportResponse response =
                    this.apiGeeSender.sendToApiGee(req, LapnetReportResponse.class, params);

            if (Validator.isNull(response) || Validator.isNull(response.getData())) {
                throw new BadRequestAlertException(ErrorCode.MSG100023);
            }

            if (_log.isDebugEnabled()) {
                _log.debug("getReport response: {}", response);
            }

            return response;

        } catch (BadRequestAlertException | HttpClientErrorException ex) {
            throw ex;
        } catch (HttpResponseException ex) {
            T24ErrorCode errorCode = T24ErrorCode.valueOfCode(ex.getErrorCode());

            throw new BadRequestAlertException(errorCode.getErrorCode());
        } catch (Exception ex) {
            _log.error(" has error : {}", ex);

            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_AN_UNEXPECTED_ERROR_HAS_OCCURRED),
                    ErrorCode.MSG1023.name(), LabelKey.ERROR_AN_UNEXPECTED_ERROR_HAS_OCCURRED);
        }
    }

}
