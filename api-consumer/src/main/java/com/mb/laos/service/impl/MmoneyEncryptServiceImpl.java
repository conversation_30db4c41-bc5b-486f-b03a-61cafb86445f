package com.mb.laos.service.impl;

import com.mb.laos.configuration.ConsumerProperties;
import com.mb.laos.service.MmoneyEncryptService;
import com.mb.laos.util.RandomGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class MmoneyEncryptServiceImpl implements MmoneyEncryptService {
    private final ConsumerProperties consumerProperties;


    @Override
    public String decrypt(String encryptedText) {
        try {
            String key = generateKey(consumerProperties.getApiMmoney().getSecretKey());
            // Lấy initialization vector (IV) từ văn bản mã hóa (16 byte đầu tiên)
            byte[] ivBytes = hexStringToByteArray(encryptedText.substring(0, 32));

            // Chuẩn bị khóa bí mật từ secretKey
            SecretKeySpec secretKeySpec = new SecretKeySpec(
                    Objects.requireNonNull(key).getBytes(), "AES");

            // Chuẩn bị đối tượng Cipher để giải mã với AES-256-CBC
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, new IvParameterSpec(ivBytes));

            // Giải mã văn bản còn lại (sau 32 byte đầu tiên) từ hex sang UTF-8
            byte[] encryptedBytes = hexStringToByteArray(encryptedText.substring(32));
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);

            // Chuyển đổi mảng byte đã giải mã thành chuỗi UTF-8
            return new String(decryptedBytes, "UTF-8");
        } catch (Exception e) {
            _log.error("Mmoney decrypt has error");
            e.printStackTrace();
            return null;
        }
    }

    public static byte[] hexStringToByteArray(String hexString) {
        int len = hexString.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return data;
    }

    @Override
    public String encrypt(String text) {
        try {
            String key = generateKey(consumerProperties.getApiMmoney().getSecretKey());
            byte[] iv = RandomGenerator.generateIV(16);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
            SecretKeySpec secretKeySpec = new SecretKeySpec(Objects.requireNonNull(key).getBytes(), "AES");

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
            byte[] encryptedBytes = cipher.doFinal(text.getBytes());

            String encryptedData = bytesToHex(encryptedBytes);
            String ivString = bytesToHex(iv);

            return ivString + encryptedData;
        } catch (Exception ex) {
            _log.error("Mmoney encrypt has error");
            ex.printStackTrace();
            return null;
        }
    }

    public static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0'); // Đảm bảo rằng mỗi byte sẽ được biểu diễn bằng 2 ký tự hexa
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    public static String generateKey(String secretKey) {
        try {
            // Tạo một đối tượng MessageDigest sử dụng thuật toán SHA-256
            MessageDigest digest = MessageDigest.getInstance("SHA-256");

            // Băm chuỗi secretKey và lấy giá trị băm dưới dạng mảng byte
            byte[] hash = digest.digest(secretKey.getBytes());

            // Chuyển đổi mảng byte thành chuỗi hexa
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                // Convert mỗi byte thành chuỗi hexa và nối vào chuỗi hexString
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0'); // Đảm bảo rằng mỗi byte sẽ được biểu diễn bằng 2 ký tự hexa
                }
                hexString.append(hex);
            }

            // Lấy 32 ký tự đầu tiên của chuỗi hexa làm khóa
            return hexString.substring(0, 32);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }
}
