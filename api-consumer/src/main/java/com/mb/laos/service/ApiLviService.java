package com.mb.laos.service;

import com.mb.laos.api.request.lvi.LviHealthInsuranceBuyRequest;
import com.mb.laos.api.request.lvi.LviHealthPackageFeeRequest;
import com.mb.laos.api.request.lvi.LviVehicleInsuranceBuyRequest;
import com.mb.laos.api.request.lvi.LviVehiclePackageFeeRequest;
import com.mb.laos.api.response.lvi.*;
import com.mb.laos.cache.util.ConsumerCacheConstants;
import org.springframework.cache.annotation.Cacheable;

public interface ApiLviService {
    @Cacheable(cacheNames = ConsumerCacheConstants.LviCache.VEHICLE_PACKAGE)
    LviVehiclePackageResponse getVehiclePackage();

    @Cacheable(cacheNames = ConsumerCacheConstants.LviCache.HEALTH_PACKAGE)
    LviHealthPackageResponse getHealthPackage();

    @Cacheable(cacheNames = ConsumerCacheConstants.LviCache.VEHICLE_TYPE)
    LviVehicleTypeResponse getVehicleType();

    @Cacheable(cacheNames = ConsumerCacheConstants.LviCache.VEHICLE_PACKAGE_FEE, key = "{#request.packageCode,#request.vehicleCode}")
    LviVehiclePackageFeeResponse getVehiclePackageFee(LviVehiclePackageFeeRequest request);

    @Cacheable(cacheNames = ConsumerCacheConstants.LviCache.HEALTH_PACKAGE_FEE, key = "#request.healthPackage")
    LviHealthPackageFeeResponse getHealthPackageFee(LviHealthPackageFeeRequest request);

    LviVehicleBuyInsuranceResponse buyVehicleInsurance(LviVehicleInsuranceBuyRequest request);

    LviVehicleBuyInsuranceResponse renewVehicleInsurance(LviVehicleInsuranceBuyRequest request);

    LviHealthBuyInsuranceResponse buyHealthInsurance(LviHealthInsuranceBuyRequest request);

    @Cacheable(cacheNames = ConsumerCacheConstants.LviCache.DELIVERY)
    LviDeliveryResponse getDelivery();

    LviVehicleInsuranceResponse getVehicleInsurance(String certificate);
}
