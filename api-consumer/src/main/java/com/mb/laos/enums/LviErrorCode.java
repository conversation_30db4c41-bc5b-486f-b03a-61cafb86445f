package com.mb.laos.enums;

import com.mb.laos.api.util.ErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class LviErrorCode {

    @AllArgsConstructor
    @Getter
    public enum GetPackageMV {
        INVALID_VEHICLE("02", ErrorCode.MSG101003),
        INVALID_USERNAME_PASSWORD("03", ErrorCode.MSG1004),
        DEFAULT("-1", ErrorCode.MSG1023),
        ;
        private final String code;

        private final ErrorCode errorCode;

        public static GetPackageMV valueOfCode(String code) {
            for (GetPackageMV status : GetPackageMV.values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }

            return DEFAULT;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum GetVehicleInsurance {
        CERTIFICATE_NOT_AVAILABLE("02", ErrorCode.MSG101002),
        INVALID_USERNAME_PASSWORD("03", ErrorCode.MSG1004),
        CERTIFICATE_EXTENDED("04", ErrorCode.MSG101004),
        DEFAULT("-1", ErrorCode.MSG1023),
        ;
        private final String code;

        private final ErrorCode errorCode;

        public static GetVehicleInsurance valueOfCode(String code) {
            for (GetVehicleInsurance status : GetVehicleInsurance.values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }

            return DEFAULT;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum SetVehicleInsuranceNew {
        INVALID_USERNAME_PASSWORD("03", ErrorCode.MSG1004),
        DEFAULT("-1", ErrorCode.MSG1023),
        ;
        private final String code;

        private final ErrorCode errorCode;

        public static SetVehicleInsuranceNew valueOfCode(String code) {
            for (SetVehicleInsuranceNew status : SetVehicleInsuranceNew.values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }

            return DEFAULT;
        }
    }


    @AllArgsConstructor
    @Getter
    public enum SetVehicleInsuranceRenew {
        CERTIFICATE_NOT_AVAILABLE("02", ErrorCode.MSG101002),
        INVALID_USERNAME_PASSWORD("03", ErrorCode.MSG1004),
        CERTIFICATE_EXTENDED("04", ErrorCode.MSG101004),
        DEFAULT("-1", ErrorCode.MSG1023),
        ;
        private final String code;

        private final ErrorCode errorCode;

        public static SetVehicleInsuranceRenew valueOfCode(String code) {
            for (SetVehicleInsuranceRenew status : SetVehicleInsuranceRenew.values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }

            return DEFAULT;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum GetVehicleType {
        INVALID_VEHICLE("02", ErrorCode.MSG101003),
        INVALID_USERNAME_PASSWORD("03", ErrorCode.MSG1004),
        DEFAULT("-1", ErrorCode.MSG1023),
        ;
        private final String code;

        private final ErrorCode errorCode;

        public static GetVehicleType valueOfCode(String code) {
            for (GetVehicleType status : GetVehicleType.values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }

            return DEFAULT;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum GetVehiclePackage {
        INVALID_VEHICLE("02", ErrorCode.MSG101003),
        INVALID_USERNAME_PASSWORD("03", ErrorCode.MSG1004),
        DEFAULT("-1", ErrorCode.MSG1023),
        ;
        private final String code;

        private final ErrorCode errorCode;

        public static GetVehiclePackage valueOfCode(String code) {
            for (GetVehiclePackage status : GetVehiclePackage.values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }

            return DEFAULT;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum GetVehiclePackageFee {
        INVALID_PACKAGE_VEHICLE("02", ErrorCode.MSG101003),
        INVALID_USERNAME_PASSWORD("03", ErrorCode.MSG1004),
        DEFAULT("-1", ErrorCode.MSG1023),
        ;
        private final String code;

        private final ErrorCode errorCode;

        public static GetVehiclePackageFee valueOfCode(String code) {
            for (GetVehiclePackageFee status : GetVehiclePackageFee.values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }

            return DEFAULT;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum GetHealthPackage {
        INVALID_PACKAGE("02", ErrorCode.MSG101001),
        INVALID_USERNAME_PASSWORD("03", ErrorCode.MSG1004),
        DEFAULT("-1", ErrorCode.MSG1023),
        ;
        private final String code;

        private final ErrorCode errorCode;

        public static GetHealthPackage valueOfCode(String code) {
            for (GetHealthPackage status : GetHealthPackage.values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }

            return DEFAULT;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum GetHealthPackageFee {
        INVALID_PACKAGE("02", ErrorCode.MSG101001),
        INVALID_USERNAME_PASSWORD("03", ErrorCode.MSG1004),
        DEFAULT("-1", ErrorCode.MSG1023),
        ;
        private final String code;

        private final ErrorCode errorCode;

        public static GetHealthPackageFee valueOfCode(String code) {
            for (GetHealthPackageFee status : GetHealthPackageFee.values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }

            return DEFAULT;
        }
    }


}
