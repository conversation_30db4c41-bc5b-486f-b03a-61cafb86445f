package com.mb.laos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EkycStatusCode {
    SUCCESS("1"),
    
    INTERNAL_SERVER_ERROR("1001"),

    PERMISSION_DENIED("1002"),
    
    NOT_FOUND("1003"),
    
    EXISTS("1004"),
    
    WRONG_INPUT_FIELD("1005"),
    
    USER_ERROR("2001");
    
    private String code;

    public static EkycStatusCode valueOfCode(String code) {
        for (EkycStatusCode status : EkycStatusCode.values()) {
            
            if (status.getCode().equals(code)) {
                return status;
            }
        }

        return null;
    }
}
