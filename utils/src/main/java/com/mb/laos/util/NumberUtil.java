package com.mb.laos.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class NumberUtil {
    public static boolean isConflictBetweenNumber(Double startSource, Double endSource, Double startValue, Double endValue) {
        return (startSource <= startValue && endValue <= endSource)
                || (startValue <= startSource && startSource <= endValue)
                || (startValue <= endSource && endSource <= endValue);
    }

    public static String formatPhoneNumber(String phoneNumber) {
        if (phoneNumber.charAt(0) == '0') {
            phoneNumber = phoneNumber.substring(1);
        }

        if (phoneNumber.startsWith("856")) {
            phoneNumber = phoneNumber.substring(3);
        }

        return phoneNumber;
    }
}
