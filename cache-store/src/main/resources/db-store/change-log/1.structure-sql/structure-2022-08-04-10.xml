<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.3.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="LinhLH" id="s-2022-08-04-10-01">
		<sql>
CREATE TABLE "INDEX_DATA_LuceneIndexesData" (
  "K" VARCHAR2(255 BYTE) VISIBLE NOT NULL,
  "V" BLOB VISIBLE NOT NULL,
  "T" NUMBER VISIBLE NOT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Primary Key structure for table INDEX_DATA_LuceneIndexesData
-- ----------------------------
ALTER TABLE "INDEX_DATA_LuceneIndexesData" ADD CONSTRAINT "SYS_C0014086" PRIMARY KEY ("K");

-- ----------------------------
-- Checks structure for table INDEX_DATA_LuceneIndexesData
-- ----------------------------
ALTER TABLE "INDEX_DATA_LuceneIndexesData" ADD CONSTRAINT "SYS_C0014080" CHECK ("K" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "INDEX_DATA_LuceneIndexesData" ADD CONSTRAINT "SYS_C0014082" CHECK ("V" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "INDEX_DATA_LuceneIndexesData" ADD CONSTRAINT "SYS_C0014084" CHECK ("T" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table INDEX_DATA_LuceneIndexesData
-- ----------------------------
CREATE INDEX "IDX_INDEX_DATA_LuceneIndexes"
  ON "INDEX_DATA_LuceneIndexesData" ("T" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;
		</sql>
    </changeSet>
    
    <changeSet author="LinhLH" id="s-2022-08-04-10-02">
		<sql>
CREATE TABLE "INDEX_META_LuceneIndexesMetadata" (
  "K" VARCHAR2(255 BYTE) VISIBLE NOT NULL,
  "V" BLOB VISIBLE NOT NULL,
  "T" NUMBER VISIBLE NOT NULL
)
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;

-- ----------------------------
-- Primary Key structure for table INDEX_META_LuceneIndexesMetadata
-- ----------------------------
ALTER TABLE "INDEX_META_LuceneIndexesMetadata" ADD CONSTRAINT "SYS_C0014085" PRIMARY KEY ("K");

-- ----------------------------
-- Checks structure for table INDEX_META_LuceneIndexesMetadata
-- ----------------------------
ALTER TABLE "INDEX_META_LuceneIndexesMetadata" ADD CONSTRAINT "SYS_C0014079" CHECK ("K" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "INDEX_META_LuceneIndexesMetadata" ADD CONSTRAINT "SYS_C0014081" CHECK ("V" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "INDEX_META_LuceneIndexesMetadata" ADD CONSTRAINT "SYS_C0014083" CHECK ("T" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table INDEX_META_LuceneIndexesMetadata
-- ----------------------------
CREATE INDEX "IDX_INDEX_META_LuceneIndexes"
  ON "INDEX_META_LuceneIndexesMetadata" ("T" ASC)
  LOGGING
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;
		</sql>
    </changeSet>

</databaseChangeLog>
