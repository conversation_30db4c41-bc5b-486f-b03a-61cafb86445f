/*
 * CacheStoreConfiguration.java
 *
 * Copyright (C) 2022 by Evotek. All right reserved. This software is the confidential and proprietary information of
 * Evotek
 */
package com.mb.laos.configuration;

import javax.naming.NamingException;
import javax.sql.DataSource;
import org.apache.catalina.Context;
import org.apache.catalina.startup.Tomcat;
import org.apache.tomcat.util.descriptor.web.ContextResource;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.embedded.tomcat.TomcatWebServer;
import org.springframework.context.annotation.Bean;
import org.springframework.jndi.JndiObjectFactoryBean;
import org.springframework.stereotype.Component;
import liquibase.integration.spring.SpringLiquibase;
import lombok.Data;

/**
 * 02/08/2022 - LinhLH: Create new
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "cache-store")
@ConditionalOnProperty(prefix = "spring.jpa.properties.hibernate.search.default", name = "directory_provider",
                havingValue = "infinispan",
                matchIfMissing = false)
public class CacheStoreConfiguration {
    private String type;
    
    private String factory;

    private String url;

    private String driverClassName;
    
    private String dataSourceClassName;

    private String username;

    private String password;
    
    private String minimumIdle;
    
    private String maximumPoolSize;
    
    private String connectionTimeout;
    
    private String implicitCachingEnabled;

    private String jndiName;
    
    private String changeLog;

    @Bean
    public TomcatServletWebServerFactory tomcatFactory() {
        return new TomcatServletWebServerFactory() {

            @Override
            protected TomcatWebServer getTomcatWebServer(
                            Tomcat tomcat) {
                tomcat.enableNaming();

                return super.getTomcatWebServer(tomcat);
            }

            @Override
            protected void postProcessContext(Context context) {
                ContextResource resource = new ContextResource();

                resource.setName(jndiName);
                resource.setType(type);
                resource.setProperty("factory", factory);
                resource.setProperty("dataSourceClassName", dataSourceClassName);
                resource.setProperty("minimumIdle", minimumIdle);
                resource.setProperty("maximumPoolSize", maximumPoolSize);
                resource.setProperty("connectionTimeout", connectionTimeout);
                resource.setProperty("dataSource.url", url);
                resource.setProperty("dataSource.user", username);
                resource.setProperty("dataSource.password", password);
                resource.setProperty("dataSource.implicitCachingEnabled", implicitCachingEnabled);

                context.getNamingResources().addResource(resource);
            }
        };
    }
    
    public DataSource jndiDataSource() throws IllegalArgumentException, NamingException {
		JndiObjectFactoryBean bean = new JndiObjectFactoryBean();
		
		bean.setJndiName("java:comp/env/" + this.jndiName);
		bean.setProxyInterface(DataSource.class);
		bean.setLookupOnStartup(true);
		bean.afterPropertiesSet();
		
		return (DataSource)bean.getObject();
	}
    
    @Bean("storeLiquibase")
	public SpringLiquibase storeLiquibase() throws IllegalArgumentException, NamingException {
		SpringLiquibase liquibase = new SpringLiquibase();
		
		liquibase.setDataSource(jndiDataSource());
		
		liquibase.setChangeLog(this.changeLog);
		
		return liquibase;
	}
}
