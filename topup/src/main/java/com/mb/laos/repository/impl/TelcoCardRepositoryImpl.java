package com.mb.laos.repository.impl;

import com.mb.laos.model.TelcoCard;
import com.mb.laos.model.search.TelcoCardSearch;
import com.mb.laos.repository.extend.TelcoCardRepositoryExtend;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.Validator;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class TelcoCardRepositoryImpl implements TelcoCardRepositoryExtend {
    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<TelcoCard> search(TelcoCardSearch telcoCardSearch, Pageable pageable) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT e from TelcoCard e ");

        Map<String, Object> values = new HashMap<>();

        sql.append(createWhereQuery(telcoCardSearch, values));
        sql.append(QueryUtil.createOrderQuery(TelcoCard.class, telcoCardSearch.getOrderByType(),
                telcoCardSearch.getOrderByColumn()));

        Query query = entityManager.createQuery(sql.toString(), TelcoCard.class);

        values.forEach(query::setParameter);

        if (pageable != null) {
            query.setFirstResult(pageable.getPageNumber() * pageable.getPageSize());

            query.setMaxResults(pageable.getPageSize());
        } else {
            query.setFirstResult(QueryUtil.FIRST_INDEX);

            query.setMaxResults(QueryUtil.MAX_RESULT);
        }

        return query.getResultList();
    }

    @Override
    public Long count(TelcoCardSearch telcoCardSearch) {
        StringBuilder sql = new StringBuilder();
        Map<String, Object> values = new HashMap<>();

        sql.append("SELECT COUNT(1) FROM TelcoCard e ");

        sql.append(createWhereQuery(telcoCardSearch, values));

        Query query = entityManager.createQuery(sql.toString(), Long.class);

        values.forEach(query::setParameter);

        return (Long) query.getSingleResult();
    }

    private String createWhereQuery(TelcoCardSearch params, Map<String, Object> values) {
        StringBuilder sql = new StringBuilder();

        sql.append(" WHERE 1 = 1 AND e.status != -1 ");

        if (Validator.isNotNull(params.getTelcoId())) {
            sql.append(" AND e.telcoId = :telcoId ");
            values.put("telcoId", params.getTelcoId());
        }

        if (Validator.isNotNull(params.getStatus())) {
            sql.append(" AND e.status = :status ");

            values.put("status", params.getStatus());
        }

        return sql.toString();
    }
}
