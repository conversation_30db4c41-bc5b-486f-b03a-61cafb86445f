#Generated by ResourceBundle Editor (http://essiembre.github.io/eclipse-rbe/)

error.account-does-not-exist                                             = Account does not exist. Please login again.
error.account-does-not-exist-in-whitelist                                = User doesnt exist in whitelist
error.account-has-not-change-password-or-verify-ekyc                     = Account has not change password or verify ekyc
error.account-number-invalid                                             = Account number invalid
error.account-number-is-not-owned-by-the-customer                        = Account number not owned by the user
error.account-number-is-required                                         = Account number is required
error.account-number-max-length                                          = Max length of account numeber is 20
error.account-number-was-lock-transaction-due-spam-otp                   = Account number was lock transaction due spam otp
error.account-type-invalid                                               = Account type is invalid
error.account-type-not-supported                                         = Account type is not supported
error.action-invalid                                                     = Action is invalid
error.customer.issue-date-is-invalid                                     = Issue date is invalid
error.amount-is-required                                                 = Amount is required
error.amount-transaction-is-invalid                                      = The limit of transaction types per transaction does not exceed the daily sector limit
error.amount-transaction-min-must-smaller-than-amount-max                = The minimum trade amount must be less than the maximum trade amount
error.an-unexpected-error-has-occurred                                   = An unexpected error has occurred. Please try again.
error.an-unexpected-error-has-occurred-when-export                       = An unexpected error has occurred when export {0}
error.announcement-type-id-is-invalid                                    = Accouncement invalid
error.async-request-is-invalid                                           = The synchronization request has been executed successfully or has been executed more than the specified number of times.
error.authen-unsuccessful                                                = Authentication unsuccessful
error.available-balance-not-enough                                       = Your available balance does not enough to excute this transaction
error.bad-request                                                        = Bad request 
error.bank-code-has-been-used                                            = Bank code already in use
error.bank-code-is-required                                              = Bank code is required
error.bank-code-number-has-been-used                                     = Bank code number already in use
error.bank-is-not-support                                                = Bank is not support
error.banner-has-not-been-set-up-or-has-been-deleted                     = Banner has not been set up or has been deleted
error.beneficiary-account-does-not-exist                                 = Beneficiary account does not exist
error.beneficiary-account-must-not-be-equal-to-transfer-account          = Beneficiary account must not be equal to transfer account
error.beneficiary-account-number-does-not-exist                          = Beneficiary Account Number does not exist
error.beneficiary-bank-code-is-required                                  = Beneficiary bank code is required
error.beneficiary-currency-is-required                                   = Beneficiary currency is required
error.beneficiary-is-required                                            = Beneficiary is required
error.bill-has-been-changed                                              = Your bill has been changed. Please check and confirm again.
error.bill-has-been-paid                                                 = Your transaction is not successful, bill has been paid
error.billing-code-does-not-exist                                        = Billing code does not exist
error.branch-code-invalid                                                = Branch code is invalid
error.branch-code-is-required                                            = Branch code is required
error.branch-code-not-empty                                              = Branch code is not empty
error.building-of-child-department-must-be-the-same-as-the-parent        = Building of child department must be the same as the parent
error.campaign-could-not-be-found                                        = Campaign could not be found
error.campaign-has-expired                                               = Campaign has expired
error.campaign-id-is-required                                            = Campaign id is required
error.campaign-name-is-required                                          = Campaign name is required
error.campaign-name-length-is-invalid                                    = Campaign name length is invalid
error.campaign-time-coincides-with-the-time-of-another-campaign    = Campaign time coincides with the time of campaign "{0}"
error.can-not-process-your-request                                       = We can not process your request. Please try again
error.cannot-assign-role-that-has-higher-level-than-yours                = Cannot assign role with name {0} that has higher level (or equal) than your roles
error.cannot-create-data-with-existed-id                                 = Cannot create {0} with existed id
error.cannot-create-otp                                                  = Cannot create OTP
error.cannot-decrypt-data                                                = Cannot decrypt data
error.cannot-delete-role-while-someone-is-active                         = Unable to lock/delete role assigned to user
error.cannot-encrypt-data                                                = Cannot encrypt your data
error.cannot-export-too-much-record                                      = Cannot export too much record
error.cannot-inactive-role-while-someone-is-active                       = Unable to lock a role while being assigned to an active user
error.cannot-override-data                                               = Cannot override to a existed {0}
error.cannot-refund-trans                                                = Cannot refund transaction
error.cannot-send-sms                                                    = Cannot send SMS
error.cannot-update-data                                                 = Cannot update {0}. Please try again later.
error.card-not-lao                                                       = Not a Lao's card
error.card-too-blur                                                      = Card is too blur
error.card-too-bright                                                    = Card is too bright
error.card-too-dark                                                      = Card is too dark
error.card-too-glare                                                     = Card is glare
error.card-too-small                                                     = Card is too small
error.change-password-time-limit                                         = Your password is expired. To access, reset your password in MBBank or bring your identification to the nearest transaction counter.
error.channel-invalid                                                    = Channel is invalid
error.charge-account-name-invalid                                        = Charge account name is invalid
error.charge-account-name-does-not-match                                 = Account name does not match account number
error.charge-account-number-invalid                                      = Charge account number is invalid
error.charge-amount-invalid                                              = Charge amount is invalid
error.charge-code-invalid                                                = Charge code name is invalid
error.charge-currency-invalid                                            = Charge cucrency name is invalid
error.charge-description-invalid                                         = Charge description is invalid
error.closed-eye-closed-eye                                              = Eyes are closing
error.closed-eye-open-eye                                                = Left eye is closing
error.collateral-max-length                                              = Collateral maxlength 255
error.concurrency-failure                                                = Concurrency failure
error.configuration-type-fee-is-required                                 = Charge configuration type cannot be empty
error.confirm-pw-is-empty                                                = Confirm password is empty
error.connect-timeout                                                    = Connect timeout
error.constraint-violation                                               = Constraint Violation
error.could-not-find-account-with-id                                     = The account that corresponds to the ID number could not be found.
error.could-not-get-any-response-from-lapnet                             = No response from Lapnet
error.could-not-get-any-response-from-t24                                = No response from T24
error.could-not-get-response-from-otp                                    = No response from OTP
error.could-not-identify-error-code                                      = Could not identify error code
error.credit-account-currency-invalid                                    = Credit account currency is invalid
error.credit-account-invalid                                             = Credit account is invalid
error.credit-account-name-invalid                                        = Credit account name is invalid
error.credit-account-number-invalid                                      = Credit account number is invalid
error.credit-account-type-invalid                                        = Credit account type is invalid
error.credit-bank-code-invalid                                           = Credit bank code is invalid
error.credit-bank-name-invalid                                           = Credit bank name is invalid
error.currency-invalid                                                   = Currency is invalid
error.currency-max-length                                                = Max length of currency is 10
error.current-address-is-max-length                                      = Current address max length 255
error.current-address-is-required                                        = Current address is required
error.current-address-max-length                                         = Current address maxlength 255
error.customer-account-number-does-not-existed                           = Customer Account Number does not exist
error.customer-account-number-is-required                                = Beneficary customer Account is required
error.customer-code-invalid                                              = Customer code invalid
error.customer-currency-is-required                                      = Customer currency is required
error.customer-does-not-exist-on-t24                                     = Customer does not exist on T24 system
error.customer-id-is-invalid                                             = Customer id is invalid
error.customer-id-is-required                                            = Customer id is required
error.customer-info-has-been-used                                        = ID/Phone number already in use
error.customer-is-invalid                                                = Customer is invalid
error.customer-is-pending                                                = Customer is pending. You cannot perform this action.
error.customer-not-attachted-to-any-account                              = Customer is not attachted to any accounts
error.customer-sector-does-not-found                                     = Sector of customer does not found.
error.customer-sector-code                                               = Sector
error.data-could-not-be-found                                            = {0} could not be found
error.data-does-not-exist                                                = {0} does not exist
error.data-does-not-exist-or-you-are-not-allowed-to-perform-this-action  = {0} does not exist or you are not allowed to perform this action
error.data-does-not-exist-with-code                                      = {0} with code {1} does not exist
error.data-does-not-exist-with-name                                      = {0} with name {1} does not exist
error.data-is-already-activated                                          = {0} is already activated
error.data-is-already-approved                                           = {0} is already approved
error.data-is-already-deleted                                            = {0} is already deleted
error.data-is-already-inactivated                                        = {0} is already inactivated
error.data-is-incorrect-with-name-or-code                                = {0} is incorrect with name or code
error.data-is-not-active                                                 = {0} is not active
error.data-is-not-approved-yet                                           = {0} is not approved yet
error.date-cannot-be-in-the-future                                       = {0} cannot be in the future
error.date-cannot-be-in-the-past                                         = {0} cannot be in the past
error.date-of-birth-is-required                                          = Date Of Birth is required.
error.date-of-birth-is-invalid                                           = Date Of Birth is invalid
error.date-of-birth-must-not-be-greater-than-now                         = Date Of Birth must not be greater than now
error.debit-account-currency-invalid                                     = Debit account currency is invalid
error.debit-account-invalid                                              = Debit account is invalid
error.debit-account-name-invalid                                         = Debit account name is invalid
error.debit-account-number-invalid                                       = Debit account number is invalid
error.debit-account-type-invalid                                         = Debit account type is invalid
error.deleted-was-not-successful                                         = Deleted was not successful
error.denomination-card-is-required                                      = Denomination card is required
error.department-code-is-invalid                                         = Department code must only contain latin letters, numbers and "_" character
error.department-has-active-children                                     = Department has active children
error.department-has-active-users                                        = Department has active users
error.department-has-inactive-parent                                     = Department has inactive parent
error.department-is-required                                             = Department is required
error.description-length-is-invalid                                      = Description length is invalid
error.description-max-length                                             = Description max length is 255.
error.destination-not-empty                                              = Destination is not empty
error.destination-not-found                                              = Destination not found
error.device-is-not-certified                                            = Device is not certified
error.discount-percent-is-required                                       = The discount percentage cannot be blank.
error.district-does-not-belong-to-selected-province                      = The selected district does not belong to the selected province
error.dob-is-empty                                                       = Date of birth is required
error.dob-must-not-be-greater-than-now                                   = Date of birth must not be greater than current time
error.duplicate-data                                                     = {0} already exists
error.duplicate-record                                                   = Duplicated record
error.duplicate-trans-id                                                 = Duplicated transaction id
error.transaction-does-not-exist                                               = Transaction does not exist
error.dotp-otp-is-invalid                                                = Transaction ID is invalid
error.email-does-not-exist                                               = Email does not exist.
error.email-is-already-existed                                           = Email is already existed.
error.email-is-invalid                                                   = Email is invalid. Please try again
error.email-is-required                                                  = Email is required
error.embed-link-format-is-invalid                                       = Embed link format is invalid
error.embed-link-is-required                                             = Embed link is required
error.embed-link-length-is-invalid                                       = Embed link length is invalid
error.embed-links-must-not-be-null-when-files-exist                      = Embed links must not be null when files exists
error.encrypted-data-is-invalid                                          = Encrypted data is invalid
error.end-date-is-required                                               = End date is required
error.end-date-must-be-not-before-now                                    = The end date cannot before now
error.exceed-max-length                                                  = {0} cannot exceed {1} characters
error.expected-time-must-not-be-smaller-than-now                         = Expected time must not be smaller than now
error.expiration-time-gttt-invalid                                       = The expiration time of the identification document does not match the regulations
error.face-ahead                                                         = Look straight
error.face-down                                                          = Stay down
error.face-good                                                          = Eligible face
error.face-left                                                          = Turn left
error.face-matching                                                      = Face matches
error.face-no-matching                                                   = Face does not match
error.face-nonliveness                                                   = Face is non-liveness
error.face-right                                                         = Turn right
error.face-too-big                                                       = Face is too big
error.face-too-blur                                                      = Face is too blur
error.face-too-bright                                                    = Face is too bright
error.face-too-dark                                                      = Face is too dark
error.face-too-small                                                     = Face is too small
error.face-up                                                            = Face up
error.failed-authen-user-is-locked                                       = Failed authentication. User is now locked
error.fee-does-not-exist                                                 = Fee does not exist
error.fee-id-is-required                                                 = Fee id is required
error.fee-name-is-required                                               = Fee name cannot be empty
error.fee-name-max-length                                                = The name of the fee cannot exceed 100 characters
error.fee-rate-does-not-exist                                            = Fee rate does not exist
error.file-content-not-allow                                             = File is not an image
error.file-could-not-be-found                                            = File could not be found
error.file-is-empty                                                      = File is empty
error.file-not-allow-ext                                                 = File format is invalid
error.from-account-invalid                                               = From account invalid
error.from-date-invalid                                                  = From date invalid
error.from-date-is-required                                              = From date is required
error.from-date-must-not-be-greater-than-to-date                         = From date must not be greater than to date
error.from-user-invalid                                                  = From user invalid
error.fullname-is-empty                                                  = Fullname is required
error.fullname-is-invalid                                                = Fullname is invalid. Please try again.
error.user-code-is-invalid = Invalid employee code. Please try again.
error.rm-code-is-invalid = Invalid RM code. Please try again.
error.function-does-not-exist                                            = Function doesnt exist
error.fund-config-is-empty                                               = Fund config cannot be empty
error.gender-is-invalid                                                  = Gender is invalid
error.gender-is-required                                                 = Gender is required
error.id-card-issue-date-is-empty                                        = ID card issue date is required
error.id-card-issue-date-is-not-eligible                                 = The issue date of the id card is not eligible. ID cards need to have an issue date of less than 5 years.
error.id-card-or-phone-number-already-in-use                             = Id card or phone number already in use
error.id-card-type-does-not-exist                                        = Identity card does not exist
error.id-card-type-is-empty                                              = Identity card type can not be empty
error.id-is-required                                                     = Id is required
error.id-number-does-not-exist                                           = Id number does not exist
error.id-number-is-empty                                                 = ID number is required
error.id-number-is-invalid                                               = ID number is invalid
error.identification-has-been-used                                       = Identification has been used.
error.identity-paper-does-not-exist                                      = Identity paper does not exist
error.image-has-multiple-cards                                           = Image has multiple cards
error.image-has-multiple-faces                                           = Image has multiple faces
error.image-has-no-card-or-incomplete-card                               = Image has no card or only a part of card 
error.image-has-no-face                                                  = Image has no face
error.income-is-invalid                                                  = Income is invalid
error.income-is-required                                                 = Income is required
error.incorrect-captcha                                                  = Incorrect captcha
error.incorrect-otp                                                      = Incorrect OTP
error.incorrect-signature                                                = Incorrect signature
error.infomation-is-invalid                                              = Information not available. Please check the information again. Or contact 021 752 777 for support.
error.information-name-and-display-name-already-exists                   = Information name and display name already exists
error.information-template.code-existed                                  = Information template code already exists
error.information-verify-is-invalid                                      = Information verify is invalid
error.input-cannot-be-empty                                              = "{0}" cannot be empty
error.input-date-must-not-be-greater-than-now                            = Input date must not be greater than now
error.input-does-not-match                                               = The information you provided does not match the registration at MB. Please check again. 
error.input-invalid                                                      = Input invalid
error.input-invalid-length                                               = {0} must has at least {1} characters and at most {2} characters
error.input-invalid-max-length                                           = {0} must has at most {1} characters
error.input-invalid-range-value                                          = {0} must have value between {1} and {2}
error.input-must-be-assigned                                             = {0} must be assigned
error.input-must-be-non-negative                                         = {0} must be non-negative
error.input-must-be-positive                                             = {0} must be positive
error.invalid                                                            = {0} invalid
error.invalid-app-version                                                = Invalid application version
error.invalid-area                                                       = Invalid province, district, ward
error.invalid-data                                                       = Invalid {0}
error.invalid-data-format                                                = Invalid {0} format
error.invalid-device                                                     = Invalid device
error.invalid-encryted-data                                              = Invalid data
error.invalid-low-amount-value                                           = Invalid amount
error.invalid-duplicate-transaction                                      = Duplicate transaction
error.invalid-add-on-amount                                              = Invalid add on amount
error.invalid-add-on-code                                                = Invalid add on code
error.invalid-excel-template                                             = Invalid excel template
error.invalid-file-type                                                  = Invalid file type
error.invalid-input-data                                                 = Invalid {0}
error.invalid-refresh-token                                              = Invalid refresh token
error.invalid-request                                                    = Invalid request
error.invalid-token                                                      = Invalid token
error.invalid-trans-status                                               = Transaction status is invalid
error.invalid-username                                                   = Invalid username
error.invalid-username-or-password                                       = Invalid username or password
error.issue-data-must-not-be-greater-than-now                            = Issue date must not be greater than now
error.issue-place-is-max-length                                          = Issue place max length 255
error.issue-place-is-required                                            = Issue place is required
error.job-max-length=Job maxlength 65
error.jwt-token-is-unsupported                                           = JWT token is unsupported
error.lapnet-account-inactive                                            = Lapnet account is inactive
error.limit-amount-per-day                                               = The amount of transaction on day has exceeded the limit
error.limit-amount-per-month                                             = The amount of transaction on month has exceeded the limit
error.limit-amount-per-transaction                                       = The amount of transaction on 1 time has exceeded the limit
error.limit-otp-attempts                                                 = You have exceeded maximum number of OTP requests attempts. Please try again in a few minutes.
error.link-expire                                                        = Link is expire. Please check the information sent to Email.
error.loan-amount-is-invalid                                             = Loan amount is invalid
error.loan-amount-is-required                                            = Loan amount is required
error.loan-amount-min                                                    = Loan amount min 5 million Lak
error.loan-online-registration-exceeding                                 = You have reached the limit of creating an online loan request. Please try again later.
error.loan-product-id-did-not-existed                                    = Loan product did not existed
error.loan-product-id-invalid                                            = Loan product id is invalid
error.loan-product-id-required                                           = Loan product id is required
error.loan-time-is-required                                              = Loan time is required
error.loan-time-max                                                      = Loan time max 300 month
error.loan-time-min                                                      = Loan time min 12 month
error.ld-code-required                                                   = Loan code is required
error.ld-code-does-not-exist                                             = Account does not exist loan account information
error.mapping-doesnt-exist                                               = Mapping doesnt exist
error.marital-status-invalid                                             = Your marital status invalid
error.marital-status-is-invalid                                          = Marital status is invalid
error.mask                                                               = Wearing mask
error.maximum-number-of-allowable-file-uploads-has-been-exceeded         = Maximum number of allowable file uploads has been exceeded. You can only upload total {0} files for this function.
error.maximum-number-of-otp-reached                                      = Maximum number of OTP reached. Cannot request more than {0} OTPs in under {1} minutes 
error.merchant-account-number-is-invalid                                 = Master Merchant account number must only contain latin letters, numbers, minimum 4 characters,  maximum 50 characters and do not include special characters
error.merchant-code-is-invalid                                           = Merchant code must only contain latin letters, numbers and "_" character
error.merchant-code-is-not-match                                         = Merchant code is invalid
error.merchant-name-is-invalid                                           = Merchant name must only contain latin letters, numbers and spaces
error.merchant.id.required                                               = MerchantId is required
error.merchant.not-found                                                 = Merchant not found
error.message-is-required                                                = Message is required
error.method-argument-not-valid                                          = Method argument not valid
error.more-than-one-data-with-the-given-name-was-found                   = More than one {0} with the given name {1} was found
error.name-of-ekyc-image-is-invalid                                      = Name of ekyc image is invalid
error.name.max-length                                                    = Max length of name is 255
error.transfer-transaction-type-max-length                               = Max length of type is 50
error.nation-code-is-max-length                                          = Nation code is invalid
error.nation-code-is-required                                            = Nation is required
error.nation-is-invalid                                                  = Nationality is invalid
error.national-id-already-existed                                        = National id already exxisted
error.new-password-can-not-be-the-same-as-old-password                   = New password can not be the same as old password
error.new-pw-is-empty                                                    = New password can not be empty
error.no-card-found                                                      = No card found
error.no-data-to-import                                                  = No data to import
error.no-face-found                                                      = No face found
error.no-registry-found                                                  = No registry found
error.no-select-file                                                     = No file chosen
error.not-allow-image-size                                               = Image size is invalid
error.not-enough-five-images                                             = Not enough five images
error.not-verified-ekyc                                                  = You have not verified eKYC. Please go to the counter to change your password.
error.not-verified-ekyc-please-try-again                                 = You have not verified eKYC. Please try again.
error.notification-does-not-exist                                        = Notification does not exist
error.notification.alert-type-is-required                                = Notification alert type is required
error.notification.announcement-type-id.required                         = Announcement type is required
error.notification.content.maxLength                                     = Max length of notification content is 500
error.notification.content.required                                      = Notification content is required
error.notification.expected-time.required                                = Expected time is required
error.notification.receiver-type.required                                = Receiver type is required
error.notification.sender-user-id.required                               = Sender user is required
error.notification.title.maxLength                                       = Max length of notification title is 100
error.notification.title.required                                        = Notification title is required
error.number-of-banner-must-be-greater-than-or-equal                     = Number of banner must be greater than or equal to {0}
error.number-of-banner-must-be-less-than-or-equal                        = Number of banner must be less than or equal to {0}
error.number-of-banners-must-be-equal-to-number-of-embed-links           = The number of banners must be equal to the number of embed links
error.number-of-ekyc-images-is-invalid                                   = The number of eKYC images is invalid
error.occured-authenticating-otp                                         = Error while ooccuring authenticating OTP
error.open-eye-closed-eye                                                = Right eye is closing
error.otp-can-not-be-sent                                                = OTP cannot be sent
error.otp-has-expired                                                    = OTP has expired
error.otp-is-expired                                                     = The OTP has expired. Please click Resend OTP to receive a new code.
error.dotp-is-expired                                                    = The OTP has expired. Please do the DOTP reconfirmation.
error.otp-is-in-correct                                                  = OTP is incorect.
error.otp-is-in-correct-one-time                                         = The OTP code is incorrect. You have 2 entries left.
error.otp-is-in-correct-two-time                                         = The OTP code is incorrect. You have 1 entry left.
error.otp-is-incorrect-or-has-expired                                    = OTP is incorrect or has expired
error.otp-is-invalid-entries-left                                        = The OTP code is incorrect. You have {0} entries left.
error.otp-is-missing                                                     = OTP is missing
error.otp-trans-does-not-exist                                           = OTP transaction doesnt exist
error.parent-department-has-the-same-building-as-children                = Parent department has the same building as children
error.parent-department-has-the-same-project-as-children                 = Parent department has the same project as children
error.passport-issue-date-is-not-eligible                                = The issue date of passport is not eligible. ID cards need to have an issue date of less than 10 years.
error.password-has-expired                                               = Password has expired
error.password-must-not-be-empty                                         = Password must not be empty
error.payment-is-empty                                                   = Payment cannot be empty
error.phone-contact-is-invalid                                           = Phone contact is invalid
error.phone-contact-is-required                                          = Phone contact is required
error.phone-number-does-not-exist                                        = Phone number does not existed
error.phone-number-does-not-match                                        = Phone number information does not match
error.phone-number-has-been-used                                         = Phone number has been used
error.phone-number-is-invalid                                            = Phone number is invalid
error.phone-number-is-required                                           = Phone number is required
error.phone-number-is-duplicate                                          = Phone number is duplicate
error.phone-number-max-length                                            = Max length of phone number is 13
error.phone-number-not-existed-in-telco                                  = The phone number does not exist
error.pilot-format-invalid                                               = Pilot format is invalid
error.place-of-issue-is-empty                                            = Issue place can not be empty
error.place-of-issue-is-invalid                                          = Issue place is invalid
error.place-of-issue-or-current-address-is-too-long                      = Place of issue or current address is too long
error.place-of-origin-is-empty                                           = Place of origin can not be empty
error.place-of-origin-is-invalid                                         = Place of origin is invalid
error.place-of-origin-out-country-is-empty                               = Place of origin out country can not be empty
error.place-of-origin.max-length                                         = Place of birth is no longer than 255 characters
error.place-of-residence-is-empty                                        = Place of residence can not be empty
error.place-of-residence-is-invalid                                      = Place of residence is invalid
error.place-of-residence-out-country.max-length                          = Place of residence out country is no longer than 255 characters
error.place-of-residence.max-length                                      = Place of residence is no longer than 255 characters
error.position-code-is-invalid                                           = Position code must only contain latin letters, numbers and "_" character
error.position-is-required                                               = Position is required
error.position-max-length                                                = Position maxlength 255
error.position.code-existed                                              = Position code already exists
error.position.max-length                                                = Position max length 65 characters
error.project-has-building-limited                                       = The project {0} has building limited. Only {1} buildings can be added.
error.project-of-child-department-must-be-the-same-as-the-parent         = Project of child department must be the same as the parent
error.pw-is-invalid                                                      = Password is invalid
error.qr.can-not-gen-qr-of-payment                                       = Can not generate qr of payment
error.qr.can-not-gen-qr                                                  = Can not generate QR code
error.qr.qr-code-existed                                                 = QR code existed
error.qr.qr-code-not-exist = QR code does not exist or is no longer in use
error.qr.qr-code-expired = The QR code has expired
error.qr.qr-code-unsupported = This QR Code is not supported. Please choose another.
error.qr.qr-code-malformed = QR code image is invalid. Please choose another.
error.qr.qr-content-is-invalid = Content is invalid
error.qr.qr-code-invalid                                                 = Invalid QR code
error.qr.qr-type-is-required                                             = QR type is required
error.qr.qr-type-is-invalid                                              = QR type is invalid
error.qr.qr-code-is-max-length                                           = QR code exceeds the quantity limit
error.qr.you-do-not-have-permission-to-gen-qr-of-this-account            = You do not have permission to generate QR code of this account
error.qr.qr-code-account-has-generated                                   = This account has created the QR code
error.reason-is-required                                                 = Reason is required
error.remark-invalid                                                     = Remark is invalid
error.request-body-not-null                                              = Request body is not null
error.request-for-approval-was-not-successful                            = Request for approval was not successful
error.request-id-invalid                                                 = Request id is invalid
error.resource-has-not-been-set-up-or-has-been-deleted                   = {0} has not been set up or has been deleted
error.revert-data-is-empty                                               = Reversed data cannot be empty
error.referral-rm-code-is-invalid                                        = RM code is invalid
error.referral-user-code-is-invalid                                      = Staff code is invalid
error.referral-type-is-invalid                                           = Type is invalid
error.referral-row                                                       = Row {0}: {1}
error.referral-user-code-has-been-used                                   = User code has been used
error.referral-user-code-is-duplicated                                   = User code is duplicate
error.referral-rm-code-has-been-used                                     = RM code has been used
error.referral-rm-code-is-duplicated                                     = RM code is duplicate
error.role-is-required                                                   = Role is required
error.role-name-has-existed                                              = Role name has existed
error.routine-does-not-exist                                             = Routine does not exist
error.routine-no-response                                                = No response from routine
error.routine-return-null                                                = Routine return null
error.send-mail                                                          = Send mail failed
error.send-mail-already                                                  = You are already send mail
error.sending-and-receiving-bank-code-cannot-be-the-same                 = Sending and receiving bank code cannot be the same
error.seperator-list-invalid                                             = Seperator list is invalid
error.server-not-supported                                               = Server not supported
error.service-indicator-invalid                                          = Service indicator is invalid
error.service-not-configured                                             = Service is not configured
error.service-type-invalid                                               = Service type is invalid
error.service-unavailable                                                = Service unavailable
error.socket-timeout-exception                                           = Request timeout
error.some-data-are-missing                                              = Some data are missing
error.start-date-cannot-after-end-date                                   = The start date cannot after the end date
error.start-date-cannot-before-now                                       = The start date cannot before now
error.start-date-is-required                                             = Start date is required
error.status-is-required                                                 = Status cannot be empty
error.sunglasses                                                         = Wearing glasses
error.system-is-maintaining                                              = System is maintaining
error.system-was-not-supported-this-telco                                = The system does not support this telco
error.t-customer-is-invalid                                              = T customer is invalid
error.telco-code-is-required                                             = Telco code is required
error.telco.telco-code-does-not-existed                                  = Carrier code does not exist
error.the-file-does-not-exist                                            = The file does not exist
error.the-minimum-age-to-register-the-app                                = The minimum age to register the app is from 18 years old
error.the-option-of-question-could-not-be-found                          = The option of question "{0}" could not be found
error.the-password-confirmation-does-not-match                           = The password confirmation does not match
error.the-question-has-only-one-choice                                   = the question "{0}" has only one choice
error.this-file-is-too-large-to-upload                                   = This file is too large to upload. Files must be less than {0}
error.this-role-is-not-assignable                                        = The role {0} is not assignable
error.time-line-between-day                                              = The search period should not exceed 90 days
error.to-account-invalid                                                 = To account invalid
error.to-date-invalid                                                    = To date invalid
error.to-date-is-required                                                = To date is required
error.to-date-must-not-exceed-30-day-compared-to-from-date               = The search period must not exceed 30 days. Please check again
error.to-date-must-not-exceed-90-day-compared-to-from-date               = Maximum search period in 90 days
error.to-member-invalid                                                  = To member invalid
error.to-type-invalid                                                    = To type invalid
error.token-has-expired                                                  = Token has expired
error.token-is-required                                                  = Token is required
error.token-malformed                                                    = Token malformed
error.trans-already-reversed                                             = Transaction is already reversed
error.trans-expired                                                      = Transaction is expired
error.trans-failed                                                       = Transaction failed
error.trans-id-does-not-exist                                            = Tramsaction id doesnt exist
error.trans-not-found                                                    = Transaction not found
error.trans-type-invalid                                                 = Transaction type is invalid
error.transfer-transaction-type-is-required                              = Transfer type is required
error.transaction-amount-max-is-required                                 = The maximum transaction amount cannot be left blank
error.transaction-amount-max-length                                      = Transaction amount length cannot exceed 13 digits
error.transaction-amount-min                                             = Transaction amount must be greater than 0
error.transaction-amount-min-is-required                                 = The minimum transaction amount cannot be left blank
error.transaction-and-phone-number-does-not-match                        = You must verify the phone number
error.transaction-fee-type-is-required                                   = Transaction fee type cannot be blank
error.transaction-has-been-processed                                     = The transaction has been processed
error.transaction-id-is-invalid                                          = Transaction ID is invalid
error.transaction-id.required                                            = Transaction id is required
error.transaction-is-not-successful                                      = Your transaction is not successful
error.transaction.transaction-has-been-completed                         = The transaction has been completed, please do not do it again
error.transfer-money-failed                                              = Transfer money failed
error.transfer-transaction-id.required                                   = Transfer transaction id is required
error.transfer-type-invalid                                              = Transfer type is invalid
error.transfer-type-is-required                                          = Transfer type is required
error.unable-to-delete-active-data                                       = Unable to delete active {0}
error.unable-to-delete-department-an-active-user-under-it                = Unable to lock/delete a department that has a user in it
error.unable-to-delete-position-an-active-user-under-it                  = Unable to delete a position and an active user under it
error.unable-to-lock-merchant-an-active-child-under-it                   = Unable to lock/delete a master merchant and an active child under it
error.unable-to-lock-position-an-active-user-under-it                    = Unable to lock a position and an active user under it
error.unacceptable-trans-fee                                             = Unacceptanle transaction fee
error.updates-have-not-been-approved                                     = Updates have not been approved
error.upload-configuration-is-missing-or-invalid                         = Upload configuration is missing or invalid
error.user-could-not-be-found                                            = User could not be found
error.user-does-not-exist-or-has-been-deactivated                        = User does not exist or has been deactivated
error.user-does-not-have-one-time-password                               = User doesnt have one-time password
error.user-is-not-logged-in                                              = User is not logged in
error.user-must-be-assigned-a-flat                                       = User must be assigned a flat
error.user-with-phone-number-cannot-be-found                             = User with phone number {0} cannot be found
error.username-must-not-be-empty                                         = Username must not be empty
error.device-id-must-not-be-empty                                        = Device id must not be empty
error.device-name-must-not-be-empty                                      = Device name must not be empty
error.dotp-must-not-be-empty                                             = D-otp must not be empty
error.dotp-existed = D-OTP registered device
error.list-dotp-register-must-not-be-empty                               = List dotp register must not be empty
error.trans-data-must-not-be-empty                                       = TransData must not be empty
error.dotp-not-existed = Customer has not registered D-OTP
error.requset-token-invalid = Invalid Request token
error.verify-provisioning = Verify Provisioning failed
error.init-token = Obtaining token failed
error.dotp-max-regis = The maximum number of D-OTP registered devices is {0} devices
error.token-must-not-be-empty                                            = Token must not be empty
error.enc-key-must-not-be-empty                                          = EncKey must not be empty
error.vat-is-required                                                    = VAT value cannot be empty
error.ward-does-not-belong-to-selected-district                          = The selected ward does not belong to the selected district
error.workplace-max-length                                               = Workplace maxlength 500
error.you-are-temporarily-blocked-from-performing-this-action            = You are temporarily blocked from performing this action. Please try again later.
error.you-cannot-create-a-role-has-higher-level-roles-than-yours         = You cannot create a role has higher level (or equal) roles than yours
error.you-cannot-create-the-role-has-lower-level-roles-than-yours        = You cannot create the role has lower level roles than yours
error.you-cannot-delete-the-role-has-higher-level-roles-than-yours       = Unable to delete role whose level is higher than yours
error.you-cannot-lock-yourself                                           = You cannot modify your account
error.you-cannot-modify-your-account                                     = You cannot lock yourself 
error.you-cannot-update-a-role-has-lower-level-roles-than-yours          = You cannot update a role has lower level roles than yours
error.you-cannot-update-the-role-has-lower-level-roles-than-yours        = You cannot update the role has lower level roles than yours
error.you-cannot-update-the-user-has-higher-level-roles-than-yours       = You cannot update the user has higher level roles than yours
error.you-cannot-update-the-user-has-lower-level-roles-than-yours        = You cannot update the user has lower level roles than yours
error.you-cannot-update-your-profile                                     = You cannot update your profile because your profile has been sent.
error.you-do-not-have-permission-to-assign-this-district                 = You do not have permission to assign this district
error.you-do-not-have-permission-to-assign-this-province                 = You do not have permission to assign this province
error.you-do-not-have-permission-to-assign-this-ward                     = You do not have permission to assign this ward
error.you-have-already-created-a-merge-request-for-this-user             = You have already created a merge request for this user who has email or password {0}. Please wait for Admin approval
error.you-have-reached-limit-for-confirm-otp                             = You have reached the limit of OTP validation attempts. Please try again later.
error.you-have-reached-the-limit-for-otp-requests                        = You have reached the limit for otp requests. Please try again in 3 minutes.
error.you-have-reached-the-limit-for-otp                                 = You have reached the limit for otp requests.
error.you-have-reached-the-limit-for-requests                            = You have sent too many requests. Please try again later 10 minus.
error.you-have-reached-the-limit-for-verify-pw-failed                    = You have entered the wrong password more than 3 times. Try again after 60 minus.
error.you-might-not-have-permission-to-assign-this-data                  = You might have not permission to assign this {0}
error.you-might-not-have-permission-to-perform-this-action               = You might not have permission to perform this action
error.you-must-answer-the-question                                       = You must answer the questions: "{0}"
error.you-must-choose-at-least-one-item                                  = You must choose at least one {0}
error.you-must-complete-the-previous-step                                = You must complete the previous step
error.you-must-enter-a-value-for-all-required-fields                     = You must enter a value for all required fields
error.you-must-enter-a-value-for-required-field                          = You must enter a value for {0} field
error.you-must-update-data                                               = You must update {0} before go to next step
error.you-must-verify-your-password                                      = You must verify your password
error.you-must-verify-your-phone-number                                  = Bad request. You must verify your phone number.
error.you-only-can-perform-this-action-on-data-that-has-status           = You only can perform this action on data that has status "{0}"\r\n
error.you-only-can-perform-this-action-on-the-data-that-you-have-created = you only can perform this action on the data that you have created
error.your-account-has-been-locked                                       = Your account has been locked
error.your-account-has-been-temporarily-locked                           = Your account is temporarily locked {0} hours {1} minutes because you entered the wrong password many times
error.your-confirm-password-is-not-match                                 = Your confirm password is not match
error.your-current-password-is-incorrect                                 = Your current password is incorrect
error.your-current-password-is-missing                                   = Your current password is missing
error.your-current-password-is-missing-or-incorrect                      = Your current password is missing or incorrect
error.your-customer-account-number-was-not-register-on-system            = Your customer account number was not register on system
error.your-new-password-is-missing                                       = Your new password is missing
error.your-new-password-is-missing-or-confirm-password-not-match         = Your new password is missing or confirm password not match
error.your-profile-has-been-completed                                    = Your profile has been completed. Please send profile to us.
error.your-registration-was-not-successful                               = Your registration was not successful
error.rm.rm-code-existed = RM code already exists
error.rm.rm-code-not-existed = Referral code does not exist
error.rm.ref-code-existed = Referral code already exists
error.rm.rm-type-invalid = Invalid referrer type
error.rm.rm-type-is-required = Referral type cannot be blank

label.account-number                               = Account number
label.account-type                                 = Account Type
label.account-type.current-account-5               = Recent Accounts
label.account-type.guarantee-deposit-account       = Guarantee deposit account
label.account-type.month-saving-child-account      = Month saving child account
label.account-type.month-saving-individual-account = Month saving individual account
label.account-type.payment-account                 = Payment account
label.account-type.beneficiary-account             = Beneficiary account
label.account-type.saving-account                  = Saving account
label.account-type.saving-corporate-account        = Saving corporate account
label.account-type.saving-individual-account       = Saving individual account
label.account-type.smart-saving-account            = Smart saving account
label.account-type.vostro-account                  = Vostro account
label.account-type.within-one-year-account         = Deposit accounts of other credit institutions
label.active                                       = Active
label.sold                                         = Sold
label.active-status                                = Active status
label.activated                                    = Activated
label.active-token                                 = Active token
label.account-mb                                   = Customer opens account from MB system
label.account-umoney                               = Customer opens account from UMoney
label.address                                      = Address
label.advise                                       = Advise request
label.allow-display-on-portal                      = Allow display on portal
label.amount                                       = Amount
label.currency                                     = Currency
label.amount-transaction                           = Amount
label.appointment                                  = Appointment
label.approval                                     = Approval
label.approval-error                               = Approval error
label.approval-wait                                = Waiting for approval
label.approval-cancel                              = Cancel for approval
label.approval-update-customer                     = approval update customer
label.architect-id                                 = Design consultant unit ID
label.article                                      = Article
label.article-id                                   = Article ID
label.asset                                        = Asset
label.at = at
label.attachment                                   = Attachment
label.avatar-image                                 = Avatar
label.average-price                                = Average price
label.background-image                             = Background
label.bancony-orientation                          = Bancony orientation
label.beneficiary-bank                             = Beneficiary Bank
label.notification                                 = Notification
label.billion                                      = billion
label.building                                     = Buiding
label.building-id                                  = Building ID
label.built-up-area                                = Built-up area
label.campaign                                     = Campaign
label.campaign-id                                  = Campaign Id
label.cancel                                       = Cancel
label.carpet-area                                  = Carpet area
label.cif-number                                   = CIF number
label.confirm-password                             = Confirm password
label.contact-phone-number                         = Contact phone number
label.place-of-origin                              = Place of origin
label.nation-other                                 = Nation other
label.content                                      = Content
label.coordinate                                   = Coordinate
label.create-customer                              = Create Customer
label.credit-account                               = Credit Account
label.customer                                     = Customer
label.customer-account-number                      = Customer account number
label.customer-cif                                 = Customer cif
label.customer-name                                = Customer name
label.customer-title                               = List of customers
label.client-message-id                            = Client message id
label.date                                         = Date
label.date-of-birth                                = Date of birth
label.date-of-issue                                = Date of issue
label.debit-account                                = Debit Account
label.delete-customer                              = Delete customer
label.deleted                                      = Deleted
label.density-of-building                          = Density of building
label.deny-update-customer                         = Deny update customer
label.department                                   = Department
label.department-id                                = Department ID
label.department-introduction                      = Department introduction
label.department-name                              = Department name
label.department-short-name                        = Department short name
label.description                                  = Description
label.design-consultant-unit                       = Design consultant unit
label.design-document                              = Design document
label.district                                     = District
label.document-sample                              = Document sample
label.door-orientation                             = Door orientation
label.draft                                        = Draft
label.email                                        = Email
label.email-customer                               = Email
label.email-or-phone-number                        = Email or phone number
label.error-report                                 = Error report
label.ethnicity                                    = Ethnicity
label.expired                                      = Expired
label.failed                                       = Thất bại
label.family-member                                = Family member
label.fax                                          = Fax
label.file                                         = File
label.file-id                                      = File ID
label.file-import-log                              = File import log
label.financial-proofing                           = Financial proofing
label.flat                                         = Flat
label.flat-id                                      = Flat ID
label.floor                                        = Floor
label.from-date                                    = From date
label.fullname                                     = Full name
label.fullname-customer                            = Full name
label.gallery                                      = Gallery
label.gallery-image                                = Gallery image
label.gender                                       = Gender
label.hand-over-date                               = Hand-over date
label.health-insurance-number                      = Health insurance number
label.highlight-of-flat                            = Highlight of flat
label.house-condition-proofing                     = House-condition proofing
label.householder                                  = Householder
label.identification                               = Identification
label.image                                        = Image
label.inactive                                     = Inactive
label.notActivated                                 = Not activated
label.indentity-paper                              = Identity papers
label.information-template                         = Information template
label.interior                                     = Interior
label.reference                                    = Reference
label.job                                          = Job
label.lapnet-amount                                = Amount
label.lapnet-currency                              = Currency
label.lapnet-fee                                   = Fee
label.lapnet-from-account                          = From account
label.lapnet-from-member                           = From member
label.lapnet-from-user                             = From user
label.lapnet-id                                    = Transaction id
label.lapnet-purpose                               = Purpose
label.lapnet-reference-number                      = Reference number
label.lapnet-time                                  = Time
label.lapnet-to-account                            = To account
label.lapnet-to-member                             = To member
label.lapnet-to-type                               = To type
label.legal-document                               = Legal document
label.loan-amount                                  = Loan amount (LAK)
label.loan-online                                  = Loan online
label.loan-online-title                            = List loan online request
label.loan-purpose                                 = Loan purpose
label.loan-time                                    = Loan time (month)
label.lock-customer                                = Lock customer
label.lock-unlock-delete-customer                  = Lock, unlock, delete customer
label.locked                                       = Locked
label.max-flat-area                                = Max flat area
label.merchant                                     = Merchant
label.merchant-title                               = Merchant list
label.merchant-billing                             = PAYING BILLS
label.merchant-code                                = Merchant code
label.merchant-master-code                         = Master merchant code
label.merchant-master-name                         = Master merchant name
label.merchant-name                                = Merchant name
label.merchant-desc                                = Merchant description
label.merchant-bank                                = Merchant bank
label.merchant-qrcode                              = Pay at
label.merchant-topup                               = MOBILE REFUND PRE-PAYED
label.merchant-umoney                              = Umoney-CASHIN
label.million                                      = million
label.min-flat-area                                = Min flat area
label.name                                         = Name
label.nation                                       = Nation
label.nation-beneficiary                           = Nation
label.new                                          = New
label.news-site                                    = News link
label.note                                         = Note
label.notice                                       = Notice
label.notice-id                                    = Notice ID
label.object-type                                  = Object type
label.on-sale-date                                 = On sale date
label.option                                       = Option
label.option-id                                    = Option ID
label.otp-confirm                                  = Confirm OTP
label.ownership-type                               = Ownership type
label.male                                         = Male
label.female                                       = Female
label.other                                        = Other
label.parent-department                            = Parent department
label.password                                     = Password
label.payment-qr-code                              = Payment QrCode
label.pending                                      = Pending
label.permanent-residence                          = Permanent residence
label.phone-number                                 = Phone number
label.place-of-issue                               = Place of issue
label.point                                        = Point
label.position                                     = Position
label.processing                                   = Chưa thực hiện
label.processing-method                            = Processing method
label.profile                                      = Profile
label.profile-id                                   = Profile ID
label.progress                                     = Progress
label.project                                      = Project
label.project-area                                 = Project area
label.project-id                                   = Project ID
label.project-name                                 = Project name
label.project-template                             = Project template
label.province                                     = Province/City
label.publish-date                                 = Publish date
label.qr-code                                      = QrCode
label.read-time                                    = Read time
label.receiver-notification-list                   = Receiver notification list
label.reject                                       = Reject
label.relationship-to-the-householder              = Relationship to the householder
label.residential-status-proofing                  = Residential status proofing
label.role                                         = Role
label.role-name                                    = Role name
label.referral-staff-code                          = Staff code
label.referral-code                                = Referral code
label.referral-rm-code                             = RM code
label.referrer-code                                = Referrer code
label.referrer-phone-number                        = Referrer phone number
label.referrer-name                                = Referrer's name
label.referral-link                                = Referral link
label.referral-type                                = Type
label.referral-quantity                            = Quantity Referred customer
label.referral-staff                               = Staff
label.referral-collaborators                       = Collaborators
label.referral-title                               = REFERRAL LIST
label.saleable-area                                = Saleable area
label.schedule-time                                = Schedule time
label.serial                                       = No.
label.service                                      = Service
label.service-id                                   = Service ID
label.service-type                                 = Service type
label.short-description                            = Short description
label.situation                                    = Situation
label.situation-proofing                           = Situation proofing
label.status                                       = Status
label.status-draff                                 = Draff
label.status-not-sent                              = Not sent
label.subject                                      = Subject
label.success                                      = Thành công
label.survey                                       = Survey
label.survey-id                                    = Survey ID
label.survey-question                              = Survey question
label.survey-question-group                        = Survey question group
label.survey-question-group-id                     = Survey question group ID
label.survey-question-id                           = Survey question ID
label.template                                     = Template
label.template-customer-notification-file-name     = Customer_Notification_List_%s.xlsx
label.template-customer-registration-file-name     = Customer_Registration_List_%s.xlsx
label.template-loan-file-name                      = Loan_Online_Registration_List_%s.xlsx
label.template-referral-file-name                  = Referral_List_%s.xlsx
label.template-customer-file-name                  = Customer_List_%s.xlsx
label.template-import-referral-file-name           = Referral_Template_%s.xlsx
label.template-master-merchant-file-name           = Master_Merchant_Transaction_History_%s_%s.xlsx
label.template-merchant-file-name                  = Merchant_Transaction_History_%s_%s.xlsx
label.template-merchant-file                       = Merchant_List_%s.xlsx
label.template-master-merchant-export-file-name    = Master_Merchant_List_%s.xlsx
label.template-transaction-umoney-file-name        = Report_Transaction_Umoney_%s.xlsx
label.template-umoney-on-behalf-file-name          = Report_Umoney_On_Behalf_%s.xlsx
label.template-transaction-lapnet-file-name        = Report_Lapnet_%s.xlsx
label.template.master-merchant-title               = MASTER MERCHANT TRANSACTION REPORT
label.template.master-merchant-list-title          = MASTER MERCHANT LIST
label.template.merchant-title                      = MERCHANT TRANSACTION REPORT
label.template.transaction-title                   = TRANSACTION REPORT
label.template.transaction-mmoney-title            = ELECTRICITY AND WATER PAYMENT TRANSACTION REPORT
label.template.notification-limit-title            = TRANSACTION EXCEEDING LIMIT REPORT
label.template.premium-account-revert-title        = REPORT OF FAILED PREMIUM ACCOUNT NUMBER FEE COLLECTION TRANSACTION
label.template.sms-log                             = SMS REPORT
label.template.premium-account-number                             = LUCKY ACCOUNT NUMBER SOLD REPORT
label.template.premium-acc-number                             = LUCKY ACCOUNT NUMBER LOOKUP REPORT
label.template.transaction-qr-pay                  = QRPAY TRANSACTION REPORT
label.thousand                                     = thousand
label.thumbnail-image                              = Thumbnail image
label.timeout                                      = Time out
label.title                                        = Title
label.to-date                                      = To date
label.total-bancony                                = Total bancony
label.total-bedroom                                = Total bedroom
label.total-building                               = Total building
label.total-flat                                   = Total flat
label.total-floor                                  = Total floor
label.total-price                                  = Total price
label.total-remaining-flat                         = Total remaining flat
label.total-room                                   = Total room
label.total-toilet                                 = Total toilet
label.transaction-customer-account-number          = Root account
label.transaction-amount                           = Transaction amount (LAK)
label.transaction-content                          = Transaction content
label.transaction-date                             = Transaction date
label.transaction-date-export                      = Transaction date
label.transaction-date-invalid                     = Transaction date is invalid
label.transaction-receiving-account                = Receiving account
label.transaction-recipient-name                   = Recipient name
label.transaction-currency                         = Currency
label.transaction-transfer-type                    = Transfer type
label.transaction-fee                              = Transaction fee (LAK)
label.transaction-id                               = Transaction ID
label.transaction-total-amount                     = Transaction total amount (LAK)
label.transaction-customer-cif                     = Customer Code (CIF)
label.transaction-result                           = Trading results
label.cross-border                                 = Cross border
label.transfer-transaction                         = Transfer Transaction
label.transfer = Transfer
label.transfer-transaction-account-number          = Transfer money via account number
label.transfer-transaction-qr-code                 = QR code
label.transfer-transaction-type                    = Transfer transaction type
label.transaction-trans-id                         = TransID
label.transfer-transaction-inter-bank              = Inter bank
label.transfer-transaction-internal-bank           = Internal bank
label.transfer-transaction-billing                 = Billing
label.transfer-transaction-cash-in                 = Cash in
label.transfer-transaction-top-up                  = Top up
label.unlock-customer                              = Unlock customer
label.unclosed-customer                            = Unclosed customer
label.update-customer                              = Update Customer
label.url                                          = URL
label.user                                         = User
label.username                                     = Username
label.utility                                      = Utility
label.video                                        = Video
label.waiting                                      = Wating
label.ward                                         = Ward
label.workplace                                    = Workplace
label.you-cannot-merge-yourself                    = You cannot merge yourself
label.code-department                              = Department code
label.name-department                              = Department name
# Your MBLAOS mobile banking registration is successful. Username: ${_USERNAME_} Password: ${PASSWORD} Please sign in and change passwords within 48 hours
label.sms.new-customer-content = Your MBLAOS mobile banking registration is successful.
label.sms.new-customer-title = SMS Registration Successful
# 'Your MBBANK LAO OTP code for online service is ${OTP_CODE}. DO NOT share OTP code with anyone. ${TIMESTAMP}'
label.sms.otp-content = <#> Your MBBANK LAO OTP code for online service is {0}. DO NOT share OTP code with anyone. {1} vPtXkDCLPIw
label.sms.otp-title = SMS OTP
# Username ${_USERNAME_} lock successful. ${TIMESTAMP}
label.sms.approval-lock-content = Username {0} lock successful. {1}
label.sms.approval-lock-title = SMS Lock Successful
label.sms.approval-unlock-content = Username {0} unlock successful. {1}
label.sms.approval-unclosed-content = Username {0} unclosed successful. {1}
label.sms.approval-unlock-title = SMS Unlock Successful
label.sms.approval-unclosed-title = SMS Unclosed Successful
label.sms.approval-delete-content = Username {0} delete successful. {1}
label.sms.approval-delete-title = SMS Delete Successful
# Username ${_USERNAME_} and Password ${PASSWORD}
label.sms.approval-create-content = Username: {0} \
  Password: {1}
label.sms.approval-create-title = SMS Create Account Successful
label.sms.create-customer-client-umoney-title = SMS successfully created a new umoney customer account.
label.sms.create-customer-client-umoney-content = You have successfully registered for App MB Laos. The username: {0}. Password is {1}. The password is valid for 48 hours. Download and Login now to receive 20,000 LAK: {2}
label.sms.new-device-login-notice-content = You sign in from a place where you usually do not sign in.
label.sms.new-device-login-notice-title = Login from new device
# Your MBLAOS mobile banking passwords is changed successfully. New passwords: ${PASSWORD}
label.sms.reset-password-content = Your MBLAOS mobile banking passwords is changed successfully. New passwords: {0}
label.sms.reset-password-title = SMS Reset Password
# Your password is expired in ${DAY} days. Please change your password. ${TIMESTAMP}
label.sms.password-expired-notice-content = Your password is expired in {0} days. Please change your password. {1}
label.sms.password-expired-notice-title = Your password has expired
# Your MBLAOS mobile banking ${ACCOUNT_NUMBER} is stand by temporary. Please contact with your bank for supporting. Thank you. ${TIMESTAMP}
label.sms.account-is-temp-locked-content = Your MBLAOS mobile banking {0} is stand by temporary. Please contact with your bank for supporting. Thank you. {1}
label.sms.account-is-temp-locked-title = Your account is temporarily locked
label.sms.reissue-password-content = Password reset successful! Your new password is: {0} \
  The password is valid for 48 hours. Please do not share your activation password for security reason
label.sms.reissue-password-title = SMS Reset Password
# Your MBLAOS account ${ACCOUNT_NUMBER} is closed successfully. Thank you for being our valued customer and we are so grateful for the pleasure of serving you. ${TIMESTAMP}
label.sms.account-is-closed-content = Your MBLAOS account {0} is closed successfully. Thank you for being our valued customer and we are so grateful for the pleasure of serving you. {1}
label.sms.account-is-closed-title = Your account is closed
label.noti.payment-merchant-title = Notice of balance fluctuations
label.noti.payment-lapnet-title = Notice of balance fluctuations
label.noti.balance-change-receive-money-title = Notice of balance fluctuations
label.noti.change-saving-account-title = Notification of savings account
label.noti.balance-change-transfer-money-title = Notice of balance fluctuations
label.noti.password-expired-notice-title = Notice Password Expired
# Your password is expired in {DAY} days. Please change your password. ${TIMESTAMP}
label.noti.password-expired-notice-content = Your password will expire in the next {0} days. Please change to a new password. {1}

message.failed                                          = Failed
message.first                                           = First
message.login-successful                                = Login successful
message.otp-has-been-sent-to-your-mobile-number         = OTP has been sent to your mobile number
message.qr-payment                                      = QR Payment
message.request-for-approval-was-successful             = Request for approval was successful
message.second                                          = Second
message.server-has-been-started                         = Server has been started
message.successful                                      = Successful
message.third                                           = Third
message.topup                                           = Topup
message.transfer-money                                  = Transfer money
message.you-have-successfully-changed-password          = You have successfully changed password
message.you-have-successfully-registered-for-an-account = You have successfully registered for an account
message.you-have-successfully-verified-your-account     = You have successfully verified your account
error.otp-type-is-missing=Otp type is missing
label.template-transaction-history-file-name=Insurance_List_%s.xlsx
label.template-international-transaction-history-file-name= International_Transaction_List_%s.xlsx
label.template-lapnet-umoney-file-name=Lapnet_Umoney_report_%s.xlsx
label.List-of-failed-sms-collection-accounts=List_of_failed_SMS_collection_accounts_%s.xlsx
error.otp-transaction-type-required=OTP transaction type is required
error.device-not-registered-dotp=Your device has not registered DOTP
error.bank-code-not-existed-contact-support=Bank is not supported, please call 190000 for support.
error.verification-code-is-expired = The verification code has expired. Please go through the authentication process again.
error.app-version-already-exist=Version code and version name must not simultaneously match with another record
label.template-version-file-name=Export_Version_List_%s.xlsx
label.version-name=Version name
label.version-code=Version code
label.version-force-update=Force update
label.version-manual-update=Manual update
label.version-notification=Notification
label.version-installed=Installed
label.version-url-store=Url store
label.version-active-status=Released
label.version-incative-status=Not released yet
error.version-code-max-length=Max length of version code is 10
error.version-name-max-length=Max length of version name is 10
error.version-name-is-invalid=Version name is invalid
error.version-release-date-is-invalid=Version release date is invalid
error.version-status-is-invalid=Version status is invalid
error.app-version-does-not-exist=Version does not exist
error.version-description-max-length=Max length of version description is 500
error.version-id-must-not-be-empty=Version id must not be empty
error.version-operating-system-is-required=Operating system information must not be empty
label.version-created-date=Created date
label.version-title=List of versions
error.version-release-date-is-required=Version release date must not be empty
label.boolean-yes=Yes
label.boolean-no=No
error.package-empty=
error.certificate-empty=Certificate must not be empty
error.delivery-code-empty=Delivery code must not be empty
error.vehicle-code-empty=Vehicle code must not be empty
error.lvi-delivery-code-not-existed=Delivery code is not existed
error.billing-type-is-required=Billing type cannot be blank
error.billing-type-is-invalid=Billing type is invalid
error.billing-type-is-not-supported=The network operator does not support paying this type of bill
error.telco-not-found=Telco could not be found
label.identification-no=Identification no
label.product-type=Product type
label.package-code=Package code
label.vehicle-code=Car type
label.premium=Pre-tax fee (LAK)
label.discount=Discount (%)
label.pay-total=Payment (LAK)
label.cif-term-month-payment=Monthly Savings for Individuals
label.cif-term-pay-interest=Savings for Individuals
label.cif-unlimited-saving=Savings Account
label.cif-smart-saving=Smart savings account
label.buy-insurance=Buy insurance LVI
label.buy-insurance-renew=Renewal of insurance LVI
error.telco-card-id-is-required=The information of the recharge card is required
error.customer-code-does-not-exist = Customer code does not exits
error.billing.billing-code-is-required=Please enter billing code
label.service_name = Service name
label.approved_by = Approved By
label.approved_at = Approved At
label.config-trans-limit-title = Configuration Transaction Limit List
label.sector-id = SectorType
error.expected-time-is-valid = Time overlaps with previous limit configuration
error.configuration-transaction-limit-does-not-exist = Configure transaction limit does not exist
error.you-status-do-not-have-permission-approve = Your status is not approved
label.transaction-limit-file-name = Configuration_Transaction_Limit_List_%s.xlsx
error.service-name-is-required= Service name is required
error.sector-id-is-required = Sector id is required
error.max-transfer-amt-of-date-is-required = Max transfer amount of date is required
error.start-date-is-valid = The start time must be at least 2 minutes longer than the current time
error.transaction-limit-status-is-valid = Transaction limit status is valid
error.transaction-limit-id-does-not-exist = Transaction limit id does not exist
error.transfer-type-list-is-required = Transfer type list is required
error.limit-amount-per-year = The amount of transaction on year has exceeded the limit
error.transfer-list-is-invalid = Transfer list is invalid
error.transfer-type-list-length-is-invalid = Invalid transaction types list length
error.transfer-type-is-duplicate = Duplicate transaction type
error.billing-does-not-exist = Billing does not exist
label.campaign-name=Campaign name
label.start-date=Start date
label.end-date=End date
label.template-campaign-file-name=Report_campaign_%s.xlsx
error.campaign-position-is-required=Campaign position is required
label.campaign-title=CAMPAIGN LIST
error.transaction-limit-of-day-min = Transaction limit of day must be greater than 0
error.you-status-do-not-have-permission-update = Status not allowed to update
error.transaction-limit-of-month-min = Transaction limit of month must be greater than 0
error.transaction-limit-of-year-min = Transaction limit of year must be greater than 0
error.transaction-limit-amount-min = Transaction limit amount be greater than 0
error.max-transaction-limit-of-month-is-required = Transaction limit of month is required
error.max-transaction-amt-is-valid = The transaction limit must be in the following order: day =< month =< year
error.max-transfer-amount-is-required = Max transfer amount is required
label.vehicle-insurance=Vehicle insurance
label.health-insurance=Health insurance
error.vehicle-code-invalid=Vehicle code is invalid
label.approval-success= Approval success
error.package-invalid=Package code is invalid
error.delivery-code-invalid=Delivery code is invalid
error.certificate-invalid=Certificate is invalid
error.already-exist-inactive-version=An unreleased version already exists
error.insured-name-csv-injection=Customer name cannot start with: =, +, - , @, 0x09, 0x0D
error.insured-phone-csv-injection=Phone number cannot start with: =, +, - , @, 0x09, 0x0D
error.id-number-csv-injection=Id number cannot start with: =, +, - , @, 0x09, 0x0D
error.delivery-code-csv-injection=Delivery code cannot start with: =, +, - , @, 0x09, 0x0D
error.package-code-csv-injection=Package code cannot start with: =, +, - , @, 0x09, 0x0D
error.vehicle-code-csv-injection=Vehicle code cannot start with: =, +, - , @, 0x09, 0x0D
error.certificate-csv-injection=Certificate cannot start with: =, +, - , @, 0x09, 0x0D
error.inappropriate-file-format=File format does not match, only png/jpg is accepted
error.banner-and-link-are-required=You have not entered the path or the path is invalid
error.campaign-name-csv-injection=Campaign name must not start with: =, +, - , @, 0x09, 0x0D
label.campaign-position=Display position
label.campaign-home=Home
label.campaign-login=Login
error.service-name-is-valid = Service name is required
error.end-date-must-be-greater-than-start-date = End date must be greater than start date
error.transaction-limit-id-is-required = Transaction limit id is required
error.reason-length-is-invalid = Reason length is valid
error.service-name-length-is-invalid = Service name length is valid

label.reset-password=Reset password
label.change-password=Change Password
label.register=Register an account
label.device-verify=Device Authentication
label.change-phone-number=Change phone number
label.customer-verify=Customer Verify
label.lock-account=Lock your account
label.verify-sms-dotp=Authentication sms DOTP
label.verify-old-device-on-new-device=Authentication DOTP old device on new device
label.verify-delete-dotp=DOTP unsubscribe verification
label.login=Login
label.verify-registration=Otp verification upon registration
label.cancel-approval=Approve to cancel cms account 
label.create-approval=Approve to create new cms account
label.lock-approval=Approve to lock cms account 
label.unlock-approval=Approve to unlock cms account
label.message-type=Message type
label.quantity-message-success=Number of messages sent success
label.quantity-message-fail=Number of messages sent fail
label.total-message=Total number of messages sent
label.mblaos-sms=SMS OTP BE sent
label.t24-sms=SMS balance fluctuations
label.t24-sms-loan-balance=SMS loan balance

label.sms.approval-update-username-content=The new username for your MB Bank Laos account is: {0}
label.sms.approval-update-username-title=SMS update username successfully
error.device-os-is-invalid=Device OS is invalid
error.new-version-is-required=Please update to the latest version to use the app
error.already-exist-active-campaign=Can't unlock because {0} campaign is still active
error.file-is-required=Image files cannot be left blank
label.campaign-description=Campaign description
error.cannot-update-while-dotp-exists=The phone number cannot be updated while the D-OTP exists. Please cancel the D-OTP service first
error.service-type-is-required = Service type is required
label.topup = Topup
label.billing = Billing
error.merchant-code-is-not-valid = Merchant code is not valid for the service being selected
label.transfer-transaction-cash-out=Cash out Umoney
label.transfer-transaction-lapnet-qr-code=Transfer lapnet via qr code


label.reference-id = Reference Number
error.customer.id-card-number-max-length=Valid identification number has a minimum of 8 characters and a maximum of 15 characters
error.customer.id-card-number.required            = Identity card number required
error.customer.id-card-type-id.invalid            = Identity card type id invalid
error.customer.id-card-type-id.required           = Identity card type id required
error.customer.issue-date-cannot-greater-than-now = Issue date cannot greater than now
error.customer.issue-date.required                = Issue date is required
error.customer.phone-number.invalid               = Identity email number max length 100
error.customer.reason-max-length                  = Customer reason max length 255
error.customer.resident-status.invalid            = Resident status invalid
error.customer.sector-id-invalid                  = Customer sector id is invalid
error.customer.sector-id-required                 = Customer sector id is required
error.customer.staff-code.invalid                 = Staff code invalid
error.customer.marital-status.invalid=Marital status invalid
label.transfer-transaction-insurance=LVI insurance payment
error.sector-invalid=Sector is invalid
label.mb-discount=MB discount (%)
label.lvi-discount=LVI discount (%)
label.mb-fee=MB transaction fee (LAK)
label.lvi-paid=Amount LVI returns (LAK)
error.campaign-name-has-special-character=Campaign names cannot contain special characters

label.resident-status = Resident status
label.id-card-type-id = Id card type id
label.id-card-type-number = Id card type number
label.current-address = Current address
label.place-of-resident-out-country = Place of resident out country
label.martial-status = Martial status
label.customer-info-title = List of customer information
label.identification-image = Photo identification documents
label.place-of-residence = Place of residence
label.field=Field
label.error-detail=Error detail
error.data-is-duplicate={0} in the record is duplicated
error.data-is-required={0} is required
label.import-customer-failed-list=Import_customer_failed_list_%s.txt
error.can-not-delete-active-account=Can not delete active account
error.cashout-account.not-existed=Cashout account is not existed
error.empty-excel-template=Invalid input, data cannot be empty.
label.passport=Passport
label.identify-card=Identify card
label.married=Married
label.single=Single
label.temporary=Temporary
label.permanent=Permanent
error.cashout-account.existed=Account number or account name is existed
error.account-name-required=Account name is required
error.account-name.max-length=Account name max lenght is 65
label.campaign-other=Other
error.fullname-customer-is-empty=Customer name cannot be blank
error.customer.sector-id.required=
error.fullname-customer-is-max-length=The customer name cannot exceed 100 characters
error.customer.id-card-number.invalid=Identity card number only contains numbers and letters
error.customer.sector-id.invalid=
error.failed-to-import=Import failed. Please check again
label.list-number-of-transaction = List_of_Transaction_Quantities_%s.xlsx
label.total-transaction-debit-success = Number of successful debit transactions
label.total-transaction-credit-success = Number of successful credit transactions
label.list-of-transaction-of-transaction-quantity-title = List of Transaction Quantities
error.number-of-transaction-min = Minimum transaction number maximum 5 characters
error.number-of-transaction-max = The maximum number of transactions is 5 characters
error.customer.data-exceed-max-length=Each customer is allowed a maximum of {0} {1}
error.number-of-transaction-is-invalid = The minimum transaction quantity field is always smaller than the largest transaction quantity field
error.number-of-transaction-min-is-invalid = Minimum minimum transaction amount must be greater than 0
error.number-of-transaction-max-is-invalid = The minimum number of largest transactions must be greater than 0
label.staff-code=Staff code
error.customer.work-place-max-length=Customer's work place max length is 255
error.fullname-customer-is-invalid=Customer fullname is invalid
error.customer-image-is-invalid=Invalid image: The card data image is invalid
error.async-date-is-required=Async date information cannot be left blank
error.time-zone-is-invalid = Invalid time zone
error.bank-code-invalid=Bank code is invalid
error.interest-rate-is-required=Interest rate is required
error.tenor-is-required=Tenor is required
error.finalization-method-is-required=finalization method is required
error.receive-acc-number-is-required=Receive account number is required
error.type-acc-saving-is-required=Account saving type is required
label.start-time=Open time
label.account-owner=Account owner
label.saving-account-type=Saving type
label.status-finalized=Finalized
label.saving-account.fixed-type=Fixed saving
label.saving-account.accumulated-type=Accumulated saving
label.saving-account.no-term-type=No term saving

error.account-has-not-change-password = Account password expired. Please change new password

error.amount-fixed-acc-saving-is-invalid=Minimum Fixed Savings Deposit Amount 1,000,000 LAK

error.currency-is-required = Current is required
error.customer-id-or-customer-account-is-required = Customer id or customer account cannot be blank
error.info-interest-not-existed = Interest rate and term information does not exist
error.source-account-is-required = Source account is required
error.amount-currency-is-required = Amount currency is required
error.product-category-is-required = Product category is required
error.maturity-instruction-is-required = Maturity instruction is required
error.deposit-min = Minimum transaction amount {0} LAK
error.tenor-not-existed = Savings term interest rates do not exist
error.create-interest-is-invalid = Creating a savings account failed
error.saving-account-is-required = Saving account is required
error.saving-account-not-existed = Saving account not existed
error.saving-account-id-not-existed = Savings account id does not exist
error.closed-interest-was-not-successful = Settlement failed
error.list-in-rate-does-not-existed = List of interest rates and terms does not exist
error.content-is-required = Content is required
label.rotation-of-princial-and-interest = Revolving principal and interest
label.root-rotation = Root rotation
label.no-rotation = No rotation
label.dotp = D-OTP
message.closed-interest = Settlement
message.create-interest = Open a savings account
label.month = Month
label.year = Year
label.saving-account-title = List of savings accounts
message.saving-account-title = List of savings accounts
error.tenor-period-not-existed = Tenor period does not exist
error.info-saving-account-not-existed = Savings account information does not exist
label.end-of-term = End of the term
error.tenor-is-invalid = Tenor is invalid
error-deposit-more-is-invalid = You can only deposit additional money 30 days before the due date.
error.remark-max-length = Content maximum 35 characters
error.saving-account-number-is-not-owned-by-the-customer = The savings account is not owned by the user
label.block = Block
error.saving-account-is-block = Savings account frozen
label.interest-amt = Interest payment
label.auto-settlement = Automatic settlement
label.closed-saving-account = Close savings account
label.payment-of-principal-and-interest = payment of principal and interest
label.fixed = Fixed
label.accumulated = Accumulated
error.saving-account-or-receive-principal-not-match-t24 = SavingAccountNumber or ReceivePrincipalAccount not match in T24
error.client-mess-id-is-required=CLIENT_MESSAGE_ID is required.
error.cob-system = You cannot perform settlement during system downtime. Please try again later.
error.transaction-limit-is-required = Transaction limit is required
error.saving-account-type-is-invalid = Saving account type is invalid
error.account-has-not-verify-ekyc = You have not authenticated ekyc so this transaction cannot be performed
error.an-unexpected-error-has-occurred-umoney=The balance or limit in the account is not enough to make payments, please check your account again.
error.configuration-type-is-required= Configuration type is required.
error.customer-id-does-not-existed=CIF number does not exist on T24 system.
error.api-key-cannot-empty = Api key cannot empty
error.amount-is-more-than-debit = Your amount is more than debit
error.fees-is-invalid = Invalid charge configuration
error.ewid-is-required = Vendor id is required
error.supplier-info-not-existed = Supplier information does not exist
error.province-code-is-required = Province code is required
label.water-billing = Water payment
label.electric-billing = Electricity payment
label.invoice-number= Invoice Number (AccNo)
label.pro-code= Province code (ProCode)
label.remark= Payment content (Remark)
label.title-la= Lao title (Title_LA)
label.acc-name= Account name (AccName)
error.title-is-required = Title is required
error.language-is-required = Language is required
error-interest-rate-does-not-existed = Interest rate does not existed
error.interest-rate-language-already-exists = Interest rate language already exists
error.interest-rate-detail-id-is-required = Interest rate detail id is required
error.interest-rate-id-required = Interest rate id required
error.interest-rate-detail-id-does-not-existed = Interest rate detail id does not existed
label.interest-rate = Interest rate
error.interest-rate-is-already-existed-with-status = An interest with active status already exists.
error.data-not-existed=Data does not exist or is no longer in use.
label.category-internal= Internal news
label.category-inter= News
label.discount-fixed= Fixed discount (LAK)
error.transaction-id-is-duplicate = TransactionID is duplicated..
error.fee-schedule-id-is-required = Fee schedule Id is required
error.fee-schedule-does-not-existed = Fee schedule does not existed
error.language-already-exists = Language already exists
error.fee-schedule-detail-does-not-existed = Fee schedule detail does not existed
label.fee-schedule = Fee Schedule
error.language-is-invalid = Language is invalid
error.fee-schedule-is-already-existed-with-status = The fee schedule already exists in an active status
label.not-contact-yet = Not yet contacted
label.contacted = Contacted
label.limit-transaction = Transaction limit
label.limit-per-day = Limit per day
label.limit-per-month = Limit per month
label.limit-per-year = Limit per year
label.exceeded-daily-limit = Exceeded daily limit
label.exceeded-monthly-limit = Exceeded monthly limit
label.customer-no-transaction-file-name = Customer_No_Transaction_limit_List_%s.xlsx
label.customer-no-transaction-title = Customer list does not generate transactions
label.noti.cancel-customer-account = ແຈ້ງການປິດບັນຊີລູກຄ້າ
label.content-cancel-customer-account = <p>ບັນຊີຂອງທ່ານຖືກລ໋ອກ,ເພາະທ່ານບໍ່ມີການເຄື່ອນໄຫວທຸລະກຳພາຍໃນໄລຍະເວລາ 180 ວັນ,ກະລຸນາເຂົ້າໄປປະກອບເອກະສານຢູ່ທີ່ປ່ອງບໍລິການເພື່ອປົດລ໋ອກ</p>
label.closed-account = Close Account
error.closed-list-customer = Close Account {0} failed!
error.list-customer-is-pending = {0}is pending. You cannot perform this action.
label.noti.no-transaction = ແຈ້ງ​ການ​ບໍ່​ມີ​ການ​ເຮັດ​ທຸ​ລະ​ກໍາ​
label.noti.content-no-transaction =<p>ບັນຊີຂອງທ່ານຈະຖືກລ໋ອກ, ຖ້າຫາກບໍ່ມີການເຄື່ອນໄຫວທຸລະກຳເທິງແອັບພາຍໃນໄລຍະເວລາ 180 ວັນ</p>
label.approval-status = Approval status
label.contact-status = Contact status
label.customer-manual-activation-title = List of manually activated customers
label.template-manual-customer-activation-file-name = Customer_Manual_Activation_List_%s.xlsx
error.list-customer-activation = Activation of account {0} failed
error.list-customer-id-is-invalid = Customer {0} is invalid. Please check your request again.
error.merchants-max-length = You are only allowed to create a maximum of {0} merchants.
error.merchant-name-is-required = Merchant name is required
error.merchant-account-number-is-required = Merchant account number is required
label.merchant-create-origin = Created At
label.customer-noti-template-file-name = Customer_Noti_Template_List_%s.xlsx
label.customer-notification-title = List of sending customer notifications
error.cif-does-not-existed = Customer cif does not existed

error.color-code.can.be.bigger = Color code cannot be larger than 20 characters
error.color-code.required = Color code cannot be empty
label.image-login-app = Login background image
label.data-existed = Data {0} already exists or in use.
label.template-premium-number-structure-file-name = Premium_Number_Structure_List_%s.xlsx
label.number-group = Number group
label.number-structure = Number store
label.structure-name = Structure name
label.price = Price
label.listed-price = Listed price
label.payment-price = Payment price
label.pattern = Pattern
label.premium-number-structure-title = List of lucky number structures
error.master-merchant-existed = Master merchant already exists
error.master-merchant-name-max-length = Master merchant name maximum 50 characters
error.merchant-name-max-length = Merchant name maximum 50 characters
label.saving-account-deposits = Deposits
error.cif-is-required = Cif is required
error.merchant-account-number-already-existed            = Merchant account number already existed
error.merchant-is-invalid = There exists a master merchant that has been locked to your account. Contact 021 752 777 for support
error.cannot-update-merchant-from-origin-app = Unable to update account number with merchant created from app
error.partner-id-is-invalid          = Partner Id is invalid
error.number-group-is-invalid = Number group is invalid
error.pattern-is-required = Pattern is required
error.premium-number-structure-does-not-existed = premium-number-structure-does-not-existed
error.number-structure-is-required = Number structure is required
error.number-group-is-required = Number group is required
error.premium-structure-id-is-required = Lucky structure id is required

label.template-special-premium-account-number-file-name = Special_Premium_Account_Number_List_%s.xlsx
label.special-premium-account-number-title=List of special lucky number
label.hide=Hide
label.hide-note=Hide value: \n Show \n Hide
label.status-note=Status value: \n Sold \n Suspended \n Active \n Inactive

error.discount-is-invalid = Discount is invalid
error.discount-is-required = Discount is required
error.list-special-acc-number-is-required = The number list cannot be empty
error.special-premium-acc-number-is-required = Lucky account number is required
error.premium-acc-number-id-is-required = Lucky account number id is required
error.price-is-required = Price is required
error.premium-acc-number-not-match = Lucky account number {0} not match number structure
label.premium-account-number = Lucky account number
error.premium-acc-number-is-already-existed = Lucky account number {0} is already existed
error.premium-acc-number-is-not-existed = Lucky account number  {0} is not existed
error.premium-acc-number-duplicate = Lucky account number  {0} is not duplicate
label.insurance = Insurance
label.billing-internet = Network billing
label.billing-water = Electricity and water bill
label.international-payment = International billing
label.saving-account = Savings
label.fixed-type = Fixed
label.accumulated-type=Accumulated
label.no-term-type=Term
error.date-search-invalid= Invalid lookup date
error.time-out = Time out exception
error.bank-account-is-required = Bank account is required
error.number-structure-not-match-number-group = The number structure does not match the number group
error.customer-support-phone-number-is-required = Customer support phone number is required
error.customer-support-is-already-existed = customer support is already existed
error.customer-support-does-not-existed = customer-support does not existed
error.customer-support-phone-number-max-length = Up to 10 customer support phone numbers
error.number-structure-name-length-is-invalid = Invalid number structure name length
error.special-acc-number-max-length = Maximum {0} special number accounts
error.hidden-acc-number-max-length =  Maximum {0} hidden number accounts
label.number-group-value = {0} number
error.phone-number-customer-support-min-length = Minimum 8-digit customer support phone number
error.phone-number-customer-support-max-length = Customer support phone number maximum 30 numbers
error.date-of-birth-is-not-eligible = Date of birth is under 18 years old.
error.number-structure-name-is-required = The numeric structure name is required
error.premium-acc-number-structure-does-not-existed = Lucky account number structure id does not exist
label.partner-id = Partner ID
error.amount-is-invalid = Amount is invalid
error.transaction-time-is-required = Transaction time is required
error.qr-code-is-required = QR code is required
error.transaction-time-does-not-match = The transaction time is not in the correct format dd-MM-yyyy HH:mm:ss.SS
error.account-type-is-required = Account type is required
error.beneficiary-merchant-account-number-is-required = Beneficiary merchant account number is required
error.place-of-receive-money-is-required = Place of receive money is required
error.merchant-name-is-already-existed = Merchant name already exists with account number
error.merchant-name-does-not-duplicate = Merchant names cannot be the same
error.customer-phone-number-is-already-existed = Customer phone number is already existed
error.username-does-not-exist = Username does not exist
label.other-branch = Other Branch
label.other-bank = Other Bank
error.transaction-fee-min = Transaction fees must be greater than 0
error.transaction-fee-does-not-match = Transaction fee does not match
error.signature-is-already-existed = Signature is already existed
error.signature-file-is-required = Signature file is required
error.signature-file-does-not-existed = Signature file does not existed
error.transaction-type-is-required = Entry type is required
error.dr-cr-is-required = Transaction type is required
error.fee-cannot-less-than = The fee amount must be greater than or equal to 0
error.beneficiary-account-number-max-length = Beneficiary account number up to 50 characters
error.transaction-id-max-length = Transaction code up to 50 characters
error.entry-type-is-max-length = Entry type maximum 50 characters
error.transaction-fee-max-length = Transaction fee up to 19 characters
error.dr-cr-max-length = Dr cr max 50 characters
error.phone-max-length = Phone number up to 75 characters
error.sector-max-length = Sector max 10 characters
error.nat-id-type-max-length = Nation ID type up to 19 characters
error.cif-max-length = Cif max 255 characters
error.message-max-length = Message max 255 characters
error.transaction-fee-is-invalid = Transaction fee is invalid
error.dr-cris-invalid  = DrCr is invalid
error.balance-max-length = Balance max 19 characters
error.balance-is-invalid = Balance is invalid
error.notifications-is-max-length = Notifications up to 100 records
error.notifications-is-min-length = List of notifications of at least 1 record
error.amount-max-length = Maximum transaction amount 19 characters
error.phone-does-not-exist = Phone number {0} does not exist for customer {1}
error.sector-not-match-with-cif = Sector {0} not match with cif {1}
error.benefit-account-number-not-match = Beneficiary account number {0} does not exist for customer {1}

error.service-pack-maintenance = The service is currently down for maintenance. Please try again later.
error.clientId-existed = ClientID already exists
error.service-pack-existed = Service type already exists
error.sms-balance-is-already-existed = Information has been registered to receive SMS
error.customer-id-mobile-number-does-not-match = Customer id, mobile number does not match
error.sms-balance-does-not-existed = Unregistered SMS information
label.sms-balance-title = List of services on/off sms balance fluctuations
label.registration-date = Registration date
label.cancellation-date = Cancellation date
label.sms-balance-register-success-title = Register to receive SMS banking notifications successfully
label.sms-balance-register-success-content = You have successfully registered to receive SMS balance changes !
label.sms-balance-cancel-success-title = Unsubscribe to receive SMS banking notifications successfully
label.cancel-sms-balance-success-content = You have successfully unsubscribed from receiving balance change SMS !
label.account-register = Registered account

label.umoney = Umoney Partner
label.mbbank = Mbbank T24 Partner
label.qr-payment = Internal merchant payment
label.pstn-billing = Postpaid landline bill payment
label.postpaid-mobile-billing = Postpaid mobile bill payment
label.internet-billing = Internet bill payment
label.time = Time
label.registration-time = Registration time
label.cancellation-time = Cancellation time
error.api-is-required = API information is required
error.service-pack-invalid = This service is currently unavailable
error.service-pack-type = Service type is required
error.delete-client = You cannot delete client {0} because this client is currently associated with one or more services.
label.fee-type = For fee configuration.
label.fee-type-other = For other configuration.
error.deny-permission-service-pack = You do not have permission to operate the service, please contact the administrator for assistance.
error.premium-account-type = Lucky account type is required
error.expect-string-is-required = Expect string is required
error.expect-string-is-invalid = The desired number sequence consists of only numeric characters with a length between 4 and 10.
error.rm.ref-code-is-required = Referral code is required
error.premium-acc-number-is-required = Lucky number account is required
error.premium-acc-number-is-invalid = Lucky number account is invalid
error.price-is-invalid = Price is invalid
error.create-premium-account-number = Create lucky number account not successfully
error.premium-account-number-already-existed = Lucky number account already existed
error.user-does-not-existed = User doesn't existed
label.original-price = List price (LAK)
label.total-price-premium = Payment price(LAK)
label.open-date = Open date(DD-MM-YYYY)
label.length = Length
label.structure = Structure
label.pending-premium = Pending
error.premium-acc-number-does-not-existed = Lucky number account does not existed
error.premium-acc-number-structure-already-existed = Lucky number account is already existed
label.premium-acc-number-pending-status = Suspended
label.premium-acc-number-take-back-status = Revoked
label.create-premium-acc-number = Open a lucky number account
label.create-premium-acc-number-successfully = You have opened a lucky number account: {0}. Contact support: 021 752 777
label.title-create-premium-acc-number-success = Open a lucky number account successfully
label.account-length-is-required = The account number group is required
label.account-length-does-not-existed = The account number group does not exist
error.account-length-is-invalid = Length cannot be less than the length of the sequence
label.number-type = Number type
label.special-number = Special Number
label.hidden-number = Hidden Number
label.length-is-required = Length of number is required
label.length-is-invalid = The length of the number sequence must be 4,5,9,10
error.length-is-required = Structure length is required
label.number-group-v1 = VIP Group 1
label.number-group-v2 = VIP Group 2
label.number-group-v3 = VIP Group 3
label.number-group-v4 = VIP Group 4
label.number-group-v5 = VIP Group 5
label.number-group-v6 = VIP Group 6
error.number-group-match = Price is not valid for number group
error.group-name-is-required = Group name is required
error.min-price-is-required = Min price is required
error.max-price-is-required = Max price is required
error.number-group-id-is-required = Number group id is required
error.premium-acc-number-is-not-match = Lucky number account must be from 4 to 11 digits
error.lucky-number-account-type-is-invalid = Invalid lucky number account type
error.lucky-number-account-match-phone-is-invalid =  Each customer can only buy one lucky account number that matches the phone number.
label.number = number
label.template-number-group-file-name = Number_Group_List_%s.xlsx
label.number-group-title = List of number group
label.number-group-code = Code
label.number-group-name = Number group name
label.min-price = Min price
label.max-price = Max price
label.last-modified-by = Last modified by
label.last-modified-date = Last modified date
label.created-by = Created by
error.consultant-code-is-incorrect = Incorrect consultant code
label.unknown = Unknown
error.number-structure-is-max-length = The maximum number structure is 75 characters
error.number-structure-name-is-invalid = The structure name is not in the correct format.
error.expect-string-search-is-required = The search string cannot be empty
error.description-is-required = Description is required
error.delete-number-group-is-invalid = To successfully delete a group of numbers, you must break the dependencies of the number structures.
label.display= Display
label.min-price-special = Minimum price
label.max-price-special = Maximum price

label.premium-account-number.payment-deadline-premium-acc-number-title = Notice of payment deadline for lucky account number
label.premium-account-number.payment-deadline-premium-acc-number-content = Lucky account number {0} is about to be paid. Amount to pay {1}. Complete the day before {2} to avoid service interruptions.
error.premium-account-number.premium-acc-number-status-is-invalid = Invalid status for deletion
error.premium-acc-number.cannot-create-premium-acc-number = You cannot register a lucky account number
error.premium-account-number.premium-acc-number-not-match-phone-number = Lucky number account does not match phone number
error.premium-account-number.benefit-acc-number-not-yet-charged = The beneficiary account is a lucky account number without fees.
error.premium-acc-number.premium-acc-number-has-been-locked = Your account is locked. Please contact support center 021 752 777
error.premium-account-number-not-yet-charged = The source account is a lucky account number that has not been charged yet. Please check again.

error.transaction.transaction-id-is-not-blank = Transaction id cannot be left blank

error.customer-info-is-not-null = Customer information list cannot be left blank
error.cifs-is-required = Cif is required
error.sync-customer-info = Syncing customer information {0} failed.
error.update-customer-sector = Update of customer sector {0} failed
error.cifs-max-length = Customer cif list up to 500 characters

error.transfer-internal-currency.currency-invalid = The currency is not valid.
error.transfer-internal-currency.currency-is-invalid = Currency invalid.
error.transfer-internal-currency.account-currency-invalid = Account currency invalid.

error.currency.code.can.be.bigger= Currency code cannot be longer than 50 characters
error.currency.name.can.be.bigger= Currency name cannot be longer than 255 characters
error.currency.code-is-required= Currency code is required
error.currency.name-is-required= Currency name is required
error.currency.code-is-existed= Currency code already exists
error.transfer-internal-currency.not-existed= Customer is not included in this service package.
error.currency.value-is-required= The qr currency code is required.
error.currency.value.can.be.bigger= The qr currency code cannot be larger than 3 characters.
error.currency.customer-balance-invalid= Minimum balance after transaction is not enough {0} {1}

error.customer.customer-is-existed = User existed
error.currency.value-is-invalid= Currency code when generating QR is invalid
error.currency.code-is-invalid= Currency code is invalid
error.currency.name-is-invalid= Currency name is invalid
error.transfer-internal-currency.amount-is-invalid = transaction amount is invalid

label.premium-account-revert-date = Fee Collection Error Date
label.cash-deposit-is-successfully = Successfully deposited money at the counter
label.cash-withdrawal-is-successfully = Successfully withdrew money at the counter
label.purchase-foreign-currency-is-successfully = Successfully purchased foreign currency
label.currency-sold-is-successfully = Successfully sold foreign currency
label.service-fee-charge-is-successfully = Successfully charged service fee
label.open-saving-account-is-successfully = Successfully opened a savings account
label.close-saving-account-is-successfully = Successfully closed a savings account
error.merchant.cif-and-account-number-is-invalid = Cif number and account number are not consistent on T24 system.
label.merchant.total-amount=Total Payments
label.merchant.total-quantity=Total Transactions
label.interest-paid-is-successfully = Successfully paid deposit interest
error.debit-contract-pay-is-successfully = Successfully paid deposit contract interest
error.bad-request-too-many = Request sent too many times, please try again later.

label.premium-account-revert-error.message = The T24 system collects fees for account numbers
label.premium-account-revert-error.message-acc = with an error. Please check and contact the customer for handling
label.noti.premium-account-number-revert-error = Unsuccessfully paid the beautiful number account fee
error.premium-account-revert-does-not-existed = Premium account number {0} does not exist
error.premium-account-revert-does-not-transaction = Premium account number {0} has no transaction
label.template-notification-history-file-name        = Collect_Fee_Fail_Premium_Account_Number_%s.xlsx
label.template.notification-history-title = LIST OF COLLECT FEE FAIL PREMIUM ACCOUNT NUMBER
label.template.register-sms-balance-fail-title = List of SMS fee collection errors balance fluctuations
label.cash-in-accumulated-saving-account-successfully = Deposit into the savings accumulation account successfully
label.credit-saving-account-is-successfully = Successfully transferred money to the savings account
error.customer.otp-is-required = OTP code is required.
error.premium-acc-number-does-not-existed-T24 = The premium account number has been used or does not exist in the T24 system
error.notification.email-duplicate = Duplicate email.
error.customer-phone-number-is-not-premium-acc = Customers can only open a premium account registration with a beautiful number that matches the registered phone number.
error.customer-premium-acc-invalid = Premium account number {0} is not available
label.withdraw-money-e-wallet-successfully = Successfully cashed money out from U-money wallet
error.premium-acc-number-does-not-existed-in-T24 = The premium account number has been used or does not exist on the T24 system
label.sms.balance-change-failed= <p>Dear Valued Customer,<br>MB Laos informs you SMS balance notification fee was not processed correctly. Account number {0}, Fee {1} LAK on {2}.<br>Contact 021 752 777<br>Best regards,<br>MB Laos</p>
label.sms.balance-change-failed-title = Notification of SMS Fee Error
error.notification-fail-type-is-required = Fee error notification type must not be empty
error.sms-otp-is-invalid = Entered wrong OTP code more than 3 times. Please try again after 5 minutes.
error.revert-fee-is-successfully = ON SMS fail, Reverted fee success!
error.fee-config-not-found = Fee config not found!
error.on-off-sms-balance-is-failed = On/Off SMS fail!
error.on-sms-fail-customer-have-charge = ON SMS fail, Customers have been charged
message.register-sms-balance = Register sms balance
message.cancel-sms-balance = Cancel sms balance
error.register-sms-balance-is-failed = Register sms balance is failed
label.sms-balance-charge-fee-successfully = Successfully fee collection for balance fluctuation SMS
error.debit-deposit-account-not-exist-in-whitelist = The account does not exist in the whitelist!
error.parameter-method-argument-not-valid = Invalid parameter, please check again
label.debit = Debit
label.deposit = Deposit
label.debit-receiving-bank = Receiving bank
label.debit-receiving-bank-code = Receiving bank code
label.debit-remark = Transfer content
label.debit-full-name-transfer = Name of transferring customer
label.debit-account-number-transfer = Payment account number of transferring customer
label.debit-remark-payment = Fee collection content
label.debit-from-user-full-name = Customer name
label.debit-from-member = Bank code calling for accounting
label.debit-from-account = Sending account phone number
label.debit-from-user = Wallet/bank account number
error.account-number-not-included-cif = Account number not included in CIF number
error.account-number-existed = Account number already exists.
error.transaction-limit-per-transaction-is-required = Maximum transaction limit per transaction cannot be empty
label.pay-salary-is-successfully= You have received your salary
label.merchant-discount= Merchant Discount Configuration
label.fixed-amount= Fixed amount
error.discount-exceeds-transaction=The discount must not exceed the transaction value;
error.discount-plus-fee-exceeds-transaction=The total of discount and fee must not exceed the transaction amount;
error.amount-min-is-invalid = Minimum payment amount {0}
error.currency-value-is-existed = Currency code when creating QR already exists
label.transaction-description = Accounting transaction code
label.foreign-currency=Foreign currency
label.exchange-rate=Exchange rate
label.foreign-fee=Foreign transaction fee
label.total-foreign-amount=Total foreign transaction amount
label.foreign-amount=Foreign transaction amount