/*
 * MesssageConfiguaration.java
 *
 * Copyright (C) 2021 by Vinsmart. All right reserved.
 * This software is the confidential and proprietary information of Vinsmart
 */
package com.mb.laos.messages.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;
import com.mb.laos.messages.Labels;
import lombok.Setter;

/**
 * The Class MesssageConfiguaration.
 */

@Setter
// @Component
@ConfigurationProperties(prefix = "spring.messages")
public class MesssageConfiguaration {

    private String encoding;
    
    private String[] basename;
    
    private int cacheDuration;

    private boolean useCodeAsDefaultMessage;
    /**
     * Locale resolver.
     *
     * @return the locale resolver
     */
    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver localeResolver = new SessionLocaleResolver();
        
        localeResolver.setDefaultLocale(Labels.VN);
        
        return localeResolver;
    }

    /**
     * Bundle message source.
     *
     * @return the message source
     */
    @Bean
    public MessageSource messageSource() {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        
        messageSource.setBasenames(this.basename);
        messageSource.setDefaultEncoding(this.encoding);
        messageSource.setCacheSeconds(this.cacheDuration);
        messageSource.setUseCodeAsDefaultMessage(this.useCodeAsDefaultMessage);
        
        return messageSource;
    }
}
