<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="DucNT" id="s-2024-05-14-01">
        <sql>
            ALTER TABLE "MERCHANT" ADD "CUSTOMER_ID" NUMBER(19);
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="s-2024-05-14-02">
        <sql>
            create table PREMIUM_ACCOUNT_NUMBER_STRUCTURE
            (
                PREMIUM_ACCOUNT_NUMBER_STRUCTURE_ID NUMBER(19) NOT NULL
                    constraint "PREMIUM_ACCOUNT_NUMBER_STRUCTURE_pk"
                    primary key,
                NAME                        VARCHAR2(50),
                PRICE                       NUMBER(19) DEFAULT 0,
                NUMBER_GROUP                NUMBER(5) NOT NULL,
                NUMBER_STRUCTURE            VARCHAR2(75) NOT NULL,
                PATTERN                     VARCHAR2(500) NOT NULL,
                DESCRIPTION                 VARCHAR2(255),
                STATUS                      NUMBER(2) NOT NULL,
                DISCOUNT                    NUMBER(19,0) DEFAULT 0,
                CREATED_DATE                TIMESTAMP(6),
                CREATED_BY                  VARCHAR2(75),
                LAST_MODIFIED_DATE          TIMESTAMP(6),
                LAST_MODIFIED_BY            VARCHAR2(75)
            )
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="s-2024-05-14-03">
        <sql>
            CREATE SEQUENCE "PREMIUM_ACCOUNT_NUMBER_STRUCTURE_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="s-2024-05-14-04">
        <sql>
            ALTER TABLE PREMIUM_ACCOUNT_NUMBER_STRUCTURE MODIFY DISCOUNT NUMBER (19,2) DEFAULT 0;
        </sql>
    </changeSet>
</databaseChangeLog>
