<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="DucNT" id="s-2023-03-23-01">
        <sql>
            CREATE TABLE DOTP (
            DOTP_ID NUMBER(19,0) NOT NULL,
            DEVICE_ID VARCHAR2(255) NOT NULL,
            PHONE_NUMBER VARCHAR2(20) NOT NULL,
            ENC_KEY VARCHAR2(255) NOT NULL,
            TOKEN VARCHAR2(255) NOT NULL,
            CUSTOMER_ID NUMBER(19,0) NOT NULL,
            STATUS NUMBER(2,0) DEFAULT 0,
            CONSTRAINT DOTP_PK PRIMARY KEY (DOTP_ID),
            "CREATED_BY" VARCHAR2(75 BYTE) VISIBLE ,
            "CREATED_DATE" TIMESTAMP(6) VISIBLE ,
            "LAST_MODIFIED_BY" VARCHAR2(75 BYTE) VISIBLE ,
            "LAST_MODIFIED_DATE" TIMESTAMP(6) VISIBLE
            );
        </sql>
    </changeSet>

    <changeSet author="tuyen.td" id="s-2023-03-23-02">
        <sql>
            CREATE SEQUENCE "DOTP_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;
        </sql>
    </changeSet>

<!--    <changeSet author="tuyen.td" id="s-2023-03-23-03">-->
<!--        <sql>-->
<!--            CREATE TABLE COMMON (-->
<!--            ID NUMBER(19,0) NOT NULL,-->
<!--            QR_LAPNET_TYPE VARCHAR2(25) DEFAULT NULL,-->
<!--            PAYMENT_TYPE VARCHAR2(25) DEFAULT NULL,-->
<!--            AID_NUMBER VARCHAR2(25) DEFAULT NULL,-->
<!--            IIN VARCHAR2(25) DEFAULT NULL,-->
<!--            MERCHANT_CATEGORY_CODE VARCHAR2(25) DEFAULT NULL,-->
<!--            TRANSACTION_AMOUNT VARCHAR2(25) DEFAULT NULL,-->
<!--            CURRENCY VARCHAR2(25) DEFAULT NULL,-->
<!--            COUNTRY_CODE VARCHAR2(25) DEFAULT NULL,-->
<!--            MERCHANT_CITY VARCHAR2(25) DEFAULT NULL,-->
<!--            CYCLIC_REDUNDANCY_CHECK VARCHAR2(25) DEFAULT NULL,-->
<!--            CONSTRAINT COMMON_PK PRIMARY KEY (ID),-->
<!--            "CREATED_BY" VARCHAR2(75 BYTE) VISIBLE ,-->
<!--            "CREATED_DATE" TIMESTAMP(6) VISIBLE ,-->
<!--            "LAST_MODIFIED_BY" VARCHAR2(75 BYTE) VISIBLE ,-->
<!--            "LAST_MODIFIED_DATE" TIMESTAMP(6) VISIBLE-->
<!--            );-->
<!--        </sql>-->
<!--    </changeSet>-->

<!--    <changeSet author="tuyen.td" id="s-2023-03-23-04">-->
<!--        <sql>-->
<!--            CREATE SEQUENCE "COMMON_SEQ" MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 CACHE 20;-->
<!--        </sql>-->
<!--    </changeSet>-->

    <changeSet author="DucNT" id="s-2023-04-10-01">
        <sql>
            ALTER TABLE DOTP ADD DEVICE_NAME VARCHAR2(255);
        </sql>
    </changeSet>

</databaseChangeLog>
