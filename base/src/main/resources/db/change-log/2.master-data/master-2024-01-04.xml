<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="DucNT" id="m-2024-01-04-01">
        <sql>
            <![CDATA[
            INSERT INTO COMMON(COMMON_ID, CATEGORY, CODE, VALUE, DESCRIPTION, STATUS) VALUES (COMMON_SEQ.nextval, 'TENOR_PERIOD', 'D', 'label.date', '', 1);
            INSERT INTO COMMON(COMMON_ID, CATEGORY, CODE, VALUE, DESCRIPTION, STATUS) VALUES (COMMON_SEQ.nextval, 'TENOR_PERIOD', 'M', 'label.month', '', 1);
            INSERT INTO COMMON(COMMON_ID, CATEGORY, CODE, VALUE, DESCRIPTION, STATUS) VALUES (COMMON_SEQ.nextval, 'TENOR_PERIOD', 'Y', 'label.year', '', 1);
            INSERT INTO COMMON(COMMON_ID, CATEGORY, CODE, VALUE, DESCRIPTION, STATUS) VALUES (COMMON_SEQ.nextval, 'TENOR_PERIOD', 'R', 'R', '', 1);
            ]]>
        </sql>
    </changeSet>
    <changeSet author="DucNT" id="m-2024-01-04-02">
        <sql>
            ALTER TABLE T_TRANSACTION ADD SAVING_ACCOUNT_TYPE VARCHAR2(50);
        </sql>
    </changeSet>
</databaseChangeLog>
