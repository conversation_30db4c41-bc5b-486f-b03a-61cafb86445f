<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet author="hieu.daominh" id="m-2023-03-08-1">
        <sql>
            <![CDATA[
				UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = 'label.sms.otp-content', "TITLE" = 'label.sms.otp-title' WHERE "TEMPLATE_CODE" = 'SMS_OTP';
				UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = 'label.sms.new-customer-content', "TITLE" = 'label.sms.new-customer-title' WHERE "TEMPLATE_CODE" = 'SMS_NEW_CUSTOMER';
				UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = 'label.sms.approval-lock-content', "TITLE" = 'label.sms.approval-lock-title' WHERE "TEMPLATE_CODE" = 'SMS_APPROVAL_LOCK';
				UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = 'label.sms.approval-unlock-content', "TITLE" = 'label.sms.approval-unlock-title' WHERE "TEMPLATE_CODE" = 'SMS_APPROVAL_UNLOCK';
				UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = 'label.sms.approval-delete-content', "TITLE" = 'label.sms.approval-delete-title' WHERE "TEMPLATE_CODE" = 'SMS_APPROVAL_DELETE';
				UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = 'label.sms.approval-create-content', "TITLE" = 'label.sms.approval-create-title' WHERE "TEMPLATE_CODE" = 'SMS_APPROVAL_CREATE_CUSTOMER';
			    UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = 'label.sms.new-device-login-notice-content', "TITLE" = 'label.sms.new-device-login-notice-title' WHERE "TEMPLATE_CODE" = 'NEW_DEVICE_LOGIN_NOTICE';
				UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = 'label.sms.reset-password-content', "TITLE" = 'label.sms.reset-password-title' WHERE "TEMPLATE_CODE" = 'SMS_RESET_PASSWORD';
				UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = 'label.sms.account-is-temp-locked-content', "TITLE" = 'label.sms.account-is-temp-locked-title' WHERE "TEMPLATE_CODE" = 'SMS_ACCOUNT_IS_TEMP_LOCKED';
				UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = 'label.sms.reissue-password-content', "TITLE" = 'label.sms.reissue-password-title' WHERE "TEMPLATE_CODE" = 'SMS_REISSUE_PASSWORD';
				UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = 'label.sms.account-is-closed-content', "TITLE" = 'label.sms.account-is-closed-title' WHERE "TEMPLATE_CODE" = 'SMS_ACCOUNT_IS_CLOSED';
            ]]>
        </sql>
    </changeSet>
    <changeSet author="hieu.daominh" id="m-2023-03-08-2">
        <sql>
            <![CDATA[
                UPDATE "EX_CONTENT_TEMPLATE" SET "TITLE" = 'label.noti.payment-merchant-title' WHERE "TEMPLATE_CODE" = 'BALANCE_CHANGE_PAY_MERCHANT';
            ]]>
        </sql>
    </changeSet>
    <changeSet author="hieu.daominh" id="m-2023-03-08-3">
        <sql>
            <![CDATA[
                UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = 'label.noti.password-expired-notice-content', "TITLE" = 'label.noti.password-expired-notice-title' WHERE "TEMPLATE_CODE" = 'PASSWORD_EXPIRED_NOTICE';
                UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = '<h1>${LABEL_PAYMENT_ACCOUNT}: ${PAYMENT_ACCOUNT}.</h1>
                    <h2>${LABEL_TRANSACTION_AMOUNT}: ${TRANSACTION_AMOUNT} ${CURRENCY} ${TRANSACTION_TIME}.</h2>
                    <h3>${LABEL_CONTENT_TRANSACTION}: ${LABEL_ACCOUNT_NUMBER} ${SOURCE_ACCOUNT}</h3>
                    <h4>${LABEL_AT} ${BANK_CODE}. ${MESSAGE} ${LABEL_TRANSFER} - ${TRANSACTION_CODE}.</h4>', "TITLE" = 'label.noti.balance-change-transfer-money-title' WHERE "TEMPLATE_CODE" = 'BALANCE_CHANGE_TRANSFER_MONEY';
                UPDATE "EX_CONTENT_TEMPLATE" SET "CONTENT" = '<h1>${LABEL_PAYMENT_ACCOUNT}: ${PAYMENT_ACCOUNT}.</h1>
                    <h2>${LABEL_TRANSACTION_AMOUNT}: ${TRANSACTION_AMOUNT} ${CURRENCY} ${TRANSACTION_TIME}.</h2>
                    <h3>${LABEL_CONTENT_TRANSACTION}: ${LABEL_ACCOUNT_NUMBER} ${BENEFICIARY_ACCOUNT}</h3>
                    <h4>${LABEL_AT} ${BANK_CODE}. ${MESSAGE} ${LABEL_TRANSFER} - ${TRANSACTION_CODE}.</h4>', "TITLE" = 'label.noti.balance-change-receive-money-title' WHERE "TEMPLATE_CODE" = 'BALANCE_CHANGE_RECEIVE_MONEY';
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>
