<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="DucNT" id="m-2024-01-17-01">
        <sql>
            <![CDATA[
            INSERT INTO  "EX_CONTENT_TEMPLATE"("TEMPLATE_CODE", "CREATED_BY", "CREATED_DATE", "LAST_MODIFIED_BY", "LAST_MODIFIED_DATE", "DESCRIPTION", "NAME", "STATUS", "CONTENT", "TITLE") VALUES ('BALANCE_CHANGE_TRANSFER_MONEY_V2', NULL, NULL, NULL, NULL, 'Mẫu thông báo biến động số dư', 'Mẫu thông báo biến động số dư', '1'
                                                                                                                                                                                                    , '<p>${LABEL_PAYMENT_ACCOUNT}: ${PAYMENT_ACCOUNT}.<br>${LABEL_TRANSACTION_AMOUNT}: ${TRANSACTION_AMOUNT} ${CURRENCY}.<br>${LABEL_TRANSACTION_TIME}: ${TRANSACTION_TIME}<br>${LABEL_CONTENT_TRANSACTION}: ${MESSAGE}.</p>',  'label.noti.balance-change-transfer-money-title');
            ]]>
        </sql>
    </changeSet>
    <changeSet author="TuDV" id="m-2024-10-17-01">
        <sql>
            INSERT INTO "GROUP_PRIVILEGE"
            VALUES ('46', 'Nhóm quyền quản lý tài khoản số đẹp đặc biệt', 'group-privilege.special-premium-account-number', '46', '1', NULL, NULL,
                    NULL, NULL);
            INSERT INTO  "PRIVILEGE" VALUES ('244', 'SPECIAL_PREMIUM_ACCOUNT_NUMBER_CREATE', '46', 'privilege.special-premium-account-number-create', NULL, NULL, NULL, NULL);
            INSERT INTO  "PRIVILEGE" VALUES ('245', 'SPECIAL_PREMIUM_ACCOUNT_NUMBER_READ', '46', 'privilege.special-premium-account-number-read', NULL, NULL, NULL, NULL);
            INSERT INTO  "PRIVILEGE" VALUES ('246', 'SPECIAL_PREMIUM_ACCOUNT_NUMBER_UPDATE', '46', 'privilege.special-premium-account-number-update', NULL, NULL, NULL, NULL);
            INSERT INTO  "PRIVILEGE" VALUES ('247', 'SPECIAL_PREMIUM_ACCOUNT_NUMBER_HIDE', '46', 'privilege.special-premium-account-number-hide', NULL, NULL, NULL, NULL);
            INSERT INTO  "PRIVILEGE" VALUES ('248', 'SPECIAL_PREMIUM_ACCOUNT_NUMBER_UNHIDE', '46', 'privilege.special-premium-account-number-unhide', NULL, NULL, NULL, NULL);
            INSERT INTO  "PRIVILEGE" VALUES ('249', 'SPECIAL_PREMIUM_ACCOUNT_NUMBER_DELETE', '46', 'privilege.special-premium-account-number-delete', NULL, NULL, NULL, NULL);
        </sql>
    </changeSet>

    <changeSet author="TuDV" id="m-2024-10-17-02">
        <sql>
            INSERT INTO  "PRIVILEGE" VALUES ('250', 'SPECIAL_PREMIUM_ACCOUNT_NUMBER_IMPORT', '46', 'privilege.special-premium-account-number-import', NULL, NULL, NULL, NULL);
            INSERT INTO  "PRIVILEGE" VALUES ('251', 'SPECIAL_PREMIUM_ACCOUNT_NUMBER_EXPORT', '46', 'privilege.special-premium-account-number-export', NULL, NULL, NULL, NULL);
        </sql>
    </changeSet>
</databaseChangeLog>
