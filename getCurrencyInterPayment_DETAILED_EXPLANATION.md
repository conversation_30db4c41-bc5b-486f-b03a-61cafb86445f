# G<PERSON><PERSON><PERSON> thích chi tiết method getCurrencyInterPayment

## Tổng quan
Method `getCurrencyInterPayment` là **currency mapper** quan trọng trong hệ thống thanh toán quốc tế, chuyển đổi từ payment system name sang currency code và set international flags.

## Mục đích chính
1. **Map payment system → currency**: CAMBODIA → KHR, THAILAND → THB
2. **Set international flags**: Báo T24 đây là giao dịch quốc tế
3. **Validate supported countries**: Block Vietnam, allow Cambodia/Thailand
4. **Prepare T24 request**: Set nation code và member info

## Phân tích từng dòng code

### 1. Method signature và documentation
```java
/**
 * Lấy currency cho thanh toán quốc tế dựa vào ToMember
 * 
 * Method này xử lý mapping từ ToMember (payment system) sang currency code:
 * 1. Lấy tất cả nation mapping từ master data
 * 2. Tìm nation dựa vào ToMember (CAMBODIA, THAILAND...)
 * 3. Validate nation có được hỗ trợ không (block Vietnam)
 * 4. Set international flags cho request
 * 5. Return currency code tương ứng
 * 
 * Master data mapping:
 * - ToMember "CAMBODIA" → Nation "KH" → Currency "KHR"
 * - ToMember "THAILAND" → Nation "TH" → Currency "THB"
 * - ToMember "VIETNAM" → Nation "VN" → BLOCKED (MSG101066)
 */
private String getCurrencyInterPayment(AccNumberVerifyRequest request)
```

### 2. Load master data mapping
```java
// Lấy tất cả nation mapping từ master data
// Category NATION_ID chứa mapping: code=currency, value=nation, description=payment_system
List<Common> commons = this.commonRepository
        .findAllByCategoryAndStatus(CommonCategory.NATION_ID.name(), EntityStatus.ACTIVE.getStatus());
```

**Giải thích:**
- **CommonCategory.NATION_ID**: Category chứa mapping quốc gia
- **EntityStatus.ACTIVE**: Chỉ lấy record đang hoạt động
- **Master data structure**:
  - `code`: Currency code (KHR, THB, VND...)
  - `value`: Nation code (KH, TH, VN...)
  - `description`: Payment system name (CAMBODIA, THAILAND, VIETNAM...)

### 3. Find nation by payment system
```java
// Tìm nation dựa vào ToMember (payment system name)
// Ví dụ: ToMember="CAMBODIA" → tìm record có description="CAMBODIA"
Optional<Common> common = commons.stream()
        .filter(item -> Validator.equals(item.getDescription(), request.getToMember()))
        .findFirst();
```

**Giải thích:**
- **request.getToMember()**: Payment system name từ request
- **item.getDescription()**: Payment system name trong master data
- **Stream filter**: Tìm exact match
- **Optional**: Handle trường hợp không tìm thấy

### 4. Handle not found case
```java
// Nếu không tìm thấy nation mapping thì return null
// Có thể là payment system không được hỗ trợ
if (!common.isPresent()) {
    return null;
}
```

**Giải thích:**
- **Return null**: Báo hiệu payment system không được hỗ trợ
- **Caller handling**: Method gọi sẽ xử lý null case
- **Graceful degradation**: Không crash, chỉ báo không hỗ trợ

### 5. Vietnam blocking logic
```java
// Kiểm tra nếu là Vietnam thì block (business rule)
// Vietnam hiện tại không được hỗ trợ cho international payment
if (Validator.equals(common.get().getValue(), NationCode.VN.name())) {
    throw new BadRequestAlertException(ErrorCode.MSG101066);
}
```

**Giải thích:**
- **Business rule**: Vietnam bị block cho international payment
- **NationCode.VN.name()**: "VN" - nation code của Vietnam
- **ErrorCode.MSG101066**: Error code specific cho Vietnam blocking
- **Exception**: Hard stop, không cho phép tiếp tục

### 6. Set international query flag
```java
// Set international query flag = Y để T24 biết đây là giao dịch quốc tế
// T24 sẽ áp dụng logic khác cho international transaction
request.setInternationalQuery(InternationalQuery.Y.name());
```

**Giải thích:**
- **InternationalQuery.Y**: Flag báo T24 đây là giao dịch quốc tế
- **T24 behavior**: T24 sẽ apply international logic
- **Side effect**: Method modify request object
- **Important**: Flag này quyết định T24 routing

### 7. Set nation code
```java
// Set nation code (ví dụ: "KH" cho Cambodia, "TH" cho Thailand)
// T24 cần nation code để routing và compliance
request.setToNation(common.get().getValue());
```

**Giải thích:**
- **common.get().getValue()**: Nation code từ master data
- **ISO 3166-1 alpha-2**: KH, TH, VN...
- **T24 routing**: T24 dùng nation code để route
- **Compliance**: Cần cho regulatory reporting

### 8. Set payment system name
```java
// Set payment system name (ví dụ: "CAMBODIA", "THAILAND")
// Confirm lại ToMember để đảm bảo consistency
request.setToMember(common.get().getDescription());
```

**Giải thích:**
- **common.get().getDescription()**: Payment system name từ master data
- **Consistency**: Đảm bảo ToMember đúng format
- **Standardization**: Chuẩn hóa payment system name
- **T24 requirement**: T24 cần payment system info

### 9. Return currency code
```java
// Return currency code (ví dụ: "KHR" cho Cambodia, "THB" cho Thailand)
// Currency code được sử dụng để tính tỷ giá và fee
return common.get().getCode();
```

**Giải thích:**
- **common.get().getCode()**: Currency code từ master data
- **ISO 4217**: KHR, THB, USD...
- **Usage**: Dùng cho exchange rate và fee calculation
- **Return value**: Main output của method

## Master Data Structure

### Common table - Category: NATION_ID
| Code | Value | Description | Status |
|------|-------|-------------|--------|
| **KHR** | KH | CAMBODIA | ACTIVE |
| **THB** | TH | THAILAND | ACTIVE |
| **VND** | VN | VIETNAM | ACTIVE |
| **USD** | US | UNITED_STATES | ACTIVE |

### Mapping Logic
```
ToMember (Input) → Nation Code → Currency Code (Output)
"CAMBODIA"       → "KH"        → "KHR"
"THAILAND"       → "TH"        → "THB"
"VIETNAM"        → "VN"        → BLOCKED (Exception)
```

## Usage Context

### Called from interbank() method:
```java
// Trong method interbank()
currency = this.getCurrencyInterPayment(request);
AccNumberVerifyDTO accNumberVerifyDTO = this.apiGeeTransferService.verifyAccNumber(request);
```

### Request modification:
```java
// Before getCurrencyInterPayment()
request.setInternationalQuery(InternationalQuery.N.name());
request.setToNation(null);
request.setToMember("CAMBODIA");

// After getCurrencyInterPayment()
request.setInternationalQuery(InternationalQuery.Y.name());
request.setToNation("KH");
request.setToMember("CAMBODIA");
// Return: "KHR"
```

## Business Rules

### Supported Countries:
- ✅ **Cambodia (KH)**: Full support, currency KHR
- ✅ **Thailand (TH)**: Full support, currency THB
- ❌ **Vietnam (VN)**: Blocked, throw exception

### International Flags:
- **InternationalQuery**: N → Y (domestic to international)
- **ToNation**: null → nation code
- **ToMember**: standardized payment system name

### Error Scenarios:
1. **Payment system not found**: Return null
2. **Vietnam detected**: Throw MSG101066
3. **Master data missing**: Return null

## Integration Points

### T24 Integration:
- **InternationalQuery=Y**: T24 applies international logic
- **ToNation**: Used for routing and compliance
- **ToMember**: Used for payment system identification

### Fee Calculation:
- **Currency code**: Used for international fee calculation
- **Exchange rate**: Applied based on currency
- **Compliance fee**: Added for international transactions

### Regulatory Compliance:
- **Nation code**: Required for regulatory reporting
- **Payment system**: Tracked for compliance
- **Vietnam blocking**: Compliance with regulations

## Performance Considerations

### Database Query:
- **Single query**: Load all nation mappings once
- **In-memory filtering**: Stream operations on loaded data
- **Caching opportunity**: Master data rarely changes

### Memory Usage:
- **Small dataset**: Nation mappings are limited
- **Stream processing**: Efficient filtering
- **Optional handling**: Null-safe operations

## Error Handling Strategy

### Graceful Degradation:
```java
// Caller handles null gracefully
String currency = getCurrencyInterPayment(request);
if (currency == null) {
    // Use default currency or show error
    currency = defaultCurrency;
}
```

### Hard Blocking:
```java
// Vietnam is hard blocked
if (isVietnam) {
    throw new BadRequestAlertException(ErrorCode.MSG101066);
}
```

Method `getCurrencyInterPayment` là **critical component** cho international payments, đảm bảo correct mapping và compliance với business rules!
