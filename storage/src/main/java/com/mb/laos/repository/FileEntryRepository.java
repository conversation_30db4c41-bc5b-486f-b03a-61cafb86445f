package com.mb.laos.repository;

import com.mb.laos.annotation.CacheAction;
import com.mb.laos.annotation.CacheCollection;
import com.mb.laos.annotation.CacheUpdate;
import com.mb.laos.cache.util.StorageCacheConstants;
import com.mb.laos.model.FileEntry;
import com.mb.laos.repository.extend.FileEntryRepositoryExtend;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface FileEntryRepository extends JpaRepository<FileEntry, Long>, FileEntryRepositoryExtend {
	/**
	 * @param fileEntryId
	 * @param className
	 * @param classPk
	 * @param resourceType
	 * @return
	 */
	@Cacheable(cacheNames = StorageCacheConstants.File.FIND_BY_ID_CN_CP_RN,
			key = "{#fileEntryId, #className, #classPk, #resourceType}", unless = "#result == null")
	FileEntry findByFileIdAndClassNameAndClassPkAndResourceType(Long fileEntryId, String className, long classPk,
			String resourceType);
	
	/**
	 * 
	 * @param className
	 * @param classPk
	 * @param resourceType
	 * @param status
	 * @return
	 */
	@Cacheable(cacheNames = StorageCacheConstants.File.FIND_BY_CN_CP_RN,
			key = "{#className, #classPk, #resourceType}", unless = "#result == null")
	List<FileEntry> findByClassNameAndClassPkAndResourceType(String className, long classPk,
			String resourceType);

	/**
	 *
	 * @param className
	 * @param classPkIds
	 * @param resourceType
	 * @return
	 */
	@Cacheable(cacheNames = StorageCacheConstants.File.FIND_BY_CN_CPS_PN,
			key = "{#className, #classPkIds, #resourceType}", unless = "#result.isEmpty() or #result==null")
	List<FileEntry> findAllByClassNameAndClassPkInAndResourceType(String className, List<Long> classPkIds, String resourceType);

	/**
	 *
	 * @param className
	 * @param classPk
	 * @param resourceType
	 * @param approvalStatus
	 * @return
	 */
	@Cacheable(cacheNames = StorageCacheConstants.File.FIND_BY_CN_CP_RN_AS,
			key = "{#className, #classPk, #resourceType, #approvalStatus}", unless = "#result == null")
	List<FileEntry> findByClassNameAndClassPkAndResourceTypeAndApprovalStatus(String className, long classPk,
															 String resourceType, Integer approvalStatus);

	@Caching(
			put = {
					@CachePut(cacheNames = {StorageCacheConstants.File.FIND_BY_ID_CN_CP_RN},
							key = "{#entity.fileId, #entity.className, #entity.classPk, #entity.resourceType}")
			},
			evict = {
					@CacheEvict(cacheNames = {StorageCacheConstants.File.FIND_BY_CN_CP_RN},
							key = "{#entity.className, #entity.classPk, #entity.resourceType}")
			}
	)
	@CacheUpdate(
            collection = {
                @CacheCollection(cacheNames = StorageCacheConstants.File.FIND_BY_CN_CP_RN,
                		key = "{#entity.className, #entity.classPk, #entity.resourceType}", compareProperties = "fileId")
            }
    )
	default FileEntry save_(FileEntry entity) {
		return save(entity);
	}

	/**
	 * @param entry
	 */
	@Caching(
			evict = {
					@CacheEvict(cacheNames = {StorageCacheConstants.File.FIND_BY_ID_CN_CP_RN},
							key = "{#entity.fileId, #entity.className, #entity.classPk, #entity.resourceType}")
			}
	)
	@CacheUpdate(
            collection = {
                @CacheCollection(cacheNames = StorageCacheConstants.File.FIND_BY_CN_CP_RN,
                		key = "{#entity.className, #entity.classPk, #entity.resourceType}", compareProperties = "fileId",
                		action = CacheAction.EVICT)
            }
    )
	default FileEntry delete_(FileEntry entity) {
		return save(entity);
	}


	boolean existsByHashKeyAndStatusNot(String hashKey, int status);

}
