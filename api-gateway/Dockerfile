FROM adoptopenjdk/openjdk8:alpine-jre
#ENV SPRING_PROFILES_ACTIVE $ENV
ENV JAVA_OPTS -Xmx768m -Xss256k -XX:+UseG1GC -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap -XX:MaxRAMFraction=1
WORKDIR /app
#Add Dynamic key require lib
COPY ./api-gateway/target/classes/crypto/* /lib/
RUN chmod +x /lib/*

# Add the application's jar to the container
ADD ./api-gateway/target/*.jar app.jar
# Add font for catpcha
RUN apk add --update fontconfig ttf-dejavu
# fix CVE-2023-0406 openssl:1.1.1t-r0
RUN apk update && apk upgrade
# Run the jar file
#ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom $JAVA_OPTS -jar app.jar
ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "app.jar", "--spring.profiles.active=${ENV:dev}"]

EXPOSE 80
