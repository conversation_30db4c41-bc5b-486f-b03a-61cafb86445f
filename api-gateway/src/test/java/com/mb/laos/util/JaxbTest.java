package com.mb.laos.util;

import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.xml.sax.InputSource;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.dom.DOMSource;
import java.io.StringReader;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;


@Slf4j
public class JaxbTest {
    public static String encrypt(String text) {
        try {
            String key = generateKey("LMM@2023!");
            byte[] iv = RandomGenerator.generateIV(16);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
            SecretKeySpec secretKeySpec = new SecretKeySpec(Objects.requireNonNull(key).getBytes(), "AES");

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
            byte[] encryptedBytes = cipher.doFinal(text.getBytes());

            String encryptedData = bytesToHex(encryptedBytes);
            String ivString = bytesToHex(iv);

            return ivString + encryptedData;
        } catch (Exception ex) {
            _log.error("Mmoney encrypt has error");
            ex.printStackTrace();
            return null;
        }
    }

    public static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0'); // Đảm bảo rằng mỗi byte sẽ được biểu diễn bằng 2 ký tự hexa
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    public static byte[] hexStringToByteArray(String hexString) {
        int len = hexString.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return data;
    }

    public static String decrypt(String encryptedText) {
        try {
            String key = generateKey("LMM@2023!");
            // Lấy initialization vector (IV) từ văn bản mã hóa (16 byte đầu tiên)
            byte[] ivBytes = hexStringToByteArray(encryptedText.substring(0, 32));

            // Chuẩn bị khóa bí mật từ secretKey
            SecretKeySpec secretKeySpec = new SecretKeySpec(
                    Objects.requireNonNull(key).getBytes(), "AES");

            // Chuẩn bị đối tượng Cipher để giải mã với AES-256-CBC
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, new IvParameterSpec(ivBytes));

            // Giải mã văn bản còn lại (sau 32 byte đầu tiên) từ hex sang UTF-8
            byte[] encryptedBytes = hexStringToByteArray(encryptedText.substring(32));
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);

            // Chuyển đổi mảng byte đã giải mã thành chuỗi UTF-8
            return new String(decryptedBytes, "UTF-8");
        } catch (Exception e) {
            _log.error("Mmoney decrypt has error");
            e.printStackTrace();
            return null;
        }
    }

    public static String generateKey(String secretKey) {
        try {
            // Tạo một đối tượng MessageDigest sử dụng thuật toán SHA-256
            MessageDigest digest = MessageDigest.getInstance("SHA-256");

            // Băm chuỗi secretKey và lấy giá trị băm dưới dạng mảng byte
            byte[] hash = digest.digest(secretKey.getBytes());

            // Chuyển đổi mảng byte thành chuỗi hexa
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                // Convert mỗi byte thành chuỗi hexa và nối vào chuỗi hexString
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0'); // Đảm bảo rằng mỗi byte sẽ được biểu diễn bằng 2 ký tự hexa
                }
                hexString.append(hex);
            }

            // Lấy 32 ký tự đầu tiên của chuỗi hexa làm khóa
            return hexString.substring(0, 32);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

    public void test(){}

    public static class TagValue {
        public String id;
        public int length;
        public String value;

        public TagValue(String id, int length, String value) {
            this.id = id;
            this.length = length;
            this.value = value;
        }

        @Override
        public String toString() {
            return id + "(" + length + "):" + value;
        }
    }

    // Parse toàn bộ chuỗi QR thành danh sách TagValue
    public static List<TagValue> parse(String qr) throws Exception {
        List<TagValue> result = new ArrayList<>();
        int pos = 0;
        while (pos + 4 <= qr.length()) {
            String id = qr.substring(pos, pos + 2);
            int length = Integer.parseInt(qr.substring(pos + 2, pos + 4));
            pos += 4;

            if (pos + length > qr.length()) {
                throw new Exception("Invalid length for tag " + id);
            }

            String value = qr.substring(pos, pos + length);
            pos += length;

            // Nếu là Template (ví dụ Merchant Account Info 26-51), parse đệ quy con
            if (isTemplate(id)) {
                List<TagValue> subTags = parse(value);
                // Thêm sub tags dạng id.subid cho dễ xử lý
                for (TagValue sub : subTags) {
                    result.add(new TagValue(id + "." + sub.id, sub.length, sub.value));
                }
            } else {
                result.add(new TagValue(id, length, value));
            }
        }

        return result;
    }

    // Kiểm tra ID có phải Template không (26-51)
    private static boolean isTemplate(String id) {
        try {
            int val = Integer.parseInt(id);
            return val >= 26 && val <= 51;
        } catch (Exception e) {
            return false;
        }
    }

    // Kiểm tra các trường bắt buộc có tồn tại không
    public static boolean validateMandatoryFields(List<TagValue> tags) {
        Set<String> mandatory = new HashSet<>();
        mandatory.add("00");
        mandatory.add("01");
        mandatory.add("52");
        mandatory.add("53");
        mandatory.add("58");
        mandatory.add("59");
        mandatory.add("60");
        mandatory.add("63");

        // Kiểm tra từng trường bắt buộc
        for (String m : mandatory) {
            boolean found = tags.stream().anyMatch(t -> t.id.equals(m));
            if (!found) {
                System.out.println("Missing mandatory field: " + m);
                return false;
            }
        }

        // Kiểm tra ít nhất 1 Merchant Account Info (26-51)
        boolean hasMerchantAccountInfo = tags.stream()
                .anyMatch(t -> {
                    try {
                        int val = Integer.parseInt(t.id.split("\\.")[0]);
                        return val >= 26 && val <= 51;
                    } catch (Exception e) {
                        return false;
                    }
                });

        if (!hasMerchantAccountInfo) {
            System.out.println("Missing Merchant Account Information (ID 26-51)");
            return false;
        }

        return true;
    }


    // Tính CRC16/CCITT-FALSE theo chuẩn EMVCo
    public static String calculateCrc(String input) {
        int crc = 0xFFFF;
        for (int i = 0; i < input.length(); i++) {
            crc ^= ((byte) input.charAt(i)) << 8;
            for (int j = 0; j < 8; j++) {
                if ((crc & 0x8000) != 0) {
                    crc = (crc << 1) ^ 0x1021;
                } else {
                    crc <<= 1;
                }
                crc &= 0xFFFF;
            }
        }
        return String.format("%04X", crc);
    }

    // Kiểm tra CRC
    public static boolean validateCrc(String qr) throws Exception {
        // Chuỗi không tính CRC (tính từ đầu đến trước tag 63)
        int crcIndex = qr.lastIndexOf("63");
        if (crcIndex == -1) {
            throw new Exception("No CRC field (63) found");
        }
        String withoutCrc = qr.substring(0, crcIndex + 4); // 63 + 04 length
        String givenCrc = qr.substring(crcIndex + 4, crcIndex + 8);

        String calculatedCrc = calculateCrc(withoutCrc);
        boolean valid = givenCrc.equalsIgnoreCase(calculatedCrc);
        if (!valid) {
            System.out.println("CRC mismatch: expected=" + givenCrc + ", calculated=" + calculatedCrc);
        }
        return valid;
    }


    public static void main(String[] args) throws Exception {
        String qr = "00020101021230410009khqr@aclb0114000104716610010206ACLEDA39370011855124785870101103131746758140610520420005802KH5405200005919UAT APK POS TOPWISE6010Phnom Penh530311662540113174675814061002090777897960708737300010808Purchase6304EDBE";

        System.out.println("Parsing QR: " + qr);
        List<TagValue> tags = parse(qr);

        for (TagValue tag : tags) {
            System.out.println(tag);
        }

        System.out.println("Validating mandatory fields...");
        boolean validMandatory = validateMandatoryFields(tags);
        System.out.println("Mandatory fields valid? " + validMandatory);

        System.out.println("Validating CRC...");
        boolean validCrc = validateCrc(qr);
        System.out.println("CRC valid? " + validCrc);

        if (validMandatory && validCrc) {
            System.out.println("QR code is valid according to EMVCo/KHQR standard.");
        } else {
            System.out.println("QR code is invalid.");
        }
    }

    private static DOMSource getDOMSource(String body, String resultNodeName) throws Exception {
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();

        // dbf.setNamespaceAware(true);

        DocumentBuilder db = dbf.newDocumentBuilder();

        Document doc = db.parse(new InputSource(new StringReader(body)));

        Node resultNode = doc.getElementsByTagName(resultNodeName).item(0);

        return new DOMSource(resultNode);
    }
}
