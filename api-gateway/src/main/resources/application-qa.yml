# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: DEBUG
    com.mb.laos: DEBUG
    org.springframework.cache: TRACE
    com.mb.laos.api.handler: DEBUG
    com.mb.laos.service.impl.SmsServiceImpl: DEBUG
    org.redisson: INFO
    io.netty: INFO
  file:
    max-size: 100MB
spring:
  liquibase:
    enabled: true
  jackson:
    date-format: dd-MM-yyyy
    time-zone: Asia/Ho_Chi_Minh
    serialization:
      write-dates-as-timestamps: false
      indent-output: true
      fail-on-empty-beans: false
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ***************************************
    driver-class-name: oracle.jdbc.OracleDriver
    username: mb_lao_qa
    password: 123456a@
    hikari:
      auto-commit: false
      minimum-idle: 5
      maximum-pool-size: 5
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
  jpa:
    database-platform: org.hibernate.dialect.Oracle12cDialect
    database: ORACLE
    show-sql: true
    properties:
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      hibernate.temp.use_jdbc_metadata_defaults: false
      # hibernate.cache.region.factory_class: com.hazelcast.hibernate.HazelcastCacheRegionFactory
      hibernate.cache.hazelcast.instance_name: mb_cms
      hibernate.cache.hazelcast.shutdown_on_session_factory_close: false
      hibernate.cache.use_minimal_puts: true
      hibernate.cache.hazelcast.use_lite_member: true
      hibernate.session.events.log.LOG_QUERIES_SLOWER_THAN_MS: 30
      hibernate.generate_statistics: true
      hibernate.jdbc.batch_size: 10
      hibernate.order_inserts: true
      hibernate.format_sql: true
      # hibernate search
      hibernate.search.indexing_strategy: manual
      hibernate.search.default.directory_provider: filesystem
      hibernate.search.default.indexBase: 'indexes'
      hibernate.search.default.indexmanager: near-real-time
      hibernate.search.default.reader.strategy: async
      hibernate.search.default.reader.async_refresh_period_ms: 8000
      #hibernate.search.default.infinispan.cachemanager_jndiname: java:comp/env/jdbc/CacheStoreDatasource
      #hibernate.search.infinispan.configuration_resourcename: hibernatesearch-infinispan.xml
  mail:
    host: smtp.gmail.com
    port: 465
    username: <EMAIL>
    password: 'VOvMZwqft7ABRpuTR1sHH9X+XTs+dEGoJaS6ELWYeCEAFjQMsFZo7eaCozPiN86Up4WhK47SnsaTMlijbvdkC13v53Xly3+YMZ2AkpN/Q4yoqggKnJe84jqxuwRIgQD6tZvVBHREKQxKhm5baQQjSgfvBzSdVEV3xgnDDACMsU5uQB+0eO+x3yTEXbpA6A2MYCmKkX3PpSiKZ14c/EBBk98GVDw8lU0w/yMU+2qApNrCHQHzzLx+yeDep7Ne7Nh7GP2vtMVpzNrbsHipPhtYhOlldjwqDhmUdVVfVe7z7XO5FbS+33vAKfrwZymsVrazHAwlHbzn/QXy2pC9lJOgRA=='
    from: <EMAIL>
    protocol: smtp
    properties:
      mail.debug: true
      mail.transport.protocol: smtp
      mail.smtp.auth: true
      mail.smtp.starttls.enable: true
      mail.smtp.ssl.enable: true
      mail.smtp.sendpartial: true
  messages:
    encoding: 'UTF-8'
    use-code-as-default-message: true
    basename: 'i18n/labels,i18n/gw_labels,i18n/lang_key'
    cache-duration: 60    # 60 second, see the ISO 8601 standard
  servlet:
    multipart:
      max-file-size: -1 #meaning total file size is unlimited
      max-request-size: -1 #meaning total request size for a multipart/form-data is unlimited.
      resolve-lazily: true
server:
  port: 80
  tomcat:
    accept-count: 1000 # Maximum queue length for incoming connection requests when all possible request processing threads are in use.
    max-connections: 8192 #Maximum number of connections that the server accepts and processes at any given time.
    max-http-form-post-size: 5MB #Maximum size, in bytes, of the HTTP post content.
    uri-encoding: UTF-8
    threads:
      max: 200 #Maximum number of worker threads.
    accesslog:
      buffered: false #Whether to buffer output such that it is flushed only periodically.
      directory: logs/access_log #Directory in which log files are created. Can be absolute or relative to the Tomcat base dir.
      enabled: true #Enable access log.
      file-date-format: .yyyy-MM-dd #Date format to place in the log file name.
      pattern: common #Format pattern for access logs.
      prefix: access_log #Log file name prefix.
      rename-on-rotate: false #Whether to defer inclusion of the date stamp in the file name until rotate time.
      request-attributes-enabled: true #Set request attributes for the IP address, Hostname, protocol, and port used for the request.
      rotate: true #Whether to enable access log rotation.
      suffix: .log #Log file name suffix.
  servlet:
    context-path: '/api/gateway/v1'
    encoding:
      charset: 'UTF-8'
      enabled: true
      force: true
security:
  cache:
    url-patterns: '/i18n/*,/content/*,/app/*'
    max-age: 86400 # Image cache max-age in second, duration: 86400 (1 day)
  # CORS is only enabled by default with the "dev" profile, so BrowserSync can access the API
  cors:
    allowed-origins: '*' # http://localhost:8780
    allowed-methods: 'POST,OPTIONS'
    allowed-headers: '*'
    exposed-headers: '*'
    allow-credentials: true
    max-age: 1800
  authentication:
    jwt:
      # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
      base64-secret: 'ajMywt0FWnZYET/u3Pm309ziD6eXRewGX+QsyHs6/Y4ascWDHAAK46ARsuUZKLcjMQxUa5mQfNTaujgm8Sg4TJh4wKGwwXBAtB2gsEm1TkZuSRo97/BEFIR5rEKefTBf41J4kcU/YndvNFJcMRXkgMoC6fvzjSVYx1fGNYxSJqO+neilVm3uBpQiuhQrhTMmrKZJYa5M9xutWfJsbQ1Tf6IQ6zlRKZqdt8edUD/mOuUwltlyC1UZCyALrcji2vFlgXu+eks6IjBkxlAT3LvwVe5Et4MoNaB+eHetIdL6epigf1nYxmLSPT3rWfQxRRmHAwSQnO+1atAubG/xQqvsTQ=='
    cookie:
      domain-name:
      enable-ssl: false
      http-only: true
      path: '/'
      same-site: 'Strict'
  login:
    max-attempt-time: 5
  password:
    max-attempt-time: 5
cache:
  config-type: 'redisson' # redis/redisson/hazelcast
  hazelcast:
    instanceName: 'api_gateway_qa'
    localIp: '127.0.0.1'
    remoteIp: '127.0.0.1'
    backup-count: 1
    management-center: # Full reference is available at: http://docs.hazelcast.org/docs/management-center/3.9/manual/html/Deploying_and_Starting.html
      enabled: false
      update-interval: 3
      url: http://*************:8180/hazelcast-mancenter
  redis:
    mode: 'standalone' # standalone, sentinel
    standalone:
      host: '*************'
      port: 6379
      password:
    sentinel:
      port: 26379
      password:
      master: masterredis
      nodes:
        - localhost
        #- *********
        #- *********
    lettuce-pool:
      shutdown-timeout: 200 #milliseconds
      command-timeout: 200 #milliseconds
      min-idle: 1
      max-idle: 20
      max-wait-millis: 200 #milliseconds
      max-total: 20
    cache-duration: 10 # thời gian lưu cache (đơn vị phút)
  redisson:
    mode: single #single, sentinel, cluster, replicated
    clientName: 'redis-client'
    password: # '+dshc78(jYtMAKzM'
    subscriptionsPerConnection: 5
    idleConnectionTimeout: 10000
    connectTimeout: 10000
    timeout: 3000
    retryAttempts: 3
    retryInterval: 1500
    threads: 16
    nettyThreads: 32
    transportMode: 'NIO'
    database: 0
    cache-duration: 10 # thời gian lưu cache (đơn vị phút)
    key-prefix: mb_laos_qa
    #mode single config
    single:
      address: 'redis://*************:6378' #if use ssl, the address should start with rediss://
      subscriptionConnectionMinimumIdleSize: 1
      subscriptionConnectionPoolSize: 50
      connectionMinimumIdleSize: 24
      connectionPoolSize: 64
      dnsMonitoringInterval: 5000
    #mode sentinel config
    sentinel:
      failedSlaveReconnectionInterval: 3000
      failedSlaveCheckInterval: 60000
      subscriptionConnectionMinimumIdleSize: 1
      subscriptionConnectionPoolSize: 50
      slaveConnectionMinimumIdleSize: 24
      slaveConnectionPoolSize: 64
      masterConnectionMinimumIdleSize: 24
      masterConnectionPoolSize: 64
      pingConnectionInterval: 0
      readMode: 'SLAVE' #SLAVE/MASTER/MASTER_SLAVE
      subscriptionMode: 'SLAVE' #SLAVE/MASTER
      nodes:
        - 'redis://127.0.0.1:26379'
        - 'redis://127.0.0.1:26380'
      masterName: 'masterredis'
      checkSentinelsList: false
    cluster:
      scanInterval: 20000
      failedSlaveReconnectionInterval: 3000
      failedSlaveCheckInterval: 60000
      subscriptionConnectionMinimumIdleSize: 1
      subscriptionConnectionPoolSize: 50
      slaveConnectionMinimumIdleSize: 24
      slaveConnectionPoolSize: 64
      masterConnectionMinimumIdleSize: 24
      masterConnectionPoolSize: 64
      pingConnectionInterval: 0
      readMode: 'SLAVE' #SLAVE/MASTER/MASTER_SLAVE
      subscriptionMode: 'SLAVE' #SLAVE/MASTER
      nodes: 'rediss://clustercfg.mb-laos-redis-cluster.xar9fx.apse1.cache.amazonaws.com:6379'
    replicated:
      failedSlaveReconnectionInterval: 3000
      failedSlaveCheckInterval: 60000
      subscriptionConnectionMinimumIdleSize: 1
      subscriptionConnectionPoolSize: 50
      slaveConnectionMinimumIdleSize: 24
      slaveConnectionPoolSize: 64
      masterConnectionMinimumIdleSize: 24
      masterConnectionPoolSize: 64
      pingConnectionInterval: 0
      readMode: 'SLAVE' #SLAVE/MASTER/MASTER_SLAVE
      subscriptionMode: 'SLAVE' #SLAVE/MASTER
      nodes: 'redis://gobiz-dev-001.rlopfv.0001.apse1.cache.amazonaws.com:6379,redis://gobiz-dev-002.rlopfv.0001.apse1.cache.amazonaws.com:6379'
  time-to-lives: # time to live in seconds
    default: 0
    token: 300 # 5 phút
    remember-me-token: 600 # 2 hours
    refresh-token: 86400 # 1 day
    otp: 120 # 90 seconds
    otp-verify: 3600 # lưu id xác thực otp trong 1 giờ
    temp-file: 3600 #1 hour
    login-failed: 86400 # 1 day, lưu số lần đăng nhập lỗi để hiện captcha
    captcha: 300 # 5 phút
    password-verify: 300 # 5 phút,
    customer-draft-password: 300 # 5 phút, lưu draft password khi thay đổi pass
    api-gee-token: 300 # 5 phút, lưu token api mb t24
    customer-verify-password-failed: 3600 # 60 phút, lưu số lần xác thực lại pass thất bại
    api-umoney-token: 36000 # 10 giờ, thời gian hết hạn umoney token
    fast-login-refresh-token: 0 # Không giới hạn
    d-otp-verify-qr: 120 # 120 seconds
    otp-attempt: 180 # 3 phút
    loan-account: 600
    saving-account: 600
    currency-account: 600
    session-request-header: 86400
springfox:
  documentation:
    enabled: true #enable spring fox on deverlopment mode
task:
  execution:
    pool:
      schedule:
        max-size : 5
        allow-core-thread-timeout : true
        core-size : 10
        keep-alive : 60
        queue-capacity : 100
        name : 'mb-schedule-threadpool-'
rsa:
  key-length: 2048
  algorithm: RSA/ECB/PKCS1Padding
  private-key: 'MIIEowIBAAKCAQEAmD9evqQe1hTXdRPEJKEgkTfPGYBk0c6JCY+vLn1m4pIaaDZdJmEWrug7YwRFGfE9TMKk26M3ACGOiu7Uw267Y85/Lb3maD7CplVkhjXVVcQ8aUI4t6/BFI7wLzt7DqAmlJR4FDgTDFQl1W8ZU+QpPyP8S34s9b+yvJZAPin9Pdd23uqNtBY2RFwu+QS27Q5u6IcCLFsb1wgTb3fq/umiQ4MXRzy6jsHoUQ1Z8n305oUWsxsaC6SLCJsFSJh2w6wakScGAF9hmWdGI+PkodILxhi53zM5gWFzusasW4QQJC7tElwhn4iIcJdYF1OIAhV0RQAxjfb5kZ3/1Dm7GxBYMwIDAQABAoIBADKBiz+5GFWysksvlHkGTtR97CEsm8WSO5nFZbsJdNEi4MnDhyFNQgYiGllduELD6a8GP0Rxn7RWbYAffMucd2PDxCL7hWz9rPsbBu8S+sOqBtRwWHpvPqFATeONWuReMo5FtpQWihMj8y0b3w4aeahtmqu8ntOjlXrEaWl16oHR7pa7pjYL+bHjDSModUlFSz8odOlBYOzCEO8lB4E0xbbinMxGDQ2i/ZMLrZm9yKrqz3jVLfHdEbz4v0nEcWAUJ281ZcBgJHJXpUu/66ntmGmhYSXXhloaVw7PlYw23JQfYEEtlZqujqbaILZWVNM10d7zXnL+VhRLp9mUZxIKWSECgYEAzI4WzpUaLAftjHc1qRDvQl6o42clEPgR4Agx6ulQaeVFEB3Jd+chI2XTxJl9+NoWFpGhBhLoYAuKIa2FXD8T1LmjNkzHtjWbGYia6xgCPwx3bZeNda9/jVfAbNRUNZDN6XVQBBpHM+3NcBSb66wv36qd1oxD646Z736sxfcMJ8MCgYEAvomOZH4Wwq5bsEUB7bqmgnSO9MW5zW1tahV/H/rju7XbKz3nuRFX5C5W4ju/EHJFE7R625esc/gsc4Z+v7P+o3Xj9F8bsVefAniU5R9NzMH+JXeHSfoG6Ca6mfZcjdmeBxSfzL2w2Ntf4K5cV0HQFxWbS2yPWiQzbtBcWsPwdtECgYEAtJ/rR7OljqfZfSVuvsFzZDnODC9yLY+/yQGzgmlPVpLa7yY+VqBDRMyPuJSJBgsdYgRqGcDsbJMEAEUG87PHQRpDIpmd6ClhsaRMl7d1lFLfymf+w6KakSahhfff/ATHrpSmeVjy8snTlFq+a9Z515kDEwZ8my4qg65pxc0mcJ0CgYA2iF4lquTOwWJil82Ogb5IVh3YNoMQJYKMOyzQNVlajxj0Tlp04F7cYtrhEyKDDYFxu1TyZn2oD16BjfvW2ChIFmSwu0vMvPw4k9Rho0z8DoWzqXsiFBpH9VWw3Q7uVlthQWjfC2eDGX5eaujo3GA3SyrQMnjw3q2NyLq2C6BWEQKBgHtkq7CBhIdaSk/bY9xsdKqWAHO3z0EPPjy4JPE6+MLA4vPSG7I9vq6Wlebwag8bfXETyv3geJJ9CjP8JOBqmLIKPRBiXrXJe6LpQ4Jso1+cd9LXxRNhkRf9DwtAaijYm1gkKdM7CAHLyfknn1vMzcGK82v2BOavyFC45z/QjllL'
  public-key: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmD9evqQe1hTXdRPEJKEgkTfPGYBk0c6JCY+vLn1m4pIaaDZdJmEWrug7YwRFGfE9TMKk26M3ACGOiu7Uw267Y85/Lb3maD7CplVkhjXVVcQ8aUI4t6/BFI7wLzt7DqAmlJR4FDgTDFQl1W8ZU+QpPyP8S34s9b+yvJZAPin9Pdd23uqNtBY2RFwu+QS27Q5u6IcCLFsb1wgTb3fq/umiQ4MXRzy6jsHoUQ1Z8n305oUWsxsaC6SLCJsFSJh2w6wakScGAF9hmWdGI+PkodILxhi53zM5gWFzusasW4QQJC7tElwhn4iIcJdYF1OIAhV0RQAxjfb5kZ3/1Dm7GxBYMwIDAQAB'
#  * "0 0 * * * *" = the top of every hour of every day.
#  * "*/10 * * * * *" = every ten seconds.
#  * "0 0 8-10 * * *" = 8, 9 and 10 o'clock of every day.
#  * "0 0 8,10 * * *" = 8 and 10 o'clock of every day.
#  * "0 0/30 8-10 * * *" = 8:00, 8:30, 9:00, 9:30 and 10 o'clock every day.
#  * "0 0 9-17 * * MON-FRI" = on the hour nine-to-five weekdays
#  * "0 0 0 25 12 ?" = every Christmas Day at midnight
scheduling:
  email-queue-sender:
    cron: '0 0/1 * * * *'
    enabled: false
    sent-limit: 3
    max-result: 20
    lock-at-least: PT30S
    lock-at-most: PT50S
  mass-indexing-updater:
    cron: '0/30 0/2 * * * *'
    enabled: true
    package-scan: com.mb.laos.model
  mass-indexing-update-job:
    cron: '0 0/1 * * * *'
    enabled: false
    package-scan: com.mb.laos.model
#  send-notification:
#    cron: '*/3 * * * * *' # cứ 3s chạy 1 lần
#    enabled: true
#    lock-at-least: PT3S
#    lock-at-most: PT30M
  send-notification:
    cron: '*/30 * * * * *' # cứ 30s chạy 1 lần
    enabled: true
    lock-at-least: PT15S
    lock-at-most: PT1M
  send-event:
    cron: '0 0/1 * * * *' # cứ 1 phút chạy 1 lần
    enabled: true
    lock-at-least: PT30S
    lock-at-most: PT1M
  request-log-cleaner:
    cron: '0 0/1 * * * *' # chạy lúc nửa đêm
    enabled: true
    days-back: 7 # clear log sau 7 ngày
    lock-at-least: PT1H
    lock-at-most: PT2H
  clear-cache-manager:
    cron: '0 0 0 * * *' # chạy lúc nửa đêm
    enabled: false
    lock-at-least: PT3M
    lock-at-most: PT5M
  saving-account-status:
    cron: '0 30 5 ? * *' # chạy lúc 5h30
    enabled: true
    lock-at-least: PT3M
    lock-at-most: PT30M
  premium-acc-number:
    cron: '0 0/30 * * * *' # 30 phút chạy một lần
    enabled: true
    lock-at-least: PT3M
    lock-at-most: PT2H
otp:
  enable: false
  default-otp: ******** #OTP mặc định khi không bật OTP service
  number-of-digits: 8 #Độ dài OTP
  template: Nhap ma OTP %s gui ve so dien thoai %s de xac nhan. Ma OTP se het han sau %s phut
  otp-attempt: 3 # số lần request gửi otp nhưng không được sử dụng (tránh spam otp)
  otp-register-attempt: 5 # số lần request gửi otp tối đa 5 lần
  duration: 120 # giây
  otp-confirm-attemp: 3 # tối đa 3 lần xác thực 1 otp
customer:
  pre-register-verify-limit: 5
  login-attempts: 5
  login-failed-lock-duration: 24 # đơn vị giờ
  reset-pw-verify-limit: 5
  change-pw-verify-limit: 5
  pw-duplicated-limit: 2
  pw-duration: 336 # đơn vị giờ
  change-pw-duration: 180 # đơn vị ngày
  pw-expired-notice-before: 1,5,10 # thời gian gửi thông báo mật khẩu hết hạn, trước thời hạn 1, 5, 10 ngày
  ekyc-min-images: 6 # số ảnh ekyc tối thiểu
  ekyc-max-images: 7 # số ảnh ekyc tối đa
  ekyc-non-id-images: 5 # số ảnh ekyc khi không xác thực căn cước
  default-branch-code: ********* # mã chi nhánh mặc định bên Lào
  sms-default-language: EN # ngôn ngữ mặc định gửi sms
  id-card-issue-date-less-than: 5 # year, ngày mở không được lớn hơn 5 năm
  passport-issue-date-less-than: 10 # year, ngày mở không được lớn hơn 10 năm
  pw-verify-failed-limit: 3 # số lần tối đa xác nhận mật khẩu thất bại
  transactionId_findByCustomerId: 3600 #1 giờ, thời gian lưu transactionId trong 1 luồng giao dịch chuyển tiền
  default-currency: 'LAK'
  display-account-currency: [ 'LAK', 'USD', 'THB', 'VND' ] # khi golive, đọc từ biến môi trường thì KHÔNG CẦN dấu "[", "`", chỉ set giá trị = LAX,USD,THB,VND
  exceed-date: 30
  change-pw-time-limit: 48 # hours, giới hạn thời gian đổi mật khẩu
  cif-term-month-payment: '6516'
  cif-term-pay-interest: '6518'
  cif-unlimited-saving: '6001'
  cif-smart-saving: '6011'
  client-link-app: 'https://onelink.to/4nygcj'
  date-of-birth-less-than: 18 # year, ngày sinh không được lớn hơn 18 năm
  transaction-limit-usd: 5 # số dư tối thiểu khi giao dịch usd
  transaction-limit-thb: 200 # số dư tối thiểu khi giao dịch thb
consumer:
  timeout: 600000
  default-max-per-route: 50
  max-total: 500
  timeout-test: 100
  api-gee:
    key: 'qQcBb1cRHYtMAhzLiS7EwXvRQ6caLc6s'
    secret: 'p2SQo2FruBR9KERq'
    refresh-token-before: 5 # lấy lại token trước khi hết hạn x phút
    transaction-id-length: 20
    base-url: 'https://api-sandbox.mbbank.com.vn/ms'
    base-url-root: 'https://api-sandbox.mbbank.com.vn'
    retry:
      max-attempts: 2
      max-delay: 100
    uri:
      token: 'https://api-sandbox.mbbank.com.vn/oauth2/v1/token'
      register: '/register'
      verify-acc-number-m-b: '/mbapplaos/common/customer/v1.0/infor'
      verify-acc-number-lapnet: '/mbapplaos/common/lapnet/v1.0/customer/infor'
      inquiry-transaction-status: '/mbapplaos/fund/v1.0/inquiry-transaction-status'
      request-transfer: '/mbapplaos/fund/v1.0/make-request'
      verify-transfer: '/mbapplaos/fund/v1.1/make-confirm'
      verify-transfer-other-currency: '/mbapplaos/fund/v1.0/make-confirm'
      cashout: '/mbapplaos/fund/v1.0/make'
      revert: '/mbapplaos/fund/v1.0/revert'
      account-balance: '/mbapplaos/common/account/v1.0/balance'
      account-currency: '/mbapplaos/common/account/v1.0/get-foreign-currency-account'
      account-saving: '/mbapplaos/common/account/v1.0/get-saving-account'
      account-loan: '/mbapplaos/common/account/v1.0/get-loan-account'
      transaction-history: '/mbapplaos/common/transaction/v1.0/history'
      verify-customer: '/mbapplao/v1.0/customer/verifyCustomer'
      create-payment-account: '/mbapplao/v1.0/customer/requestCreateAccount'
      query-payment-account: '/mbapplao/v1.0/customer/requestQueryCustomer'
      confirm-create-account: '/mbapplao/v1.0/customer/confirmCreateAccount'
      confirm-query-account: '/mbapplao/v1.0/customer/confirmQueryCustomer'
      update-sector: '/mbapplao/v1.0/customer/updateSector'
      query-customer-info: '/mbapplao/v1.0/customer/getCustomer'
      cms-create-payment-account: '/mbapplao/v1.0/customer/createAccount'
      dotp-init: '/mbapplao/v1.0/customer/initDOTP'
      dotp-token-assign: '/mbapplao/v1.0/customer/tokenAssign'
      dotp-verify-provision: '/mbapplao/v1.0/customer/verifyProvision'
      dotp-init-qr-token: '/mbapplao/v1.0/customer/initQRToken'
      dotp-verify-qr-token: '/mbapplao/v1.0/customer/verifyQRToken'
      dotp-confirm-qr-token: '/mbapplao/v1.0/customer/confirmQRToken'
      dotp-delete-device: '/mbapplao/v1.0/customer/cancelRegisterDOTP'
      query-infor-interest: '/mb-lao-saving/mbapplao/v1.0/saving/getInforInterest'
      query-saving-account: '/mb-lao-saving/mbapplao/v1.0/get-saving-account'
      create-interest: '/mb-lao-saving/mbapplao/v1.0/saving/createInterest'
      closed-interest: '/mb-lao-saving/mbapplao/v1.0/saving/closedInterest'
      make-transfer-interest: '/mb-lao-saving/mbapplao/v1.0/saving/make-transfer-interest'
      init-on-off-sms-cus: '/mbapplao/v1.0/customer/gen-sms-otp'
      on-off-sms-cus: '/mbapplao/v1.0/customer/onOffSmsCus'
      query-beautiful-account: '/mbapplao/v1.0/customer/queryBeautifulAccount'
      create-beautiful-account: '/mbapplao/v1.0/customer/createBeautifulAccount/user'
      create-beautiful-account-non-user: '/mbapplao/v1.0/customer/createBeautifulAccount/nonUser'
      debit-money: '/mbapplaos/fund/v1.0/make'
  api-rabiloo:
    username: 'rabiloo'
    password: 'rabiloo'
    base-url: 'https://ekyc.rabiloo.net'
    id-card-type: 1
    id-card-regex: ^[0-9]{10,}$
    height: 1280
    width: 720
    enable-resize: false
    uri:
      ekyc-token-verify: '/backend/verify'
      ekyc-id-card-verify: '/api/v1/ocr/id'
      ekyc-face-verify: '/api/v1/face/compare'
  api-umoney:
    client-id: 'MB_BANK@cli3ntId'
    client-secret: 'MB_BANK@cli3ntId'
    username: 'MB_BANK'
    password: 'Com@1357'
    settlement-report:
      username: 'fetek'
      password: 'fetek@123'
    base-url: 'https://uat-api-cvv-um.unitel.com.la:8087/api/v1.0'
    base-url-settlement-report: 'https://uat-api-cvv-um.unitel.com.la:8087/api/v3'
    service-code: 'BANK_MB'
    success-code: '00000'
    retry:
      max-attempts: 2
      max-delay: 100
    uri:
      token-report: 'https://uat-api-cvv-um.unitel.com.la:8087/ewallet/token'
      token: 'https://uat-api-cvv-um.unitel.com.la:8087/ewallet/token'
      check-ewallet-account: '/account/getInfo'
      transfer-money: '/bank/transferMoney'
      credit-account: '/partner/creditAccount'
      settlement-report: '/lapnet/settlementReport'
  api-ltc:
    user-id: 'LOTTERY'
    private-key: '9powBIDI2FWkpx8Mff9mGSDrV+Of2SPfa2jdyBGktdc='
    algorithm: SHA-1
    cipher-algorithm: 'AES/ECB/PKCS5Padding'
    url: 'http://ltcservice.laotel.com:5577/Services.asmx'
    base-url: 'http://Services.laotel.com/'
    success-code: '20'
    soap-action:
      topup: 'http://Services.laotel.com/topup'
      payment: 'http://Services.laotel.com/payment'
      check-balance: 'http://Services.laotel.com/checkBalance'

  api-unitel:
    client_id: 'test_unitel'
    username: 'TEST_VPG'
    password: '23b8593edd4c1701fca1f1b4f412b151'
    public_key: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZiofOnG0q8PLTvX/WFlyAWqJ1ubFdy7wCqHOTEcoQFfo9FARssiLiToMt3P4r/5S8Hg+PvNuWxV1dKB+wuHPtmUisO9xaGTIgvP/Aki/O+dcrKso5cFI0wME//X5XlLvwNY1QawrYc92Q/+euGmqduOndXYEGQdcNIwu6u7ypEwIDAQAB'
    secret_key: 'MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAJmKh86cbSrw8tO9f9YWXIBaonW5sV3LvAKoc5MRyhAV+j0UBGyyIuJOgy3c/iv/lLweD4+825bFXV0oH7C4c+2ZSKw73FoZMiC8/8CSL8751ysqyjlwUjTAwT/9fleUu/A1jVBrCthz3ZD/564aap246d1dgQZB1w0jC7q7vKkTAgMBAAECgYBX/J7es52LohBFbq63TJEtrMK0m/kcOwg+rbGtceBNX4rLPZFbiKmc7kkWGzI8nHXrZ36bbCqaN/fMzpV6e/lSItDx0u/JyajsC40jeuAokR4rw5d3He6z4C7ej6btdPCxoomh00qMpJszV6KhVqZSOvA6ACV/ZdndSPlw65BjgQJBAPUIB1tHb6BHtXlu8JBV9AuWt/nSMDFQ7zgIofBdnp/naeQ6ZNEkY2AO9Kk4ok+PS7wODfLzi0mqMQFh0shYwIMCQQCgag6BoXQL41F3lL7UX7UhoSEG5xzeOcgXTalgcUIv0avlOCuBmoykEoDVz7s2ek1REP/reDBR2CaFW1ALl/AxAkEAmcYtH7rIMhVurUPTUzGuE6vFz9F6DykUx9ybDIckaoPHb8S5yosElp8sKhrxue5bACzt0h/HtTZKxOxIZRVV0wJBAI+sDYwK183h7dBFV9kMU0VodBUjn5ZleBFvDDmFlvsPNn7ZHRY6HqbAr8VQBWQYi/EEYcI65kQXbQDZtMp9bbECQQCINRr7lvYvu8f0AvzhE+Z/LONXVtXefFvl3W6e4HMmeVOPNbc8vRTS5soI+ge5lxv7Ax2lHp1WfdRXBL+PNqWA'
    url: 'https://test-api-um.unitel.com.la:8080/ws'
    base-url: 'http://ws.vpg.viettel.com/'
    mti: '0200'
    success-code: '00'
    process-code:
      query-balance-prepaid-mobile: '000100'
      topup-prepaid-mobile: '000000'
      query-debt-postpaid-mobile: '000500'
      topup-postpaid-mobile: '000600'
      query-debt-postpaid-pstn: '001600'
      topup-postpaid-pstn: '001200'
      query-debt-postpaid-ftth: '002900'
      topup-postpaid-ftth: '002700'
    rest-url: 'https://test-api-um.unitel.com.la:8080/'
    uri:
      get-balance: 'api/v1/getMasterAccountBalance'

  api-etl:
    prefix-internet-id: 'FTTH'
    userId: '***********'
    algorithm: SHA512
    sign-key: '2ac63de3c3a9e968947f7f7288b1632cf9fe12dc35783cbb1f92b5ea43c05c6793cc2f8f7e75778f4f404949a1a65c6995aa3c52511e09c4313fa8a1b6c45f74'
    url: 'https://manage.etllao.com:8889/ETLPaymentTopup/services/ETLPaymentTopupWS'
    base-url: 'https://manage.etllao.com:8889/'
    name-space-uri: 'http://webservice.etlpayemtandtopup.com'
    success-code: '*********'

  api-lvi:
    default-namespace: http://tempuri.org/
    url: https://api-test.lvi.la/api.asmx
    username: MB
    password: mbos56532@KSasdflasd@KH65
    soap-action:
      vehicle-package: http://tempuri.org/get_packageMV
      vehicle-type: http://tempuri.org/get_vehicle
      vehicle-package-fee: http://tempuri.org/get_insuranceMV
      vehicle-insurance: http://tempuri.org/get_vehicle_insurance
      buy-vehicle-insurance-new: http://tempuri.org/set_vehicle_insurance_new
      buy-vehicle-insurance-renew: http://tempuri.org/set_vehicle_insurance_renew
      health-package: http://tempuri.org/get_packagePA
      health-package-fee: http://tempuri.org/get_insurancePA
      buy-health-insurance-new: http://tempuri.org/set_accident_insurance_new
      delivery: http://tempuri.org/get_Delivery
  api-best-telecom:
    token: 'TUJCQU5LOk1CQkFOS0AxMjM0'
    success-code: 'Yes'
    success-payment: 'Success'
    add-on-code: 'Y'
    billing-code: 'N'
    add-on-type: 'ADDON'
    billing-type: 'BILL'
    url:
      validate: 'https://sandbox.payment.besttelecom.la/ava-pay/validate-mobile-number'
      payment: 'https://sandbox.payment.besttelecom.la/ava-pay/apply-mobile-payment'
    retry:
      max-attempts: 2
      max-delay: 100

  api-mmoney:
    secret-key: 'LMM@2023!'
    api-key: 'eyJhbKciOiJIUzI1NiIsInR6cCI6IkpXVCJ9.*******************************************************************.fPFaTyf_LgJ1gN1Yy5xaIcJeyKR6EKpYxQbimKERzZi'
    username: '8d8fcc1abd550c5f25dbfaa57d59cb67'
    password: '268447e2de5e5c9996a8ee132e9e07da'
    base-url: 'https://gatewayuat.mmoney.la'
    base-url-electric: 'https://gatewayuat.mmoney.la'
    base-url-water: 'https://gatewayuat.mmoney.la'
    base-url-mmoney: 'https://gatewayuat.mmoney.la'
    retry:
      max-attempts: 2
      max-delay: 100
    uri:
      token: '/electric/auth'
      list-water: '/water/list-water'
      verify-water: '/water/verify-water-payment'
      payment-water: '/water/water-payment'
      list-electric: '/electric/list-electric'
      verify-electric: '/electric/verify-electric-payment'
      payment-electric: '/electric/electric-payment'
      check-transaction: '/electric/check-transaction'
      list-leasing: '/leasing/list-leasing'
      verify-leasing: '/leasing/verify-leasing-payment'
      payment-leasing: '/leasing/leasing-payment'
      verify-postpaid: '/telecom/verify-postpaid'
      verify-internet: '/telecom/verify-internet'
      verify-pstn: '/telecom/verify-pstn'
      payment-postpaid: '/telecom/payment-postpaid'
      payment-internet: '/telecom/payment-internet'
      payment-pstn: '/telecom/payment-pstn'

# base64
firebase:
  config: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

notification:
  end-user-type: 'CUSTOMER'
  topic-system-notification: 'SYSTEM_NOTIFICATION'
  thread-pool: 10 #Số luồng tối đa khi thực hiện thông báo
  transaction-type:
    inter-bank: 222  #Nhận tiền từ bank khác về MBLAOS
    cash-in-counter: 52 # Cộng tiền (thanh toán tại quầy)
    internal-bank: 213 # Thông báo cộng tiền nội bộ
  time-between: 30 # tối đa trong khoảng thời gian 30 ngày
  time-between-event: P90D # tối đa trong khoảng thời gian 90 ngày
  timeline-between: 90 # tối đa mốc thời gian là 90 ngày
  max-period-search-data: 90 # tối đa mốc thời gian là 90 ngày cho hiển thị thông báo khác và khuyến mãi
  url-get-image-firebase: 'http://qa-mb-laos-gateway-api.evotek.vn/api/gateway/v1/notifications/public/icon'
  entry-prefix: AZ
  closure-prefix: ONLINE.AC.CLOSURE
  sms-balance-msg: TransInter Charge fee SMS banking date
  salaryMsg: SALARY

application:
  constants:
    mb-api:
      otp-gen:
        account-type: 'ACCOUNT'
        qr-type: 'QR'
      inbank-transfer:
        account-type: 'ACCOUNT'
        transfer-type: 'INHOUSE'
        channel: 'MB'
        service-type: 'ACCOUNT'
        service-type-qr: 'QR'
        currency: 'LAK'
      cash-in-transfer-api:
        account-type: 'ACCOUNT'
        transfer-type: 'EWALLET'
        channel: 'UMONEY'
        service-type: 'CASH_IN'
        currency: 'LAK'
        defaultUmoneyAccount: '************'
      lapnet-transfer:
        transfer-type: 'FAST'
        channel: 'LAPNET'
        service-type: 'OUT'
        add-infor-no: 'BEN.ACCT.NO'
        add-infor-name: 'BEN.CUSTOMER'
        add-infor-type: 'T24'
        add-infor-no-value: '0'
        fee-free-number-transaction-in-month: 5
        fee-free-total-amount-in-month: 2500000
      topup-transfer:
        transfer-type: 'PAYMENT'
        channel: 'MB'
        service-type: 'TOPUP'
        default-merchant-account: '************'
      qr-payment:
        transfer-type: 'PAYMENT'
        channel: 'MB'
        service-type: 'QR'
      billing-payment:
        transfer-type: 'PAYMENT'
        channel: 'MB'
        service-type: 'BILLING'
      insurance-payment:
        transfer-type: 'PAYMENT'
        channel: 'MB'
        service-type: 'BILLING'
      cashout:
        channel-lapnet: LAPNET
        transfer-type: EWALLET
        service-type: CASH_OUT
        mb-codes: [ 'MB', 'VMB' ]
      deposit-debit:
        transfer-type: 'THU_CHI_HO'
        channel: 'UMONEY'
        service-type-debit: 'CHI_HO_FINTECH'
        service-type-deposit: 'THU_HO_FINTECH'
    qr-code:
      payload-format-indicator: '000201'     # Phiên bản QR đang sử dụng
      point-of-initiation-transfer: '010212' # 11: nhiều hơn 1 giao dịch (payment)
      point-of-initiation-payment: '010211'  # 12: từng giao dịch
      merchant-account-info:
        laos-id: '40'
        umoney-id: '29'
        cam-tag:
          id: '30'
          sub-tag:
            merchant-id: '00'
            account-number: '01'
            bank-code: '02'
        lapnet-id: '38'
        transnational-id: '15'
        laoviet-id: '27'
        bcel-id: '33'
        jdb-id: '35'
        globally-id: '00'
        globally-value: 'MBLAOS'
        globally-aid-value: 'A005266284662577'
        iin-id: '01'
        iin-value: 'VMB'
        payment-type-id: '02'
        payment-type-transfer-value: '001'
        payment-type-qr-value: '002'
        receiver-id: '03'
        merchant-category-value: '5311'
        transaction-currency-value: '418'
        country-code-value: 'LA'
        merchant-city-value: 'Vientiane'
        account-number: '01'
        master-merchant-name: '02'
        merchant-id: '03'
        merchant-name: '04'
      merchant-category-id: '52'
      transaction-currency-id: '53'
      transaction-amount-id: '54'
      tip-indicator: '55'
      country-code-id: '58'
      merchant-name: '59'
      merchant-city: '60'
      merchant-account: '50'
      postal-code: '61'
      data-object-id: '62'
      cyclic-redundancy-check-id: '63'
      cyclic-redundancy-check-value: '04'
      payment-qr:
        invoice-id: '01'
        phone-number-id: '02'
        store-label-id: '03'
        loyalty-id: '04'
        referencey-label-id: '05'
        customer-label-id: '06'
        terminal-label-id: '07'
        purpose-transaction-id: '08'
      union-pay-tag:
        id: '15'
        length: '31'
        acquirer-iin: '********' # mã ngân hàng lào
        forwarding-iin: '********' # cố định
    lvi:
      account-bank: MB
      account-number: ************
      account-name: Lao-Viet insurance Co.,Ltd
    umoney:
      account-bank: MB
      account-number: ************
      account-name: STAR FINTECH SOLE CO.,LTD
      account-type: ACCOUNT
      account-currency: LAK
websocket:
  endpoint: /ws
  application-prefix: /app
  topic-notification: /topic/notification
  topic-prefix: /topic
  allow-origins:
    - '*'
loan-online:
  limit-loan-registration-per-day: 5
  max-period-export-data: P90D #day

dynamic-key:
  enabled: false
  primary-ip: **************
  secondary-ip: *************
  port: 19957
  user-id: DynkeyDefaultUser
  token-id: 2237DF73A7
  device-id: 120163920534a6ed351ec3e5a69e9a59b870f188
  service-id: MBAPP
  validity-wnd: 150
  encrypt-mode: 0
  token-type: 3
  spid: 11

dynamic-link: # https://your_subdomain.page.link/?link=your_deep_link&apn=package_name[&amv=minimum_version][&afl=fallback_link]
  sub-domain: https://mbbanklaosqa.page.link/
  deep-link: https://mbbanklaosqa.page.link/referrals?referralsCode%3D
  package-name: la.com.mbbank.mobilebanking.dev

d-otp:
  qr-code-init-token-expired-time: PT180S # thời gian hết hạn của qr là 180s
  max-device: 2
  verify-provision: 'verifyProvisioning' # Thông tin giao dịch - Mã nối verifyProvisioning_Token_userId
transfer-trans:
  max-period: P30D #day
  exceed-date-period: P90D #day
saving-account:
  deposits-fixed: 1000000
  number-day-scanner: 3 # số  ngày muốn quét
merchant:
  max-merchant: 50 #Số lượng tối đa tạo merchant con
statistical:
  max-period: P30D # day
  date: '01-10-2023'
on-off-sms:
  service-fee: 5000
premium-acc-number:
  phone-number-prefix: ['020', '030']
  fee-code: OTHACC
  sub-product: 719
  default-price: 50000
  discount: 1.2
international-payment:
  min-amount-khr: 100
api-config:
  url: 'http://qa-mb-laos-gateway-api.evotek.vn/api/gateway/v1'
session-header:
  enabled: false
template:
  style:
    style-file: 'ckeditor.txt'
  notification-history:
    template-file: 'notification_history_template.xlsx'
    template-file-name: 'notification_history_template_%s.xlsx'
