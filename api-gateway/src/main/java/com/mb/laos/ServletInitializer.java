package com.mb.laos;

import java.util.Arrays;
import java.util.EnumSet;
import java.util.List;
import javax.servlet.DispatcherType;
import javax.servlet.FilterRegistration;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletContextInitializer;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.ResourceHttpMessageConverter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mb.laos.cache.CachingHttpHeadersFilter;
import com.mb.laos.configuration.DecryptedConverter;
import com.mb.laos.security.configuration.AuthenticationProperties;
import com.mb.laos.service.DynamicKeyService;
import com.mb.laos.util.EnvConstants;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration

@RequiredArgsConstructor
public class ServletInitializer extends SpringBootServletInitializer implements ServletContextInitializer {
    private final AuthenticationProperties ap;

    private final Environment env;

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(GatewayApplication.class);
    }

    @Override
    public void onStartup(ServletContext servletContext) throws ServletException {
        if (env.getActiveProfiles().length != 0) {
            _log.info("Web application configuration, using profiles: {}", (Object[]) env.getActiveProfiles());
        }

        EnumSet<DispatcherType> disps = EnumSet.of(DispatcherType.REQUEST, DispatcherType.FORWARD,
                        DispatcherType.ASYNC);

        if (env.acceptsProfiles(Profiles.of(EnvConstants.Profile.PRODUCTION))) {
            initCachingHttpHeadersFilter(servletContext, disps);
        }

        _log.info("Web application fully configured");
    }

    /**
     * Initializes the caching HTTP Headers Filter.
     */
    private void initCachingHttpHeadersFilter(ServletContext servletContext, EnumSet<DispatcherType> disps) {
        _log.info("Registering Caching HTTP Headers Filter");

        FilterRegistration.Dynamic cachingHttpHeadersFilter = servletContext.addFilter("cachingHttpHeadersFilter",
                        new CachingHttpHeadersFilter());

        cachingHttpHeadersFilter.addMappingForUrlPatterns(disps, true, ap.getUrlPatterns());

        cachingHttpHeadersFilter.setAsyncSupported(true);
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();

        CorsConfiguration config = new CorsConfiguration();

        config.setAllowedHeaders(
                        Arrays.asList(StringUtil.split(ap.getAllowedHeaders(), StringPool.COMMA)));
        config.setAllowedMethods(
                        Arrays.asList(StringUtil.split(ap.getAllowedMethods(), StringPool.COMMA)));
        config.setAllowedOriginPatterns(
                        Arrays.asList(StringUtil.split(ap.getAllowedOrigins(), StringPool.COMMA)));
        config.setExposedHeaders(
                        Arrays.asList(StringUtil.split(ap.getExposedHeaders(), StringPool.COMMA)));
        config.setAllowCredentials(ap.isAllowCredentials());
        config.setMaxAge(ap.getMaxAge());

        if (Validator.isNotNull(config.getAllowedOriginPatterns())) {
            _log.info("Registering CORS filter");

            source.registerCorsConfiguration("/**", config);
            source.registerCorsConfiguration("/v2/api-docs", config);
        }

        return new CorsFilter(source);
    }

    // @Bean
    @ConditionalOnBean(DynamicKeyService.class)
    public WebMvcConfigurer webMvcConfigurer(DynamicKeyService dynamicKeyService, ObjectMapper objectMapper) {
        return new WebMvcConfigurer() {
            @Override
            public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
                WebMvcConfigurer.super.configureMessageConverters(converters);

                converters.add(new DecryptedConverter(dynamicKeyService, objectMapper));
                converters.add(new ByteArrayHttpMessageConverter());
                converters.add(new ResourceHttpMessageConverter());
            }
        };
    }
}
