package com.mb.laos.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import org.hibernate.annotations.Type;
import com.mb.laos.util.Constants;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Entity
@Table(name = "CUSTOMER_DEVICE_HIS")
@EqualsAndHashCode(callSuper = false)
public class CustomerDeviceHis extends AbstractAuditingEntity {

	private static final long serialVersionUID = -5761661058956380558L;

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CUSTOMER_DEVICE_HIS_SEQ")
	@SequenceGenerator(sequenceName = "CUSTOMER_DEVICE_HIS_SEQ", name = "CUSTOMER_DEVICE_HIS_SEQ", initialValue = 1,
			allocationSize = 1)
	@Column(name = "CUSTOMER_DEVICE_HIS_ID", length = 19)
	private long customerDeviceHisId;

	@Column(name = "DEVICE_TOKEN", length = 255)
	private String deviceToken;
	
    @Column(name = "DEVICE_ID", length = 255)
    private String deviceId;

	@Column(name = "CUSTOMER_ID")
	private Long customerId;
	
	@Column(name = "USERNAME", length = 75)
	@Type(type = Constants.HibernateDefName.ENCRYPTED_STRING)
	private String username;

	@Column(name = "STATUS", nullable = false, length = 2)
	private int status;

	@Column(name = "DEVICE_NAME")
	private String deviceName;

}
