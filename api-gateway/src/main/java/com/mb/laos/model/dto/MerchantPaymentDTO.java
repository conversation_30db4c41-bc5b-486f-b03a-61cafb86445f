package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MerchantPaymentDTO implements Serializable {
    private static final long serialVersionUID = -4053044848161641750L;

    private Long merchantId;
    private String merchantName;
    private QrCodeDTO qrCode;

    public MerchantPaymentDTO(String merchantName, QrCodeDTO qrCode, Long merchantId) {
        this.qrCode = qrCode;
        this.merchantName = merchantName;
        this.merchantId = merchantId;
    }
}
