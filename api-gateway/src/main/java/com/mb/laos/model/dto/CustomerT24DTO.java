package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.Gender;
import com.mb.laos.util.Validator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerT24DTO {
    private String username;

    private String fullname;

    private String phoneNumber;

    private String phoneContact;

    private String dateOfBirth;

    private String email;

    private Integer gender;

    private String idCardTypeId;

    private String idCardTypeName;

    private String idCardNumber;

    private String issueDate;

    // Nơi cấp
    private String issuePlace;

    private String nationCode;

    private String nationName;

    private String nationCodeOther;

    private String nationOtherName;

    private String deviceToken;

    private String placeOfOrigin;

    private String residentStatus;

    private String currentAddress;

    private String placeOfResidence;

    private String placeOfResidenceOutCountry;

    private String job;

    private String position;

    private String cif;

    private String customerSectorId;

    private String maritalStatus;

    private String staffCode;

    private String reason;

    private String branchCode;

    private String smsLanguage;

    public CustomerT24DTO(CustomerInfoT24DTO customerInfoT24DTO) {
        this.fullname = customerInfoT24DTO.getFullName();
        this.dateOfBirth = customerInfoT24DTO.getDateOfBirth();
        if (Validator.isNotNull(customerInfoT24DTO.getGender())) {
            Gender gender = Gender.valueOf(customerInfoT24DTO.getGender());
            this.gender = gender.getValue();
        }
        this.idCardNumber = customerInfoT24DTO.getIdNumber();
        this.issueDate = customerInfoT24DTO.getDateIssue();
        this.issuePlace = customerInfoT24DTO.getPlaceIssue();
        this.currentAddress = customerInfoT24DTO.getCurrentAddress();
        this.placeOfResidence = customerInfoT24DTO.getResidentialAddress();
    }
}
