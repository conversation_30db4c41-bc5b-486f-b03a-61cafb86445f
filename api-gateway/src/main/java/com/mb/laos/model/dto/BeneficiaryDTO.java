package com.mb.laos.model.dto;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.annotation.Exclude;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.gateway.request.BeneficiaryRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO đại diện cho người thụ hưởng (beneficiary)
 *
 * Người thụ hưởng là một người hoặc một tổ chức nhận được tiền từ một giao dịch.
 * Trong hệ thống MB Laos, người thụ hưởng được lưu trữ trong bảng T_BENEFICIARY.
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2023
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BeneficiaryDTO implements Serializable {
    private static final long serialVersionUID = 2361109732412055517L;

    /**
     * ID của người thụ hưởng
     */
    private Long beneficiaryId;

    /**
     * ID của khách hàng (bị loại trừ khi serialize)
     */
    @Exclude
    private Long customerId;

    /**
     * Tên khách hàng thụ hưởng
     */
    private String beneficiaryCustomerName;

    /**
     * ID khách hàng thụ hưởng
     */
    private Long beneficiaryCustomerId;

    /**
     * Số tài khoản khách hàng (bị loại trừ khi serialize)
     */
    @Exclude
    private String customerAccNumber;

    /**
     * Số tài khoản người thụ hưởng (bị loại trừ khi serialize)
     */
    @Exclude
    private String beneficiaryAccountNumber;

    /**
     * Mã ngân hàng của người thụ hưởng
     */
    private String beneficiaryBankCode;

    /**
     * Tên ngân hàng của người thụ hưởng
     */
    private String beneficiaryBankName;

    /**
     * ID ngân hàng của người thụ hưởng
     */
    private Long beneficiaryBankId;

    /**
     * Tên gợi nhớ cho người thụ hưởng
     */
    private String reminiscentName;

    /**
     * Trạng thái của người thụ hưởng
     */
    private Integer status;

    /**
     * Mô tả thêm
     */
    private String description;

    /**
     * Icon đại diện cho người thụ hưởng
     */
    private DocumentDTO icon;

    public BeneficiaryDTO(TransactionDTO request) {
        this.beneficiaryAccountNumber = request.getTarget();
        this.customerId = request.getCustomerId();
        this.beneficiaryCustomerId = request.getBeneficiaryCustomerId();
        this.customerAccNumber = request.getCustomerAccNumber();
        this.beneficiaryBankCode = request.getBankCode();
        this.status = EntityStatus.ACTIVE.getStatus();
        this.beneficiaryCustomerName = request.getBeneficiaryCustomerName();
        this.reminiscentName = request.getBeneficiaryCustomerName();
    }

    public void delete() {
        this.status = EntityStatus.DELETED.getStatus();
    }

    public void update(BeneficiaryRequest request) {
        this.reminiscentName = request.getReminiscentName();
    }
}
