package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.validator.ValidationConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MerchantUpdateDTO implements Serializable {
    private static final long serialVersionUID = -4053044848161641750L;
    private Long merchantId;

    @Size(max = ValidationConstraint.LENGTH.MERCHANT.MERCHANT_NAME_MAX_LENGTH, message = LabelKey.ERROR_MERCHANT_NAME_MAX_LENGTH)
    private String merchantName;
}
