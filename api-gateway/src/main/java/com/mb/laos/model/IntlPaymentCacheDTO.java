// Package chứa các model classes cho api-gateway module
package com.mb.laos.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * IntlPaymentCacheDTO - DTO cache cho thanh toán quốc tế
 *
 * Class này được sử dụng để cache thông tin thanh toán quốc tế giữa các bước:
 * 1. Verify beneficiary (QR Code verification)
 * 2. Fee calculation (exchange rate và fee processing)
 * 3. Confirm payment (final transaction execution)
 *
 * Cache Strategy:
 * - Lưu trữ trong Redis với TTL (Time To Live)
 * - Key: transaction ID
 * - Value: IntlPaymentCacheDTO object (serialized)
 * - TTL: 15 phút (đủ thời gian cho user complete transaction)
 *
 * Use Cases:
 * - Cross-border QR payments (LAK → KHR, LAK → THB)
 * - International money transfers
 * - Multi-step transaction validation
 * - Exchange rate consistency across steps
 *
 * Security Considerations:
 * - QR Code value validation để prevent manipulation
 * - Transaction ID uniqueness
 * - Cache expiration để prevent stale data
 * - Sensitive data encryption trong cache
 *
 * Business Flow:
 * Step 1: Verify QR → Create cache với QR info
 * Step 2: Calculate fee → Update cache với beneficiary info
 * Step 3: Confirm payment → Consume cache và execute transaction
 *
 * <AUTHOR> Laos Development Team
 * @version 1.0
 * @since 2023
 */
@Getter                    // Lombok: Tự động generate getter methods cho tất cả fields
@Builder                   // Lombok: Tạo builder pattern để khởi tạo object dễ dàng
@AllArgsConstructor        // Lombok: Tạo constructor với tất cả parameters
@Setter                    // Lombok: Tự động generate setter methods cho tất cả fields
public class IntlPaymentCacheDTO implements Serializable {

    /**
     * Số tiền giao dịch trong ngoại tệ (Foreign Currency Amount)
     *
     * Đây là số tiền gốc mà customer muốn gửi, tính bằng LAK.
     * Sẽ được convert sang currency đích theo exchange rate.
     *
     * Ví dụ:
     * - Customer muốn gửi 100,000 LAK
     * - Exchange rate LAK/KHR = 4.0
     * - Beneficiary sẽ nhận được 400,000 KHR
     *
     * Business Rules:
     * - Phải > 0 (không cho phép số âm)
     * - Phải nằm trong transaction limits
     * - Được validate với minimum transfer amount
     *
     * Usage:
     * - Fee calculation: Tính phí dựa trên amount này
     * - Exchange rate: Convert sang destination currency
     * - Limit checking: Validate với daily/monthly limits
     * - Audit trail: Log original amount cho compliance
     */
    private Double foreignAmount;

    /**
     * Loại tiền tệ của người thụ hưởng (Destination Currency)
     *
     * Currency mà beneficiary sẽ nhận được sau khi convert.
     * Phải match với currency được support bởi destination country.
     *
     * Supported Currencies:
     * - "KHR": Cambodian Riel (Cambodia)
     * - "THB": Thai Baht (Thailand) - planned
     * - "USD": US Dollar (international) - planned
     * - "VND": Vietnamese Dong (Vietnam) - planned
     *
     * Business Logic:
     * - Quyết định exchange rate nào được sử dụng
     * - Ảnh hưởng đến fee calculation
     * - Validate với supported corridors
     * - Compliance với international regulations
     *
     * Examples:
     * - LAK → KHR: Cross-border payment to Cambodia
     * - LAK → THB: Cross-border payment to Thailand
     * - LAK → USD: International wire transfer
     *
     * Validation Rules:
     * - Phải nằm trong danh sách supported currencies
     * - Phải match với destination country regulations
     * - Exchange rate phải available cho currency pair
     */
    private String beneficiaryCurrency;

    /**
     * Giá trị QR Code được scan (QR Code Content)
     *
     * Raw content của QR Code mà customer đã scan.
     * Được sử dụng để validate consistency giữa các bước.
     *
     * QR Code Structure (EMVCo Standard):
     * - Payload Format Indicator (00): "01"
     * - Point of Initiation Method (01): "11" hoặc "12"
     * - Merchant Account Information (26-51): Bank và account info
     * - Transaction Currency (53): Currency code
     * - Transaction Amount (54): Amount (optional)
     * - Country Code (58): "KH" cho Cambodia
     * - Merchant Name (59): Tên merchant/beneficiary
     * - Merchant City (60): Thành phố
     * - CRC (63): Checksum
     *
     * Security Validation:
     * - CRC checksum verification
     * - Format validation theo EMVCo standard
     * - Anti-tampering check giữa verify và confirm steps
     * - Expiry time validation (nếu có)
     *
     * Business Usage:
     * - Parse để extract beneficiary information
     * - Validate với LAPNET network
     * - Prevent QR Code manipulation attacks
     * - Audit trail cho compliance
     *
     * Example QR Content:
     * "00020101021126580014com.aba.mobile01091234567890520459995303840540510.005802KH5913JOHN DOE6010PHNOM PENH6304ABCD"
     */
    private String qrCodeValue;

    /**
     * Tên người thụ hưởng (Beneficiary Name)
     *
     * Tên chủ tài khoản đích, được lấy từ:
     * - QR Code merchant information (field 59)
     * - LAPNET account verification response
     * - Bank account lookup results
     *
     * Name Formats:
     * - Individual: "JOHN DOE", "NGUYEN VAN A"
     * - Business: "ABC COMPANY LTD", "XYZ RESTAURANT"
     * - Government: "MINISTRY OF FINANCE", "TAX DEPARTMENT"
     *
     * Validation Rules:
     * - Không được empty hoặc null
     * - Length: 1-100 characters
     * - Allowed characters: A-Z, 0-9, space, hyphen
     * - No special characters để tránh injection attacks
     *
     * Business Usage:
     * - Hiển thị cho customer confirmation
     * - AML/CFT compliance checking
     * - Transaction description
     * - Audit trail và investigation
     * - Customer service support
     *
     * Security Considerations:
     * - Sanitize để prevent XSS attacks
     * - Validate với sanctions lists
     * - Log cho compliance monitoring
     * - Encrypt trong cache storage
     */
    private String beneficiaryCustomerName;

    /**
     * ID giao dịch duy nhất (Unique Transaction Identifier)
     *
     * UUID được generate để track transaction qua toàn bộ lifecycle.
     * Được sử dụng làm cache key và correlation ID.
     *
     * Generation Strategy:
     * - UUID v4 (random): Đảm bảo uniqueness
     * - Format: "550e8400-e29b-41d4-a716-************"
     * - Generated tại verify beneficiary step
     * - Consistent across tất cả steps
     *
     * Usage Scenarios:
     * - Cache key trong Redis: "intl_payment:{transactionId}"
     * - Correlation ID cho logging và monitoring
     * - Reference number cho customer support
     * - Idempotency key để prevent duplicate transactions
     * - Audit trail linking
     *
     * Business Rules:
     * - Phải unique trong hệ thống
     * - Không được reuse sau khi transaction complete
     * - TTL trong cache: 15 phút
     * - Permanent storage trong transaction log
     *
     * Security Features:
     * - Unpredictable (UUID v4 random)
     * - No sequential pattern
     * - Safe để expose trong APIs
     * - Audit trail correlation
     *
     * Integration Points:
     * - Redis cache key
     * - Database transaction reference
     * - External system correlation
     * - Customer notification reference
     */
    private String transactionId;

    /**
     * Mã ngân hàng của người thụ hưởng (Beneficiary Bank Code)
     *
     * Identifier của ngân hàng đích trong destination country.
     * Được sử dụng để route payment đến đúng financial institution.
     *
     * Bank Code Standards:
     * - Cambodia: ABA Bank codes
     *   + "ABA": ABA Bank
     *   + "ACLEDA": ACLEDA Bank
     *   + "CANADIA": Canadia Bank
     *   + "FTB": Foreign Trade Bank
     *
     * - Thailand: Thai bank codes (planned)
     *   + "BBL": Bangkok Bank
     *   + "SCB": Siam Commercial Bank
     *   + "KTB": Krung Thai Bank
     *
     * - International: SWIFT BIC codes (planned)
     *   + "ABKLKHPP": ABA Bank Cambodia
     *   + "BKKBTHBK": Bangkok Bank Thailand
     *
     * Business Logic:
     * - Routing logic cho payment processing
     * - Fee calculation (bank-specific rates)
     * - Compliance checking (sanctions, restrictions)
     * - Settlement network selection
     *
     * Validation Rules:
     * - Phải nằm trong supported bank list
     * - Active status trong partner network
     * - Available cho destination currency
     * - Compliance với regulatory requirements
     *
     * Integration Usage:
     * - LAPNET routing decisions
     * - Partner bank API selection
     * - Fee calculation engine input
     * - Compliance screening
     * - Settlement instruction generation
     *
     * Examples:
     * - "ABA": Route to ABA Bank Cambodia via LAPNET
     * - "ACLEDA": Route to ACLEDA Bank Cambodia
     * - "BBL": Route to Bangkok Bank Thailand (future)
     */
    private String beneficiaryBankCode;
}
