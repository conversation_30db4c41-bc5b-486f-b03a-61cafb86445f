package com.mb.laos.gateway.controller;

import com.mb.laos.annotation.InboundRequestLog;
import com.mb.laos.api.request.EmptyRequest;
import com.mb.laos.api.response.ListElectricMmoneyResponse;
import com.mb.laos.api.response.ListWaterMmoneyResponse;
import com.mb.laos.gateway.request.ConfirmOtpBillingV2Request;
import com.mb.laos.gateway.request.OtpUtilityRequest;
import com.mb.laos.gateway.request.UtilityRequest;
import com.mb.laos.gateway.response.BillingHistoryV2Response;
import com.mb.laos.gateway.response.BillingResponse;
import com.mb.laos.gateway.response.OtpTransferConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferTransResponse;
import com.mb.laos.model.search.BillingHistoryV2Search;
import com.mb.laos.service.UtilityService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * Controller xử lý thanh toán hóa đơn tiện ích
 *
 * Controller này cung cấp dịch vụ thanh toán các hóa đơn tiện ích:
 *
 * ⚡ HÓA ĐƠN ĐIỆN:
 * - EDL (Electricité du Laos): Công ty điện lực quốc gia
 * - Các nhà cung cấp điện địa phương
 *
 * 💧 HÓA ĐƠN NƯỚC:
 * - Nam Papa: Công ty cấp nước Vientiane
 * - Các công ty cấp nước địa phương
 *
 * 🌐 INTERNET & TV:
 * - LTC: Internet, TV, điện thoại cố định
 * - Unitel: Internet, TV
 * - ETL: Internet, TV
 *
 * Luồng thanh toán:
 * 1. Lấy danh sách nhà cung cấp
 * 2. Verify billing code (kiểm tra hóa đơn)
 * 3. Request OTP (tạo giao dịch)
 * 4. Confirm OTP (thực hiện thanh toán)
 * 5. Xem lịch sử thanh toán
 *
 * Endpoint base: /utility
 *
 * <AUTHOR> Development Team
 * @version 2.0
 * @since 2023
 */
@RestController
@RequestMapping("/utility")
@RequiredArgsConstructor
public class UtilityController {

    /** Service xử lý business logic cho thanh toán tiện ích */
    private final UtilityService utilityService;

    /**
     * Xác thực mã hóa đơn tiện ích
     *
     * API này kiểm tra mã hóa đơn và lấy thông tin thanh toán:
     * - Validate billing code với nhà cung cấp
     * - Lấy thông tin khách hàng (tên, địa chỉ)
     * - Lấy số tiền cần thanh toán
     * - Kiểm tra trạng thái hóa đơn (chưa thanh toán/đã thanh toán/quá hạn)
     *
     * Các loại hóa đơn:
     * - Điện: EDL và các nhà cung cấp địa phương
     * - Nước: Nam Papa và các công ty cấp nước
     * - Internet/TV: LTC, Unitel, ETL
     *
     * @param request HTTP request
     * @param billingRequest Thông tin hóa đơn (billing code, provider ID, billing type)
     * @return BillingResponse Thông tin hóa đơn và số tiền cần thanh toán
     */
    @InboundRequestLog
    @PostMapping("/billing/verify-billing-code")
    public ResponseEntity<BillingResponse> inquiryPhoneNumber(HttpServletRequest request,
                                                                @RequestBody @Valid UtilityRequest billingRequest) {
        return ResponseEntity.ok(this.utilityService.verifyBillingCode(billingRequest));
    }

    /**
     * Tạo OTP cho thanh toán hóa đơn tiện ích
     *
     * API này tạo giao dịch thanh toán và gửi OTP:
     * - Validate thông tin hóa đơn từ bước verify
     * - Kiểm tra số dư tài khoản khách hàng
     * - Tạo transaction record
     * - Gửi OTP qua SMS
     *
     * @param request HTTP request
     * @param otpRequest Thông tin thanh toán (billing code, account, amount)
     * @return OtpTransferTransResponse Chứa OTP info và transaction ID
     */
    @InboundRequestLog
    @PostMapping("/billing/request-otp")
    public ResponseEntity<OtpTransferTransResponse> requestOtpBilling(HttpServletRequest request,
                                                                        @RequestBody @Valid OtpUtilityRequest otpRequest) {
        return ResponseEntity.ok(this.utilityService.requestOtpBilling(otpRequest, request));
    }

    /**
     * Xác thực OTP và thực hiện thanh toán hóa đơn
     *
     * API này hoàn tất thanh toán hóa đơn tiện ích:
     * - Xác thực OTP từ khách hàng
     * - Trừ tiền từ tài khoản MB Bank
     * - Gọi API nhà cung cấp để thanh toán hóa đơn
     * - Cập nhật trạng thái giao dịch
     * - Gửi thông báo kết quả
     *
     * @param request HTTP request
     * @param confirmRequest Chứa OTP và transaction ID
     * @return OtpTransferConfirmResponse Kết quả thanh toán
     */
    @InboundRequestLog
    @PostMapping("/confirm-billing")
    public ResponseEntity<OtpTransferConfirmResponse> confirmOtpBilling(HttpServletRequest request,
                                                                          @RequestBody ConfirmOtpBillingV2Request confirmRequest) {
        return ResponseEntity.ok(this.utilityService.confirmOtpBilling(confirmRequest, request));
    }

    /**
     * Lấy danh sách nhà cung cấp nước
     *
     * API trả về danh sách tất cả công ty cấp nước tại Lào:
     * - Nam Papa (Vientiane Capital)
     * - Các công ty cấp nước tỉnh
     * - Thông tin: tên, mã nhà cung cấp, khu vực phục vụ
     *
     * @param request HTTP request
     * @param emptyRequest Empty request body
     * @return List<ListWaterMmoneyResponse> Danh sách nhà cung cấp nước
     */
    @InboundRequestLog
    @GetMapping("/water/list")
    public ResponseEntity<List<ListWaterMmoneyResponse>> waterSupplier(HttpServletRequest request, EmptyRequest emptyRequest) {
        return ResponseEntity.ok().body(this.utilityService.waterSuppliers(request));
    }

    /**
     * Lấy danh sách nhà cung cấp điện
     *
     * API trả về danh sách tất cả công ty điện lực tại Lào:
     * - EDL (Electricité du Laos) - công ty quốc gia
     * - Các công ty điện lực địa phương
     * - Thông tin: tên, mã nhà cung cấp, khu vực phục vụ
     *
     * @param request HTTP request
     * @param emptyRequest Empty request body
     * @return List<ListElectricMmoneyResponse> Danh sách nhà cung cấp điện
     */
    @InboundRequestLog
    @GetMapping("/electric/list")
    public ResponseEntity<List<ListElectricMmoneyResponse>> electricSuplier(HttpServletRequest request, EmptyRequest emptyRequest) {
        return ResponseEntity.ok().body(this.utilityService.electricSupplies(request));
    }

    /**
     * Lấy lịch sử thanh toán hóa đơn tiện ích
     *
     * API trả về lịch sử thanh toán hóa đơn của khách hàng:
     * - Danh sách hóa đơn đã thanh toán
     * - Thông tin: billing code, nhà cung cấp, số tiền, thời gian
     * - Hỗ trợ phân trang và filter theo thời gian
     * - Có thể filter theo loại hóa đơn (điện/nước/internet)
     *
     * @param request HTTP request
     * @param historyV2Search Điều kiện tìm kiếm và phân trang
     * @return List<BillingHistoryV2Response> Lịch sử thanh toán hóa đơn
     */
    @InboundRequestLog
    @PostMapping("/billing/history")
    public ResponseEntity<List<BillingHistoryV2Response>> historyBilling(HttpServletRequest request,
                                                                        @RequestBody @Valid BillingHistoryV2Search historyV2Search) {
        return ResponseEntity.ok(this.utilityService.getHistoryBilling(historyV2Search));
    }
}
