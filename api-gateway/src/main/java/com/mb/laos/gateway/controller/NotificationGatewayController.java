package com.mb.laos.gateway.controller;

import com.mb.laos.annotation.InboundRequestLog;
import com.mb.laos.controller.request.NotificationBalanceClientRequest;
import com.mb.laos.controller.request.NotificationChargeClientRequest;
import com.mb.laos.model.dto.NotificationLapnetDTO;
import com.mb.laos.service.NotificationGatewayService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * Controller xử lý thông báo từ hệ thống bên ngoài
 *
 * Controller này nhận các webhook/callback từ:
 * - T24 Core Banking: Thông báo cộng tiền, trừ tiền
 * - LAPNET: Thông báo giao dịch liên ngân hàng
 * - Telco: Thông báo thanh toán hóa đơn thất bại
 * - External Systems: Các thông báo từ hệ thống khác
 *
 * Chức năng chính:
 * - Nhận thông báo cộng tiền từ nơi khác về MB Laos
 * - Xử lý thông báo thanh toán thất bại
 * - Cập nhật trạng thái giao dịch
 * - Gửi push notification cho khách hàng
 *
 * Endpoint base: /notifications
 *
 * <AUTHOR> Development Team
 * @version 2.0
 * @since 2023
 */
@RestController
@RequestMapping("/notifications")
@RequiredArgsConstructor
public class NotificationGatewayController {

    /** Service xử lý business logic cho notification gateway */
    private final NotificationGatewayService notificationService;

    /**
     * Nhận thông báo cộng tiền từ T24 Core Banking
     *
     * API này được T24 gọi để thông báo khi có tiền được cộng vào tài khoản khách hàng từ:
     * - Chuyển tiền từ ngân hàng khác qua LAPNET
     * - Nạp tiền tại quầy giao dịch
     * - Chuyển tiền từ ứng dụng khác
     * - Cập nhật trạng thái tài khoản tiết kiệm đáo hạn
     *
     * Luồng xử lý:
     * 1. Validate thông tin từ T24
     * 2. Tìm khách hàng theo số tài khoản
     * 3. Tạo notification record
     * 4. Gửi push notification cho khách hàng
     * 5. Cập nhật số dư cache (nếu có)
     *
     * @param request HTTP request
     * @param notificationRequest Thông tin giao dịch cộng tiền từ T24
     * @return NotificationLapnetDTO Kết quả xử lý thông báo
     */
    @InboundRequestLog
    @PostMapping("/client/me/notification-balance")
    public ResponseEntity<NotificationLapnetDTO> getNotificationClientBalance(
            HttpServletRequest request, @RequestBody @Valid NotificationBalanceClientRequest notificationRequest) {
        return ResponseEntity.ok().body(this.notificationService.getNotificationClientBalance(notificationRequest));
    }

    /**
     * Nhận thông báo thanh toán thất bại từ các nhà cung cấp dịch vụ
     *
     * API này được các hệ thống bên ngoài gọi để thông báo khi thanh toán thất bại:
     * - Thanh toán hóa đơn điện, nước thất bại
     * - Nạp tiền điện thoại thất bại
     * - Thanh toán internet, TV thất bại
     * - Các dịch vụ khác thất bại
     *
     * Luồng xử lý:
     * 1. Validate thông tin từ nhà cung cấp
     * 2. Tìm giao dịch gốc trong hệ thống
     * 3. Cập nhật trạng thái giao dịch = FAILED
     * 4. Hoàn tiền cho khách hàng (nếu đã trừ tiền)
     * 5. Gửi thông báo lỗi cho khách hàng
     *
     * @param request HTTP request
     * @param notificationRequest Thông tin thanh toán thất bại
     * @return NotificationLapnetDTO Kết quả xử lý thông báo
     */
    @InboundRequestLog
    @PostMapping("/client/notification-fail-charge")
    public ResponseEntity<NotificationLapnetDTO> getNotificationClient(
            HttpServletRequest request, @RequestBody @Valid NotificationChargeClientRequest notificationRequest) {
        return ResponseEntity.ok().body(this.notificationService.getNotificationChargeClient(notificationRequest));
    }
}
