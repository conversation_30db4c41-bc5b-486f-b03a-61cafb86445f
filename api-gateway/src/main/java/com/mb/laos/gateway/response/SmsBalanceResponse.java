package com.mb.laos.gateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SmsBalanceResponse {
    private String transData;
    private String transactionId;
    private String type;
    private Integer remainTimeSecond;
}
