package com.mb.laos.gateway.controller;

import com.mb.laos.annotation.InboundRequestLog;
import com.mb.laos.api.request.EmptyRequest;
import com.mb.laos.gateway.request.OtpTopupRequest;
import com.mb.laos.gateway.request.OtpTransferConfirmRequest;
import com.mb.laos.gateway.request.PhoneInquiryRequest;
import com.mb.laos.gateway.response.OtpTransferConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferTransResponse;
import com.mb.laos.gateway.response.PhoneInquiryResponse;
import com.mb.laos.gateway.response.TopupHistoryResponse;
import com.mb.laos.service.TopupService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Set;

/**
 * Controller xử lý nạp tiền điện thoại
 *
 * Chức năng chính:
 * - Nạp tiền điện thoại trả trước (prepaid)
 * - Thanh toán cước điện thoại trả sau (postpaid)
 * - Nạp data internet
 * - Thanh toán cước điện thoại cố định (PSTN)
 *
 * Các nhà mạng được hỗ trợ:
 * - Unitel: Nhà mạng lớn nhất Lào
 * - LTC (Lao Telecom): Nhà mạng nhà nước
 * - ETL (Enterprise of Telecommunications Lao): Nhà mạng tư nhân
 * - BTC (Best Telecom): Nhà mạng nhỏ
 *
 * Luồng hoạt động:
 * 1. Inquiry số điện thoại (kiểm tra nhà mạng, loại thuê bao)
 * 2. Request OTP (tạo giao dịch và gửi OTP)
 * 3. Confirm OTP (xác thực và thực hiện nạp tiền)
 *
 * Endpoint base: /topup
 *
 * <AUTHOR> Development Team
 * @version 2.0
 * @since 2023
 */
@RestController
@RequestMapping("/topup")
@RequiredArgsConstructor
public class TopupController {

    /** Service xử lý business logic cho nạp tiền điện thoại */
    private final TopupService topupService;

    /**
     * Kiểm tra thông tin số điện thoại
     *
     * API này được gọi đầu tiên để:
     * - Xác định nhà mạng (Unitel, LTC, ETL, BTC)
     * - Kiểm tra loại thuê bao (trả trước/trả sau)
     * - Validate số điện thoại hợp lệ
     * - Lấy thông tin gói cước hiện tại
     * - Hiển thị các mệnh giá nạp tiền có sẵn
     *
     * @param request HTTP request
     * @param phoneInquiryRequest Chứa số điện thoại cần kiểm tra
     * @return PhoneInquiryResponse Thông tin nhà mạng và loại thuê bao
     */
    @InboundRequestLog
    @PostMapping("/phone-inquiry")
    public ResponseEntity<PhoneInquiryResponse> inquiryPhoneNumber(HttpServletRequest request,
                    @RequestBody @Valid PhoneInquiryRequest phoneInquiryRequest) {
        return ResponseEntity.ok(topupService.inquiryPhoneNumber(phoneInquiryRequest));
    }

    /**
     * Tạo OTP cho giao dịch nạp tiền điện thoại
     *
     * API này tạo giao dịch nạp tiền và gửi OTP:
     * - Validate thông tin nạp tiền (số điện thoại, số tiền)
     * - Kiểm tra số dư tài khoản khách hàng
     * - Tính phí giao dịch
     * - Tạo transaction record
     * - Gửi OTP qua SMS
     *
     * @param request HTTP request
     * @param otpRequest Thông tin nạp tiền (số điện thoại, số tiền, tài khoản)
     * @return OtpTransferTransResponse Chứa OTP info và transaction ID
     */
    @InboundRequestLog
    @PostMapping("/request-topup")
    public ResponseEntity<OtpTransferTransResponse> requestOtpTopup(HttpServletRequest request,
                    @RequestBody @Valid OtpTopupRequest otpRequest) {
        return ResponseEntity.ok(topupService.requestOtpTopup(otpRequest, request));
    }

    /**
     * Xác thực OTP và thực hiện nạp tiền điện thoại
     *
     * API này hoàn tất giao dịch nạp tiền:
     * - Xác thực OTP từ khách hàng
     * - Trừ tiền từ tài khoản khách hàng
     * - Gọi API nhà mạng để nạp tiền
     * - Cập nhật trạng thái giao dịch
     * - Gửi thông báo kết quả cho khách hàng
     *
     * @param request HTTP request
     * @param confirmRequest Chứa OTP và transaction ID
     * @return OtpTransferConfirmResponse Kết quả nạp tiền
     */
    @InboundRequestLog
    @PostMapping("/confirm-topup")
    public ResponseEntity<OtpTransferConfirmResponse> confirmOtpTopup(HttpServletRequest request,
                    @RequestBody OtpTransferConfirmRequest confirmRequest) {
        return ResponseEntity.ok(topupService.confirmOtpTopup(confirmRequest, request));
    }

    /**
     * Lấy lịch sử nạp tiền điện thoại
     *
     * API trả về lịch sử các giao dịch nạp tiền của khách hàng:
     * - Danh sách giao dịch nạp tiền
     * - Thông tin số điện thoại đã nạp
     * - Số tiền và phí giao dịch
     * - Trạng thái giao dịch (thành công/thất bại)
     * - Thời gian thực hiện
     *
     * @param request HTTP request
     * @param emptyRequest Empty request body
     * @return List<TopupHistoryResponse> Lịch sử nạp tiền
     */
    @InboundRequestLog
    @PostMapping("/history")
    public ResponseEntity<List<TopupHistoryResponse>> historyTopup(HttpServletRequest request, EmptyRequest emptyRequest) {
        return ResponseEntity.ok(this.topupService.getHistoryTopup(request));
    }

}
