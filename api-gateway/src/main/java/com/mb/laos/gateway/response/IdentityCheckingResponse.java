package com.mb.laos.gateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.mb.laos.api.response.Response;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@JsonInclude(Include.NON_NULL)
public class IdentityCheckingResponse extends Response {
    private static final long serialVersionUID = -6122022395193474568L;

    private String errorCode;

    private String transactionId;
}
