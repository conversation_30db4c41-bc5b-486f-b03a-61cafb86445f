package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import com.mb.laos.messages.LabelKey;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UnlinkDeviceRequest extends Request {

    private static final long serialVersionUID = -1149266494815312396L;

    @ApiModelProperty(required = true)
    @NotBlank(message = LabelKey.ERROR_DEVICE_ID_MUST_NOT_BE_EMPTY)
    private List<String> deviceIds;
}
