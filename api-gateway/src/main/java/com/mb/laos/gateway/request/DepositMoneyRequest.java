package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Request class cho chức năng gửi tiền <PERSON>oney
 *
 * Class này chứa tất cả thông tin cần thiết để thực hiện giao dịch gửi tiền
 * từ tài khoản ghi nợ (debit) sang tài khoản ghi có (credit) thông qua hệ thống Umoney.
 * Kế thừa từ Request base class để có các thuộc tính chung của request.
 */
@Getter // Lombok annotation tự động tạo getter methods cho tất cả fields
@Setter // Lombok annotation tự động tạo setter methods cho tất cả fields
@NoArgsConstructor // Lombok annotation tự động tạo constructor không tham số
@AllArgsConstructor // Lombok annotation tự động tạo constructor với tất cả tham số
@JsonInclude(JsonInclude.Include.NON_NULL) // Jackson annotation chỉ serialize các field không null
public class DepositMoneyRequest extends Request { // Kế thừa từ Request base class
    // Serial version UID để đảm bảo tính tương thích khi serialize/deserialize
    private static final long serialVersionUID = 1802196234330256607L;

    // Thông tin số tiền giao dịch bao gồm giá trị và đơn vị tiền tệ
    private Amount amount;

    // Thông tin tài khoản ghi có (tài khoản nhận tiền) - tài khoản đích của giao dịch
    private AccountMoney creditAccount;

    // Thông tin tài khoản ghi nợ (tài khoản trừ tiền) - tài khoản nguồn của giao dịch
    private AccountMoney debitAccount;

    // Thông tin phí giao dịch bao gồm loại phí, mức phí và mô tả
    private ChargeInfo chargeInfo;

    // Thông tin ngân hàng của tài khoản ghi có (nếu là giao dịch liên ngân hàng)
    private CreditBank creditBank;

    // Ghi chú hoặc nội dung chuyển khoản do người dùng nhập
    private String remark;

    // Thông tin thành viên khởi tạo giao dịch (member ID hoặc code)
    private String fromMember;

    // ID hoặc username của người dùng thực hiện giao dịch
    private String fromUser;

    // Họ tên đầy đủ của người dùng thực hiện giao dịch
    private String fromUserFullName;

    // Số tài khoản nguồn (có thể khác với debitAccount trong một số trường hợp đặc biệt)
    private String fromAccount;

    // Các field được comment out - có thể sử dụng trong tương lai
//    private String transferType; // Loại chuyển khoản (nội bộ, liên ngân hàng, quốc tế...)
//    private String channel; // Kênh giao dịch (mobile, web, ATM...)
//    private String serviceType; // Loại dịch vụ (transfer, payment, topup...)
//    private String branchCode; // Mã chi nhánh thực hiện giao dịch
}
