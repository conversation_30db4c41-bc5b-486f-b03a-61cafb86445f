package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import com.mb.laos.messages.LabelKey;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UtilityRequest extends Request {
    private static final long serialVersionUID = 1802196234330256607L;

    @NotBlank(message = LabelKey.ERROR_PHONE_NUMBER_IS_REQUIRED)
    private String billingCode;

    @NotBlank(message = LabelKey.ERROR_ACCOUNT_NUMBER_IS_REQUIRED)
    private String accountNumber;

    @NotNull(message = LabelKey.ERROR_BILLING_TYPE_IS_REQUIRED)
    private int billingType;

    @NotNull(message = LabelKey.ERROR_EWID_IS_REQUIRED)
    private Integer providerId;

}
