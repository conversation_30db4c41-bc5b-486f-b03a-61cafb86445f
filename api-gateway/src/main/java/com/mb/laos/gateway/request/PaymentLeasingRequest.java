package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentLeasingRequest extends Request {
    private static final long serialVersionUID = 6008080343994332932L;

    private Integer leasId;

    private Integer providerId;

    private String nameCode;

    private String fee;

    private String accName;

    private String transactionId;

    private String accNo;

    private Long amount;

    private String phoneUser;

    private String remark;
}
