package com.mb.laos.gateway.controller;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import com.mb.laos.api.request.EmptyRequest;
import com.mb.laos.model.dto.CampaignDTO;
import com.mb.laos.service.CampaignService;
import lombok.RequiredArgsConstructor;

@Controller
@RequestMapping("/campaign")
@RequiredArgsConstructor
public class CampaignController {
	private final CampaignService campaignService;

	@PostMapping("/active")
	public ResponseEntity<List<CampaignDTO>> getActiveCampaign(HttpServletRequest request, EmptyRequest emptyRequest) {
		return new ResponseEntity<>(this.campaignService.getActiveCampaign(), HttpStatus.OK);
	}

	@PostMapping("/banner/{imageId}/{normalizeName}")
	public ResponseEntity<InputStreamResource> getBannerImage(HttpServletRequest request, @RequestBody CampaignDTO dto,
			@PathVariable("imageId") Long imageId, @PathVariable("normalizeName") String normalizeName) {
		return this.campaignService.getBannerImage(dto.getCampaignId(), imageId, normalizeName);
	}

}
