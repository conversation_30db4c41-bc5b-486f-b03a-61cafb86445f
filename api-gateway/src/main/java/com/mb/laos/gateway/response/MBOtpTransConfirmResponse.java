package com.mb.laos.gateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.T24ErrorCode;
import com.mb.laos.enums.TransactionStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MBOtpTransConfirmResponse {
    private String t24ReferenceNumber;

    private String referenceNumber;

    private TransactionStatus transactionStatus;

    private String transactionId;

    private T24ErrorCode t24ErrorCode;
}
