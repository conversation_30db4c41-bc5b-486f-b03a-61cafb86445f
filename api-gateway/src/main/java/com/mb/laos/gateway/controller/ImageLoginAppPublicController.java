package com.mb.laos.gateway.controller;

import com.mb.laos.model.dto.ImageLoginAppDTO;
import com.mb.laos.service.ImageLoginAppService;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/public/image-login-app")
@RequiredArgsConstructor
public class ImageLoginAppPublicController {
    private final ImageLoginAppService imageLoginAppService;

    @PostMapping("/icon")
    public ResponseEntity<InputStreamResource> getIcon() {
        if (Validator.isNull(this.imageLoginAppService.getIcon())) {
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
        return this.imageLoginAppService.getIcon();
    }

    @PostMapping("/info")
    public ResponseEntity<ImageLoginAppDTO> getInfoImageLogin() {
        if (Validator.isNull(this.imageLoginAppService.getInfoImageLogin())) {
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
        return ResponseEntity.ok().body(this.imageLoginAppService.getInfoImageLogin());
    }
}
