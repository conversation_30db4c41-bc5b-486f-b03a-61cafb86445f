package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.validator.ValidationConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FeeTransactionRequest extends Request implements Serializable {

    private static final long serialVersionUID = 7983619061892969073L;

    @Min(value = ValidationConstraint.LENGTH.TRANSACTION_AMOUNT_VALUE_MIN, message = LabelKey.ERROR_TRANSACTION_AMOUNT_MIN)
    @Max(value = ValidationConstraint.LENGTH.TRANSACTION_AMOUNT_MAX, message = LabelKey.ERROR_TRANSACTION_AMOUNT_MAX_LENGTH)
    @NotNull(message = LabelKey.ERROR_AMOUNT_IS_REQUIRED)
    private Double transactionAmount;

    @NotNull(message = LabelKey.ERROR_TRANSACTION_FEE_TYPE_IS_REQUIRED)
    private String transactionFeeTypeCode;

    private String customerAccountNumber;

    private String telcoCode;

    private String beneficiaryQrValue;

    @Size(max = ValidationConstraint.LENGTH.BANK_CODE_MAX, message = LabelKey.ERROR_BANK_CODE_MAX_LENGTH)
    private String beneficiaryBankCode;

    private String beneficiaryCurrency;

    private String transactionId;
}
