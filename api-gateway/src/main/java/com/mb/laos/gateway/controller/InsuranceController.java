package com.mb.laos.gateway.controller;

import com.mb.laos.annotation.InboundRequestLog;
import com.mb.laos.annotation.MaxSize;
import com.mb.laos.api.request.EmptyRequest;
import com.mb.laos.enums.InsuranceType;
import com.mb.laos.gateway.request.LviBuyHealthInsuranceRequest;
import com.mb.laos.gateway.request.LviBuyVehicleInsuranceRequest;
import com.mb.laos.gateway.request.LviRenewVehicleInsuranceRequest;
import com.mb.laos.gateway.request.OtpTransferConfirmRequest;
import com.mb.laos.gateway.response.OtpTransansferLviConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferTransResponse;
import com.mb.laos.model.dto.LviDeliveryDTO;
import com.mb.laos.model.dto.LviHealthPackageDTO;
import com.mb.laos.model.dto.LviHealthPackageFeeDTO;
import com.mb.laos.model.dto.LviVehicleInsuranceDTO;
import com.mb.laos.model.dto.LviVehiclePackageDTO;
import com.mb.laos.model.dto.LviVehiclePackageFeeDTO;
import com.mb.laos.model.dto.LviVehicleTypeDTO;
import com.mb.laos.model.dto.TransactionInsuranceDTO;
import com.mb.laos.model.search.FileEntrySearch;
import com.mb.laos.service.InsuranceService;
import com.mb.laos.util.PropKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping(value = "/insurance")
@Slf4j
@RequiredArgsConstructor
@Validated
public class InsuranceController {

    private final InsuranceService insuranceService;


    @GetMapping("/lvi/type")
    @InboundRequestLog
    public ResponseEntity<List<InsuranceType>> getInsuranceType(HttpServletRequest request, EmptyRequest emptyRequest) {
        return ResponseEntity.ok(InsuranceType.getInsuranceType());
    }

    @GetMapping("/lvi/delivery")
    @InboundRequestLog
    public ResponseEntity<LviDeliveryDTO> getDelivery(HttpServletRequest request, EmptyRequest emptyRequest) {
        return ResponseEntity.ok(insuranceService.getDelivery());
    }

    @GetMapping("/lvi/vehicle/package")
    @InboundRequestLog
    public ResponseEntity<LviVehiclePackageDTO> getVehiclePackage(HttpServletRequest request, EmptyRequest emptyRequest) {
        return ResponseEntity.ok(insuranceService.getLviVehiclePackage());
    }

    @GetMapping("/lvi/vehicle/type")
    @InboundRequestLog
    public ResponseEntity<LviVehicleTypeDTO> getVehicleType(HttpServletRequest request, EmptyRequest emptyRequest) {
        return ResponseEntity.ok(insuranceService.getLviVehicleType());
    }

    @GetMapping("/lvi/vehicle/packge-fee")
    @InboundRequestLog
    public ResponseEntity<LviVehiclePackageFeeDTO> getVehiclePackageFee(
            HttpServletRequest request,
            @RequestParam String packageCode,
            @RequestParam String vehicleCode) {
        return ResponseEntity.ok(insuranceService.getLviVehiclePackageFee(packageCode, vehicleCode));
    }

    @GetMapping("/lvi/vehicle/insurance")
    @InboundRequestLog
    public ResponseEntity<LviVehicleInsuranceDTO> getVehicleInsurance(
            HttpServletRequest request,
            @RequestParam String certificate) {
        return ResponseEntity.ok(insuranceService.getVehicleInsurance(certificate));
    }

    @PostMapping("/lvi/vehicle/buy/request-otp")
    @InboundRequestLog
    public ResponseEntity<OtpTransferTransResponse> requestBuyVehicleInsurance(HttpServletRequest httpServletRequest,
                                                                               @Valid LviBuyVehicleInsuranceRequest request,
                                                                               @MaxSize(property = PropKey.MAX_SIZE_DEFAULT) @RequestParam(value = "file") MultipartFile file) {
        return ResponseEntity.ok(insuranceService.requestBuyLviVehicleInsurance(request, file));
    }

    @PostMapping("/lvi/vehicle/renew/request-otp")
    @InboundRequestLog
    public ResponseEntity<OtpTransferTransResponse> requestRenewVehicleInsurance(HttpServletRequest httpServletRequest,
                                                                                 @Valid @RequestBody LviRenewVehicleInsuranceRequest request) {
        return ResponseEntity.ok(insuranceService.requestRenewLviVehicleInsurance(request));
    }

    @GetMapping("/lvi/health/package")
    @InboundRequestLog
    public ResponseEntity<LviHealthPackageDTO> getHealthPackage(HttpServletRequest request, EmptyRequest emptyRequest) {
        return ResponseEntity.ok(insuranceService.getLviHealthPackage());
    }

    @GetMapping("/lvi/health/package-fee")
    @InboundRequestLog
    public ResponseEntity<LviHealthPackageFeeDTO> getHealthPackageFee(HttpServletRequest request,
                                                                      @RequestParam String healthPackage) {
        return ResponseEntity.ok(insuranceService.getLviHealthPackageFeeDTO(healthPackage));
    }

    @PostMapping("/lvi/health/buy/request-otp")
    @InboundRequestLog
    public ResponseEntity<OtpTransferTransResponse> requestBuyHealthInsurance(HttpServletRequest httpServletRequest,
                                                                              @Valid @RequestBody LviBuyHealthInsuranceRequest request) {
        return ResponseEntity.ok(insuranceService.requestBuyLviHealthInsurance(request));
    }

    @PostMapping("/lvi/buy/confirm-otp")
    @InboundRequestLog
    public ResponseEntity<OtpTransansferLviConfirmResponse> confirmBuyVehicleInsurance(HttpServletRequest request,
                                                                                       @RequestBody @Valid OtpTransferConfirmRequest confirmRequest) {
        return ResponseEntity.ok(insuranceService.confirmBuyInsurance(confirmRequest));
    }

    @GetMapping("/me/transaction")
    @InboundRequestLog
    public ResponseEntity<List<TransactionInsuranceDTO>> getUserInsuranceTransaction(HttpServletRequest request, EmptyRequest emptyRequest) {
        return ResponseEntity.ok(insuranceService.getCustomerInsuranceTransaction());
    }

    @InboundRequestLog
    @PostMapping("/registration-image")
    public ResponseEntity<ByteArrayResource> getIcon(HttpServletRequest request, @RequestBody @Valid FileEntrySearch search) {
        return this.insuranceService.getRegistrationImage(search);
    }
}
