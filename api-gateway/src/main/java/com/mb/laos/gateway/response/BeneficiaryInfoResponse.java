// Package chứa các response classes cho gateway API
package com.mb.laos.gateway.response;

// Import Jackson annotation để control JSON serialization
import com.fasterxml.jackson.annotation.JsonInclude;
// Import base Response class chứa các field chung
import com.mb.laos.api.response.Response;
// Import DTO cho account number verification
import com.mb.laos.model.dto.AccNumberVerifyDTO;
// Import response cho QR Code information
import com.mb.laos.response.QrCodeResponse;
// Import Lombok annotations để tự động generate code
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

// Import Java standard library
import java.util.List;

/**
 * BeneficiaryInfoResponse - Response chứa thông tin người thụ hưởng
 *
 * Class này được sử dụng để trả về thông tin chi tiết của người thụ hưởng
 * sau khi verify thành công số tài khoản hoặc QR Code.
 *
 * Đ<PERSON><PERSON><PERSON> sử dụng trong các API:
 * - Verify beneficiary account number (nội bộ MB Bank)
 * - Verify beneficiary QR Code (LAPNET network)
 * - Confirm transfer information trước khi thực hiện giao dịch
 *
 * Response này chứa đầy đủ thông tin cần thiết để:
 * - Hiển thị thông tin người nhận cho khách hàng xác nhận
 * - Tính toán phí giao dịch theo từng mức
 * - Xử lý chuyển tiền nội bộ hoặc liên ngân hàng
 * - Hỗ trợ thanh toán QR Code với thông tin merchant
 *
 * <AUTHOR> Laos Development Team
 * @version 1.0
 * @since 2023
 */
@Getter                                           // Lombok: tự động tạo getter methods cho tất cả fields
@Setter                                           // Lombok: tự động tạo setter methods cho tất cả fields
@Builder                                          // Lombok: tạo builder pattern để khởi tạo object dễ dàng
@JsonInclude(JsonInclude.Include.NON_NULL)       // Jackson: chỉ serialize các field không null vào JSON
public class BeneficiaryInfoResponse extends Response {

    /**
     * Serial version UID cho Java serialization
     * Đảm bảo compatibility khi class structure thay đổi
     */
    private static final long serialVersionUID = 9124872365782320915L;

    /**
     * Mã ngân hàng của người thụ hưởng
     *
     * Ví dụ:
     * - "MB": MB Bank (chuyển tiền nội bộ)
     * - "BCEL": BCEL Bank (chuyển tiền liên ngân hàng qua LAPNET)
     * - "LDB": Lao Development Bank
     * - "APB": Agricultural Promotion Bank
     */
    private String bankCode;

    /**
     * Số tài khoản của người thụ hưởng
     *
     * Format tùy theo ngân hàng:
     * - MB Bank: 10-16 digits (ví dụ: "****************")
     * - LAPNET banks: theo chuẩn của từng ngân hàng
     * - QR Code: có thể là merchant ID hoặc account number
     */
    private String accountNumber;

    /**
     * Tên người thụ hưởng (chủ tài khoản)
     *
     * Được lấy từ:
     * - T24 Core Banking (cho MB Bank)
     * - LAPNET network (cho ngân hàng khác)
     * - QR Code merchant information
     *
     * Ví dụ: "NGUYEN VAN A", "CONG TY TNHH ABC"
     */
    private String beneficiaryName;

    /**
     * Phí chuyển tiền (deprecated - sử dụng feeList thay thế)
     *
     * Giá trị string representation của phí giao dịch
     * Đơn vị: LAK (Lao Kip)
     *
     * Note: Field này đang được thay thế bằng feeList để hỗ trợ
     * multiple fee tiers dựa trên transaction amount
     */
    private String transferFee;

    /**
     * Loại thanh toán/chuyển tiền
     *
     * Các giá trị có thể:
     * - "INTERNAL": Chuyển tiền nội bộ MB Bank
     * - "LAPNET": Chuyển tiền liên ngân hàng qua LAPNET
     * - "QR_MERCHANT": Thanh toán QR Code merchant
     * - "QR_P2P": Chuyển tiền QR Code cá nhân
     */
    private String paymentType;

    /**
     * Loại tiền tệ của giao dịch
     *
     * Các giá trị hỗ trợ:
     * - "LAK": Lao Kip (tiền tệ chính)
     * - "USD": US Dollar (cho giao dịch quốc tế)
     * - "THB": Thai Baht (cho giao dịch với Thái Lan)
     * - "KHR": Cambodian Riel (cho giao dịch với Cambodia)
     */
    private String currency;

    /**
     * Mã response từ hệ thống backend
     *
     * Các giá trị thường gặp:
     * - "00": Thành công
     * - "01": Tài khoản không tồn tại
     * - "02": Tài khoản bị khóa
     * - "03": Ngân hàng không hỗ trợ
     * - "99": Lỗi hệ thống
     */
    private String responseCode;

    /**
     * Danh sách phí giao dịch theo từng mức amount
     *
     * Mỗi element trong list chứa:
     * - fromAmount: Số tiền từ (LAK)
     * - toAmount: Số tiền đến (LAK)
     * - fee: Phí giao dịch (LAK)
     * - discount: Giảm giá (nếu có)
     *
     * Ví dụ:
     * - 0 - 1,000,000 LAK: phí 5,000 LAK
     * - 1,000,001 - 5,000,000 LAK: phí 10,000 LAK
     * - > 5,000,000 LAK: phí 15,000 LAK
     */
    private List<AccNumberVerifyDTO.Lak> feeList;

    /**
     * Thông tin QR Code response (nếu là giao dịch QR)
     *
     * Chứa thông tin chi tiết về QR Code:
     * - QR Code content/data
     * - Merchant information
     * - Payment amount (nếu có)
     * - Expiry time
     * - QR Code image (base64)
     *
     * Chỉ có giá trị khi paymentType là QR_MERCHANT hoặc QR_P2P
     */
    private QrCodeResponse qrCodeResponse;

    /**
     * Số tham chiếu giao dịch từ backend
     *
     * Được sử dụng để:
     * - Track giao dịch trong hệ thống
     * - Reconciliation với T24/LAPNET
     * - Customer support và dispute resolution
     * - Audit trail
     *
     * Format: UUID hoặc sequential number tùy theo hệ thống backend
     */
    private String referenceNumber;

    /**
     * Tỷ giá hối đoái (cho giao dịch đa tiền tệ)
     *
     * Được sử dụng khi:
     * - Chuyển tiền quốc tế (LAK -> USD/THB/KHR)
     * - Hiển thị equivalent amount cho khách hàng
     * - Tính toán final amount sau conversion
     *
     * Ví dụ:
     * - 1 USD = 20,000 LAK (rate = 20000.0)
     * - 1 THB = 600 LAK (rate = 600.0)
     *
     * Null nếu là giao dịch cùng tiền tệ (LAK -> LAK)
     */
    private Double rate;
}
