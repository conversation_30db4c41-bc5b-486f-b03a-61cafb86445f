package com.mb.laos.gateway.response;

import java.time.Instant;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class OtpTransferConfirmResponse {
    /*
    Mã giao dịch trả về từ MB
     */
    private String transactionCode;

    private Long availableBalance;

    private Boolean isSuccess;

    private Instant transactionTime;

    private double transactionFee;

    private String transactionId;
}
