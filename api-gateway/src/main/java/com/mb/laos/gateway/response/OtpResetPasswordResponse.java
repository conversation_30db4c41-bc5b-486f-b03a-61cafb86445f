package com.mb.laos.gateway.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.mb.laos.api.response.Response;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class OtpResetPasswordResponse extends Response {
    
    private static final long serialVersionUID = -3296774295278201944L;
    
    private String transactionId;

    private Integer duration;
    
    private String registryId;
    
}
