package com.mb.laos.gateway.request;

import com.mb.laos.annotation.Exclude;
import com.mb.laos.api.request.Request;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class OtpVerifyResetPasswordRequest extends Request {
    
    private static final long serialVersionUID = 5088906211059388189L;

    @Exclude
    private String phoneNumber;

    private String otp;

    private String transactionId;
    
    private String token;
}
