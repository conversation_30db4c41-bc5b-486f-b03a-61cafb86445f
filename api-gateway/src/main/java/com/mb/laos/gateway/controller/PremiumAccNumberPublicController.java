package com.mb.laos.gateway.controller;

import com.mb.laos.annotation.InboundRequestLog;
import com.mb.laos.gateway.request.QueryPremiumAccNumberRequest;
import com.mb.laos.model.dto.QueryPremiumAccNumberDTO;
import com.mb.laos.service.PremiumAccNumberService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/public/premium-account-number")
@RequiredArgsConstructor
public class PremiumAccNumberPublicController {
    private final PremiumAccNumberService premiumAccNumberService;

    @InboundRequestLog
    @PostMapping("/query")
    public ResponseEntity<List<QueryPremiumAccNumberDTO>> queryPremiumAccNumberNonUser(HttpServletRequest request, @RequestBody @Valid QueryPremiumAccNumberRequest queryPremiumAccNumberRequest) {
        return ResponseEntity.ok().body(this.premiumAccNumberService.queryPremiumAccNumberNonUser(queryPremiumAccNumberRequest));
    }

    @InboundRequestLog
    @PostMapping("/detail")
    public ResponseEntity<QueryPremiumAccNumberDTO> detailNonUser(HttpServletRequest request, @RequestBody @Valid QueryPremiumAccNumberRequest queryPremiumAccNumberRequest) {
        return ResponseEntity.ok().body(this.premiumAccNumberService.detailNonUser(queryPremiumAccNumberRequest));
    }
}
