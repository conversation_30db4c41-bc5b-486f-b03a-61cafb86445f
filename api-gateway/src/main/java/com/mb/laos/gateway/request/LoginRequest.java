package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.annotation.Exclude;
import com.mb.laos.api.request.Request;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LoginRequest extends Request {
    /** The Constant serialVersionUID */
    private static final long serialVersionUID = -8340388451714375195L;

    private String username;

    @Exclude
    private String password;

    private Boolean rememberMe;

    private String otp;

    private String transactionId;
    
    private String deviceId;
    
    private String deviceName;
    
    private String packageName;
    
    private String appVersion;
    
    private String deviceImei;
    
    private String deviceOs;

    @Override
    public String toString() {
        return "LoginRequest{" + "username='" + this.username + '\'' + ", rememberMe=" + this.rememberMe + '}';
    }
}
