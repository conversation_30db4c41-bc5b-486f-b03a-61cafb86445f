package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.annotation.MaxSize;
import com.mb.laos.api.request.Request;
import com.mb.laos.enums.MaturityInstructionType;
import com.mb.laos.enums.SavingAccountType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.validator.ValidationConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OtpSavingAccountRequest extends Request {
    /** The Constant serialVersionUID */
    private static final long serialVersionUID = 844200794034907761L;

//    @Min(value = ValidationConstraint.LENGTH.DEPOSIT_MIN, message = LabelKey.ERROR_DEPOSIT_MIN)
    private Long amount;

    @NotBlank(message = LabelKey.ERROR_AMOUNT_CURRENCY_IS_REQUIRED)
    private String amountCurrency;

    private String sourceAccount;

    private String beneficiaryAccount;

    @NotBlank(message = LabelKey.ERROR_DEVICE_ID_MUST_NOT_BE_EMPTY)
    private String deviceId;

    @NotNull(message = LabelKey.ERROR_TRANSFER_TYPE_IS_REQUIRED)
    private TransferType transferType;

    @NotNull(message = LabelKey.ERROR_TYPE_ACC_SAVING_IS_REQUIRED)
    private SavingAccountType savingAccountType;

    private MaturityInstructionType maturityInstruction;

    private String savingAccountNumber;

    private String rm;

    @Pattern(regexp = ValidationConstraint.PATTERN.TENOR_REGEX, message = LabelKey.ERROR_TENOR_IS_INVALID)
    private String tenor;

    private boolean setDefaultAccount;

    @Pattern(regexp = ValidationConstraint.PATTERN.REMARK_REGEX, message = LabelKey.ERROR_QR_CONTENT_INVALID)
    @Size(max = ValidationConstraint.LENGTH.REMARK_MAX, message = LabelKey.ERROR_REMARK_MAX_LENGTH)
    private String remark;
}
