package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import com.mb.laos.messages.LabelKey;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransactionHistoryRequest extends Request {
    /** The Constant serialVersionUID */
	private static final long serialVersionUID = 8099785071609558594L;

	@NotNull(message = LabelKey.ERROR_FROM_DATE_IS_REQUIRED)
    private LocalDate fromDate;

    @NotNull(message = LabelKey.ERROR_TO_DATE_IS_REQUIRED)
    private LocalDate toDate;

    @NotNull(message = LabelKey.ERROR_ACCOUNT_NUMBER_IS_REQUIRED)
    private String accountNumber;
}
