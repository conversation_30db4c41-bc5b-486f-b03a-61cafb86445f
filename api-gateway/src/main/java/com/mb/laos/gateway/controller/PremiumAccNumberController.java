package com.mb.laos.gateway.controller;

import com.mb.laos.annotation.InboundRequestLog;
import com.mb.laos.api.request.EmptyRequest;
import com.mb.laos.gateway.request.OtpPremiumAccNumberRequest;
import com.mb.laos.gateway.request.QueryPremiumAccNumberRequest;
import com.mb.laos.response.OtpPremiumAccNumberResponse;
import com.mb.laos.model.dto.InformationTemplateContentDTO;
import com.mb.laos.model.dto.PremiumAccNumberDTO;
import com.mb.laos.model.dto.QueryPremiumAccNumberDTO;
import com.mb.laos.request.ConfirmOtpRequest;
import com.mb.laos.service.PremiumAccNumberService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/premium-account-number")
@RequiredArgsConstructor
public class PremiumAccNumberController {
    private final PremiumAccNumberService premiumAccNumberService;

    @InboundRequestLog
    @PostMapping("/query")
    public ResponseEntity<List<QueryPremiumAccNumberDTO>> queryPremiumAccNumber(HttpServletRequest request, @RequestBody @Valid QueryPremiumAccNumberRequest queryPremiumAccNumberRequest) {
        return ResponseEntity.ok().body(this.premiumAccNumberService.queryPremiumAccNumber(queryPremiumAccNumberRequest));
    }

    @InboundRequestLog
    @PostMapping("/detail")
    public ResponseEntity<QueryPremiumAccNumberDTO> detail(HttpServletRequest request, @RequestBody @Valid QueryPremiumAccNumberRequest queryPremiumAccNumberRequest) {
        return ResponseEntity.ok().body(this.premiumAccNumberService.detail(queryPremiumAccNumberRequest));
    }

    @InboundRequestLog
    @PostMapping("/request-otp")
    public ResponseEntity<OtpPremiumAccNumberResponse> requestOtp(HttpServletRequest request, @RequestBody @Valid OtpPremiumAccNumberRequest requestOtp) {
        return ResponseEntity.ok().body(this.premiumAccNumberService.requestOtp(requestOtp));
    }

    @InboundRequestLog
    @PostMapping("/confirm-otp")
    public ResponseEntity<PremiumAccNumberDTO> confirmOtp(HttpServletRequest request, @RequestBody @Valid ConfirmOtpRequest confirmOtpRequest) {
        return ResponseEntity.ok().body(this.premiumAccNumberService.confirmOtp(confirmOtpRequest));
    }

    @InboundRequestLog
    @PostMapping("/notification")
    public ResponseEntity<InformationTemplateContentDTO> notificationDetail(HttpServletRequest request, EmptyRequest emptyRequest) {
        return ResponseEntity.ok(this.premiumAccNumberService.notificationDetail());
    }
}
