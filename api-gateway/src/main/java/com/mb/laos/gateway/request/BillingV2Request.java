package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import com.mb.laos.messages.LabelKey;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BillingV2Request extends Request {
    private static final long serialVersionUID = 1802196234330256607L;

    @NotBlank(message = LabelKey.ERROR_PHONE_NUMBER_IS_REQUIRED)
    private String billingCode;

    @NotBlank(message = LabelKey.ERROR_TELCO_CODE_IS_REQUIRED)
    private String telcoCode;

    @NotBlank(message = LabelKey.ERROR_ACCOUNT_NUMBER_IS_REQUIRED)
    private String accountNumber;

    @NotNull(message = LabelKey.ERROR_BILLING_TYPE_IS_REQUIRED)
    private int billingType;

}
