package com.mb.laos.gateway.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.enums.MaturityInstructionType;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreateInterestResponse {
    private String productName;
    private String savingAccountNumber;
    private String accountTransfer;
    private String amount;
    private String tenor;
    private String transData;
    private String savingInfoTime;
    private String valueDate;
    private String maturityDate;
    private String interestRate;
    private String beneficiaryAccountNumber;
    private String sourceAccountNumber;
    private String interestAmount;
    private Instant finishTime;
    private String frequencyInterestPayment;
    private MaturityInstructionType finalizationMethod;
    private Instant createdDate;
}
