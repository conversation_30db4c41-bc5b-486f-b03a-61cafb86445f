package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import com.mb.laos.messages.LabelKey;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DotpCheckDeviceRegisteredRequest extends Request {

    @NotBlank(message = LabelKey.ERROR_INVALID_DEVICE)
    private String deviceIdNew;

}
