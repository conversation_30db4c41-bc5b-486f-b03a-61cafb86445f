package com.mb.laos.gateway.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.annotation.Exclude;
import com.mb.laos.api.request.Request;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OtpVerifyRequest extends Request {
    /** The Constant serialVersionUID */
    private static final long serialVersionUID = 1802196234330256607L;

    @Exclude
    private String phoneNumber;

    private String otp;

    private String transactionId;
}
