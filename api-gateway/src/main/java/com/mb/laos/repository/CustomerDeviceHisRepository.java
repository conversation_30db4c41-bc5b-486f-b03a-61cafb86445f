package com.mb.laos.repository;

import com.mb.laos.model.CustomerDeviceHis;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface CustomerDeviceHisRepository extends JpaRepository<CustomerDeviceHis, Long> {

    CustomerDeviceHis findTopByCustomerIdOrderByLastModifiedDateDesc(Long customerId);

    List<CustomerDeviceHis> findByCustomerIdAndDeviceTokenAndStatus(Long customerId, String deviceToken, Integer status);

    boolean existsByCustomerIdAndDeviceIdAndStatus(Long customerId, String deviceId, Integer status);

    List<CustomerDeviceHis> findAllByCustomerIdAndStatus(long customerId, Integer status);

   Optional<CustomerDeviceHis> findByCustomerIdAndDeviceIdAndStatus(long customerId, String deviceId, Integer status);

   @Query("from CustomerDeviceHis c  where c.customerId = :customerId and c.status = :status and c.deviceId in :deviceIds")
   List<CustomerDeviceHis> getInDeviceIds(List<String> deviceIds, long customerId, Integer status);

    List<CustomerDeviceHis> findAllByCustomerIdAndDeviceIdAndStatus(long customerId, String deviceId, Integer status);
}
