package com.mb.laos.repository;

import com.mb.laos.cache.util.OtpCacheConstants;
import com.mb.laos.model.dto.SavingAccountVerifyCacheDTO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;

public interface SavingAccountCacheRepository {
    @CachePut(cacheNames = OtpCacheConstants.Others.D_OTP_SAVING_ACCOUNT, key = "#key", unless = "#result == null")
    default SavingAccountVerifyCacheDTO put(String key, SavingAccountVerifyCacheDTO otp) {
        return otp;
    }

    @Cacheable(cacheNames = OtpCacheConstants.Others.D_OTP_SAVING_ACCOUNT, key = "#key", unless = "#result == null")
    default SavingAccountVerifyCacheDTO get(String key) {
        return null;
    }

    @CacheEvict(cacheNames = OtpCacheConstants.Others.D_OTP_SAVING_ACCOUNT, key = "#key")
    default String evict(String key) {
        return key;
    }
}
