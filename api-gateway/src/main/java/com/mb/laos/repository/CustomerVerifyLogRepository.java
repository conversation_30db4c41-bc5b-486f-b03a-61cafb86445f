/**
 * 
 */
package com.mb.laos.repository;

import java.time.Instant;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.mb.laos.enums.VerifyType;
import com.mb.laos.model.CustomerVerifyLog;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface CustomerVerifyLogRepository extends JpaRepository<CustomerVerifyLog, Long> {
	
	@SuppressWarnings("unchecked")
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	CustomerVerifyLog save(CustomerVerifyLog entity);

	/**
	 * @param phoneNumber
	 * @param type
	 * @param from
	 * @param to
	 * @return
	 */
    long countByPhoneNumberAndTypeAndSuccessAndCreatedDateBetween(String phoneNumber, VerifyType type, Boolean success,
                    Instant from, Instant to);

}
