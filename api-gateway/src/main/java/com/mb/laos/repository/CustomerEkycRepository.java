package com.mb.laos.repository;

import com.mb.laos.cache.util.GatewayCacheConstants;
import com.mb.laos.model.CustomerEkyc;
import com.mb.laos.repository.extend.CustomerEkycRepositoryExtend;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerEkycRepository extends JpaRepository<CustomerEkyc, Long>, CustomerEkycRepositoryExtend {

    @Cacheable(cacheNames = GatewayCacheConstants.CustomerEkyc.FIND_BY_TRANSACTION_ID, key = "#p0", unless = "#result == null")
    public CustomerEkyc findByTransactionId(String transactionId);

    @CachePut(cacheNames = GatewayCacheConstants.CustomerEkyc.FIND_BY_TRANSACTION_ID, key = "#entity.transactionId", unless = "#result == null")
    default CustomerEkyc save_(CustomerEkyc entity) {
        return this.save(entity);
    }

    @CachePut(cacheNames = GatewayCacheConstants.CustomerEkyc.FIND_BY_CUSTOMER_ID, key = "#customerId", unless = "#result == null")
    public CustomerEkyc findByCustomerId(Long customerId);

    public Boolean existsByCustomerId(Long customerId);

    CustomerEkyc findByCustomerIdAndStatusNot(long cif, int status);
}
