package com.mb.laos.repository;

import com.mb.laos.cache.util.OtpCacheConstants;
import com.mb.laos.model.SmsBalance;
import com.mb.laos.model.SmsBalanceCacheDTO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SmsBalanceCacheRepository extends JpaRepository<SmsBalance, Long> {

    @CachePut(cacheNames = OtpCacheConstants.Others.OTP_REGISTER_SMS, key = "#key", unless = "#result == null")
    default SmsBalanceCacheDTO put(String key, SmsBalanceCacheDTO value) {
        return value;
    }

    @Cacheable(cacheNames = OtpCacheConstants.Others.OTP_REGISTER_SMS, key = "#key", unless = "#result == null")
    default SmsBalanceCacheDTO get(String key) {
        return null;
    }

    @CacheEvict(cacheNames = OtpCacheConstants.Others.OTP_REGISTER_SMS, key = "#key")
    default String evict(String key) {
        return key;
    }

    @CachePut(cacheNames = OtpCacheConstants.Others.OTP_CANCEL_SMS, key = "#key", unless = "#result == null")
    default SmsBalanceCacheDTO putCancelSms(String key, SmsBalanceCacheDTO value) {
        return value;
    }

    @Cacheable(cacheNames = OtpCacheConstants.Others.OTP_CANCEL_SMS, key = "#key", unless = "#result == null")
    default SmsBalanceCacheDTO getCancelSms(String key) {
        return null;
    }

    @CacheEvict(cacheNames = OtpCacheConstants.Others.OTP_CANCEL_SMS, key = "#key")
    default String evictCancelSms(String key) {
        return key;
    }
}
