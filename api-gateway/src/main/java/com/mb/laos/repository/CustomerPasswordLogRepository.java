package com.mb.laos.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.mb.laos.model.CustomerPasswordLog;
import com.mb.laos.repository.extend.CustomerPasswordLogRepositoryExtend;

import java.util.List;

@Repository
public interface CustomerPasswordLogRepository extends JpaRepository<CustomerPasswordLog, Long>, CustomerPasswordLogRepositoryExtend {
    List<CustomerPasswordLog> findAllByCustomerIdAndStatus(long customerId, int status);
}
