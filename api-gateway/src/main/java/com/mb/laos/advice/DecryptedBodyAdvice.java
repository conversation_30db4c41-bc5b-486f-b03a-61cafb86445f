/*
 * DecryptedBodyAdvice.java
 *
 * Copyright (C) 2022 by Evotek. All right reserved. This software is the confidential and proprietary information of
 * Evotek
 */
package com.mb.laos.advice;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import org.apache.logging.log4j.ThreadContext;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.gateway.request.DynamicKeyRequest;
import com.mb.laos.service.DynamicKeyService;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.mb.laos.util.Constants.Common.CLIENT_PATH;
import static com.mb.laos.util.Constants.Common.REQUEST_URI;

/**
 * 24/11/2022 - LinhLH: Create new
 *
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "dynamic-key", name = "enabled", havingValue = "true", matchIfMissing = false)
public class DecryptedBodyAdvice extends RequestBodyAdviceAdapter {

    private final ObjectMapper objectMapper;

    private final DynamicKeyService dynamicKeyService;

    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType,
                    Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    private static final List<MediaType> ALLOW_MEDIA_TYPES = Arrays.asList(MediaType.APPLICATION_JSON,
                    new MediaType("application", "*+json", StandardCharsets.UTF_8));

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage, MethodParameter parameter, Type targetType,
                    Class<? extends HttpMessageConverter<?>> converterType) throws IOException {


        //request url tu ben thu 3, vi du umoney, khong dung dynamic key bypass
        if (ThreadContext.get(REQUEST_URI).contains(CLIENT_PATH)) {
            return super.beforeBodyRead(inputMessage, parameter, targetType, converterType);
        }
        HttpHeaders header = inputMessage.getHeaders();

        if (Objects.isNull(header.getContentType())) {
            throw new BadRequestAlertException(ErrorCode.MSG1025);
        }

        if (ALLOW_MEDIA_TYPES.stream().noneMatch(media -> media.isCompatibleWith(header.getContentType()))) {
            _log.info("Not Support MediaType: {} ", header.getContentType());

            return inputMessage;
        }

        try (InputStream inputStream = inputMessage.getBody()) {

            // Read request body
            byte[] body = StreamUtils.copyToByteArray(inputStream);

            String bodyRaw = new String(body).replace("\n", "");

            _log.info("bodyRaw: {}", bodyRaw);

            if (Validator.isNull(bodyRaw)) {
            	return inputMessage;
            }

            // decrypted
            DynamicKeyRequest request = this.objectMapper.readValue(bodyRaw, DynamicKeyRequest.class);

            // if body is null, nothing to decrypted
            if (Validator.isNull(request) || Validator.isNull(request.getData())) {
                return inputMessage;
            }

            String decryptRequestString = this.dynamicKeyService.decrypted(request);

            _log.info("decrypted String: {}", decryptRequestString);

            // Return the decoded body
            return new DecryptedHttpInputMessage(header, new ByteArrayInputStream(decryptRequestString.getBytes()));
        }
    }
}
