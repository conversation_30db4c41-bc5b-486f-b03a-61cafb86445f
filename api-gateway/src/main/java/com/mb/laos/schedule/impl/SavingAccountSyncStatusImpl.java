package com.mb.laos.schedule.impl;

import com.mb.laos.schedule.Worker;
import com.mb.laos.service.SavingAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;

@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "scheduling.saving-account-status", name = "enabled", havingValue = "true", matchIfMissing = false)
public class SavingAccountSyncStatusImpl implements Worker {
    private final SavingAccountService savingAccountService;

    @Override
    @Scheduled(cron = "${scheduling.saving-account-status.cron}")
    @SchedulerLock(name = "saving-account-status", lockAtLeastFor = "${scheduling.saving-account-status.lock-at-least}", lockAtMostFor = "${scheduling.saving-account-status.lock-at-most}")
    @Async("mbScheduleExecutor")
    public void run() {
        try {
            _log.info("----- SavingAccountStatus thread is started at: {} --------", Instant.now());

            this.savingAccountService.updateSavingAccount();

            _log.info("----- SavingAccountStatus thread is finished at: {} --------", Instant.now());

        } catch (Exception e) {
            _log.error("SavingAccountStatus thread error", e);
        }
    }
}
