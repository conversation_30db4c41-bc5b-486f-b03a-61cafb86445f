/*
 * DynamicKeyStatus.java
 *
 * Copyright (C) 2022 by Evotek. All right reserved.
 * This software is the confidential and proprietary information of Evotek
 */
package com.mb.laos.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 01/11/2022 - LinhLH: Create new
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DynamicKeyStatus {
    SUCCESS("0000");
    
    private String status;
    
    public static DynamicKeyStatus of(String status) {
        for (DynamicKeyStatus s : DynamicKeyStatus.values()) {
            if (s.getStatus().equals(status))
                return s;
        }

        return null;
    }
}
