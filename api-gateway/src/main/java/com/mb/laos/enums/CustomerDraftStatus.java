package com.mb.laos.enums;

import com.mb.laos.util.Validator;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CustomerDraftStatus {
    PENDING(1),
    CREATED(0)
    ;
    
    private int status;
    
    public static CustomerDraftStatus valueOfStatus(int status) {
        for (CustomerDraftStatus e : values()) {
            if (Validator.equals(status, e.getStatus())) {
                return e;
            }
        }

        return null;
    }

}
