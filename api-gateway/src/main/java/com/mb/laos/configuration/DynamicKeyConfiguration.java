/*
 * DynamicKeyProperties.java
 *
 * Copyright (C) 2022 by Evotek. All right reserved. This software is the confidential and proprietary information of
 * Evotek
 */
package com.mb.laos.configuration;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import GWClient.GatewayClientPoolHA;
import GWClient.GatewayPooledHAClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 29/10/2022 - LinhLH: Create new
 *
 * <AUTHOR>
 */
@Slf4j
@Data
// @EnableWebMvc
@Component
@ConfigurationProperties(prefix = "dynamic-key")
@ConditionalOnProperty(prefix = "dynamic-key", name = "enabled", havingValue = "true", matchIfMissing = false)
public class DynamicKeyConfiguration {
	private boolean enabled;

	private String primaryIp;

	private String secondaryIp;

	private int port;

	private String userId;

	private String tokenId;

	private String deviceId;

	private String serviceId;

	private short validityWnd;

	private int encryptMode;

	private short tokenType;

	private short spid;
	
	@Bean
	public GatewayPooledHAClient dkClientHA() {
		_log.info("DynamicKey server info with primary ip {} and secondary ip {}", this.primaryIp, this.secondaryIp);

		GatewayClientPoolHA dkClientPoolHA = new GatewayClientPoolHA(this.primaryIp, this.port, this.secondaryIp,
				this.port, this.serviceId);

		GatewayPooledHAClient dkClientHA = new GatewayPooledHAClient(dkClientPoolHA);

		return dkClientHA;
	}
}
