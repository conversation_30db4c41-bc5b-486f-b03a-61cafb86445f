package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.NoRollBackBadRequestAlertException;
import com.mb.laos.api.request.AuthenMethodV2;
import com.mb.laos.api.request.CreateBeautifulAccountRequest;
import com.mb.laos.api.request.InitOnOffSmsCus;
import com.mb.laos.api.request.QueryBeautifulAccountRequest;
import com.mb.laos.api.response.CreateBeautifulAccountResponse;
import com.mb.laos.api.response.QueryBeautifulAccountResponse;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.CustomerProperties;
import com.mb.laos.configuration.OtpProperties;
import com.mb.laos.configuration.PremiumAccNumberProperties;
import com.mb.laos.enums.Channel;
import com.mb.laos.enums.CommonCategory;
import com.mb.laos.enums.CurrencyType;
import com.mb.laos.enums.EndUserType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.InformationTemplateType;
import com.mb.laos.enums.NotificationStatus;
import com.mb.laos.enums.NotificationType;
import com.mb.laos.enums.OtpConfirmType;
import com.mb.laos.enums.OtpType;
import com.mb.laos.enums.PremiumAccNumberStatus;
import com.mb.laos.enums.PremiumAccountType;
import com.mb.laos.enums.ReferralType;
import com.mb.laos.enums.TargetType;
import com.mb.laos.enums.TemplateCode;
import com.mb.laos.enums.TemplateField;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.gateway.request.OtpPremiumAccNumberRequest;
import com.mb.laos.gateway.request.QueryPremiumAccNumberRequest;
import com.mb.laos.response.OtpPremiumAccNumberResponse;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Common;
import com.mb.laos.model.ContentTemplate;
import com.mb.laos.model.Customer;
import com.mb.laos.model.CustomerLogin;
import com.mb.laos.model.CustomerSupport;
import com.mb.laos.model.CustomerSupportPhoneNumber;
import com.mb.laos.model.DOTP;
import com.mb.laos.model.InformationTemplate;
import com.mb.laos.model.MoneyAccount;
import com.mb.laos.model.NotiTransaction;
import com.mb.laos.model.Notification;
import com.mb.laos.model.OtpValue;
import com.mb.laos.model.PremiumAccNumber;
import com.mb.laos.model.PremiumAccNumberCacheDTO;
import com.mb.laos.model.PremiumAccountNumberStructure;
import com.mb.laos.model.Referral;
import com.mb.laos.model.SpecialPremiumAccountNumber;
import com.mb.laos.model.Transaction;
import com.mb.laos.model.dto.InformationTemplateContentDTO;
import com.mb.laos.model.dto.NotificationDTO;
import com.mb.laos.model.dto.PremiumAccNumberDTO;
import com.mb.laos.model.dto.QueryBeautifulAccountDTO;
import com.mb.laos.model.dto.QueryPremiumAccNumberDTO;
import com.mb.laos.model.dto.ReferralHistoryDTO;
import com.mb.laos.model.dto.TransactionDTO;
import com.mb.laos.repository.CommonRepository;
import com.mb.laos.repository.ContentTemplateRepository;
import com.mb.laos.repository.CustomerLoginRepository;
import com.mb.laos.repository.CustomerSupportPhoneNumberRepository;
import com.mb.laos.repository.CustomerSupportRepository;
import com.mb.laos.repository.DotpRepository;
import com.mb.laos.repository.InformationTemplateContentRepository;
import com.mb.laos.repository.InformationTemplateRepository;
import com.mb.laos.repository.MoneyAccountRepository;
import com.mb.laos.repository.NotiTransactionRepository;
import com.mb.laos.repository.NotificationRepository;
import com.mb.laos.repository.OtpRepository;
import com.mb.laos.repository.PremiumAccNumberRepository;
import com.mb.laos.repository.PremiumAccountNumberStructureRepository;
import com.mb.laos.repository.ReferralHistoryRepository;
import com.mb.laos.repository.ReferralRepository;
import com.mb.laos.repository.SpecialPremiumAccountNumberRepository;
import com.mb.laos.repository.TransactionRepository;
import com.mb.laos.request.ConfirmOtpRequest;
import com.mb.laos.service.ApiGeeBeautifulService;
import com.mb.laos.service.ApiGeeSmsBalanceService;
import com.mb.laos.service.ApiGeeTransferService;
import com.mb.laos.service.ConsumerOtpService;
import com.mb.laos.service.PremiumAccNumberService;
import com.mb.laos.service.SendService;
import com.mb.laos.service.mapper.InformationTemplateContentMapper;
import com.mb.laos.service.mapper.NotificationMapper;
import com.mb.laos.service.mapper.PremiumAccNumberMapper;
import com.mb.laos.service.mapper.ReferralHistoryMapper;
import com.mb.laos.service.mapper.TransactionMapper;
import com.mb.laos.util.DateUtil;
import com.mb.laos.util.DiagnosticContextUtil;
import com.mb.laos.util.MD5Generator;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.Validator;
import com.mb.laos.validator.ValidationConstraint;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PremiumAccNumberServiceImpl implements PremiumAccNumberService {
    private final ApiGeeBeautifulService apiGeeBeautifulService;
    private final CommonRepository commonRepository;
    private final CustomerServiceImpl customerService;
    private final CustomerProperties customerProperties;
    private final PremiumAccountNumberStructureRepository premiumAccountNumberStructureRepository;
    private final SpecialPremiumAccountNumberRepository specialPremiumAccountNumberRepository;
    private final MoneyAccountRepository moneyAccountRepository;
    private final DotpRepository dotpRepository;
    private final CustomerLoginRepository customerLoginRepository;
    private final OtpProperties otpProperties;
    private final ApiGeeSmsBalanceService apiGeeSmsBalanceService;
    private final ConsumerOtpService consumerOtpService;
    private final ReferralRepository referralRepository;
    private final TransactionMapper transactionMapper;
    private final TransactionRepository transactionRepository;
    private final OtpRepository otpRepository;
    private final PremiumAccNumberRepository premiumAccNumberRepository;
    private final PremiumAccNumberMapper premiumAccNumberMapper;
    private final ContentTemplateRepository contentTemplateRepository;
    private final NotificationRepository notificationRepository;
    private final NotificationMapper notificationMapper;
    private final NotiTransactionRepository notiTransactionRepository;
    private final ReferralHistoryMapper referralHistoryMapper;
    private final ReferralHistoryRepository referralHistoryRepository;
    private final ApiGeeTransferService apiGeeTransferService;
    private final PremiumAccNumberProperties premiumAccNumberProperties;
    private final InformationTemplateRepository informationTemplateRepository;
    private final InformationTemplateContentRepository informationTemplateContentRepository;
    private final InformationTemplateContentMapper informationTemplateContentMapper;
    private final CustomerSupportRepository customerSupportRepository;
    private final CustomerSupportPhoneNumberRepository customerSupportPhoneNumberRepository;
    private final SendService sendService;

    @Override
    public QueryPremiumAccNumberDTO detailNonUser(QueryPremiumAccNumberRequest request) {
        QueryPremiumAccNumberDTO dto;

        if (Validator.isNull(request.getExpectString())) {
            throw new BadRequestAlertException(ErrorCode.MSG101184);
        }

        if (request.getType().equals(PremiumAccountType.ACCOUNT_MATCH_PHONE.getValue())) {
            if (!request.getExpectString().matches(ValidationConstraint.PATTERN.PHONE_NUMBER_REGEX)) {
                throw new BadRequestAlertException(ErrorCode.MSG101187);
            }

            this.validatePhoneNumberNonUser(request);
        }
        dto = getPremiumAccNumber(request, request.getExpectString());
        return dto;
    }

    private void validatePhoneNumberNonUser(QueryPremiumAccNumberRequest request) {
        if (Validator.isNull(request.getTransactionId())) {
            throw new BadRequestAlertException(ErrorCode.MSG101199);
        }

        String transactionId = this.otpRepository.getTransactionIfPresent(request.getExpectString());
        if (Validator.isNull(transactionId)) {
            throw new BadRequestAlertException(ErrorCode.MSG101198);
        }

        int fromIndex = transactionId.indexOf(StringPool.COLON);

        if (!Validator.equals(request.getTransactionId(), transactionId) || fromIndex < 0) {
            throw new BadRequestAlertException(ErrorCode.MSG101198);
        }
    }

    @Override
    public QueryPremiumAccNumberDTO detail(QueryPremiumAccNumberRequest request) {
        QueryPremiumAccNumberDTO dto;
        Customer customer = this.customerService.getCustomerLogin();
        dto = getPremiumAccNumber(request, customer.getUsername());
        return dto;
    }

    private QueryPremiumAccNumberDTO getPremiumAccNumber(QueryPremiumAccNumberRequest request, String phoneNumber) {
        QueryBeautifulAccountRequest query;
        QueryPremiumAccNumberDTO dto;
        if (request.getType().equals(PremiumAccountType.ACCOUNT_MATCH_PHONE.getValue())) {
            request.setExpectString(phoneNumber);
            query = QueryBeautifulAccountRequest.builder()
                    .expectedString(phoneNumber)
                    .channel(Channel.APP.name())
                    .accountType(String.valueOf(PremiumAccountType.ACCOUNT_MATCH_PHONE.getValue()))
                    .branchCode(this.customerProperties.getDefaultBranchCode())
                    .build();
        } else {
            if (Validator.isNull(request.getExpectString())) {
                throw new BadRequestAlertException(ErrorCode.MSG101184);
            }

            if (!request.getExpectString().matches(ValidationConstraint.PATTERN.EXPECT_STRING)) {
                throw new BadRequestAlertException(ErrorCode.MSG101185);
            }

            query = QueryBeautifulAccountRequest.builder()
                    .expectedString(request.getExpectString().startsWith(StringPool.NUMBER_0) ? request.getExpectString().substring(1) : request.getExpectString())
                    .maxRecord("1")
                    .channel(Channel.APP.name())
                    .accountType(String.valueOf(PremiumAccountType.CUSTOMIZED_ACCOUNT.getValue()))
                    .accountLength(String.valueOf(request.getExpectString().length()))
                    .branchCode(this.customerProperties.getDefaultBranchCode())
                    .build();
        }

        QueryBeautifulAccountResponse queryBeautifulAccount = this.apiGeeBeautifulService.queryBeautifulAccount(query);

        if (Validator.isNotNull(queryBeautifulAccount)) {
            dto = this.getPriceWithMatchPhone(request.getExpectString(), queryBeautifulAccount);
        } else {
            throw new BadRequestAlertException(ErrorCode.MSG101193);
        }
        return dto;
    }

    @Override
    public List<QueryPremiumAccNumberDTO> queryPremiumAccNumberNonUser(QueryPremiumAccNumberRequest request) {
        List<QueryPremiumAccNumberDTO> dtos = new ArrayList<>();
        if (!request.getType().equals(PremiumAccountType.ACCOUNT_MATCH_PHONE.getValue())
                && !request.getType().equals(PremiumAccountType.CUSTOMIZED_ACCOUNT.getValue())) {
            throw new BadRequestAlertException(ErrorCode.MSG101208);
        }

        if (Validator.isNull(request.getExpectString())) {
            throw new BadRequestAlertException(ErrorCode.MSG101184);
        }

        if (request.getType().equals(PremiumAccountType.ACCOUNT_MATCH_PHONE.getValue())) {
            if (!request.getExpectString().matches(ValidationConstraint.PATTERN.PHONE_NUMBER_REGEX)) {
                throw new BadRequestAlertException(ErrorCode.MSG101187);
            }

            this.validatePhoneNumberNonUser(request);
            QueryBeautifulAccountRequest queryPremiumAccNumberRequest = QueryBeautifulAccountRequest.builder()
                    .expectedString(request.getExpectString())
                    .channel(Channel.APP.name())
                    .accountType(String.valueOf(PremiumAccountType.ACCOUNT_MATCH_PHONE.getValue()))
                    .branchCode(this.customerProperties.getDefaultBranchCode())
                    .build();

            QueryBeautifulAccountResponse queryBeautifulAccount = this.apiGeeBeautifulService.queryBeautifulAccount(queryPremiumAccNumberRequest);

            if (Validator.isNotNull(queryBeautifulAccount)) {
                dtos.add(this.getPriceWithMatchPhone(request.getExpectString(), queryBeautifulAccount));
            } else {
                throw new BadRequestAlertException(ErrorCode.MSG101209);
            }
        } else {
            this.queryCustomAccNumber(request, dtos);
        }
        return dtos;
    }

    @Override
    public List<QueryPremiumAccNumberDTO> queryPremiumAccNumber(QueryPremiumAccNumberRequest request) {
        List<QueryPremiumAccNumberDTO> dtos = new ArrayList<>();
        Customer customer = this.customerService.getCustomerLogin();

        if (!request.getType().equals(PremiumAccountType.ACCOUNT_MATCH_PHONE.getValue())
                && !request.getType().equals(PremiumAccountType.CUSTOMIZED_ACCOUNT.getValue())) {
            throw new BadRequestAlertException(ErrorCode.MSG101208);
        }

        if (request.getType().equals(PremiumAccountType.ACCOUNT_MATCH_PHONE.getValue())) {
            request.setExpectString(customer.getPhoneNumber());

            QueryBeautifulAccountRequest queryPremiumAccNumberRequest = QueryBeautifulAccountRequest.builder()
                    .expectedString(customer.getPhoneNumber())
                    .channel(Channel.APP.name())
                    .accountType(String.valueOf(PremiumAccountType.ACCOUNT_MATCH_PHONE.getValue()))
                    .branchCode(this.customerProperties.getDefaultBranchCode())
                    .build();

            QueryBeautifulAccountResponse queryBeautifulAccount = this.apiGeeBeautifulService.queryBeautifulAccount(queryPremiumAccNumberRequest);

            if (Validator.isNotNull(queryBeautifulAccount)) {
                dtos.add(this.getPriceWithMatchPhone(request.getExpectString(), queryBeautifulAccount));
            } else {
                throw new BadRequestAlertException(ErrorCode.MSG101209);
            }
        } else {
            this.queryCustomAccNumber(request, dtos);
        }
        return dtos;
    }

    private void queryCustomAccNumber(QueryPremiumAccNumberRequest request, List<QueryPremiumAccNumberDTO> dtos) {
        if (Validator.isNull(request.getExpectString())) {
            throw new BadRequestAlertException(ErrorCode.MSG101184);
        }

        if (!request.getExpectString().matches(ValidationConstraint.PATTERN.EXPECT_STRING)) {
            throw new BadRequestAlertException(ErrorCode.MSG101185);
        }

        int numberGroup = request.getExpectString().length();

        List<Common> commons = this.commonRepository.findAllByCategoryAndStatus(CommonCategory.NUMBER_GROUP.name(), EntityStatus.ACTIVE.getStatus());
        List<String> numberGroups = commons.stream().map(Common::getCode)
                .filter(code -> Integer.parseInt(code) >= numberGroup && Integer.parseInt(code) <= QueryUtil.PAGE_SIZE_DEFAULT)
                .collect(Collectors.toList());

        numberGroups.forEach(item -> {
            QueryBeautifulAccountRequest queryPremiumAccNumberRequest = QueryBeautifulAccountRequest.builder()
                    .expectedString(request.getExpectString().startsWith(StringPool.NUMBER_0) ?
                            request.getExpectString().replaceFirst(ValidationConstraint.PATTERN.FIRST_ZERO, StringPool.BLANK) : request.getExpectString())
                    .maxRecord(String.valueOf(QueryUtil.PAGE_SIZE_DEFAULT))
                    .channel(Channel.APP.name())
                    .accountType(String.valueOf(PremiumAccountType.CUSTOMIZED_ACCOUNT.getValue()))
                    .accountLength(item)
                    .branchCode(this.customerProperties.getDefaultBranchCode())
                    .build();

            QueryBeautifulAccountResponse queryBeautifulAccount = this.apiGeeBeautifulService.queryBeautifulAccount(queryPremiumAccNumberRequest);

            if (Validator.isNotNull(queryBeautifulAccount)) {
                if (Validator.isNotNull(queryBeautifulAccount.getData())) {
                    List<String> accountNumbers = queryBeautifulAccount.getData().stream().map(QueryBeautifulAccountDTO::getAccountNumber).collect(Collectors.toList());
                    dtos.addAll(this.getPrice(accountNumbers, Integer.valueOf(item)));
                }
            }
        });
    }

    private QueryPremiumAccNumberDTO getPriceWithMatchPhone(String premiumAccNumber, QueryBeautifulAccountResponse queryBeautifulAccount) {
        QueryPremiumAccNumberDTO dto = new QueryPremiumAccNumberDTO();
        SpecialPremiumAccountNumber special = this.specialPremiumAccountNumberRepository
                .findBySpecialPremiumAccountNumberAndStatus(premiumAccNumber, EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(special)) {
            if (special.isHided()) {
                throw new BadRequestAlertException(ErrorCode.MSG101193);
            } else {
                for (QueryBeautifulAccountDTO item : queryBeautifulAccount.getData()) {
                    dto = new QueryPremiumAccNumberDTO
                            (item.getAccountNumber(), item.getAccountLength(), special.getPrice(),
                                    this.getTotalPrice(special.getPrice(), Validator.isNotNull(special.getDiscount()) ? special.getDiscount() : 0),
                                    Validator.isNotNull(special.getDiscount()) ? special.getDiscount() : 0);
                }
            }
        } else {
            List<PremiumAccountNumberStructure> structures = this.premiumAccountNumberStructureRepository
                    .findAllByLengthAndStatus(premiumAccNumber.length(), EntityStatus.ACTIVE.getStatus());

            if (Validator.isNotNull(structures)) {
                List<PremiumAccountNumberStructure> numberStructures = structures.stream()
                        .filter(item -> premiumAccNumber.matches(item.getPattern()))
                        .collect(Collectors.toList());

                if (Validator.isNotNull(numberStructures)) {
                    Optional<PremiumAccountNumberStructure> structure;

                    if (numberStructures.size() > 1) {
                        structure = numberStructures.stream()
                                .max(Comparator.comparingLong(PremiumAccountNumberStructure::getTotalPrice));
                    } else {
                        structure = numberStructures.stream().findFirst();
                    }

                    dto = new QueryPremiumAccNumberDTO(premiumAccNumber, String.valueOf(structure.get().getLength()),
                            structure.get().getPrice(), this.getTotalPrice(structure.get().getPrice(),
                            structure.get().getDiscount()), structure.get().getDiscount());
                } else {
                    dto = new QueryPremiumAccNumberDTO(
                            premiumAccNumber,
                            String.valueOf(premiumAccNumber.length()),
                            this.premiumAccNumberProperties.getDefaultPrice(),
                            this.getTotalPrice(this.premiumAccNumberProperties.getDefaultPrice(), this.premiumAccNumberProperties.getDiscount()),
                            this.premiumAccNumberProperties.getDiscount()
                    );
                }
            } else {
                dto = new QueryPremiumAccNumberDTO(
                        premiumAccNumber,
                        String.valueOf(premiumAccNumber.length()),
                        this.premiumAccNumberProperties.getDefaultPrice(),
                        this.getTotalPrice(this.premiumAccNumberProperties.getDefaultPrice(), this.premiumAccNumberProperties.getDiscount()),
                        this.premiumAccNumberProperties.getDiscount()
                );
            }
        }
        return dto;
    }

    private List<QueryPremiumAccNumberDTO> getPrice(List<String> accountNumbers, Integer numberGroup) {
        List<QueryPremiumAccNumberDTO> dtos = new ArrayList<>();
        List<String> specials;

        List<SpecialPremiumAccountNumber> list = this.specialPremiumAccountNumberRepository
                .findAllBySpecialPremiumAccountNumberInAndStatus(accountNumbers, EntityStatus.ACTIVE.getStatus());

        List<SpecialPremiumAccountNumber> specialAccNumbers = list.stream().filter(item -> Validator.equals(item.isHided(), false)).collect(Collectors.toList());
        List<SpecialPremiumAccountNumber> hiddens = list.stream().filter(item -> Validator.equals(item.isHided(), true)).collect(Collectors.toList());

        if (Validator.isNotNull(specialAccNumbers)) {
            specials = specialAccNumbers.stream().map(SpecialPremiumAccountNumber::getSpecialPremiumAccountNumber).collect(Collectors.toList());

            specialAccNumbers.forEach(item -> dtos.add(new QueryPremiumAccNumberDTO(
                    item.getSpecialPremiumAccountNumber(),
                    String.valueOf(item.getSpecialPremiumAccountNumber().length()),
                    item.getPrice(), this.getTotalPrice(item.getPrice(), Validator.isNull(item.getDiscount()) ? 0 : item.getDiscount()),
                    Validator.isNull(item.getDiscount()) ? 0 : item.getDiscount())
            ));

            accountNumbers.removeAll(specials);
        }

        if (Validator.isNotNull(hiddens)) {
            List<String> hiddenNumbers = hiddens.stream().map(SpecialPremiumAccountNumber::getSpecialPremiumAccountNumber).collect(Collectors.toList());
            accountNumbers.removeAll(hiddenNumbers);
        }

        List<PremiumAccountNumberStructure> structures = this.premiumAccountNumberStructureRepository
                .findAllByLengthAndStatus(numberGroup, EntityStatus.ACTIVE.getStatus());

        accountNumbers.forEach(accountNumber -> {
            List<PremiumAccountNumberStructure> structureMatches = structures.stream()
                    .filter(item -> accountNumber.matches(item.getPattern()))
                    .collect(Collectors.toList());

            if (Validator.isNotNull(structureMatches)) {
                Optional<PremiumAccountNumberStructure> structure;
                if (structureMatches.size() > 1) {
                    structure = structureMatches.stream()
                            .max(Comparator.comparingLong(PremiumAccountNumberStructure::getTotalPrice));
                } else {
                    structure = structureMatches.stream().findFirst();
                }

                dtos.add(new QueryPremiumAccNumberDTO(
                        accountNumber, String.valueOf(accountNumber.length()), structure.get().getPrice(),
                        this.getTotalPrice(structure.get().getPrice(), structure.get().getDiscount()),
                        structure.get().getDiscount()));
            } else {
                dtos.add(new QueryPremiumAccNumberDTO(
                        accountNumber,
                        String.valueOf(accountNumber.length()),
                        this.premiumAccNumberProperties.getDefaultPrice(),
                        this.getTotalPrice(this.premiumAccNumberProperties.getDefaultPrice(), this.premiumAccNumberProperties.getDiscount()),
                        this.premiumAccNumberProperties.getDiscount())
                );
            }
        });

        return dtos;
    }

    @Override
    public OtpPremiumAccNumberResponse requestOtp(OtpPremiumAccNumberRequest request) {
        Customer customer = this.customerService.getCustomerLogin();
        OtpPremiumAccNumberResponse response = new OtpPremiumAccNumberResponse();
        long price = 0;
        double discount = 0;
        boolean isSpecialNumber = false;
        Long premiumAccNumberStructureId = null;

        this.verifyDeviceId(request.getDeviceId(), customer);

        if (Validator.isNotNull(request.getReferralCode())) {
            Optional<Referral> referral;
            referral = this.referralRepository.findByRmCodeAndTypeAndStatus(request.getReferralCode(), ReferralType.STAFF, EntityStatus.ACTIVE.getStatus());

            if (!referral.isPresent()) {
                referral = this.referralRepository.findByPhoneNumberAndTypeAndStatus(request.getReferralCode(), ReferralType.STAFF, EntityStatus.ACTIVE.getStatus());

                if (!referral.isPresent()) {
                    throw new BadRequestAlertException(ErrorCode.MSG100150);
                }
            }
        }

        Optional<MoneyAccount> moneyAccount = this.moneyAccountRepository
                .findByAccountNumberAndCustomerIdAndStatus(request.getPaymentAccNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

        if (!moneyAccount.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG1033);
        }

        if (!Validator.equals(request.getCurrency(), moneyAccount.get().getCurrency())) {
            throw new BadRequestAlertException(ErrorCode.MSG100056);
        }

        List<PremiumAccountNumberStructure> structures = this.premiumAccountNumberStructureRepository
                .findAllByLengthAndStatus(request.getPremiumAccNumber().length(), EntityStatus.ACTIVE.getStatus());

        List<SpecialPremiumAccountNumber> specials = this.specialPremiumAccountNumberRepository
                .findAllBySpecialPremiumAccountNumberAndStatus(request.getPremiumAccNumber(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(specials)) {
            boolean hidden = specials.stream().anyMatch(item -> Validator.equals(item.isHided(), true));
            Optional<SpecialPremiumAccountNumber> special = specials.stream().filter(item -> Validator.equals(item.isHided(), false)).findFirst();

            if (hidden) {
                throw new BadRequestAlertException(ErrorCode.MSG101187);
            }

            if (special.isPresent()) {
                price = special.get().getPrice();
                discount = special.get().getDiscount();
                isSpecialNumber = true;
            }
        } else {
            List<PremiumAccountNumberStructure> numberStructures = structures.stream()
                    .filter(item -> Pattern.compile(item.getPattern()).matcher(request.getPremiumAccNumber()).matches())
                    .collect(Collectors.toList());

            if (Validator.isNotNull(numberStructures)) {
                Optional<PremiumAccountNumberStructure> structure;

                if (numberStructures.size() > 1) {
                    structure = numberStructures.stream()
                            .max(Comparator.comparingLong(PremiumAccountNumberStructure::getTotalPrice));
                } else {
                    structure = numberStructures.stream().findFirst();
                }

                price = structure.get().getPrice();
                discount = structure.get().getDiscount();
                premiumAccNumberStructureId = structure.get().getPremiumAccountNumberStructureId();
            } else {
                price = this.premiumAccNumberProperties.getDefaultPrice();
                discount = this.premiumAccNumberProperties.getDiscount();
            }
        }

        TransactionDTO transactionDTO = new TransactionDTO(request);
        transactionDTO.setPhoneNumber(customer.getUsername());
        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode());
        transactionDTO.setCustomerId(customer.getCustomerId());
        transactionDTO.setMessage(StringUtil.join(new String[]{customer.getFullname(), Labels.getLabels(LabelKey.LABEL_CREATE_PREMIUM_ACC_NUMBER).toLowerCase(), request.getPremiumAccNumber()}, StringPool.SPACE));

        DOTP dotp = this.dotpRepository.findByPhoneNumberAndDeviceIdAndStatus(customer.getPhoneNumber(), request.getDeviceId(), EntityStatus.ACTIVE.getStatus());
        String transData = null;
        this.consumerOtpService.requestOtp(transactionDTO.getTransactionId(), customer.getPhoneNumber(), OtpType.PREMIUM_ACCOUNT_NUMBER);

        if (Validator.isNotNull(dotp)) {
            transData = request.getPaymentAccNumber() + DiagnosticContextUtil.getClientMessageId() + request.getDeviceId();
            transactionDTO.setOtpType(OtpConfirmType.DOTP);
            response.setType(OtpConfirmType.DOTP.name());
            response.setTransData(transData);
        } else {
            this.consumerOtpService.checkSpam(customer.getPhoneNumber(), OtpType.PREMIUM_ACCOUNT_NUMBER);

            InitOnOffSmsCus initOnOffSmsCus = InitOnOffSmsCus.builder()
                    .idNumber(customer.getIdCardNumber())
                    .mobileSms(customer.getUsername())
                    .build();

            this.apiGeeSmsBalanceService.initOnOffSmsCus(initOnOffSmsCus);

            transactionDTO.setOtpType(OtpConfirmType.SMS);
            response.setType(OtpConfirmType.SMS.name());
        }

        premiumAccNumberRepository.put(transactionDTO.getTransactionId(),
                new PremiumAccNumberCacheDTO(request.getPremiumAccNumber(),
                        customer.getCif(), request.getReferralCode(),
                        request.getPaymentAccNumber(),
                        request.getCurrency(),
                        request.getDeviceId(),
                        transData,
                        price,
                        discount,
                        request.getReferralCode(),
                        Validator.isNotNull(premiumAccNumberStructureId) ? premiumAccNumberStructureId : null,
                        isSpecialNumber,
                        null
                ));

        response.setRemainTimeSecond(this.otpProperties.getDuration());
        response.setTransactionId(transactionDTO.getTransactionId());
        transactionDTO.setTransactionAmount(Validator.isNotNull(price) ? (double) price : 0D);
        transactionDTO.setTotalAmount(Validator.isNotNull(this.getTotalPrice(price, discount)) ? this.getTotalPrice(price, discount).doubleValue() : 0D);
        this.transactionRepository.save(transactionMapper.toEntity(transactionDTO));
        return response;
    }

    @Override
    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    public PremiumAccNumberDTO confirmOtp(ConfirmOtpRequest request) {
        PremiumAccNumberDTO premiumAccNumberDTO = new PremiumAccNumberDTO();
        Customer customer = this.customerService.getCustomerLogin();

        Optional<Transaction> transaction = this.transactionRepository
                .findByTransactionIdAndCustomerIdAndStatusAndTransactionStatus(request.getTransactionId(),
                        customer.getCustomerId(), EntityStatus.ACTIVE.getStatus(), TransactionStatus.PROCESSING);

        if (!transaction.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }

        AuthenMethodV2 authenMethodV2 = AuthenMethodV2.builder()
                .otpValue(request.getOtpValue())
                .build();
        this.verifyDeviceId(request.getDeviceId(), customer);

        DOTP dotp = this.dotpRepository.findByPhoneNumberAndDeviceIdAndStatus(customer.getPhoneNumber(), request.getDeviceId(), EntityStatus.ACTIVE.getStatus());
        if (Validator.isNotNull(dotp)) {
            if (Validator.isNull(request.getTransData())) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_TRANS_DATA_MUST_NOT_BE_EMPTY), PremiumAccNumber.class.getSimpleName(),
                        LabelKey.ERROR_TRANS_DATA_MUST_NOT_BE_EMPTY);
            }

            authenMethodV2.setType(OtpConfirmType.DOTP.getValue());
            authenMethodV2.setDeviceId(request.getDeviceId());
            authenMethodV2.setToken(dotp.getToken());
            authenMethodV2.setTransData(MD5Generator.md5(request.getTransData()));
            authenMethodV2.setUserId(customer.getCif() + customer.getIdCardNumber());
        } else {
            authenMethodV2.setType(OtpConfirmType.SMS.getValue());
        }

        OtpConfirmType otpConfirmType = Validator.isNotNull(dotp) ? OtpConfirmType.DOTP : OtpConfirmType.SMS;
        String key = customer.getPhoneNumber() + StringPool.COMMA + OtpType.PREMIUM_ACCOUNT_NUMBER.name();
        OtpValue otpValue = this.otpRepository.getIfPresent(key);

        if (Validator.isNull(otpValue)) {
            throw new BadRequestAlertException(ErrorCode.MSG10004);
        }

        PremiumAccNumberCacheDTO cache = this.premiumAccNumberRepository.get(otpValue.getTransactionId());

        if (!Validator.equals(request.getTransactionId(), otpValue.getTransactionId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }

        Long feeAmount = cache.getPrice() > 0 ? this.getTotalPrice(cache.getPrice(), cache.getDiscount()) : 0;

        if (cache.isSpecialNumber()) {
            this.updateSpecialNumber(cache.getPremiumAccNumber(), cache.getPrice(), cache.getDiscount());
        } else {
            SpecialPremiumAccountNumber specialNumber = this.specialPremiumAccountNumberRepository
                    .findBySpecialPremiumAccountNumberAndStatus(cache.getPremiumAccNumber(), EntityStatus.ACTIVE.getStatus());

            if (Validator.isNotNull(specialNumber)) {
                throw new BadRequestAlertException(ErrorCode.MSG101193);
            }
        }

        CreateBeautifulAccountRequest createBeautifulAccountRequest = CreateBeautifulAccountRequest.builder()
                .beautifulAccountNo(cache.getPremiumAccNumber())
                .custId(customer.getCif())
                .staffCode(cache.getStaffCode())
                .branchCode(this.customerProperties.getDefaultBranchCode())
                .accountNo(cache.getAccountNo())
                .customerName(customer.getFullname())
                .channel(Channel.APP.name())
                .currency(cache.getCurrency())
                .feeAmount(String.valueOf(feeAmount))
                .authenMethod(authenMethodV2)
                .build();

        CreateBeautifulAccountResponse response = this.consumerOtpService.verifyOtp(otpValue.getTransactionId(),
                customer.getPhoneNumber(), OtpType.PREMIUM_ACCOUNT_NUMBER, createBeautifulAccountRequest,
                this.apiGeeBeautifulService::createBeautifulAccount, otpConfirmType);

        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.SUCCESS)) {
            premiumAccNumberDTO.setCustomerId(customer.getCustomerId());
            premiumAccNumberDTO.setPremiumAccNumber(cache.getPremiumAccNumber());
            premiumAccNumberDTO.setStatus(EntityStatus.ACTIVE.getStatus());
            premiumAccNumberDTO.setOriginalPrice(cache.getPrice());
            premiumAccNumberDTO.setReferralCode(cache.getReferralCode());
            premiumAccNumberDTO.setDiscount(cache.getDiscount());
            premiumAccNumberDTO.setTotalPrice(this.getTotalPrice(cache.getPrice(), cache.getDiscount()));
            premiumAccNumberDTO.setPremiumAccNumberStructureId(Validator.isNotNull(cache.getPremiumAccNumberStructureId()) ? cache.getPremiumAccNumberStructureId() : null);

            if (Validator.isNotNull(cache.getStaffCode())) {
                Optional<Referral> referral = this.referralRepository.findByRmCodeAndTypeAndStatus(cache.getReferralCode(), ReferralType.STAFF, EntityStatus.ACTIVE.getStatus());

                if (!referral.isPresent()) {
                    referral = this.referralRepository.findByPhoneNumberAndTypeAndStatus(cache.getReferralCode(), ReferralType.STAFF, EntityStatus.ACTIVE.getStatus());

                    if (!referral.isPresent()) {
                        throw new BadRequestAlertException(ErrorCode.MSG100150);
                    }
                }

                premiumAccNumberDTO.setReferralId(referral.get().getReferralId());

                // Lưu ls gioi thieu
                this.createReferralHistory(customer, referral.get().getReferralId());
            }

            transaction.get().setTransactionStatus(TransactionStatus.SUCCESS);
            transaction.get().setTransactionFinishTime(Instant.now());
        } else if (Validator.equals(response.getTransactionStatus(), TransactionStatus.FAILED)) {
            transaction.get().setTransactionStatus(TransactionStatus.FAILED);
            transaction.get().setTransactionFinishTime(Instant.now());

            throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());

        } else if (Validator.equals(response.getTransactionStatus(), TransactionStatus.TIMEOUT)) {
            transaction.get().setTransactionStatus(TransactionStatus.TIMEOUT);
            transaction.get().setTransactionFinishTime(Instant.now());

            throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
        }

        if (transaction.get().getTotalAmount() > 0) {
            this.createNotification(this.transactionMapper.toDto(transaction.get()));
        }

        this.notificationCreatePremiumAccNumber(customer, premiumAccNumberDTO.getPremiumAccNumber());

        PremiumAccNumber premiumAccNumber = this.premiumAccNumberRepository.save(this.premiumAccNumberMapper.toEntity(premiumAccNumberDTO));
        this.premiumAccNumberRepository.evict(transaction.get().getTransactionId());
        this.apiGeeTransferService.evictCheckAccCurrency(customer.getCif());
        return this.premiumAccNumberMapper.toDto(premiumAccNumber);
    }

    private void updateSpecialNumber(String specialAccNumber, Long price, Double discount) {

        SpecialPremiumAccountNumber specialNumber = this.specialPremiumAccountNumberRepository
                .findBySpecialPremiumAccountNumberAndStatus(specialAccNumber, EntityStatus.ACTIVE.getStatus());

        if (Validator.isNull(specialNumber)) {
            throw new BadRequestAlertException(ErrorCode.MSG101193);
        }

        if (!specialNumber.getPrice().equals(price)) {
            throw new BadRequestAlertException(ErrorCode.MSG101188);
        }

        if (!specialNumber.getDiscount().equals(discount)) {
            throw new BadRequestAlertException(ErrorCode.MSG101212);
        }

        specialNumber.setStatus(PremiumAccNumberStatus.SOLD.getValue());
    }

    private void verifyDeviceId(String deviceId, Customer customer) {
        CustomerLogin customerLogin = this.customerLoginRepository.findFirstByUsernameAndSuccessOrderByLastModifiedDateDesc(customer.getUsername(), Boolean.TRUE);
        if (!Validator.equals(customerLogin.getDeviceId(), deviceId)) {
            throw new BadRequestAlertException(ErrorCode.MSG1024);
        }
    }

    private Long getTotalPrice(Long price, double discount) {
        return Math.round(price - (price * discount) / 100);
    }

    private void createNotification(TransactionDTO transactionDTO) {
        Map<String, String> valuesAccount = new HashMap<>();

        String transactionType = StringPool.MINUS;

        valuesAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(),
                Labels.getLabels(LabelKey.LABEL_ACCOUNT_TYPE_PAYMENT_ACCOUNT));

        valuesAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), transactionDTO.getCustomerAccNumber());

        valuesAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(),
                Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION));

        valuesAccount.put(TemplateField.TRANSACTION_AMOUNT.name(),
                transactionType + StringUtil.formatMoney(transactionDTO.getTotalAmount()));

        valuesAccount.put(TemplateField.CURRENCY.name(), transactionDTO.getTransactionCurrency());

        valuesAccount.put((TemplateField.LABEL_TRANSACTION_TIME.name()), Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));

        valuesAccount.put(TemplateField.TRANSACTION_TIME.name(), transactionDTO.formatTransactionFinishTime());

        valuesAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(), Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT));

        valuesAccount.put(TemplateField.MESSAGE.name(), transactionDTO.getMessage());

        ContentTemplate templatePayMerchant = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.BALANCE_CHANGE_TRANSFER_MONEY_V2.name(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(templatePayMerchant)) {
            NotificationDTO notificationDTO = new NotificationDTO();

            notificationDTO.setEndUserType(EndUserType.CUSTOMER);
            notificationDTO.setClassPk(transactionDTO.getCustomerId());
            notificationDTO.setNotificationStatus(NotificationStatus.PENDING);
            notificationDTO.setNotificationType(NotificationType.TRANSFER_TRANSACTION);
            notificationDTO.setTargetType(TargetType.USER);
            notificationDTO.setPublishTime(LocalDateTime.now());

            notificationDTO.setTitle(Labels.getLabels(templatePayMerchant.getTitle()));
            notificationDTO.setContent(StringUtil.replaceMapValue(templatePayMerchant.getContent(),
                    valuesAccount));
            notificationDTO.setDescription(templatePayMerchant.getDescription());

            Notification notificationAfterSaved = this.notificationRepository.save(
                    this.notificationMapper.toEntity(notificationDTO));

            this.createNotiTransaction(notificationAfterSaved.getNotificationId(),
                    transactionDTO.getTransferTransactionId());

            this.sendService.sendNotification(this.notificationMapper.toDto(notificationAfterSaved));
        }
    }

    private void createNotiTransaction(Long notificationId, Long transactionId) {
        NotiTransaction notiTransaction = new NotiTransaction();

        notiTransaction.setNotificationId(notificationId);
        notiTransaction.setTransactionId(transactionId);
        notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus());

        this.notiTransactionRepository.save(notiTransaction);
    }

    private void notificationCreatePremiumAccNumber(Customer customer, String premiumAccNumber) {
        NotificationDTO notificationDTO = new NotificationDTO();

        notificationDTO.setEndUserType(EndUserType.CUSTOMER);
        notificationDTO.setClassPk(customer.getCustomerId());
        notificationDTO.setNotificationStatus(NotificationStatus.PENDING);
        notificationDTO.setNotificationType(NotificationType.OTHER);
        notificationDTO.setTargetType(TargetType.USER);
        notificationDTO.setPublishTime(DateUtil.convertToCurrentTimeZoneLA());
        notificationDTO.setTitle(Labels.getLabels(LabelKey.LABEL_TITLE_CREATE_PREMIUM_ACC_NUMBER_SUCCESS));
        notificationDTO.setContent(Labels.getLabels(LabelKey.LABEL_CREATE_PREMIUM_ACC_NUMBER_SUCCESSFULLY,
                new Object[]{premiumAccNumber}));

        this.notificationRepository.save(
                this.notificationMapper.toEntity(notificationDTO));
    }

    private void createReferralHistory(Customer customer, Long referralId) {
        ReferralHistoryDTO dto = new ReferralHistoryDTO();
        dto.setReferralId(referralId);
        dto.setTargetUserFullName(customer.getFullname());
        dto.setTargetUserPhoneNumber(customer.getUsername());
        dto.setTargetUserId(customer.getCustomerId());
        dto.setStatus(EntityStatus.ACTIVE.getStatus());

        this.referralHistoryRepository.save(this.referralHistoryMapper.toEntity(dto));
    }

    @Override
    public InformationTemplateContentDTO notificationDetail() {
        String phoneNumber = null;
        InformationTemplateContentDTO informationTemplateContent = null;
        Customer customer = this.customerService.getCustomerLogin();
        String language = Labels.getLanguageFromRequest();

        CustomerSupport customerSupport = this.customerSupportRepository.findByStatus(EntityStatus.ACTIVE.getStatus());
        if (Validator.isNotNull(customerSupport)) {
            List<CustomerSupportPhoneNumber> customerSupportPhoneNumbers = this.customerSupportPhoneNumberRepository
                    .findAllByCustomerSupportIdAndStatus(customerSupport.getCustomerSupportId(), EntityStatus.ACTIVE.getStatus());

            if (Validator.isNotNull(customerSupportPhoneNumbers)) {
                List<String> phoneNumbers = customerSupportPhoneNumbers.stream().map(CustomerSupportPhoneNumber::getPhoneNumber).collect(Collectors.toList());

                phoneNumber = phoneNumbers.size() > 1 ? String.join(StringPool.COMMA, phoneNumbers) : phoneNumbers.get(0);
            }
        }

        PremiumAccNumber premiumAccNumber = this.premiumAccNumberRepository.findByCustomerIdAndStatus(customer.getCustomerId(), EntityStatus.PENDING.getStatus());

        InformationTemplate informationTemplate = this.informationTemplateRepository
                .findByInformationTemplateCodeAndStatus(InformationTemplateType.LUCKY_ACCOUNT_NUMBER.name(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(informationTemplate) && Validator.isNotNull(premiumAccNumber)) {
            informationTemplateContent =
                    this.informationTemplateContentMapper
                            .toDto(this.informationTemplateContentRepository
                                    .findByInformationTemplateIdAndLanguage(informationTemplate.getInformationTemplateId(), language));

            if (Validator.isNotNull(informationTemplateContent)) {
                String totalAmount = StringUtil.formatMoney(premiumAccNumber.getTotalPrice()) + StringPool.SPACE + CurrencyType.LAK.name();
                LocalDate date = premiumAccNumber.getPaymentDueDate().atZone(ZoneId.systemDefault()).toLocalDate();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.SHORT_DATE_PATTERN_DASH);
                String paymentDueDate = date.format(formatter);

                informationTemplateContent.setContentNoHtml(
                        MessageFormat.format(
                                informationTemplateContent.getContentNoHtml(),
                                paymentDueDate,
                                customer.getFullname().toUpperCase(),
                                premiumAccNumber.getPremiumAccNumber(),
                                totalAmount,
                                phoneNumber,
                                Validator.isNotNull(customerSupport) ? customerSupport.getEmail() : null
                        ));

                informationTemplateContent.setContent(
                        MessageFormat.format(
                                informationTemplateContent.getContent(),
                                paymentDueDate,
                                customer.getFullname().toUpperCase(),
                                premiumAccNumber.getPremiumAccNumber(),
                                totalAmount,
                                phoneNumber,
                                Validator.isNotNull(customerSupport) ? customerSupport.getEmail() : null
                        ));
            }
        }

        return informationTemplateContent;
    }
}
