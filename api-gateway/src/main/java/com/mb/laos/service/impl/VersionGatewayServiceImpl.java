package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.OperatingSystemType;
import com.mb.laos.gateway.request.UpdateVersionRequest;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Version;
import com.mb.laos.model.dto.VersionDTO;
import com.mb.laos.repository.VersionRepository;
import com.mb.laos.service.VersionGatewayService;
import com.mb.laos.service.mapper.VersionMapper;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;

@Service
@RequiredArgsConstructor
public class VersionGatewayServiceImpl implements VersionGatewayService {
    private final VersionRepository versionRepository;

    private final VersionMapper versionMapper;

    @Override
    public VersionDTO getLastestVersion(VersionDTO request) {
        OperatingSystemType type = request.getOperatingSystem();

        if (Validator.isNull(type)) {
            throw new BadRequestAlertException(ErrorCode.MSG100172);
        }

        List<Version> activeVersions = versionRepository.findByOperatingSystemAndStatus(type, EntityStatus.ACTIVE.getStatus());

        VersionDTO latestVersion = versionMapper.toDto(activeVersions.stream()
                .max(Comparator.comparing(Version::getReleaseDate))
                .orElseThrow(
                        () -> new BadRequestAlertException(ErrorCode.MSG4031)));

        // Trả về true để cho app check, nếu app cũ thì sẽ bắt phải lên app mới
        String clientVersionName = request.getName();
        if (Validator.isNull(clientVersionName)) {
            latestVersion.setForceUpdate(true);
            return latestVersion;
        }

        int compareResult = StringUtil.compareAppVersion(clientVersionName, latestVersion.getName());
        if (compareResult >= 0) {
            return latestVersion;
        }

        // Nếu bản mới nhất ko bắt buộc cập nhật --> Cần check xem các bản trung gian có bắt buộc phải cập nhật không
        if (!latestVersion.isForceUpdate()) {
            // Force update is true and client version is older
            Version clientVersion = activeVersions.stream()
                    .filter(v -> Validator.equals(v.getName(), clientVersionName))
                    .findFirst()
                    .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG4031));

            boolean hasIntermediateForceUpdate = activeVersions.stream()
                    .anyMatch(v -> v.getReleaseDate().isAfter(clientVersion.getReleaseDate())
                            && v.getReleaseDate().isBefore(latestVersion.getReleaseDate())
                            && v.isForceUpdate());

            latestVersion.setForceUpdate(hasIntermediateForceUpdate);
        }
        return latestVersion;
    }

    @Override
    public VersionDTO updateVersion(UpdateVersionRequest request) {

        //check version exits
        Version versionEntity = versionRepository.findByVersionId(request.getVersionId()).orElseThrow(
                () -> new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_VERSION_DOES_NOT_EXIST), ErrorCode.MSG4030.name(), LabelKey.ERROR_VERSION_DOES_NOT_EXIST));
        //check status
        if (Validator.equals(versionEntity.getStatus(), EntityStatus.INACTIVE.getStatus())) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_VERSION_STATUS_IS_INVALID), ErrorCode.MSG4030.name(), LabelKey.ERROR_VERSION_STATUS_IS_INVALID);
        }
        versionEntity.setInstalled(versionEntity.getInstalled() + 1);
        return versionMapper.toDto(versionRepository.save(versionEntity));


    }
}
