package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.UnauthorizedException;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.CustomerProperties;
import com.mb.laos.configuration.StatisticalProperties;
import com.mb.laos.enums.BillingType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.SavingAccountType;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.enums.TransferTransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Currency;
import com.mb.laos.model.Customer;
import com.mb.laos.model.MoneyAccount;
import com.mb.laos.model.Transaction;
import com.mb.laos.model.Transaction_;
import com.mb.laos.model.dto.StatisticalDTO;
import com.mb.laos.model.dto.StatisticalTransferDTO;
import com.mb.laos.model.search.TransactionSearchRequest;
import com.mb.laos.repository.CurrencyRepository;
import com.mb.laos.repository.MoneyAccountRepository;
import com.mb.laos.repository.StatisticalTransferRepository;
import com.mb.laos.security.util.GwSecurityUtils;
import com.mb.laos.service.StatisticalTransferService;
import com.mb.laos.util.DateUtil;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@Transactional
@RequiredArgsConstructor
public class StatisticalTransferServiceImpl implements StatisticalTransferService {

    private final StatisticalTransferRepository statisticalTransferRepository;

    private final StatisticalProperties statisticalProperties;

    private final MoneyAccountRepository moneyAccountRepository;

    private final CurrencyRepository currencyRepository;

    private final CustomerProperties customerProperties;

    @Override
    public StatisticalTransferDTO chart(TransactionSearchRequest request) {

        Customer customer = getCustomerLogin();

        MoneyAccount moneyAccountOfCustomer = this.validateStatisticalTransfer(request, customer);

        request.setCustomerId(customer.getCustomerId());
        request.setTransactionStatus(Collections.singletonList(TransactionStatus.SUCCESS));

        StatisticalTransferDTO statisticalTransferDTO = new StatisticalTransferDTO();

        List<Transaction> transactions = this.statisticalTransferRepository.search(request);

        Double totalTransfer = transactions.stream().filter(item -> (Validator.equals(item.getTransferType(), TransferType.TRANSFER_MONEY) || Validator.equals(item.getTransferType(), TransferType.BILLING))
                        && TransferTransactionType.getTransfer().contains(item.getType()))
                .map(Transaction::getTransactionAmount).reduce(0D, Double::sum);
        statisticalTransferDTO.setTotalTransfer(StringUtil.formatMoneyCurrencyForeign(totalTransfer));
        statisticalTransferDTO.setTotalBilling(transactions.stream().filter(item ->
                        (TransferType.getBilling().contains(item.getTransferType()) && !Validator.equals(item.getType(), TransferTransactionType.QR_CODE_INTERNAL_MERCHANT))
                        || item.getType().equals(TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT))
                .map(Transaction::getTransactionAmount).reduce(0D, Double::sum));
        statisticalTransferDTO.setTotalSavingAccount(transactions.stream().filter(item -> TransferType.getSavingAccount().contains(item.getTransferType()))
                .map(Transaction::getTransactionAmount).reduce(0D, Double::sum));
        statisticalTransferDTO.setTotalPremiumAccountNumber(transactions.stream().filter(item -> Validator.equals(item.getTransferType(), TransferType.PREMIUM_ACCOUNT_NUMBER)
                        && Validator.equals(item.getTransactionStatus(), TransactionStatus.SUCCESS)
                        && Validator.equals(item.getStatus(), EntityStatus.ACTIVE.getStatus()))
                .map(Transaction::getTotalAmount).reduce(0D, Double::sum));

        Double total = statisticalTransferDTO.getTotalTransfer() + statisticalTransferDTO.getTotalBilling() + statisticalTransferDTO.getTotalSavingAccount() + statisticalTransferDTO.getTotalPremiumAccountNumber();

        statisticalTransferDTO.setTotal(StringUtil.formatMoneyCurrencyForeign(total));

        statisticalTransferDTO.setCurrency(moneyAccountOfCustomer.getCurrency());

        return statisticalTransferDTO;
    }

    @Override
    public StatisticalTransferDTO search(TransactionSearchRequest request) {

        Customer customer = getCustomerLogin();

        MoneyAccount moneyAccountOfCustomer = this.validateStatisticalTransfer(request, customer);

        request.setCustomerId(customer.getCustomerId());
        request.setTransactionStatus(Collections.singletonList(TransactionStatus.SUCCESS));

        StatisticalTransferDTO statisticalTransferDTO = new StatisticalTransferDTO();

        List<Transaction> transactions = this.statisticalTransferRepository.search(request);

        if (Validator.equals(request.getTransferType(), TransferType.TRANSFER_MONEY)) {
            statisticalTransferDTO.setTransferType(TransferType.TRANSFER_MONEY);
            statisticalTransferDTO.setText(Labels.getLabels(LabelKey.LABEL_TRANSFER));
            List<StatisticalDTO> statistical = new ArrayList<>();
            StatisticalDTO statisticalInter = StatisticalDTO.builder()
                    .text(Labels.getLabels(LabelKey.LABEL_TRANSFER_TRANSACTION_INTER_BANK))
                    .total(transactions.stream().filter(item -> Validator.equals(item.getTransferType(), TransferType.TRANSFER_MONEY) && TransferTransactionType.getInterBank().contains(item.getType()))
                            .map(Transaction::getTransactionAmount).reduce(0D, Double::sum))
                    .type(TransferType.INTER_BANK.name())
                    .currency(moneyAccountOfCustomer.getCurrency())
                    .build();

            Double total = transactions.stream().filter(item -> (Validator.equals(item.getTransferType(), TransferType.TRANSFER_MONEY) || Validator.equals(item.getTransferType(), TransferType.BILLING))
                            && TransferTransactionType.getInternalBank().contains(item.getType()) && Validator.equals(item.getTransactionCurrency(), moneyAccountOfCustomer.getCurrency()))
                    .map(Transaction::getTransactionAmount).reduce(0D, Double::sum);
            StatisticalDTO statisticalInternal = StatisticalDTO.builder()
                    .text(Labels.getLabels(LabelKey.LABEL_TRANSFER_TRANSACTION_INTERNAL_BANK))
                    .total(StringUtil.formatMoneyCurrencyForeign(total))
                    .type(TransferType.INTERNAL_BANK.name())
                    .currency(moneyAccountOfCustomer.getCurrency())
                    .build();

            statistical.add(statisticalInternal);
            statistical.add(statisticalInter);
            statisticalTransferDTO.setStatistical(statistical);
        } else if (Validator.equals(request.getTransferType(), TransferType.BILLING)) {
            statisticalTransferDTO.setTransferType(TransferType.BILLING);
            statisticalTransferDTO.setText(Labels.getLabels(LabelKey.LABEL_BILLING));
            List<StatisticalDTO> statistical = new ArrayList<>();
            StatisticalDTO statisticalBillingInternet = StatisticalDTO.builder()
                    .text(Labels.getLabels(LabelKey.LABEL_BILLING_INTERNET))
                    .total(transactions.stream().filter(item -> Validator.equals(item.getTransferType(), TransferType.TOPUP) || (Validator.equals(item.getTransferType(), TransferType.BILLING)
                                    && BillingType.getBillingInternet().contains(item.getBillingType())
                                    && !Validator.equals(item.getType(), TransferTransactionType.QR_CODE_INTERNAL_MERCHANT)))
                            .map(Transaction::getTransactionAmount).reduce(0D, Double::sum))
                    .type(TransferType.BILLING_INTERNET.name())
                    .currency(moneyAccountOfCustomer.getCurrency())
                    .build();
            StatisticalDTO statisticalBillingWaterElectric = StatisticalDTO.builder()
                    .text(Labels.getLabels(LabelKey.LABEL_BILLING_WATER))
                    .total(transactions.stream().filter(item -> Validator.equals(item.getTransferType(), TransferType.BILLING) && BillingType.getElectricWater().contains(item.getBillingType()))
                            .map(Transaction::getTransactionAmount).reduce(0D, Double::sum))
                    .type(TransferType.BILLING_ELECTRIC_WATER.name())
                    .currency(moneyAccountOfCustomer.getCurrency())
                    .build();
            StatisticalDTO statisticalInsurance = StatisticalDTO.builder()
                    .text(Labels.getLabels(LabelKey.LABEL_INSURANCE))
                    .total(transactions.stream().filter(item -> Validator.equals(item.getTransferType(), TransferType.INSURANCE))
                            .map(Transaction::getTransactionAmount).reduce(0D, Double::sum))
                    .type(TransferType.INSURANCE.name())
                    .currency(moneyAccountOfCustomer.getCurrency())
                    .build();
            StatisticalDTO statisticalInternationalPayment = StatisticalDTO.builder()
                    .text(Labels.getLabels(LabelKey.LABEL_INTERNATIONAL_PAYMENT))
                    .total(transactions.stream().filter(item -> Validator.equals(item.getTransferType(), TransferType.TRANSFER_MONEY) && Validator.equals(item.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT))
                            .map(Transaction::getTransactionAmount).reduce(0D, Double::sum))
                    .type(TransferType.INTERNATIONAL_BANK.name())
                    .currency(moneyAccountOfCustomer.getCurrency())
                    .build();
            statistical.add(statisticalBillingInternet);
            statistical.add(statisticalBillingWaterElectric);
            statistical.add(statisticalInsurance);
            statistical.add(statisticalInternationalPayment);
            statisticalTransferDTO.setStatistical(statistical);
        } else if (Validator.equals(request.getTransferType(), TransferType.SAVING_ACCOUNT)) {
            statisticalTransferDTO.setTransferType(TransferType.SAVING_ACCOUNT);
            statisticalTransferDTO.setText(Labels.getLabels(LabelKey.LABEL_SAVING_ACCOUNT));
            List<StatisticalDTO> statistical = new ArrayList<>();
            StatisticalDTO statisticalBillingWaterElectric = StatisticalDTO.builder()
                    .text(Labels.getLabels(LabelKey.LABEL_ACCUMULATED_TYPE))
                    .total(transactions.stream().filter(item -> TransferType.getSavingAccount().contains(item.getTransferType()) && Validator.equals(SavingAccountType.ACCUMULATED.name(), item.getSavingAccountType()))
                            .map(Transaction::getTransactionAmount).reduce(0D, Double::sum))
                    .type(SavingAccountType.ACCUMULATED.name())
                    .currency(moneyAccountOfCustomer.getCurrency())
                    .build();
            StatisticalDTO statisticalInsurance = StatisticalDTO.builder()
                    .text(Labels.getLabels(LabelKey.LABEL_FIXED_TYPE))
                    .total(transactions.stream().filter(item -> Validator.equals(item.getTransferType(), TransferType.SAVING_ACCOUNT) && Validator.equals(SavingAccountType.FIXED.name(), item.getSavingAccountType()))
                            .map(Transaction::getTransactionAmount).reduce(0D, Double::sum))
                    .type(SavingAccountType.FIXED.name())
                    .currency(moneyAccountOfCustomer.getCurrency())
                    .build();
            statistical.add(statisticalBillingWaterElectric);
            statistical.add(statisticalInsurance);
            statisticalTransferDTO.setStatistical(statistical);
        } else if (Validator.equals(request.getTransferType(), TransferType.PREMIUM_ACCOUNT_NUMBER)) {
            statisticalTransferDTO.setTransferType(TransferType.PREMIUM_ACCOUNT_NUMBER);
            statisticalTransferDTO.setText(Labels.getLabels(LabelKey.LABEL_PREMIUM_ACC_NUMBER));
            List<StatisticalDTO> statistical = new ArrayList<>();
            StatisticalDTO statisticalPremiumAccountNumber = StatisticalDTO.builder()
                    .text(Labels.getLabels(LabelKey.LABEL_PREMIUM_ACC_NUMBER))
                    .total(transactions.stream().filter(item -> Validator.equals(item.getTransferType(), TransferType.PREMIUM_ACCOUNT_NUMBER)
                                    && Validator.equals(item.getTransactionStatus(), TransactionStatus.SUCCESS)
                                    && Validator.equals(item.getStatus(), EntityStatus.ACTIVE.getStatus()))
                            .map(Transaction::getTotalAmount).reduce(0D, Double::sum))
                    .type(TransferType.PREMIUM_ACCOUNT_NUMBER.name())
                    .currency(moneyAccountOfCustomer.getCurrency())
                    .build();
            statistical.add(statisticalPremiumAccountNumber);
            statisticalTransferDTO.setStatistical(statistical);
        }

        return statisticalTransferDTO;
    }

    private MoneyAccount validateStatisticalTransfer(TransactionSearchRequest request, Customer customer) {
        LocalDate date = LocalDate.parse(this.statisticalProperties.getDate(), DateTimeFormatter.ofPattern(DateUtil.SHORT_DATE_PATTERN_DASH));
        LocalDateTime localDateTime = LocalDateTime.now(ZoneOffset.UTC).plusHours(7);
        if (request.getToDate().isBefore(request.getFromDate())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_FROM_DATE_MUST_NOT_BE_GREATER_THAN_TO_DATE,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_CUSTOMER)}),
                    Transaction_.class.getSimpleName(),
                    LabelKey.ERROR_FROM_DATE_MUST_NOT_BE_GREATER_THAN_TO_DATE);

        } else if (Period.between(request.getToDate().minus(this.statisticalProperties.getMaxPeriod().minusDays(1)), request.getFromDate()).isNegative()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_TO_DATE_MUST_NOT_EXCEED_30_DAY_COMPARED_FROM_DATE),
                    Transaction_.class.getSimpleName(),
                    LabelKey.ERROR_TO_DATE_MUST_NOT_EXCEED_30_DAY_COMPARED_FROM_DATE);
        } else if (request.getFromDate().isBefore(date) || request.getToDate().isBefore(date)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.LABEL_DATE_SEARCH_INVALID,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_CUSTOMER)}),
                    Transaction_.class.getSimpleName(),
                    LabelKey.LABEL_DATE_SEARCH_INVALID);
        } else if (request.getFromDate().isAfter(localDateTime.toLocalDate()) || request.getToDate().isAfter(localDateTime.toLocalDate())) {
            throw new BadRequestAlertException(ErrorCode.MSG1060);
        }

        MoneyAccount moneyAccountOfCustomer = this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(
                request.getCustomerAccNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG1038));

        if (!Validator.equals(moneyAccountOfCustomer.getCustomerId(), customer.getCustomerId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1151);
        }

        return moneyAccountOfCustomer;
    }

    private Customer getCustomerLogin() {
        return GwSecurityUtils.getCustomerLogin()
                .orElseThrow(() -> new UnauthorizedException(ErrorCode.MSG1077));
    }
}
