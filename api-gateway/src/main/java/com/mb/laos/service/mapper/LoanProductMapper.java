package com.mb.laos.service.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.LoanProduct;
import com.mb.laos.model.dto.LoanProductDTO;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.Validator;

@Mapper(componentModel = "spring")
public interface LoanProductMapper extends EntityMapper<LoanProductDTO, LoanProduct> {

    @Mapping(source = "name", target = "name", qualifiedByName = "translateName")
    LoanProductDTO toDto(LoanProduct loanProduct);

    @Named("translateName")
    default String translateName(String name) {
        return Validator.isNotNull(name) ? Labels.getLabels(name) : StringPool.BLANK;
    }
}
