package com.mb.laos.service.impl;

import com.mb.laos.enums.EntityStatus;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.InformationTemplate;
import com.mb.laos.model.dto.InformationTemplateContentDTO;
import com.mb.laos.model.dto.InformationTemplateDTO;
import com.mb.laos.repository.InformationTemplateContentRepository;
import com.mb.laos.repository.InformationTemplateRepository;
import com.mb.laos.service.InformationTemplateService;
import com.mb.laos.service.mapper.InformationTemplateContentMapper;
import com.mb.laos.service.mapper.InformationTemplateMapper;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service // Annotation đánh dấu đây là Spring Service component
@RequiredArgsConstructor // Annotation tự động tạo constructor với các final fields
public class InformationTemplateServiceImpl implements InformationTemplateService { // Class triển khai interface InformationTemplateService để xử lý các thao tác với Information Template

    private final InformationTemplateRepository informationTemplateRepository; // Dependency injection cho repository thao tác với bảng InformationTemplate

    private final InformationTemplateContentRepository informationTemplateContentRepository; // Dependency injection cho repository thao tác với bảng InformationTemplateContent

    private final InformationTemplateMapper supportInformationMapper; // Dependency injection cho mapper chuyển đổi InformationTemplate entity/DTO

    private final InformationTemplateContentMapper informationTemplateContentMapper; // Dependency injection cho mapper chuyển đổi InformationTemplateContent entity/DTO

    @Override // Override method từ interface InformationTemplateService
    public InformationTemplateDTO findByCode(String informationTemplateCode) { // Method tìm information template theo code

        InformationTemplate informationTemplate = this.informationTemplateRepository.findAllByInformationTemplateCodeAndStatus( // Tìm information template theo code và status
                informationTemplateCode, EntityStatus.ACTIVE.getStatus()).stream().findFirst().orElse(null); // Lấy template đầu tiên với status ACTIVE, trả về null nếu không tìm thấy

        InformationTemplateDTO informationTemplateDTO = null; // Khai báo biến DTO kết quả
        if (Validator.isNotNull(informationTemplate)) { // Nếu tìm thấy information template
            informationTemplateDTO = this.supportInformationMapper.toDto(informationTemplate); // Chuyển đổi entity thành DTO
        }

        String language = Labels.getLanguageFromRequest(); // Lấy ngôn ngữ từ request hiện tại

        if (Validator.isNotNull(informationTemplateDTO)) { // Nếu có information template DTO
            InformationTemplateContentDTO informationTemplateContent = this.informationTemplateContentMapper // Lấy nội dung template theo ngôn ngữ
                    .toDto(this.informationTemplateContentRepository // Chuyển đổi entity thành DTO
                            .findByInformationTemplateIdAndLanguage(informationTemplateDTO.getInformationTemplateId(), language)); // Tìm content theo template ID và ngôn ngữ

            if (Validator.isNotNull(informationTemplateContent)) { // Nếu tìm thấy content theo ngôn ngữ
                informationTemplateContent.setContentNoHtml(informationTemplateContent.getContentNoHtml()); // Set content không có HTML (giữ nguyên giá trị)

                informationTemplateDTO.setInformationTemplateContentDTO(informationTemplateContent); // Set content DTO vào template DTO

                informationTemplateDTO.setTemplate(informationTemplateContent.getContent()); // Set template content
            } else { // Nếu không tìm thấy content theo ngôn ngữ
                informationTemplateDTO.setTemplate(null); // Set template là null
                informationTemplateDTO.setInformationTemplateName(null); // Set template name là null
                informationTemplateDTO.setDisplayName(null); // Set display name là null
            }
        }

        return informationTemplateDTO; // Trả về information template DTO
    }

}
