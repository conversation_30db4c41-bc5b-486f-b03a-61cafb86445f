package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.TemplateProperties;
import com.mb.laos.enums.CategoryType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.Resource;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Category;
import com.mb.laos.model.FileEntry;
import com.mb.laos.model.FileExtra;
import com.mb.laos.model.NewsContent;
import com.mb.laos.model.dto.NewsContentDTO;
import com.mb.laos.model.dto.NewsDTO;
import com.mb.laos.model.search.FileEntrySearch;
import com.mb.laos.model.search.NewsSearch;
import com.mb.laos.repository.CategoryRepository;
import com.mb.laos.repository.NewsContentRepository;
import com.mb.laos.repository.NewsRepository;
import com.mb.laos.security.configuration.AuthenticationProperties;
import com.mb.laos.service.NewsGatewayService;
import com.mb.laos.service.StorageService;
import com.mb.laos.service.mapper.NewsContentMapper;
import com.mb.laos.service.mapper.NewsMapper;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.CacheControl;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class NewsGatewayServiceImpl implements NewsGatewayService {

    private final NewsRepository newsRepository;

    private final NewsMapper newsMapper;

    private final NewsContentMapper newsContentMapper;

    private final NewsContentRepository newsContentRepository;

    private final CategoryRepository categoryRepository;

    private final StorageService storageService;

    private final AuthenticationProperties properties;

    private final TemplateProperties templateProperties;

    @Override
    public Page<NewsDTO> search(NewsSearch newsSearch, Pageable pageable) {
        newsSearch.setStatus(EntityStatus.ACTIVE.getStatus());
        if (Validator.isNotNull(newsSearch.getCategoryType())) {
            newsSearch.setCategoryId(Objects.requireNonNull(CategoryType.valueOfCategory(newsSearch.getCategoryType().name())).getIdCategoryType());
        }

        List<NewsDTO> news = this.newsRepository.search(newsSearch, pageable);

        news.forEach(item -> {
            item.setNewsContentDTO(this.newsContentMapper.toDto(item.getNewsContent()));
            item.setNewsContent(null);
            if (Validator.equals(item.getCategoryId(), CategoryType.NEWS_INTERNAL.getIdCategoryType())) {
                item.setType(CategoryType.NEWS_INTERNAL);
            } else {
                item.setType(CategoryType.NEWS_INTER);
            }
            item.getCategory().setName(Labels.getLabels(item.getCategory().getName()));
            item.getNewsContentDTO().setContentNoHtml(item.getNewsContentDTO().getContentNoHtml());
        });

        List<NewsContentDTO> newsContents = news.stream().map(NewsDTO::getNewsContentDTO).collect(Collectors.toList());

        List<Long> newsContentIds = newsContents.stream().map(NewsContentDTO::getNewsContentId).collect(Collectors.toList());

        if (Validator.isNotNull(newsContentIds)) {

            List<FileEntry> fileEntries =
                    storageService.getFileEntries(NewsContent.class.getName(), newsContentIds, Resource.ICON);

            news.forEach(newsDTO -> {
                Optional<FileEntry> fileEntriesOfNews = fileEntries.stream()
                        .filter(fileEntry -> Validator.equals(fileEntry.getClassPk(), newsDTO.getNewsContentDTO().getNewsContentId()))
                        .findFirst();

                fileEntriesOfNews.ifPresent(fileEntry -> newsDTO.getNewsContentDTO().setIcon(storageService.getFileInfo(fileEntry)));
            });

            return new PageImpl<>(news, pageable, newsRepository.count(newsSearch));
        }

        return new PageImpl<>(new ArrayList<>(), pageable, 0);
    }

    @Override
    public NewsDTO detail(Long newsId) {
        if (Validator.isNull(newsId)) {
            throw new BadRequestAlertException(ErrorCode.MSG1167);
        }

        String language = Labels.getLanguageFromRequest();

        NewsDTO newsDTO = this.newsMapper.toDto(this.newsRepository.findById(newsId).orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101183)));

        List<NewsContentDTO> newsContent = this.newsContentMapper.toDto(this.newsContentRepository.findAllByNewsId(newsId));

        Category category = this.categoryRepository.findById(newsDTO.getCategoryId()).orElse(null);

        if (Validator.isNotNull(category)) {
            category.setName(Labels.getLabels(category.getName()));
        }

        newsDTO.setCategory(category);

        if (Validator.equals(newsDTO.getCategoryId(), CategoryType.NEWS_INTERNAL.getIdCategoryType())) {
            newsDTO.setType(CategoryType.NEWS_INTERNAL);
        } else {
            newsDTO.setType(CategoryType.NEWS_INTER);
        }

        NewsContentDTO newsContentDTO = newsContent.stream().filter(item -> Validator.equals(item.getLanguage(), language)).findFirst().orElse(null);

        if (Validator.isNotNull(newsContentDTO)) {
            List<FileEntry> fileEntries = storageService.getFileEntries(NewsContent.class.getName(), newsContentDTO.getNewsContentId(), Resource.ICON);

            if (Validator.isNotNull(fileEntries)) {
                fileEntries.stream().findFirst().ifPresent(fileContent -> newsContentDTO.setIcon(storageService.getFileInfo(fileContent)));
            }

            newsDTO.setNewsContentDTO(newsContentDTO);

            newsDTO.getNewsContentDTO().setContentNoHtml(newsDTO.getNewsContentDTO().getContentNoHtml());

            try (InputStream inputStream = this.storageService
                    .getStyleFromResource(this.templateProperties.getStyle().getStyleFile())) {
                String result = new BufferedReader(new InputStreamReader(inputStream))
                        .lines().collect(Collectors.joining(StringUtil.NEW_LINE));
                newsDTO.getNewsContentDTO().setCss(result);
            } catch (IOException e) {
                _log.error("Error occurred when export when read file ckeditor", e);
            }
        }

        return newsDTO;
    }

    @Override
    public ResponseEntity<InputStreamResource> getIcon(FileEntrySearch search) {
        try {
            FileExtra file = this.storageService.getFileExtra(search.getFileEntryId(), search.getNormalizeName(),
                    NewsContent.class.getName(), search.getClassPk(), Resource.ICON);

            return ResponseEntity.ok().contentType(MediaType.parseMediaType(file.getContentType()))
                    .cacheControl(CacheControl.maxAge(this.properties.getCacheMaxAge(), TimeUnit.SECONDS))
                    .body(new InputStreamResource(file.getInputStream()));
        } catch (Exception e) {
            throw new BadRequestAlertException(ErrorCode.MSG101183);
        }
    }

}
