// Package chứa các service implementation của hệ thống MB Laos
package com.mb.laos.service.impl;

// Import các exception classes để xử lý lỗi
import com.mb.laos.api.exception.BadRequestAlertException; // Exception cho các lỗi business logic
import com.mb.laos.api.util.ErrorCode; // Enum chứa các mã lỗi hệ thống

// Import các configuration properties
import com.mb.laos.configuration.CustomerProperties; // Properties cấu hình khách hàng
import com.mb.laos.configuration.MBApiConstantProperties; // Properties constants của MB API

// Import các enum classes để type-safe constants
import com.mb.laos.enums.BillingType; // Enum loại hóa đơn
import com.mb.laos.enums.CommonCategory; // Enum danh mục dữ liệu chung
import com.mb.laos.enums.ConfigurationFeeType; // Enum loại cấu hình phí
import com.mb.laos.enums.EndUserType; // Enum loại người dùng cuối
import com.mb.laos.enums.EntityStatus; // Enum trạng thái entity
import com.mb.laos.enums.NotificationHistoryEnum; // Enum lịch sử thông báo
import com.mb.laos.enums.NotificationLimitStatus; // Enum trạng thái giới hạn thông báo
import com.mb.laos.enums.NotificationLimitType; // Enum loại giới hạn thông báo
import com.mb.laos.enums.NotificationStatus; // Enum trạng thái thông báo
import com.mb.laos.enums.NotificationType; // Enum loại thông báo
import com.mb.laos.enums.TargetType; // Enum loại đích thông báo
import com.mb.laos.enums.TemplateCode; // Enum mã template
import com.mb.laos.enums.TemplateField; // Enum trường template
import com.mb.laos.enums.TransactionFeeTypeCode; // Enum mã loại phí giao dịch
import com.mb.laos.enums.TransactionStatus; // Enum trạng thái giao dịch
import com.mb.laos.enums.TransactionType; // Enum loại giao dịch (PLUS/MINUS)
import com.mb.laos.enums.TransferTransactionType; // Enum loại giao dịch chuyển tiền
import com.mb.laos.enums.TransferType; // Enum loại chuyển tiền
// Import gateway response classes
import com.mb.laos.gateway.response.FeeTransactionResponse; // Response chứa thông tin phí giao dịch

// Import message classes cho đa ngôn ngữ
import com.mb.laos.messages.LabelKey; // Key cho label đa ngôn ngữ
import com.mb.laos.messages.Labels; // Utility để lấy localized messages

// Import các model entities
import com.mb.laos.model.Common; // Entity dữ liệu chung master data
import com.mb.laos.model.ContentTemplate; // Entity template nội dung thông báo
import com.mb.laos.model.Currency; // Entity tiền tệ
import com.mb.laos.model.Customer; // Entity khách hàng
import com.mb.laos.model.Fee; // Entity cấu hình phí
import com.mb.laos.model.FeeRate; // Entity biểu phí theo mức
import com.mb.laos.model.InternationalPayment; // Entity thanh toán quốc tế
import com.mb.laos.model.Merchant; // Entity merchant
import com.mb.laos.model.MoneyAccount; // Entity tài khoản tiền
import com.mb.laos.model.NotiTransaction; // Entity thông báo giao dịch
import com.mb.laos.model.Notification; // Entity thông báo
import com.mb.laos.model.NotificationLimit; // Entity giới hạn thông báo
import com.mb.laos.model.Transaction; // Entity giao dịch
import com.mb.laos.model.TransactionFeeType; // Entity loại phí giao dịch
import com.mb.laos.model.TransactionLimit; // Entity hạn mức giao dịch
import com.mb.laos.model.TransactionLimitDetail; // Entity chi tiết hạn mức giao dịch
// Import các DTO classes cho data transfer
import com.mb.laos.model.dto.NotificationDTO; // DTO thông báo
import com.mb.laos.model.dto.TransactionDTO; // DTO giao dịch

// Import các repository interfaces cho database access
import com.mb.laos.repository.CommonRepository; // Repository dữ liệu chung
import com.mb.laos.repository.ContentTemplateRepository; // Repository template nội dung
import com.mb.laos.repository.CustomerRepository; // Repository khách hàng
import com.mb.laos.repository.FeeRateRepository; // Repository biểu phí
import com.mb.laos.repository.FeeRepository; // Repository cấu hình phí
import com.mb.laos.repository.InternationalPaymentRepository; // Repository thanh toán quốc tế
import com.mb.laos.repository.MerchantRepository; // Repository merchant
import com.mb.laos.repository.MoneyAccountRepository; // Repository tài khoản tiền
import com.mb.laos.repository.NotiTransactionRepository; // Repository thông báo giao dịch
import com.mb.laos.repository.NotificationLimitRepository; // Repository giới hạn thông báo
import com.mb.laos.repository.NotificationRepository; // Repository thông báo
import com.mb.laos.repository.TransactionFeeTypeRepository; // Repository loại phí giao dịch
import com.mb.laos.repository.TransactionLimitDetailRepository; // Repository chi tiết hạn mức
import com.mb.laos.repository.TransactionLimitRepository; // Repository hạn mức giao dịch
import com.mb.laos.repository.TransactionRepository; // Repository giao dịch
// Import request classes
import com.mb.laos.request.TransLimitRecordRequest; // Request cho truy vấn hạn mức giao dịch

// Import service interfaces
import com.mb.laos.service.SendService; // Service gửi thông báo
import com.mb.laos.service.TransactionService; // Interface mà class này implement

// Import mapper classes cho chuyển đổi Entity/DTO
import com.mb.laos.service.mapper.NotificationLimitTransactionMapper; // Mapper giới hạn thông báo giao dịch
import com.mb.laos.service.mapper.NotificationMapper; // Mapper thông báo
import com.mb.laos.service.mapper.TransactionMapper; // Mapper giao dịch

// Import utility classes
import com.mb.laos.util.CalendarUtil; // Utility xử lý calendar và thời gian
import com.mb.laos.util.StringPool; // Pool các string constants
import com.mb.laos.util.StringUtil; // Utility xử lý string
import com.mb.laos.util.UUIDUtil; // Utility tạo UUID
import com.mb.laos.util.Validator; // Utility validation

// Import Lombok annotations
import lombok.RequiredArgsConstructor; // Annotation tự động tạo constructor
import lombok.extern.slf4j.Slf4j; // Annotation tự động tạo logger

// Import Spring framework annotations
import org.springframework.stereotype.Service; // Annotation đánh dấu service component

// Import Java standard libraries
import java.time.Instant; // Java 8 time API cho thời điểm tức thì
import java.time.LocalDate; // Java 8 time API cho ngày địa phương
import java.time.LocalDateTime; // Java 8 time API cho ngày giờ địa phương
import java.time.ZoneId; // Java 8 time API cho múi giờ
import java.util.Collections; // Utility cho collections
import java.util.HashMap; // Map implementation
import java.util.List; // Interface danh sách
import java.util.Map; // Interface map
import java.util.Optional; // Java 8 optional cho null safety
import java.util.stream.Collectors; // Java 8 stream collectors

/**
 * TransactionServiceImpl - Service implementation cho transaction management
 *
 * Class này implement TransactionService interface và cung cấp các chức năng:
 * 1. Transaction limit checking - Kiểm tra hạn mức giao dịch
 * 2. Account balance validation - Xác thực số dư tài khoản
 * 3. Fee calculation - Tính toán phí giao dịch
 * 4. Notification creation - Tạo thông báo cho khách hàng
 * 5. Transaction history tracking - Theo dõi lịch sử giao dịch
 * 6. International payment support - Hỗ trợ thanh toán quốc tế
 *
 * Các loại hạn mức được kiểm tra:
 * - Per transaction limit: Hạn mức mỗi giao dịch
 * - Daily limit: Hạn mức theo ngày
 * - Monthly limit: Hạn mức theo tháng
 * - Yearly limit: Hạn mức theo năm
 *
 * <AUTHOR> Laos Development Team
 * @version 1.0
 * @since 2023
 */
@Service                    // Spring annotation: đánh dấu đây là service component
@Slf4j                      // Lombok annotation: tự động tạo logger _log
@RequiredArgsConstructor    // Lombok annotation: tạo constructor với tất cả final fields
public class TransactionServiceImpl implements TransactionService {

    /** Repository để truy cập customer data */
    private final CustomerRepository customerRepository;

    /** Repository để truy cập money account data */
    private final MoneyAccountRepository moneyAccountRepository;

    /** Repository để truy cập transaction data */
    private final TransactionRepository transactionRepository;

    /** Repository để truy cập content template data cho thông báo */
    private final ContentTemplateRepository contentTemplateRepository;

    /** Repository để truy cập notification data */
    private final NotificationRepository notificationRepository;

    /** Repository để truy cập notification transaction data */
    private final NotiTransactionRepository notiTransactionRepository;

    /** Mapper để convert giữa Notification entity và DTO */
    private final NotificationMapper notificationMapper;

    /** Repository để truy cập fee configuration data */
    private final FeeRepository feeRepository;

    /** Repository để truy cập transaction fee type data */
    private final TransactionFeeTypeRepository transactionFeeTypeRepository;

    /** Repository để truy cập fee rate data */
    private final FeeRateRepository feeRateRepository;

    /** Repository để truy cập transaction limit configuration */
    private final TransactionLimitRepository transactionLimitRepository;

    /** Repository để truy cập transaction limit detail configuration */
    private final TransactionLimitDetailRepository transactionLimitDetailRepository;

    /** Mapper để convert giữa Transaction entity và DTO */
    private final TransactionMapper transactionMapper;

    /** Mapper để convert giữa NotificationLimit và Transaction */
    private final NotificationLimitTransactionMapper notificationLimitTransactionMapper;

    /** Repository để truy cập notification limit data */
    private final NotificationLimitRepository notificationLimitRepository;

    /** Properties cấu hình khách hàng (currency, limits...) */
    private final CustomerProperties customerProperties;

    /** Properties constants của MB API */
    private final MBApiConstantProperties mbApiConstantProperties;

    /** Repository để truy cập merchant data */
    private final MerchantRepository merchantRepository;

    /** Service để gửi thông báo (SMS, email, push notification) */
    private final SendService sendService;

    /** Repository để truy cập common master data */
    private final CommonRepository commonRepository;

    /** Repository để truy cập international payment data */
    private final InternationalPaymentRepository internationalPaymentRepository;

    /**
     * Tạo transaction ID duy nhất cho giao dịch
     *
     * Method này tạo UUID làm transaction ID để đảm bảo tính duy nhất:
     * - Sử dụng UUIDUtil để generate UUID với độ dài specified
     * - Transaction ID được sử dụng để track giao dịch trong toàn hệ thống
     * - Đảm bảo không trùng lặp giữa các giao dịch
     *
     * @param count Số lượng ký tự của UUID (thường là 32)
     * @param customerId ID khách hàng (hiện tại chưa sử dụng)
     * @param isClearTransactionId Flag để clear transaction ID (hiện tại chưa sử dụng)
     * @return String Transaction ID duy nhất
     */
    @Override
    public String generateTransactionId(int count, Long customerId, boolean isClearTransactionId) {
        // Tạo UUID với số lượng ký tự specified
        // TODO: Có thể enhance để sử dụng customerId và isClearTransactionId trong tương lai
        return UUIDUtil.generateUUID(count);
    }

    /**
     * Kiểm tra hạn mức giao dịch của khách hàng
     *
     * @param transferType Loại chuyển tiền
     * @param customerId ID khách hàng
     * @param customerAccountNumber Số tài khoản khách hàng
     * @param amountTransaction Số tiền giao dịch
     * @param type Loại giao dịch chuyển tiền
     * @param transaction Thông tin giao dịch
     */
    @Override
    public void checkTransactionLimit(String transferType, Long customerId, String customerAccountNumber,
                                      Double amountTransaction, TransferTransactionType type, Transaction transaction) {
        // Khai báo các biến để lưu trữ thông tin hạn mức và thời gian
        boolean checkTransactionLimit; // Cờ kiểm tra có cấu hình hạn mức hay không
        Double limitPerTransaction;    // Hạn mức mỗi giao dịch
        Double limitPerDay;           // Hạn mức theo ngày
        Double limitPerMonth;         // Hạn mức theo tháng
        Double limitPerYear;          // Hạn mức theo năm
        Instant startTimeOfMonth;     // Thời gian bắt đầu tháng
        Instant endTimeOfMonth;       // Thời gian kết thúc tháng
        Instant startTimeOfDay;       // Thời gian bắt đầu ngày
        Instant endTimeOfDay;         // Thời gian kết thúc ngày
        Instant now = Instant.now();  // Thời gian hiện tại
        TransactionLimitDetail customerSector = null; // Chi tiết hạn mức theo sector khách hàng
        TransactionLimit transactionLimit = null;     // Cấu hình hạn mức giao dịch
        String benefitCurrency = null;                // Đồng tiền của người thụ hưởng
        Optional<InternationalPayment> interPayment = Optional.empty(); // Thông tin thanh toán quốc tế

        // Kiểm tra xem có phải giao dịch thanh toán quốc tế không
        boolean isInterPayment = Validator.equals(transaction.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT);

        // Nếu là giao dịch quốc tế, lấy thông tin thanh toán quốc tế và đồng tiền người thụ hưởng
        if (isInterPayment) {
            interPayment = this.internationalPaymentRepository
                    .findByTransferTransactionId(transaction.getTransferTransactionId());
            benefitCurrency = interPayment.map(InternationalPayment::getBeneficiaryCurrency).orElse(null);
        }

        // Lấy thông tin khách hàng, ném lỗi nếu không tìm thấy
        Customer customer = this.customerRepository.findById(customerId).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG1035));

        // Xác thực số dư tài khoản và các điều kiện liên quan
        this.validateAccBalance(customerId, customerAccountNumber, amountTransaction, transaction);

        // Chuyển đổi transaction thành DTO và tạo notification limit
        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);
        NotificationLimit notificationLimit = this.notificationLimitTransactionMapper.toEntity(transactionDTO);

        // Log thông tin khách hàng và sector
        _log.info("Customer {} has sector {} ", customer.getPhoneNumber(), customer.getCustomerSectorId());

        // Lấy danh sách các đồng tiền được hỗ trợ từ cấu hình
        List<String> currencies = this.commonRepository.findAllByCategory(CommonCategory.NATION_ID.name())
                .stream().map(Common::getCode).collect(Collectors.toList());

        // Xác định đồng tiền sử dụng cho việc kiểm tra hạn mức
        // Ưu tiên đồng tiền người thụ hưởng nếu có và nằm trong danh sách hỗ trợ
        String currency = Validator.isNotNull(benefitCurrency)
                && currencies.contains(benefitCurrency) ? interPayment.get().getBeneficiaryCurrency() : transactionDTO.getTransactionCurrency();

        // Tìm kiếm cấu hình hạn mức theo sector của khách hàng, đồng tiền và trạng thái active
        List<TransactionLimit> list = this.transactionLimitRepository
                .findAllBySectorIdAndCurrencyAndStatus(customer.getCustomerSectorId(),
                        currency, EntityStatus.ACTIVE.getStatus());

        // Nếu có cấu hình hạn mức cho sector này
        if (!list.isEmpty()) {
            // Kiểm tra thời gian hiện tại có nằm trong khoảng thời gian hiệu lực của cấu hình hạn mức không
            checkTransactionLimit = list.stream()
                    .anyMatch(item -> item.getStartDate().isBefore(now) && item.getEndDate().isAfter(now));

            // Nếu có cấu hình hạn mức đang hiệu lực
            if (Validator.equals(checkTransactionLimit, true)) {
                // Lấy cấu hình hạn mức đầu tiên đang trong thời gian hiệu lực
                transactionLimit = list.stream()
                        .filter(item -> item.getStartDate().isBefore(now) && item.getEndDate().isAfter(now))
                        .findFirst().orElse(null);

                // Chuyển đổi loại chuyển tiền để phù hợp với cấu hình
                transferType = this.getTransferType(transferType, type);

                // Lấy chi tiết hạn mức theo loại chuyển tiền cụ thể
                customerSector = Validator.isNotNull(transactionLimit) ? this.transactionLimitDetailRepository
                        .findAllByTransactionLimitIdAndTransferTypeAndStatus(transactionLimit.getTransactionLimitId(),
                                transferType, EntityStatus.ACTIVE.getStatus()).stream().findFirst().orElse(null) : null;
            }
        }

        // Kiểm tra hạn mức giao dịch theo từng loại sector trong trường hợp có cấu hình hạn mức
        if (Validator.isNotNull(customerSector) && Validator.isNotNull(transactionLimit)) {
            // Lấy các giá trị hạn mức từ cấu hình
            limitPerTransaction = customerSector.getMaxTransferAmount();    // Hạn mức mỗi giao dịch
            limitPerDay = transactionLimit.getMaxTransferAmtOfDay();        // Hạn mức theo ngày
            limitPerMonth = transactionLimit.getMaxTransferAmtOfMonth();    // Hạn mức theo tháng
            limitPerYear = transactionLimit.getMaxTransferAmtOfYear();      // Hạn mức theo năm

            // Định nghĩa điều kiện lọc giao dịch: chỉ lấy giao dịch thành công và loại trừ tiền (MINUS)
            List<TransactionStatus> transactionStatus = Collections.singletonList(TransactionStatus.SUCCESS);
            List<TransactionType> transactionType = Collections.singletonList(TransactionType.MINUS);

            // Lấy ngày hiện tại theo múi giờ được cấu hình
            LocalDate localDate = LocalDate.now(ZoneId.of(StringPool.CURRENT_TIME_ZONE));

            // Tính toán khoảng thời gian cho ngày hiện tại (từ 00:00:00 đến 23:59:59)
            startTimeOfDay = CalendarUtil.getGTInstant(localDate);
            endTimeOfDay = CalendarUtil.getLTInstant(localDate);

            // Tính toán khoảng thời gian cho tháng hiện tại (từ ngày đầu tháng đến cuối tháng)
            startTimeOfMonth = CalendarUtil.getGTInstantOfMonth(localDate);
            endTimeOfMonth = CalendarUtil.getLTInstantOfMonth(localDate);

            // Tính toán khoảng thời gian cho năm hiện tại (từ ngày đầu năm đến cuối năm)
            Instant startTimeOfYear = CalendarUtil.getGTInstantOfYear(localDate);
            Instant endTimeOfYear = CalendarUtil.getLTInstantOfYear(localDate);

            // Tạo request để truy vấn các giao dịch trong ngày
            TransLimitRecordRequest recordRequest = TransLimitRecordRequest.builder()
                    .customerId(customerId)                                    // ID khách hàng
                    .transactionStatus(transactionStatus)                     // Trạng thái giao dịch (SUCCESS)
                    .transactionTypes(transactionType)                        // Loại giao dịch (MINUS)
                    .transferTypes(TransferType.getTransferTypeLimit())        // Các loại chuyển tiền cần kiểm tra hạn mức
                    .currency(transactionDTO.getTransactionCurrency())        // Đồng tiền giao dịch
                    .startTime(startTimeOfDay)                                 // Thời gian bắt đầu ngày
                    .endTime(endTimeOfDay)                                     // Thời gian kết thúc ngày
                    .bankCode(interPayment.map(InternationalPayment::getPaymentSystem).orElse(null)) // Mã ngân hàng (cho giao dịch quốc tế)
                    .build();

            // Lấy danh sách giao dịch trong ngày hiện tại
            List<Transaction> transferTransInDay = this.transactionRepository.getListTransactionBetweenTimeAndCurrency(recordRequest);

            // Cập nhật thời gian để lấy giao dịch trong tháng
            recordRequest.setStartTime(startTimeOfMonth);
            recordRequest.setEndTime(endTimeOfMonth);
            List<Transaction> transactionInMonth = this.transactionRepository.getListTransactionBetweenTimeAndCurrency(recordRequest);

            // Cập nhật thời gian để lấy giao dịch trong năm
            recordRequest.setStartTime(startTimeOfYear);
            recordRequest.setEndTime(endTimeOfYear);
            List<Transaction> transactionInYear = this.transactionRepository.getListTransactionBetweenTimeAndCurrency(recordRequest);

            // Khai báo biến để lưu tổng số tiền giao dịch
            Double amountInDay;    // Tổng số tiền giao dịch trong ngày
            Double amountInMonth;  // Tổng số tiền giao dịch trong tháng
            Double amountInYear;   // Tổng số tiền giao dịch trong năm

            // Tính tổng số tiền giao dịch trong ngày (bao gồm cả giao dịch hiện tại)
            // Với giao dịch quốc tế sử dụng ForeignAmount, giao dịch thường sử dụng TransactionAmount
            amountInDay = transferTransInDay.stream()
                    .map(isInterPayment ? Transaction::getForeignAmount : Transaction::getTransactionAmount)
                    .reduce(0D, Double::sum);

            // Cộng thêm số tiền giao dịch hiện tại
            amountInDay += amountTransaction;

            // Tính tổng số tiền giao dịch trong tháng (bao gồm cả giao dịch hiện tại)
            amountInMonth = transactionInMonth.stream()
                    .map(isInterPayment ? Transaction::getForeignAmount : Transaction::getTransactionAmount)
                    .reduce(0D, Double::sum);

            // Cộng thêm số tiền giao dịch hiện tại
            amountInMonth += amountTransaction;

            // Tính tổng số tiền giao dịch trong năm (bao gồm cả giao dịch hiện tại)
            amountInYear = transactionInYear.stream()
                    .map(isInterPayment ? Transaction::getForeignAmount : Transaction::getTransactionAmount)
                    .reduce(0D, Double::sum);

            // Cộng thêm số tiền giao dịch hiện tại
            amountInYear += amountTransaction;

            // Thiết lập thông tin notification limit để theo dõi và thông báo
            notificationLimit.setActualTransactionAmount(transactionDTO.getActualTransactionAmount());
            notificationLimit.setLimitPerTransaction(limitPerTransaction);
            notificationLimit.setLimitPerDay(limitPerDay);
            notificationLimit.setLimitPerMonth(limitPerMonth);
            notificationLimit.setLimitPerYear(limitPerYear);
            notificationLimit.setStatus(NotificationLimitStatus.NO_CONTACT_YET.getStatus());

            // Kiểm tra hạn mức mỗi giao dịch: nếu có cấu hình và số tiền vượt quá thì ném lỗi
            if (Validator.isNotNull(limitPerTransaction) && amountTransaction.compareTo(limitPerTransaction) > 0) {
                throw new BadRequestAlertException(ErrorCode.MSG1106);
            }

            // Kiểm tra hạn mức theo ngày: nếu có cấu hình và tổng số tiền trong ngày vượt quá thì ném lỗi
            if (Validator.isNotNull(limitPerDay) && amountInDay.compareTo(limitPerDay) > 0) {
                notificationLimit.setTypeLimit(NotificationLimitType.LIMIT_PER_DAY);
                notificationLimit.setNotificationType(NotificationHistoryEnum.NOTIFICATION_LIMIT);
                // Lưu thông báo về việc vượt hạn mức ngày
                this.notificationLimitRepository.saveAnyway(notificationLimit);
                throw new BadRequestAlertException(ErrorCode.MSG1107);
            }

            // Kiểm tra hạn mức theo tháng: nếu có cấu hình và tổng số tiền trong tháng vượt quá thì ném lỗi
            if (Validator.isNotNull(limitPerMonth) && amountInMonth.compareTo(limitPerMonth) > 0) {
                notificationLimit.setTypeLimit(NotificationLimitType.LIMIT_PER_MONTH);
                notificationLimit.setNotificationType(NotificationHistoryEnum.NOTIFICATION_LIMIT);
                // Lưu thông báo về việc vượt hạn mức tháng
                this.notificationLimitRepository.saveAnyway(notificationLimit);
                throw new BadRequestAlertException(ErrorCode.MSG1155);
            }

            // Kiểm tra hạn mức theo năm: nếu có cấu hình và tổng số tiền trong năm vượt quá thì ném lỗi
            if (Validator.isNotNull(limitPerYear) && amountInYear.compareTo(limitPerYear) > 0) {
                throw new BadRequestAlertException(ErrorCode.MSG101016);
            }
        }
    }

    /**
     * Chuyển đổi loại chuyển tiền dựa trên loại giao dịch cụ thể
     * Phương thức này ánh xạ các loại giao dịch chi tiết thành các nhóm chuyển tiền chính
     *
     * @param transferType Loại chuyển tiền ban đầu
     * @param type Loại giao dịch chuyển tiền cụ thể
     * @return Loại chuyển tiền đã được chuyển đổi
     */
    private String getTransferType(String transferType, TransferTransactionType type) {
        // Chỉ xử lý chuyển đổi nếu loại chuyển tiền ban đầu là TRANSFER_MONEY
        if (Validator.equals(transferType, TransferType.TRANSFER_MONEY.name())) {

            // Nhóm các giao dịch nội bộ ngân hàng (trong cùng một ngân hàng)
            if (Validator.equals(type, TransferTransactionType.QR_CODE_INTERNAL)           // QR code nội bộ
                    || Validator.equals(type, TransferTransactionType.QR_CODE_INTERNAL_MERCHANT) // QR code merchant nội bộ
                    || Validator.equals(type, TransferTransactionType.INTERNAL_BANK)) {          // Chuyển tiền nội bộ

                transferType = TransferType.INTERNAL_BANK.name();

            }
            // Nhóm các giao dịch liên ngân hàng (giữa các ngân hàng khác nhau)
            else if (Validator.equals(type, TransferTransactionType.QR_CODE_LAPNET_TRANSFER)    // QR code LAPNET chuyển tiền
                    || Validator.equals(type, TransferTransactionType.QR_CODE_LAPNET_MERCHANT)   // QR code LAPNET merchant
                    || Validator.equals(type, TransferTransactionType.INTER_BANK)) {             // Chuyển tiền liên ngân hàng
                transferType = TransferType.INTER_BANK.name();
            }
            // Nhóm giao dịch quốc tế
            else if (Validator.equals(type, TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)) { // QR code merchant quốc tế
                transferType = TransferType.INTERNATIONAL_BANK.name();
            }
        }
        return transferType;
    }

    /**
     * Xác thực số dư tài khoản và các điều kiện liên quan trước khi thực hiện giao dịch
     *
     * @param customerId ID khách hàng
     * @param customerAccountNumber Số tài khoản khách hàng
     * @param amountTransaction Số tiền giao dịch
     * @param transaction Thông tin giao dịch
     */
    private void validateAccBalance(Long customerId, String customerAccountNumber, Double amountTransaction, Transaction transaction) {
        // Tìm tài khoản tiền của khách hàng theo số tài khoản, ID khách hàng và trạng thái active
        // Ném lỗi nếu không tìm thấy tài khoản
        MoneyAccount moneyAccount = this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(
                customerAccountNumber, customerId, EntityStatus.ACTIVE.getStatus()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG1032));

        // Kiểm tra số dư tối thiểu cho giao dịch nội bộ ngân hàng với các loại tiền tệ khác nhau
        if (Validator.equals(transaction.getBankCode(), this.mbApiConstantProperties.getInBanKTransfer().getChannel())) {

            // Kiểm tra số dư tối thiểu cho tài khoản USD
            if (Validator.equals(moneyAccount.getCurrency(), "USD") && moneyAccount.getAvailableAmount() < this.customerProperties.getTransactionLimitUsd()) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID,
                                new Object[]{this.customerProperties.getTransactionLimitThb(),
                                        moneyAccount.getCurrency()}),
                        Currency.class.getSimpleName(), LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID);
            }
            // Kiểm tra số dư tối thiểu cho tài khoản THB
            else if (Validator.equals(moneyAccount.getCurrency(), "THB") && moneyAccount.getAvailableAmount() < this.customerProperties.getTransactionLimitThb()) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID,
                                new Object[]{this.customerProperties.getTransactionLimitThb(),
                                        moneyAccount.getCurrency()}),
                        Currency.class.getSimpleName(), LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID);
            }
        }

        // Kiểm tra xem giao dịch này có đang bị giới hạn bởi notification limit không
        // Nếu có thông báo giới hạn cho giao dịch này thì không cho phép thực hiện
        NotificationLimit notificationLimitCheckTransaction = this.notificationLimitRepository
                .findAllByTransactionId(transaction.getTransactionId()).stream().findFirst().orElse(null);

        if (Validator.isNotNull(notificationLimitCheckTransaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG100082);
        }

        // Kiểm tra số dư khả dụng có đủ để thực hiện giao dịch không
        // Nếu số dư khả dụng nhỏ hơn số tiền giao dịch thì ném lỗi
        if (moneyAccount.getAvailableAmount().compareTo(amountTransaction) < 0) {
            throw new BadRequestAlertException(ErrorCode.MSG1108);
        }
    }

    /**
     * Tạo thông báo cho khách hàng về giao dịch đã thực hiện
     *
     * @param transactionDTO Thông tin giao dịch để tạo thông báo
     */
    @Override
    public void createNotification(TransactionDTO transactionDTO) {
        // Tạo map để lưu trữ các giá trị sẽ được thay thế trong template thông báo
        Map<String, String> valuesAccount = new HashMap<>();

        // Xác định dấu của giao dịch (+ cho cộng tiền, - cho trừ tiền)
        String transactionType =
                Validator.equals(transactionDTO.getTransactionType(), TransactionType.PLUS) ? StringPool.PLUS : StringPool.MINUS;

        // Thiết lập nhãn cho tài khoản thanh toán
        valuesAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(),
                Labels.getLabels(LabelKey.LABEL_ACCOUNT_TYPE_PAYMENT_ACCOUNT));

        // Thiết lập số tài khoản thanh toán
        valuesAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), transactionDTO.getCustomerAccNumber());

        // Thiết lập nhãn cho số tiền giao dịch
        valuesAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(),
                Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION));

        // Thiết lập số tiền giao dịch với dấu và định dạng tiền tệ
        valuesAccount.put(TemplateField.TRANSACTION_AMOUNT.name(),
                transactionType + StringUtil.formatMoney(transactionDTO.getActualTransactionAmount()));

        // Thiết lập đồng tiền giao dịch
        valuesAccount.put(TemplateField.CURRENCY.name(), transactionDTO.getTransactionCurrency());

        // Thiết lập nhãn cho thời gian giao dịch
        valuesAccount.put((TemplateField.LABEL_TRANSACTION_TIME.name()), Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));

        // Thiết lập thời gian hoàn thành giao dịch đã được định dạng
        valuesAccount.put(TemplateField.TRANSACTION_TIME.name(), transactionDTO.formatTransactionFinishTime());

        // Thiết lập nhãn cho nội dung giao dịch
        valuesAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(),
                Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT));

        // Xử lý nội dung giao dịch theo từng loại giao dịch cụ thể (billing, topup, cash_in, qrcode)
        this.processTypePayment(valuesAccount, transactionDTO);

        // Thiết lập mã giao dịch
        valuesAccount.put(TemplateField.TRANSACTION_CODE.name(), transactionDTO.getTransactionCode());

        // Tìm template thông báo cho thanh toán merchant
        ContentTemplate templatePayMerchant = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.BALANCE_CHANGE_PAY_MERCHANT.name(), EntityStatus.ACTIVE.getStatus());

        // Nếu tìm thấy template thì tạo và gửi thông báo
        if (Validator.isNotNull(templatePayMerchant)) {
            // Tạo notification DTO
            NotificationDTO notificationDTO = new NotificationDTO();

            // Thiết lập thông tin cơ bản cho thông báo
            notificationDTO.setEndUserType(EndUserType.CUSTOMER);                          // Loại người dùng: khách hàng
            notificationDTO.setClassPk(transactionDTO.getCustomerId());                    // ID khách hàng
            notificationDTO.setNotificationStatus(NotificationStatus.PENDING);            // Trạng thái: chờ gửi
            notificationDTO.setNotificationType(NotificationType.TRANSFER_TRANSACTION);   // Loại: giao dịch chuyển tiền
            notificationDTO.setTargetType(TargetType.USER);                               // Đích: người dùng
            notificationDTO.setPublishTime(LocalDateTime.now());                          // Thời gian publish

            // Thiết lập nội dung thông báo từ template
            notificationDTO.setTitle(Labels.getLabels(templatePayMerchant.getTitle()));   // Tiêu đề đã localized
            notificationDTO.setContent(StringUtil.replaceMapValue(templatePayMerchant.getContent(),
                    valuesAccount));                                                       // Nội dung với values thay thế
            notificationDTO.setDescription(templatePayMerchant.getDescription());         // Mô tả

            // Lưu thông báo vào database
            Notification notificationAfterSaved = this.notificationRepository.save(
                    this.notificationMapper.toEntity(notificationDTO));

            // Tạo liên kết giữa notification và transaction
            this.createNotiTransaction(notificationAfterSaved.getNotificationId(),
                    transactionDTO.getTransferTransactionId());

            // Gửi thông báo qua các kênh (SMS, email, push notification)
            this.sendService.sendNotification(this.notificationMapper.toDto(notificationAfterSaved));
        }
    }

    /**
     * Tính phí giao dịch general (không phải merchant-specific)
     *
     * Method này tính phí cho các loại giao dịch chung:
     * 1. Tìm transaction fee type configuration
     * 2. Lấy danh sách fees active cho fee type này
     * 3. So sánh với transaction amount để tìm fee tier phù hợp
     * 4. Trả về fee response với calculated amounts
     *
     * @param transactionFeeTypeCode Loại phí giao dịch (TOPUP, TRANSFER...)
     * @param transactionAmount Số tiền giao dịch
     * @param currency Loại tiền tệ
     * @return FeeTransactionResponse Response chứa fee và discount
     */
    //TODO: check tiếp tại đây
    @Override
    public FeeTransactionResponse getFeeOfTransaction(TransactionFeeTypeCode transactionFeeTypeCode, Long transactionAmount, String currency) {
        // Tạo response object mặc định
        FeeTransactionResponse feeTransactionResponse = new FeeTransactionResponse();

        // Tìm transaction fee type configuration
        Optional<TransactionFeeType> transactionFeeType = transactionFeeTypeRepository
                .findByTransactionFeeTypeCodeAndStatus(transactionFeeTypeCode.name(),
                        EntityStatus.ACTIVE.getStatus());

        // Nếu không tìm thấy fee type configuration, return empty response
        if (!transactionFeeType.isPresent()) {
            return feeTransactionResponse;
        }

        // Lấy danh sách fees active cho transaction fee type này
        List<Fee> fees = feeRepository.findByTransactionFeeTypeIdAndStatus(transactionFeeType.get().getTransactionFeeTypeId(), EntityStatus.ACTIVE.getStatus());

        // Nếu không có fees, return empty response
        if (Validator.isNull(fees)) {
            return feeTransactionResponse;
        }

        // So sánh transaction amount với fee tiers để tính phí
        return this.compareAmountOfTransaction(fees, transactionAmount, currency);
    }

    /**
     * Tính phí giao dịch merchant-specific
     *
     * Method này tính phí cho merchant cụ thể với logic:
     * 1. Tìm transaction fee type configuration
     * 2. Validate merchant tồn tại và active
     * 3. Lấy master merchant ID (parent nếu có, không thì chính nó)
     * 4. Tìm merchant-specific fees
     * 5. Combine với general fees cho INTERNAL_QR_BILLING
     *
     * @param transactionFeeTypeCode Loại phí giao dịch
     * @param merchantId ID merchant
     * @param transactionAmount Số tiền giao dịch
     * @param currency Loại tiền tệ
     * @return FeeTransactionResponse Response chứa merchant fee
     * @throws BadRequestAlertException Khi merchant không tồn tại
     */
    @Override
    public FeeTransactionResponse getFeeOfMerchantTransaction(TransactionFeeTypeCode transactionFeeTypeCode, Long merchantId, Long transactionAmount, String currency) {
        // Tạo response object mặc định
        FeeTransactionResponse merchantFeeResponse = new FeeTransactionResponse();

        // Tìm transaction fee type configuration
        Optional<TransactionFeeType> transactionFeeType = transactionFeeTypeRepository
                .findByTransactionFeeTypeCodeAndStatus(transactionFeeTypeCode.name(),
                        EntityStatus.ACTIVE.getStatus());

        // Nếu không tìm thấy fee type configuration, return empty response
        if (!transactionFeeType.isPresent()) {
            return merchantFeeResponse;
        }

        // Tìm merchant theo ID và validate tồn tại
        Merchant merchant = this.merchantRepository.findByMerchantIdAndStatus(
                merchantId,
                EntityStatus.ACTIVE.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        // Lấy master merchant ID để handle merchant hierarchy
        // Nếu merchant có parent thì dùng parent ID, không thì dùng chính nó
        Long masterMerchantId = Validator.isNotNull(merchant.getParentId()) ? merchant.getParentId() : merchant.getMerchantId();

        // Tìm merchant-specific fees
        List<Fee> fees = feeRepository.findByTransactionFeeTypeIdAndStatusAndMerchantId
                (transactionFeeType.get().getTransactionFeeTypeId(), EntityStatus.ACTIVE.getStatus(), masterMerchantId);

        // Tính merchant fee
        merchantFeeResponse = this.compareAmountOfTransaction(fees, transactionAmount, currency);

        // Special handling cho INTERNAL_QR_BILLING: combine merchant fee với general fee
        if (Validator.equals(transactionFeeTypeCode, TransactionFeeTypeCode.INTERNAL_QR_BILLING)) {
            // Lấy general fee
            FeeTransactionResponse feeResponse = this.getFeeOfTransaction(transactionFeeTypeCode, transactionAmount, currency);

            // Không có cấu hình phí riêng nên luôn lấy theo cấu hình phí chung
            merchantFeeResponse.setFee(feeResponse.getFee());
            // Nếu không có cấu hình discount riêng thì lấy theo cấu hình chung
            if ((merchantFeeResponse.getDiscount() == 0
                    && merchantFeeResponse.getDiscountFixed() == 0)) {
                merchantFeeResponse.setDiscount(feeResponse.getDiscount());
                merchantFeeResponse.setDiscountFixed(feeResponse.getDiscountFixed());
            }

            // Enrich configuration fee type dựa vào final fee structure
            this.enrichConfigurationFeeType(merchantFeeResponse);
        }
        return merchantFeeResponse;
    }

    private FeeTransactionResponse compareAmountOfTransaction(List<Fee> fees, Long transactionAmount, String
            currency) {
        FeeTransactionResponse feeTransactionResponse = new FeeTransactionResponse();

        if (Validator.isNull(fees)) return feeTransactionResponse;

        if (fees.size() > 2) {
            throw new BadRequestAlertException(ErrorCode.MSG101102);
        }

        for (Fee item : fees) {
            List<FeeRate> feeRates;
            feeRates = feeRateRepository.findAllFeeRateActiveAndHasEffective(item.getFeeId(), Instant.now());

            if (Validator.isNotNull(currency)) {
                feeRates = feeRates.stream().filter(feeRate -> Validator.equals(feeRate.getCurrency(), currency)).collect(Collectors.toList());
            }

            // compare với số tiền giao dịch để lấy ra biểu phí tương ứng
            feeRates.forEach(feeRate -> {
                if (feeRate.getTransactionAmountMin() <= transactionAmount &&
                        feeRate.getTransactionAmountMax() >= transactionAmount) {
                    if (Validator.equals(item.getConfigurationFeeType(), ConfigurationFeeType.FEE)) {
                        double fee = feeRate.getFeeAmount() * (1 + (feeRate.getVat() / 100));
                        if (Validator.equals(customerProperties.getDefaultCurrency(), currency)) {
                            feeTransactionResponse.setFee(Math.round(fee));
                        } else {
                            feeTransactionResponse.setFee(StringUtil.formatMoneyCurrencyForeign(fee));
                        }
                    } else {
                        feeTransactionResponse.setDiscount(feeRate.getDiscountPercent());
                        feeTransactionResponse.setDiscountFixed(feeRate.getDiscountFixed());
                    }
                }
            });
        }

        enrichConfigurationFeeType(feeTransactionResponse);

        feeTransactionResponse.setCurrency(Validator.isNotNull(currency) ? currency : this.customerProperties.getDefaultCurrency());

        return feeTransactionResponse;
    }

    private void enrichConfigurationFeeType(FeeTransactionResponse feeTransactionResponse) {
        if (feeTransactionResponse.getDiscount() > 0 && (feeTransactionResponse.getFee() > 0 || feeTransactionResponse.getDiscountFixed() > 0)) {
            feeTransactionResponse.setConfigurationFeeType(ConfigurationFeeType.ALL);
        } else if (feeTransactionResponse.getDiscount() > 0 || feeTransactionResponse.getDiscountFixed() > 0) {
            feeTransactionResponse.setConfigurationFeeType(ConfigurationFeeType.DISCOUNT);
        } else if (feeTransactionResponse.getFee() > 0) {
            feeTransactionResponse.setConfigurationFeeType(ConfigurationFeeType.FEE);
        }
    }

    /**
     * Tính số tiền giao dịch thực tế sau khi áp dụng discount và business rules
     *
     * Method này tính toán final transaction amount với logic:
     * 1. Trừ discount fixed từ transaction amount
     * 2. Trừ discount percentage từ transaction amount
     * 3. Apply business rules cho billing types cụ thể
     * 4. Round up final amount
     *
     * Business rule cho WATER/ELECTRIC billing:
     * - Nếu (amount sau discount + fee) < original amount thì amount = original - fee
     * - Đảm bảo customer không trả ít hơn bill amount
     *
     * @param transaction TransactionDTO chứa thông tin giao dịch
     * @return double Số tiền giao dịch thực tế đã được tính toán
     */
    @Override
    public double getAmountTransaction(TransactionDTO transaction) {
        // Lấy số tiền giao dịch gốc
        double transactionAmount = transaction.getTransactionAmount();
        // Lấy discount fixed (mặc định 0 nếu null)
        long discountFixed = Validator.isNull(transaction.getDiscountFixed()) ? 0L : transaction.getDiscountFixed();

        // Tính transaction amount sau discount
        // Formula: amount = original - fixed_discount - (original * percentage_discount / 100)
        transactionAmount = transactionAmount - discountFixed - transactionAmount * transaction.getDiscount() / 100;

        // Business rule đặc biệt cho thanh toán điện nước
        if (Validator.equals(transaction.getBillingType(), BillingType.WATER) || Validator.equals(transaction.getBillingType(), BillingType.ELECTRIC)) {
            // Số tiền tính ra theo công thức < tiền bill ==> số tiền thanh toán = tiền bill
            // Đảm bảo customer không trả ít hơn bill amount
            if ((transactionAmount + transaction.getTransactionFee()) < transaction.getTransactionAmount()) {
                transactionAmount = transaction.getTransactionAmount() - transaction.getTransactionFee();
            }
        }

        // Round up để tránh số lẻ
        return Math.ceil(transactionAmount);
    }

    /**
     * Tạo liên kết giữa notification và transaction
     *
     * Method này tạo record trong bảng noti_transaction để:
     * - Liên kết notification với transaction cụ thể
     * - Tracking notification nào thuộc về transaction nào
     * - Hỗ trợ query notification theo transaction
     *
     * @param notificationId ID của notification đã được tạo
     * @param transactionId ID của transaction liên quan
     */
    private void createNotiTransaction(Long notificationId, Long transactionId) {
        // Tạo entity liên kết
        NotiTransaction notiTransaction = new NotiTransaction();

        // Set thông tin liên kết
        notiTransaction.setNotificationId(notificationId);  // ID notification
        notiTransaction.setTransactionId(transactionId);    // ID transaction
        notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus()); // Trạng thái active

        // Lưu vào database
        this.notiTransactionRepository.save(notiTransaction);
    }

    /**
     * Xử lý nội dung giao dịch theo loại transfer type
     *
     * Method này tạo nội dung mô tả giao dịch dựa vào transfer type:
     * - BILLING: "Thanh toán hóa đơn [target]"
     * - TOPUP: "Nạp tiền điện thoại [target]"
     * - CASH_IN: "Nạp tiền Umoney [target]"
     * - QRCODE: "Thanh toán QR Code [target]"
     *
     * @param valuesAccount Map chứa các values để thay thế trong template
     * @param transactionDTO DTO giao dịch chứa transfer type và target
     */
    private void processTypePayment(Map<String, String> valuesAccount, TransactionDTO transactionDTO) {
        // Lấy loại chuyển tiền
        TransferType transferType = transactionDTO.getTransferType();

        // Xử lý theo từng loại transfer type
        if (Validator.equals(transferType, TransferType.BILLING)) {
            // Thanh toán hóa đơn
            valuesAccount.put(TemplateField.CONTENT_TRANSACTION.name(),
                    Labels.getLabels(LabelKey.LABEL_MERCHANT_BILLING) + StringPool.SPACE + transactionDTO.getTarget());
        }

        if (Validator.equals(transferType, TransferType.TOPUP)) {
            // Nạp tiền điện thoại
            valuesAccount.put(TemplateField.CONTENT_TRANSACTION.name(),
                    Labels.getLabels(LabelKey.LABEL_MERCHANT_TOPUP) + StringPool.SPACE + transactionDTO.getTarget());
        }

        if (Validator.equals(transferType, TransferType.CASH_IN)) {
            // Nạp tiền ví điện tử Umoney
            valuesAccount.put(TemplateField.CONTENT_TRANSACTION.name(),
                    Labels.getLabels(LabelKey.LABEL_MERCHANT_UMONEY) + StringPool.SPACE + transactionDTO.getTarget());
        }

        if (Validator.equals(transferType, TransferType.QRCODE)) {
            // Thanh toán QR Code
            valuesAccount.put(TemplateField.CONTENT_TRANSACTION.name(),
                    Labels.getLabels(LabelKey.LABEL_MERCHANT_QR_CODE) + StringPool.SPACE + transactionDTO.getTarget());
        }
    }
}
