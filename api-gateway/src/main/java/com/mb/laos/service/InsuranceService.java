package com.mb.laos.service;

import com.mb.laos.gateway.request.LviBuyHealthInsuranceRequest;
import com.mb.laos.gateway.request.LviBuyVehicleInsuranceRequest;
import com.mb.laos.gateway.request.LviRenewVehicleInsuranceRequest;
import com.mb.laos.gateway.request.OtpTransferConfirmRequest;
import com.mb.laos.gateway.response.*;
import com.mb.laos.model.TransactionInsurance;
import com.mb.laos.model.dto.*;
import com.mb.laos.model.dto.TransactionInsuranceDTO;
import com.mb.laos.model.search.FileEntrySearch;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface InsuranceService {

    LviVehiclePackageDTO getLviVehiclePackage();

    LviVehicleTypeDTO getLviVehicleType();

    LviVehiclePackageFeeDTO getLviVehiclePackageFee(String packageCode, String vehicleCode);

    LviHealthPackageDTO getLviHealthPackage();

    LviHealthPackageFeeDTO getLviHealthPackageFeeDTO(String healthPackage);

    OtpTransferTransResponse requestBuyLviVehicleInsurance(LviBuyVehicleInsuranceRequest request, MultipartFile registrationImage);

    OtpTransansferLviConfirmResponse confirmBuyInsurance(OtpTransferConfirmRequest confirmRequest);

    OtpTransferTransResponse requestBuyLviHealthInsurance(LviBuyHealthInsuranceRequest request);

    LviDeliveryDTO getDelivery();

    LviVehicleInsuranceDTO getVehicleInsurance(String certificate);

    OtpTransferTransResponse requestRenewLviVehicleInsurance(LviRenewVehicleInsuranceRequest request);

    List<TransactionInsuranceDTO> getCustomerInsuranceTransaction();

    ResponseEntity<ByteArrayResource> getRegistrationImage(FileEntrySearch search);
}
