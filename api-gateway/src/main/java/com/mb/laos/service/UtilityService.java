package com.mb.laos.service;

import com.mb.laos.api.response.ListElectricMmoneyResponse;
import com.mb.laos.api.response.ListWaterMmoneyResponse;
import com.mb.laos.gateway.request.BeneficiaryConfirmRequest;
import com.mb.laos.gateway.request.BillingHistoryRequest;
import com.mb.laos.gateway.request.BillingRequest;
import com.mb.laos.gateway.request.BillingV2Request;
import com.mb.laos.gateway.request.CashoutRequest;
import com.mb.laos.gateway.request.ConfirmOtpBillingRequest;
import com.mb.laos.gateway.request.ConfirmOtpBillingV2Request;
import com.mb.laos.gateway.request.InfoQrMerchantRequest;
import com.mb.laos.gateway.request.OtpBillingRequest;
import com.mb.laos.gateway.request.OtpBillingV2Request;
import com.mb.laos.gateway.request.OtpCashInRequest;
import com.mb.laos.gateway.request.OtpQrBillRequest;
import com.mb.laos.gateway.request.OtpTransferConfirmRequest;
import com.mb.laos.gateway.request.OtpTransferInsuranceRequest;
import com.mb.laos.gateway.request.OtpTransferTransRequest;
import com.mb.laos.gateway.request.OtpUtilityRequest;
import com.mb.laos.gateway.request.ResendOtpRequest;
import com.mb.laos.gateway.request.TransactionHistoryRequest;
import com.mb.laos.gateway.request.UtilityRequest;
import com.mb.laos.gateway.response.BeneficiaryInfoResponse;
import com.mb.laos.gateway.response.BillingHistoryResponse;
import com.mb.laos.gateway.response.BillingHistoryV2Response;
import com.mb.laos.gateway.response.BillingResponse;
import com.mb.laos.gateway.response.CashoutResponse;
import com.mb.laos.gateway.response.InfoQrMerchantResponse;
import com.mb.laos.gateway.response.MBOtpTransConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferTransResponse;
import com.mb.laos.model.Transaction;
import com.mb.laos.model.dto.TransactionDTO;
import com.mb.laos.model.dto.TransactionHistoryDTO;
import com.mb.laos.model.search.BillingHistoryV2Search;
import com.mb.laos.model.search.TransactionSearchRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Set;

public interface UtilityService {
    /**
     * danh sách nhà cung cấp nước
     *
     * @param request
     * @return
     */
    List<ListWaterMmoneyResponse> waterSuppliers(HttpServletRequest request);

    /**
     * danh sách nhà cung cấp điện
     *
     * @param request
     * @return
     */
    List<ListElectricMmoneyResponse> electricSupplies(HttpServletRequest request);

    /**
     * truy vấn thông tin hoá đơn điện, nước
     *
     * @return
     */
    BillingResponse verifyBillingCode(UtilityRequest billingRequest);

    /**
     * yêu cầu OTP thanh toán điện, nước
     *
     * @param request
     * @return
     */
    OtpTransferTransResponse requestOtpBilling(OtpUtilityRequest otpRequest, HttpServletRequest request);

    /**
     * thanh toán điện, nước
     *
     * @param request
     * @return
     */
    OtpTransferConfirmResponse confirmOtpBilling(ConfirmOtpBillingV2Request confirmRequest, HttpServletRequest request);

    /**
     * danh sách lịch sử điện nước
     *
     * @param request
     * @return
     */
    List<BillingHistoryV2Response> getHistoryBilling(BillingHistoryV2Search request);
}
