package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.request.AccountBalanceRequest;
import com.mb.laos.api.request.AccountSavingRequest;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.CustomerProperties;
import com.mb.laos.configuration.NotificationProperties;
import com.mb.laos.controller.request.NotificationBalanceClientRequest;
import com.mb.laos.controller.request.NotificationChargeClientRequest;
import com.mb.laos.controller.request.NotificationChargeRequest;
import com.mb.laos.controller.request.NotificationClientRequest;
import com.mb.laos.enums.*;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Common;
import com.mb.laos.model.ContentTemplate;
import com.mb.laos.model.CounterSavingAccount;
import com.mb.laos.model.Currency;
import com.mb.laos.model.Customer;
import com.mb.laos.model.Merchant;
import com.mb.laos.model.MoneyAccount;
import com.mb.laos.model.Notification;
import com.mb.laos.model.NotificationHistory;
import com.mb.laos.model.dto.NotificationLapnetDTO;
import com.mb.laos.model.PremiumAccNumber;
import com.mb.laos.model.ReceiveTransferMoney;
import com.mb.laos.model.SavingAccount;
import com.mb.laos.model.SmsBalance;
import com.mb.laos.model.Transaction;
import com.mb.laos.model.dto.AccountBalanceDTO;
import com.mb.laos.model.dto.AccountSavingDTO;
import com.mb.laos.model.dto.CounterSavingAccountDTO;
import com.mb.laos.model.dto.NotiTransactionDTO;
import com.mb.laos.model.dto.NotificationDTO;
import com.mb.laos.model.dto.ReceiveTransferMoneyDTO;
import com.mb.laos.model.dto.TransactionEntryDTO;
import com.mb.laos.repository.CommonRepository;
import com.mb.laos.repository.ContentTemplateRepository;
import com.mb.laos.repository.CounterSavingAccountRepository;
import com.mb.laos.repository.CurrencyRepository;
import com.mb.laos.repository.CustomerRepository;
import com.mb.laos.repository.MerchantRepository;
import com.mb.laos.repository.MoneyAccountRepository;
import com.mb.laos.repository.NotiTransactionRepository;
import com.mb.laos.repository.NotificationHistoryRepository;
import com.mb.laos.repository.NotificationRepository;
import com.mb.laos.repository.PremiumAccNumberRepository;
import com.mb.laos.repository.ReceiveTransferMoneyRepository;
import com.mb.laos.repository.SavingAccountRepository;
import com.mb.laos.repository.SmsBalanceRepository;
import com.mb.laos.repository.TransactionRepository;
import com.mb.laos.service.ApiGeeTransferService;
import com.mb.laos.service.NotificationGatewayService;
import com.mb.laos.service.SendService;
import com.mb.laos.service.mapper.CounterSavingAccountMapper;
import com.mb.laos.service.mapper.NotiTransactionMapper;
import com.mb.laos.service.mapper.NotificationMapper;
import com.mb.laos.service.mapper.ReceiveTransferMoneyMapper;
import com.mb.laos.util.DateUtil;
import com.mb.laos.util.DiagnosticContextUtil;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.Validator;
import com.mb.laos.validator.ValidationConstraint;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service Implementation xử lý thông báo từ LAPNET Gateway
 *
 * Service này là trung tâm xử lý thông báo từ hệ thống LAPNET:
 *
 * 🔔 CHỨC NĂNG CHÍNH:
 * - Nhận thông báo từ LAPNET về giao dịch
 * - Xử lý thông báo balance inquiry
 * - Xử lý thông báo charge fee
 * - Tạo notification cho khách hàng
 *
 * 💰 CÁC LOẠI THÔNG BÁO:
 * - Giao dịch chuyển tiền thành công
 * - Giao dịch tiết kiệm (mở/đóng)
 * - Thu phí dịch vụ
 * - SMS balance charge
 * - Premium account revert
 *
 * 🔄 LUỒNG XỬ LÝ:
 * 1. Nhận notification từ LAPNET
 * 2. Validate và parse thông tin
 * 3. Xác định loại giao dịch
 * 4. Tạo notification content
 * 5. Lưu vào database
 * 6. Gửi push notification
 *
 * 🏦 TÍCH HỢP:
 * - LAPNET Core Banking System
 * - T24 Account Balance API
 * - FCM Push Notification
 * - SMS Gateway
 *
 * <AUTHOR> Development Team
 * @version 2.0
 * @since 2023
 */
@Service // Đánh dấu class này là một Spring Service component
@RequiredArgsConstructor // Lombok tự động tạo constructor với tất cả final fields
@Log4j // Lombok tự động tạo logger instance
public class NotificationGatewayServiceImpl implements NotificationGatewayService {

    // ==================== CÁC REPOSITORY DEPENDENCIES ====================

    /** Repository quản lý notification - xử lý CRUD cho bảng notification */
    private final NotificationRepository notificationRepository;

    /** Repository quản lý premium account number - xử lý tài khoản số đẹp */
    private final PremiumAccNumberRepository premiumAccNumberRepository;

    /** Repository quản lý currency - xử lý thông tin tiền tệ */
    private final CurrencyRepository currencyRepository;

    /** Repository quản lý customer - xử lý thông tin khách hàng */
    private final CustomerRepository customerRepository;

    /** Repository quản lý merchant - xử lý thông tin merchant */
    private final MerchantRepository merchantRepository;

    /** Repository quản lý content template - xử lý template nội dung thông báo */
    private final ContentTemplateRepository contentTemplateRepository;

    /** Repository quản lý receive transfer money - xử lý lịch sử nhận tiền */
    private final ReceiveTransferMoneyRepository receiveTransferMoneyRepository;

    /** Repository quản lý notification transaction - xử lý giao dịch thông báo */
    private final NotiTransactionRepository notiTransactionRepository;

    /** Repository quản lý transaction - xử lý giao dịch chung */
    private final TransactionRepository transactionRepository;

    /** Repository quản lý common data - xử lý dữ liệu chung hệ thống */
    private final CommonRepository commonRepository;

    /** Repository quản lý counter saving account - xử lý tài khoản tiết kiệm tại quầy */
    private final CounterSavingAccountRepository counterSavingAccountRepository;

    /** Repository quản lý saving account - xử lý tài khoản tiết kiệm online */
    private final SavingAccountRepository savingAccountRepository;

    /** Repository quản lý notification history - xử lý lịch sử thông báo */
    private final NotificationHistoryRepository notificationHistoryRepository;

    /** Repository quản lý money account - xử lý tài khoản tiền */
    private final MoneyAccountRepository moneyAccountRepository;

    /** Repository quản lý SMS balance - xử lý số dư SMS */
    private final SmsBalanceRepository smsBalanceRepository;

    // ==================== CÁC MAPPER DEPENDENCIES ====================

    /** Mapper chuyển đổi notification entity/dto */
    private final NotificationMapper notificationMapper;

    /** Mapper chuyển đổi receive transfer money entity/dto */
    private final ReceiveTransferMoneyMapper receiveTransferMoneyMapper;

    /** Mapper chuyển đổi notification transaction entity/dto */
    private final NotiTransactionMapper notiTransactionMapper;

    /** Mapper chuyển đổi counter saving account entity/dto */
    private final CounterSavingAccountMapper counterSavingAccountMapper;

    // ==================== CÁC SERVICE DEPENDENCIES ====================

    /** Service tích hợp ApiGee Transfer - gọi API T24 để kiểm tra balance và saving */
    private final ApiGeeTransferService apiGeeTransferService;

    /** Service gửi notification - xử lý push notification và SMS */
    private final SendService sendService;

    // ==================== CÁC PROPERTIES DEPENDENCIES ====================

    /** Properties chứa cấu hình customer */
    private final CustomerProperties customerProperties;

    /** Properties chứa cấu hình notification */
    private final NotificationProperties notificationProperties;

    /**
     * Xử lý giao dịch trả lãi tiết kiệm (Credit Pay Interest)
     *
     * Method này xử lý các trường hợp trả lãi cho tài khoản tiết kiệm:
     * 1. Trả lãi TKTK online tất toán trước hạn tại quầy
     * 2. Quay vòng TKTK tại quầy chưa đóng
     * 3. Trả lãi và đóng tài khoản tiết kiệm
     *
     * @param request Thông tin notification từ LAPNET
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param customer Thông tin khách hàng
     * @param savingCategory Danh sách loại tài khoản tiết kiệm
     * @param transactionType Loại giao dịch
     * @return TransactionEntryDTO chứa notification và receive transfer money DTO
     */
    private TransactionEntryDTO creditPayInterest(NotificationClientRequest request, Instant transactionFinishTime,
                                                  Customer customer, List<String> savingCategory, CommonCode transactionType) {
        // Khởi tạo các DTO để lưu kết quả xử lý
        NotificationDTO notificationDTO = null;
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO = null;

        // Tạo request để kiểm tra tài khoản tiết kiệm qua T24
        AccountSavingRequest accSavingRequest = AccountSavingRequest.builder()
                .cusId(request.getCif()) // CIF của khách hàng
                .build();

        // Gọi API T24 để lấy danh sách tài khoản tiết kiệm của khách hàng
        List<AccountSavingDTO> accountSavingDTOS = this.apiGeeTransferService.checkAccountSaving(accSavingRequest);

        // Trường hợp 1: Trả lãi TKTK online tất toán trước hạn tại quầy
        // Tìm tài khoản tiết kiệm online theo số tài khoản thụ hưởng và trạng thái ACTIVE
        SavingAccount savingAccOnline = this.savingAccountRepository
                .findBySavingAccountNumberAndStatus(request.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus());

        // Kiểm tra nếu có TKTK online và thời gian giao dịch trước hạn đáo hạn
        if (Validator.isNotNull(savingAccOnline) && transactionFinishTime.isBefore(savingAccOnline.getSettlementDueTime())) {
            // Tạo notification DTO với thông tin giao dịch
            notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
            // Set tiêu đề thông báo trả lãi thành công
            notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_INTEREST_PAID_IS_SUCCESSFULLY));
            // Tạo receive transfer money DTO để lưu lịch sử nhận tiền
            receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
            // Set loại tài khoản là SAVING
            receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());
        }

        // Trường hợp 2: Quay vòng TKTK tại quầy chưa đóng
        if (Validator.isNotNull(accountSavingDTOS)) {
            // Tìm tài khoản tiết kiệm theo số tài khoản thụ hưởng
            Optional<AccountSavingDTO> accountSaving = accountSavingDTOS.stream()
                    .filter(item -> Validator.equals(item.getAcctNo(), request.getBeneficiaryAccountNumber()))
                    .findFirst();

            // Kiểm tra nếu tìm thấy tài khoản và thuộc loại tiết kiệm hợp lệ
            if (accountSaving.isPresent() && savingCategory.contains(accountSaving.get().getCategory())) {
                // Tạo notification DTO với thông tin giao dịch
                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                // Set tiêu đề thông báo trả lãi thành công
                notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_INTEREST_PAID_IS_SUCCESSFULLY));
                // Tạo receive transfer money DTO để lưu lịch sử nhận tiền
                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set loại tài khoản là SAVING
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());
            }
        // Trường hợp 3: Trả lãi và đóng tài khoản tiết kiệm tại quầy
        } else {
            // Tìm tài khoản tiết kiệm tại quầy theo số tài khoản và loại tiết kiệm
            CounterSavingAccount counterSavingAccounts = this.counterSavingAccountRepository
                    .findBySavingAccountNumberAndCategoryIn(request.getBeneficiaryAccountNumber(), savingCategory);

            // Nếu tìm thấy tài khoản tiết kiệm tại quầy
            if (Validator.isNotNull(counterSavingAccounts)) {
                // Tạo notification DTO với thông tin giao dịch
                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                // Set tiêu đề thông báo đóng tài khoản tiết kiệm thành công
                notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CLOSE_SAVING_ACCOUNT_IS_SUCCESSFULLY));
                // Tạo receive transfer money DTO để lưu lịch sử nhận tiền
                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set loại tài khoản là SAVING
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());
            }
        }

        // Kiểm tra nếu không tạo được notification hoặc receive transfer money DTO
        if (Validator.isNull(notificationDTO) || Validator.isNull(receiveTransferMoneyDTO)) {
            // Trả về null nếu không xử lý được giao dịch
            return null;
        }

        // Trả về TransactionEntryDTO chứa cả notification và receive transfer money DTO
        return new TransactionEntryDTO(notificationDTO, receiveTransferMoneyDTO);
    }

    /**
     * Xử lý giao dịch tất toán tự động tiết kiệm cố định (Credit Auto Close FD)
     *
     * Method này xử lý 2 trường hợp:
     * 1. TKTK tại quầy - tất toán khi đáo hạn
     * 2. TKTK mở online - tất toán trước hạn tại quầy
     *
     * @param request Thông tin notification từ LAPNET
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param customer Thông tin khách hàng
     * @param accountBalanceRequest Request kiểm tra balance
     * @param transactionType Loại giao dịch
     * @return TransactionEntryDTO chứa notification và receive transfer money DTO
     */
    private TransactionEntryDTO creditAutoCloseFd(NotificationClientRequest request, Instant transactionFinishTime,
                                                  Customer customer, AccountBalanceRequest accountBalanceRequest,
                                                  CommonCode transactionType) {
        // Khởi tạo các DTO để lưu kết quả xử lý
        NotificationDTO notificationDTO = null;
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO = null;

        // Extract saving account number từ transaction ID
        // Nếu transaction ID bắt đầu với entry prefix thì lấy substring từ vị trí 3
        String savingAcc = request.getTransactionId()
                .startsWith(this.notificationProperties.getEntryPrefix()) ? request.getTransactionId()
                .substring(3) : null;

        // Nếu không extract được saving account number thì return null
        if (Validator.isNull(savingAcc)) {
            return null;
        }

        // Trường hợp 1: TKTK tại quầy (counter saving account)
        // Tìm counter saving account theo số tài khoản và category (trả lãi cuối kỳ hoặc hàng tháng)
        CounterSavingAccount counterSavingAccounts = this.counterSavingAccountRepository
                .findBySavingAccountNumberAndCategoryIn(savingAcc,
                        Arrays.asList(customerProperties.getCifTermPayInterest(), // Trả lãi cuối kỳ
                                customerProperties.getCifTermMonthPayment())); // Trả lãi hàng tháng

        // Nếu tìm thấy counter saving account
        if (Validator.isNotNull(counterSavingAccounts)) {
            // Gọi API T24 để lấy danh sách account balance
            List<AccountBalanceDTO> accountBalanceDTOS = this.apiGeeTransferService.checkAccountBalance(accountBalanceRequest);

            // Nếu có account balance data từ T24
            if (Validator.isNotNull(accountBalanceDTOS)) {
                // Tìm account balance khớp với beneficiary account number
                Optional<AccountBalanceDTO> accountBalanceDTO = getAccountBalanceDTO(request, accountBalanceDTOS);

                // Nếu tìm thấy account balance hợp lệ
                if (accountBalanceDTO.isPresent()) {
                    // Tạo notification DTO
                    notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                    // Set title thông báo đóng tài khoản tiết kiệm thành công
                    notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CLOSE_SAVING_ACCOUNT_IS_SUCCESSFULLY));
                    // Tạo receive transfer money DTO
                    receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                    // Set account type là không phải tiết kiệm (vì đã đóng)
                    receiveTransferMoneyDTO.setAccountType(AccountTypeCif.NONSAVING.getStatus());
                }
            }
        } else {
            // Trường hợp 2: TKTK mở online => Tất toán trước hạn tại quầy
            // Tìm saving account online theo số tài khoản và trạng thái ACTIVE
            SavingAccount savingAccOnline = this.savingAccountRepository
                    .findBySavingAccountNumberAndStatus(savingAcc, EntityStatus.ACTIVE.getStatus());

            // Nếu tìm thấy saving account online và thời gian giao dịch trước hạn đáo hạn
            if (Validator.isNotNull(savingAccOnline) && transactionFinishTime.isBefore(savingAccOnline.getSettlementDueTime())) {
                // Tạo notification DTO
                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                // Set title thông báo đóng tài khoản tiết kiệm thành công
                notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CLOSE_SAVING_ACCOUNT_IS_SUCCESSFULLY));
                // Tạo receive transfer money DTO
                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set account type là không phải tiết kiệm (vì đã đóng)
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.NONSAVING.getStatus());

                // Cập nhật trạng thái saving account online thành INACTIVE
                savingAccOnline.setStatus(EntityStatus.INACTIVE.getStatus());
                // Set receiving account number (tài khoản nhận tiền tất toán)
                savingAccOnline.setReceivingAccountNumber(request.getBeneficiaryAccountNumber());
                // Set maturity date là thời gian tất toán thực tế
                savingAccOnline.setMaturityDate(transactionFinishTime);
            }
        }

        // Kiểm tra nếu không tạo được notification hoặc receive transfer money DTO
        if (Validator.isNull(notificationDTO) || Validator.isNull(receiveTransferMoneyDTO)) {
            // Trả về null nếu không xử lý được giao dịch
            return null;
        }

        // Trả về TransactionEntryDTO chứa cả notification và receive transfer money DTO
        return new TransactionEntryDTO(notificationDTO, receiveTransferMoneyDTO);
    }

    /**
     * Xử lý giao dịch nạp tiền tại quầy (Cash Deposit)
     *
     * Method này xử lý 2 trường hợp:
     * 1. Nạp tiền vào tài khoản thông thường (TKTT, TKSD)
     * 2. Nạp tiền vào tài khoản tiết kiệm tích lũy tại quầy
     *
     * @param request Thông tin notification từ LAPNET
     * @param customer Thông tin khách hàng
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param accountBalanceRequest Request kiểm tra balance
     * @param accountSavingRequest Request kiểm tra saving account
     * @param transactionType Loại giao dịch
     * @return TransactionEntryDTO chứa notification và receive transfer money DTO
     */
    private TransactionEntryDTO cashDeposit(NotificationClientRequest request, Customer customer, Instant transactionFinishTime,
                                            AccountBalanceRequest accountBalanceRequest, AccountSavingRequest accountSavingRequest,
                                            CommonCode transactionType) {
        // Khởi tạo các DTO để lưu kết quả xử lý
        NotificationDTO notificationDTO = null;
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO = null;

        // Gọi API T24 để lấy danh sách account balance của customer
        List<AccountBalanceDTO> accountBalanceDTOList =
                this.apiGeeTransferService.checkAccountBalance(accountBalanceRequest);

        // Nếu có account balance data từ T24
        if (Validator.isNotNull(accountBalanceDTOList)) {
            // Tìm account balance khớp với beneficiary account number
            Optional<AccountBalanceDTO> accBalanceDTO = getAccountBalanceDTO(request, accountBalanceDTOList);

            // Trường hợp 1: Nạp tiền vào tài khoản thông thường (TKTT, TKSD)
            if (accBalanceDTO.isPresent()) {
                // Tạo notification DTO
                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                // Set title thông báo nạp tiền thành công
                notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CASH_DEPOSIT_IS_SUCCESSFULLY));
                // Tạo receive transfer money DTO
                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set account type là không phải tiết kiệm
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.NONSAVING.getStatus());
            } else {
                // Trường hợp 2: Nạp tiền vào tài khoản tiết kiệm tích lũy tại quầy
                // Lấy thông tin accumulated saving account từ T24
                AccountSavingDTO accumulatedSaving = this.getAccumulatedSaving(accountSavingRequest);

                // Nếu không tìm thấy accumulated saving thì return null
                if (Validator.isNull(accumulatedSaving)) {
                    return null;
                }

                // Mở/tạo saving accumulation account trong local database
                this.openSavingAccumulation(request, customer, accumulatedSaving);

                // Tạo notification DTO
                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                // Set title thông báo nạp tiền vào tài khoản tiết kiệm tích lũy thành công
                notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CASH_IN_ACCUMULATED_SAVING_ACCOUNT_IS_SUCCESSFULLY));
                // Tạo receive transfer money DTO
                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set account type là tiết kiệm
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());
            }
        }

        // Kiểm tra nếu không tạo được notification hoặc receive transfer money DTO
        if (Validator.isNull(notificationDTO) || Validator.isNull(receiveTransferMoneyDTO)) {
            // Trả về null nếu không xử lý được giao dịch
            return null;
        }

        // Trả về TransactionEntryDTO chứa cả notification và receive transfer money DTO
        return new TransactionEntryDTO(notificationDTO, receiveTransferMoneyDTO);
    }

    /**
     * Xử lý giao dịch mở tiết kiệm cố định tại quầy (Fixed Deposit Credit)
     *
     * Method này xử lý 2 trường hợp:
     * 1. Mở TKTK cố định tại quầy (từ T24)
     * 2. Mở TKTK online, cộng tiền vào TKTK
     *
     * @param request Thông tin notification từ LAPNET
     * @param customer Thông tin khách hàng
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param accountSavingRequest Request kiểm tra saving account
     * @param transactionType Loại giao dịch
     * @param formatter Date formatter để parse date
     * @return TransactionEntryDTO chứa notification và receive transfer money DTO
     */
    private TransactionEntryDTO fixedDepositCredit(NotificationClientRequest request, Customer customer,
                                                   Instant transactionFinishTime, AccountSavingRequest accountSavingRequest,
                                                   CommonCode transactionType, DateTimeFormatter formatter) {
        // Khởi tạo các DTO để lưu kết quả xử lý
        NotificationDTO notificationDTO = null;
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO = null;

        // Gọi API T24 để lấy danh sách saving account của customer
        List<AccountSavingDTO> accountSavingDTOS = this.apiGeeTransferService.checkAccountSaving(accountSavingRequest);

        // Trường hợp 1: Mở TKTK cố định tại quầy (có data từ T24)
        if (Validator.isNotNull(accountSavingDTOS)) {
            // Tìm saving account có category là trả lãi hàng tháng hoặc cuối kỳ
            Optional<AccountSavingDTO> accountSavingDTO = accountSavingDTOS.stream()
                    .filter(item -> Validator.equals(item.getCategory(), customerProperties.getCifTermMonthPayment()) // Trả lãi hàng tháng
                            || Validator.equals(item.getCategory(), customerProperties.getCifTermPayInterest())) // Trả lãi cuối kỳ
                    .findFirst();

            // Nếu tìm thấy saving account phù hợp
            if (accountSavingDTO.isPresent()) {
                // Tạo notification DTO
                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                // Set title thông báo mở tài khoản tiết kiệm thành công
                notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_OPEN_SAVING_ACCOUNT_IS_SUCCESSFULLY));
                // Tạo receive transfer money DTO
                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set account type là tiết kiệm
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());

                // Sync counter saving account từ T24 data về local database
                extracted(customer, formatter, request.getBeneficiaryAccountNumber());
            }
        } else {
            // Trường hợp 2: Mở TKTK online, cộng tiền vào TKTK
            // Tìm saving account online theo beneficiary account number và trạng thái ACTIVE
            SavingAccount savingAccOnline = this.savingAccountRepository
                    .findBySavingAccountNumberAndStatus(request.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus());

            // Nếu tìm thấy saving account online
            if (Validator.isNotNull(savingAccOnline)) {
                // Tạo notification DTO
                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                // Set title thông báo mở tài khoản tiết kiệm thành công
                notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_OPEN_SAVING_ACCOUNT_IS_SUCCESSFULLY));
                // Tạo receive transfer money DTO
                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set account type là tiết kiệm
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());
            }
        }

        // Kiểm tra nếu không tạo được notification hoặc receive transfer money DTO
        if (Validator.isNull(notificationDTO) || Validator.isNull(receiveTransferMoneyDTO)) {
            // Trả về null nếu không xử lý được giao dịch
            return null;
        }

        // Trả về TransactionEntryDTO chứa cả notification và receive transfer money DTO
        return new TransactionEntryDTO(notificationDTO, receiveTransferMoneyDTO);
    }

    /**
     * Xử lý giao dịch bán ngoại tệ và tất toán tiết kiệm không kỳ hạn (Foreign Currency Credit)
     *
     * Method này xử lý các giao dịch:
     * 1. Bán ngoại tệ (LAK, USD, THB exchange)
     * 2. Tất toán tiết kiệm không kỳ hạn (CLOSE_NT)
     *
     * @param request Thông tin notification từ LAPNET
     * @param customer Thông tin khách hàng
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param accountBalanceRequest Request kiểm tra balance
     * @param transactionType Loại giao dịch
     * @return TransactionEntryDTO chứa notification và receive transfer money DTO
     */
    private TransactionEntryDTO foreignCurrencyCredit(NotificationClientRequest request, Customer customer,
                                                      Instant transactionFinishTime, AccountBalanceRequest accountBalanceRequest,
                                                      CommonCode transactionType) {
        // Gọi API T24 để lấy danh sách account balance của customer
        List<AccountBalanceDTO> accountBalanceDTOList =
                this.apiGeeTransferService.checkAccountBalance(accountBalanceRequest);

        // Nếu không có account balance data từ T24 thì return null
        if (Validator.isNull(accountBalanceDTOList)) {
            return null;
        }

        // Tìm account balance khớp với beneficiary account number
        Optional<AccountBalanceDTO> accBalanceDTO = getAccountBalanceDTO(request, accountBalanceDTOList);

        // Nếu không tìm thấy account balance thì return null
        if (!accBalanceDTO.isPresent()) {
            return null;
        }

        // Lấy account balance DTO
        AccountBalanceDTO accountBalanceDTO = accBalanceDTO.get();

        // Kiểm tra currency trong request có khớp với account currency không
        if (!Validator.equals(request.getCurrency(), accountBalanceDTO.getAccountCurrency())) {
            return null;
        }

        // Tạo notification và receive transfer money DTO
        NotificationDTO notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
        // Set account type là không phải tiết kiệm
        receiveTransferMoneyDTO.setAccountType(AccountTypeCif.NONSAVING.getStatus());

        // Xác định title notification dựa trên transaction type
        if (Validator.equals(transactionType, CommonCode.CLOSE_NT)) {
            // Tất toán tiết kiệm không kỳ hạn
            notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CLOSE_SAVING_ACCOUNT_IS_SUCCESSFULLY));
        } else if (!validateCurrency(request, transactionType)) {
            // Bán ngoại tệ (currency validation passed)
            notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_PURCHASE_FOREIGN_CURRENCY_IS_SUCCESSFULLY));
        } else {
            // Currency validation failed
            return null;
        }

        // Trả về TransactionEntryDTO chứa cả notification và receive transfer money DTO
        return new TransactionEntryDTO(notificationDTO, receiveTransferMoneyDTO);
    }

    /**
     * Xử lý giao dịch thanh toán nội bộ ngân hàng (Internal Credit)
     *
     * Method này xử lý các trường hợp:
     * 1. Cộng tiền vào tài khoản tiết kiệm online
     * 2. Trả lương cho nhân viên
     * 3. Mở tài khoản tiết kiệm tích lũy
     * 4. Ghi có nội bộ khác
     *
     * @param request Thông tin notification từ LAPNET
     * @param customer Thông tin khách hàng
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param accountSavingRequest Request kiểm tra saving account
     * @param transactionType Loại giao dịch
     * @param commons Danh sách common transaction types
     * @return TransactionEntryDTO chứa notification và receive transfer money DTO
     */
    private TransactionEntryDTO internalCredit(NotificationClientRequest request, Customer customer, Instant transactionFinishTime,
                                               AccountSavingRequest accountSavingRequest, CommonCode transactionType,
                                               List<Common> commons) {
        // Khởi tạo các DTO để lưu kết quả xử lý
        NotificationDTO notificationDTO = null;
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO = null;

        // Trường hợp 1: Cộng tiền vào tài khoản tiết kiệm online
        // Tìm saving account online theo beneficiary account number và trạng thái ACTIVE
        SavingAccount savingAccOnline = this.savingAccountRepository
                .findBySavingAccountNumberAndStatus(request.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus());

        // Nếu tìm thấy saving account online
        if (Validator.isNotNull(savingAccOnline)) {
            // Tạo notification DTO
            notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
            // Set title thông báo cộng tiền vào tài khoản tiết kiệm thành công
            notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CREDIT_SAVING_ACCOUNT_IS_SUCCESSFULLY));
            // Tạo receive transfer money DTO
            receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
            // Set account type là tiết kiệm
            receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());

        // Trường hợp 2: Trả lương cho nhân viên
        } else if (request.getMessage().startsWith(this.notificationProperties.getSalaryMsg())) {
            // Tạo notification DTO
            notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
            // Set title thông báo trả lương thành công
            notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_PAY_SALARY_IS_SUCCESSFULLY));
            // Tạo receive transfer money DTO
            receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
            // Set account type là không phải tiết kiệm
            receiveTransferMoneyDTO.setAccountType(AccountTypeCif.NONSAVING.getStatus());

            // Lưu thông tin ghi có cho quản lý số lượng giao dịch
            this.saveCreditCustomerAccNumber(request, commons, customer, transactionFinishTime, transactionType);
        } else {
            // Trường hợp 3 & 4: Kiểm tra money account hoặc mở tài khoản tiết kiệm tích lũy
            // Tìm money account theo account number, customer ID, status và currency
            Optional<MoneyAccount> moneyAccount = this.moneyAccountRepository
                    .findByAccountNumberAndCustomerIdAndStatusAndCurrency(request.getBeneficiaryAccountNumber(),
                            customer.getCustomerId(), EntityStatus.ACTIVE.getStatus(), request.getCurrency());

            // Nếu không tìm thấy money account
            if (!moneyAccount.isPresent()) {
                // Kiểm tra có phải accumulated saving account không
                AccountSavingDTO accumulatedSaving = this.getAccumulatedSaving(accountSavingRequest);
                // Nếu tìm thấy accumulated saving
                if (accumulatedSaving != null) {
                    // Tạo notification DTO
                    notificationDTO = this.getNotificationDTO(request, transactionFinishTime, customer);
                    // Set title thông báo cộng tiền vào tài khoản tiết kiệm thành công
                    notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CREDIT_SAVING_ACCOUNT_IS_SUCCESSFULLY));
                    // Tạo receive transfer money DTO
                    receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                    // Set account type là tiết kiệm
                    receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());

                    // Mở/tạo saving accumulation account trong local database
                    this.openSavingAccumulation(request, customer, accumulatedSaving);
                }
            } else {
                // Nếu tìm thấy money account - lưu thông tin ghi có nội bộ
                this.saveCreditCustomerAccNumber(request, commons, customer, transactionFinishTime, transactionType);
            }
        }

        // Kiểm tra nếu không tạo được notification hoặc receive transfer money DTO
        if (Validator.isNull(notificationDTO) || Validator.isNull(receiveTransferMoneyDTO)) {
            // Trả về null nếu không xử lý được giao dịch
            return null;
        }

        // Trả về TransactionEntryDTO chứa cả notification và receive transfer money DTO
        return new TransactionEntryDTO(notificationDTO, receiveTransferMoneyDTO);
    }

    /**
     * Xử lý giao dịch mở TKTK cố định tại quầy (Fixed Deposit Debit)
     *
     * Method này xử lý trừ tiền từ tài khoản customer để mở tài khoản tiết kiệm cố định
     * Đồng thời sync thông tin saving account từ T24 về local database
     *
     * @param request Thông tin notification từ LAPNET
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param customer Thông tin khách hàng
     * @param accountBalanceRequest Request kiểm tra balance
     * @param formatter Date formatter để parse date
     * @param transactionType Loại giao dịch
     * @return TransactionEntryDTO chứa notification và receive transfer money DTO
     */
    private TransactionEntryDTO fixedDepositDebit(NotificationClientRequest request, Instant transactionFinishTime,
                                                  Customer customer, AccountBalanceRequest accountBalanceRequest,
                                                  DateTimeFormatter formatter, CommonCode transactionType) {
        // Khởi tạo các DTO để lưu kết quả xử lý
        NotificationDTO notificationDTO = null;
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO = null;

        // Gọi API T24 để lấy danh sách account balance của customer
        List<AccountBalanceDTO> accountBalanceDTOList =
                this.apiGeeTransferService.checkAccountBalance(accountBalanceRequest);

        // Nếu transaction ID bắt đầu với entry prefix thì sync counter saving account
        if (request.getTransactionId().startsWith(this.notificationProperties.getEntryPrefix())) {
            // Extract customer saving account number từ transaction ID (bỏ 3 ký tự đầu)
            String customerSavingAcc = request.getTransactionId().substring(3);
            // Sync counter saving account từ T24 về local database
            extracted(customer, formatter, customerSavingAcc);
        }

        // Nếu có account balance data từ T24
        if (Validator.isNotNull(accountBalanceDTOList)) {
            // Tìm account balance khớp với beneficiary account number
            Optional<AccountBalanceDTO> accBalanceDTO = getAccountBalanceDTO(request, accountBalanceDTOList);

            // Nếu tìm thấy account balance hợp lệ
            if (accBalanceDTO.isPresent()) {
                // Tạo notification DTO
                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                // Set title thông báo mở tài khoản tiết kiệm thành công
                notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_OPEN_SAVING_ACCOUNT_IS_SUCCESSFULLY));
                // Tạo receive transfer money DTO
                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set account type là không phải tiết kiệm (vì đây là tài khoản nguồn bị trừ tiền)
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.NONSAVING.getStatus());
            }
        }

        // Kiểm tra nếu không tạo được notification hoặc receive transfer money DTO
        if (Validator.isNull(notificationDTO) || Validator.isNull(receiveTransferMoneyDTO)) {
            // Trả về null nếu không xử lý được giao dịch
            return null;
        }

        // Trả về TransactionEntryDTO chứa cả notification và receive transfer money DTO
        return new TransactionEntryDTO(notificationDTO, receiveTransferMoneyDTO);
    }

    /**
     * Xử lý giao dịch tất toán tự động TKTK cố định (Debit Auto Close FD)
     *
     * Method này xử lý 3 trường hợp tất toán:
     * 1. Trừ tiền tất toán nhưng không đóng sổ (T24 saving account)
     * 2. Tất toán counter saving account
     * 3. Tất toán saving account online
     *
     * @param request Thông tin notification từ LAPNET
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param customer Thông tin khách hàng
     * @param accountSavingRequest Request kiểm tra saving account
     * @param transactionType Loại giao dịch
     * @return TransactionEntryDTO chứa notification và receive transfer money DTO
     */
    private TransactionEntryDTO debitAutoCloseFd(NotificationClientRequest request, Instant transactionFinishTime,
                                                 Customer customer, AccountSavingRequest accountSavingRequest,
                                                 CommonCode transactionType) {
        // Khởi tạo các DTO để lưu kết quả xử lý
        NotificationDTO notificationDTO = null;
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO = null;

        // Gọi API T24 để lấy danh sách saving account của customer
        List<AccountSavingDTO> accountSavingDTOS = this.apiGeeTransferService.checkAccountSaving(accountSavingRequest);

        // Trường hợp 1: Trừ tiền tất toán nhưng không đóng sổ (T24 saving account)
        if (Validator.isNotNull(accountSavingDTOS)) {
            // Tìm saving type có category là trả lãi hàng tháng hoặc cuối kỳ
            String savingType = accountSavingDTOS.stream()
                    .map(AccountSavingDTO::getCategory) // Lấy category
                    .filter(category -> Validator.equals(category, customerProperties.getCifTermMonthPayment()) // Trả lãi hàng tháng
                            || Validator.equals(category, customerProperties.getCifTermPayInterest())) // Trả lãi cuối kỳ
                    .findFirst().toString(); // Lấy category đầu tiên tìm thấy

            // Nếu tìm thấy saving type phù hợp
            if (Validator.isNotNull(savingType)) {
                // Tạo notification DTO
                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                // Set title thông báo đóng tài khoản tiết kiệm thành công
                notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CLOSE_SAVING_ACCOUNT_IS_SUCCESSFULLY));
                // Tạo receive transfer money DTO
                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set account type là tiết kiệm
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());
            }
        } else {
            // Trường hợp 2: Tất toán counter saving account
            // Tìm counter saving account theo beneficiary account number và trạng thái ACTIVE
            CounterSavingAccount savingAccount = this.counterSavingAccountRepository
                    .findBySavingAccountNumberAndStatus(request.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus());

            // Nếu tìm thấy counter saving account
            if (Validator.isNotNull(savingAccount)) {
                // Tạo notification DTO
                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                // Set title thông báo đóng tài khoản tiết kiệm thành công
                notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CLOSE_SAVING_ACCOUNT_IS_SUCCESSFULLY));
                // Tạo receive transfer money DTO
                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set account type là tiết kiệm
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());

                // Cập nhật trạng thái counter saving account thành DELETED
                savingAccount.setStatus(EntityStatus.DELETED.getStatus());
            } else {
                // Trường hợp 3: Trừ tiền tài khoản tiết kiệm online (tất toán trên app hoặc tại quầy)
                // Tìm saving account online theo beneficiary account number (loại trừ DELETED)
                SavingAccount savingAccOnline = this.savingAccountRepository
                        .findBySavingAccountNumberAndStatusNot(request.getBeneficiaryAccountNumber(), EntityStatus.DELETED.getStatus());

                // Nếu tìm thấy saving account online
                if (Validator.isNotNull(savingAccOnline)) {
                    // Tạo notification DTO
                    notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                    // Set title thông báo đóng tài khoản tiết kiệm thành công
                    notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CLOSE_SAVING_ACCOUNT_IS_SUCCESSFULLY));
                    // Tạo receive transfer money DTO
                    receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                    // Set account type là tiết kiệm
                    receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());
                }
            }
        }

        // Kiểm tra nếu không tạo được notification hoặc receive transfer money DTO
        if (Validator.isNull(notificationDTO) || Validator.isNull(receiveTransferMoneyDTO)) {
            // Trả về null nếu không xử lý được giao dịch
            return null;
        }

        // Trả về TransactionEntryDTO chứa cả notification và receive transfer money DTO
        return new TransactionEntryDTO(notificationDTO, receiveTransferMoneyDTO);
    }

    /**
     * Xử lý giao dịch trả lãi hợp đồng tiền gửi (Debit Contract Interest Pay)
     *
     * Method này xử lý trả lãi cho TKTK và TKTT:
     * 1. Trả lãi từ counter saving account hoặc saving account online
     * 2. Trả lãi từ account balance thông thường
     *
     * @param request Thông tin notification từ LAPNET
     * @param customer Thông tin khách hàng
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param accountBalanceRequest Request kiểm tra balance
     * @param transactionType Loại giao dịch
     * @param savingCategory Danh sách loại tài khoản tiết kiệm
     * @return TransactionEntryDTO chứa notification và receive transfer money DTO
     */
    private TransactionEntryDTO debitContractInterestPay(NotificationClientRequest request, Customer customer,
                                                         Instant transactionFinishTime, AccountBalanceRequest accountBalanceRequest,
                                                         CommonCode transactionType, List<String> savingCategory) {
        // Khởi tạo các DTO để lưu kết quả xử lý
        NotificationDTO notificationDTO = null;
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO = null;

        // Tìm counter saving account theo beneficiary account number và saving category
        CounterSavingAccount accountSavingDTOS = this.counterSavingAccountRepository
                .findBySavingAccountNumberAndCategoryIn(request.getBeneficiaryAccountNumber(), savingCategory);

        // Tìm saving account online theo beneficiary account number (loại trừ DELETED)
        SavingAccount savingAccOnline = this.savingAccountRepository
                .findBySavingAccountNumberAndStatusNot(request.getBeneficiaryAccountNumber(), EntityStatus.DELETED.getStatus());

        // Trường hợp 1: Trả lãi từ counter saving account hoặc saving account online
        if (Validator.isNotNull(accountSavingDTOS) || Validator.isNotNull(savingAccOnline)) {
            // Tạo notification DTO
            notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
            // Set title thông báo trả lãi hợp đồng thành công
            notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.ERROR_DEBIT_CONTRACT_INTEREST_PAY_IS_SUCCESSFULLY));
            // Tạo receive transfer money DTO
            receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
            // Set account type là tiết kiệm
            receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());
        } else {
            // Trường hợp 2: Trả lãi từ account balance thông thường
            // Gọi API T24 để lấy danh sách account balance của customer
            List<AccountBalanceDTO> accountBalanceDTOList =
                    this.apiGeeTransferService.checkAccountBalance(accountBalanceRequest);

            // Nếu có account balance data từ T24
            if (Validator.isNotNull(accountBalanceDTOList)) {
                // Tìm account balance khớp với beneficiary account number
                Optional<AccountBalanceDTO> accountBalanceDTO = getAccountBalanceDTO(request, accountBalanceDTOList);

                // Nếu tìm thấy account balance hợp lệ
                if (accountBalanceDTO.isPresent()) {
                    // Tạo notification DTO
                    notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                    // Set title thông báo trả lãi hợp đồng thành công
                    notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.ERROR_DEBIT_CONTRACT_INTEREST_PAY_IS_SUCCESSFULLY));
                    // Tạo receive transfer money DTO
                    receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                    // Set account type là không phải tiết kiệm
                    receiveTransferMoneyDTO.setAccountType(AccountTypeCif.NONSAVING.getStatus());
                }
            }
        }

        // Kiểm tra nếu không tạo được notification hoặc receive transfer money DTO
        if (Validator.isNull(notificationDTO) || Validator.isNull(receiveTransferMoneyDTO)) {
            // Trả về null nếu không xử lý được giao dịch
            return null;
        }

        // Trả về TransactionEntryDTO chứa cả notification và receive transfer money DTO
        return new TransactionEntryDTO(notificationDTO, receiveTransferMoneyDTO);
    }

    /**
     * Xử lý giao dịch quay vòng gốc + lãi, trả lãi gửi KKH tất toán chủ động TKTK cố định trước hạn (Debit Pay Interest)
     *
     * Method này xử lý 3 trường hợp:
     * 1. Trả lãi từ T24 saving account
     * 2. Trả lãi từ saving account online
     * 3. Trả lãi từ counter saving account và đóng tài khoản
     *
     * @param request Thông tin notification từ LAPNET
     * @param customer Thông tin khách hàng
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param accountSavingRequest Request kiểm tra saving account
     * @param transactionType Loại giao dịch
     * @return TransactionEntryDTO chứa notification và receive transfer money DTO
     */
    private TransactionEntryDTO debitPayInterest(NotificationClientRequest request, Customer customer, Instant transactionFinishTime,
                                                 AccountSavingRequest accountSavingRequest, CommonCode transactionType) {
        // Khởi tạo các DTO để lưu kết quả xử lý
        NotificationDTO notificationDTO = null;
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO = null;

        // Gọi API T24 để lấy danh sách saving account của customer
        List<AccountSavingDTO> accountSavingDTOS = this.apiGeeTransferService
                .checkAccountSaving(accountSavingRequest);

        // Tìm saving account online theo beneficiary account number (loại trừ DELETED)
        SavingAccount savingAccOnline = this.savingAccountRepository
                .findBySavingAccountNumberAndStatusNot(request.getBeneficiaryAccountNumber(), EntityStatus.DELETED.getStatus());

        // Trường hợp 1: Trả lãi từ T24 saving account
        if (Validator.isNotNull(accountSavingDTOS)) {
            // Tìm saving type có category là trả lãi hàng tháng hoặc cuối kỳ
            String savingType = accountSavingDTOS.stream()
                    .map(AccountSavingDTO::getCategory) // Lấy category
                    .filter(category -> Validator.equals(category, customerProperties.getCifTermMonthPayment()) // Trả lãi hàng tháng
                            || Validator.equals(category, customerProperties.getCifTermPayInterest())) // Trả lãi cuối kỳ
                    .findFirst().toString(); // Lấy category đầu tiên tìm thấy

            // Nếu tìm thấy saving type phù hợp
            if (Validator.isNotNull(savingType)) {
                // Tạo notification DTO
                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                // Set title thông báo trả lãi thành công
                notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_INTEREST_PAID_IS_SUCCESSFULLY));
                // Tạo receive transfer money DTO
                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set account type là tiết kiệm
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());
            }
        // Trường hợp 2: Trả lãi từ saving account online
        } else if (Validator.isNotNull(savingAccOnline)) {
            // Tạo notification DTO
            notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
            // Set title thông báo trả lãi thành công
            notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_INTEREST_PAID_IS_SUCCESSFULLY));
            // Tạo receive transfer money DTO
            receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
            // Set account type là tiết kiệm
            receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());
        } else {
            // Trường hợp 3: Trả lãi từ counter saving account và đóng tài khoản
            // Tìm counter saving account theo beneficiary account number và trạng thái ACTIVE
            CounterSavingAccount savingAccount = this.counterSavingAccountRepository
                    .findBySavingAccountNumberAndStatus(request.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus());

            // Nếu tìm thấy counter saving account
            if (Validator.isNotNull(savingAccount)) {
                // Tạo notification DTO
                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                // Set title thông báo trả lãi thành công
                notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_INTEREST_PAID_IS_SUCCESSFULLY));
                // Tạo receive transfer money DTO
                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set account type là tiết kiệm
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());

                // Cập nhật trạng thái counter saving account thành DELETED (đóng tài khoản)
                savingAccount.setStatus(EntityStatus.DELETED.getStatus());
            }
        }

        // Kiểm tra nếu không tạo được notification hoặc receive transfer money DTO
        if (Validator.isNull(notificationDTO) || Validator.isNull(receiveTransferMoneyDTO)) {
            // Trả về null nếu không xử lý được giao dịch
            return null;
        }

        // Trả về TransactionEntryDTO chứa cả notification và receive transfer money DTO
        return new TransactionEntryDTO(notificationDTO, receiveTransferMoneyDTO);
    }

    /**
     * Xử lý giao dịch tất toán tiết kiệm tích lũy tại quầy (Debit Close Account)
     *
     * Method này xử lý đóng tài khoản tiết kiệm tích lũy:
     * - Counter saving account (accumulated saving)
     * - Saving account online
     *
     * @param request Thông tin notification từ LAPNET
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param customer Thông tin khách hàng
     * @param transactionType Loại giao dịch
     * @return TransactionEntryDTO chứa notification và receive transfer money DTO
     */
    private TransactionEntryDTO debitCloseAcc(NotificationClientRequest request, Instant transactionFinishTime,
                                              Customer customer, CommonCode transactionType) {
        // Khởi tạo các DTO để lưu kết quả xử lý
        NotificationDTO notificationDTO = null;
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO = null;

        // Tìm counter saving account (accumulated saving) theo beneficiary account number và trạng thái ACTIVE
        CounterSavingAccount accumulatedSaving = this.counterSavingAccountRepository
                .findBySavingAccountNumberAndStatus(request.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus());

        // Tìm saving account online theo beneficiary account number (loại trừ DELETED)
        SavingAccount savingAccOnline = this.savingAccountRepository
                .findBySavingAccountNumberAndStatusNot(request.getBeneficiaryAccountNumber(), EntityStatus.DELETED.getStatus());

        // Nếu tìm thấy accumulated saving hoặc saving account online
        if (Stream.of(accumulatedSaving, savingAccOnline).anyMatch(Validator::isNotNull)) {
            // Tạo notification DTO
            notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
            // Set title thông báo đóng tài khoản tiết kiệm thành công
            notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CLOSE_SAVING_ACCOUNT_IS_SUCCESSFULLY));
            // Tạo receive transfer money DTO
            receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
            // Set account type là tiết kiệm
            receiveTransferMoneyDTO.setAccountType(AccountTypeCif.SAVING.getStatus());

            // Nếu có accumulated saving thì cập nhật trạng thái thành DELETED
            Optional.ofNullable(accumulatedSaving)
                    .ifPresent(saving -> saving.setStatus(EntityStatus.DELETED.getStatus()));
        }

        // Kiểm tra nếu không tạo được notification hoặc receive transfer money DTO
        if (Validator.isNull(notificationDTO) || Validator.isNull(receiveTransferMoneyDTO)) {
            // Trả về null nếu không xử lý được giao dịch
            return null;
        }

        // Trả về TransactionEntryDTO chứa cả notification và receive transfer money DTO
        return new TransactionEntryDTO(notificationDTO, receiveTransferMoneyDTO);
    }

    /**
     * Xử lý giao dịch trừ tiền thu phí SMS biến động số dư (Internal Debit)
     *
     * Method này xử lý thu phí SMS balance service:
     * - Cập nhật service fee cho SMS balance
     * - Tạo notification thông báo thu phí thành công
     *
     * @param request Thông tin notification từ LAPNET
     * @param customer Thông tin khách hàng
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param transactionType Loại giao dịch
     * @return TransactionEntryDTO chứa notification và receive transfer money DTO
     */
    private TransactionEntryDTO internalDebit(NotificationClientRequest request, Customer customer,
                                              Instant transactionFinishTime, CommonCode transactionType) {
        // Khởi tạo các DTO để lưu kết quả xử lý
        NotificationDTO notificationDTO = null;
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO = null;

        // Kiểm tra nếu message bắt đầu với SMS balance message prefix
        if (request.getMessage().startsWith(this.notificationProperties.getSmsBalanceMsg())) {
            // Tìm SMS balance theo phone number, account number và trạng thái ACTIVE
            SmsBalance smsBalance = this.smsBalanceRepository.findByPhoneNumberAndCustomerAccountNumberAndStatus(
                    customer.getPhoneNumber(), request.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus()
            );

            // Nếu tìm thấy SMS balance
            if (Validator.isNotNull(smsBalance)) {
                // Cập nhật service fee cho SMS balance
                smsBalance.setServiceFee(Double.valueOf(request.getAmount()));
                // Tạo notification DTO
                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                // Set title thông báo thu phí SMS balance thành công
                notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_SMS_BALANCE_CHAGRE_FEE_SUCCESSFULLY));
                // Tạo receive transfer money DTO
                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set account type là không phải tiết kiệm
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.NONSAVING.getStatus());
            }
        }

        // Kiểm tra nếu không tạo được notification hoặc receive transfer money DTO
        if (Validator.isNull(notificationDTO) || Validator.isNull(receiveTransferMoneyDTO)) {
            // Trả về null nếu không xử lý được giao dịch
            return null;
        }

        // Trả về TransactionEntryDTO chứa cả notification và receive transfer money DTO
        return new TransactionEntryDTO(notificationDTO, receiveTransferMoneyDTO);
    }

    /**
     * Xử lý giao dịch rút tiền mặt tại quầy, mua ngoại tệ, thu phí dịch vụ (Foreign Currency Debit)
     *
     * Method này xử lý 2 trường hợp chính:
     * 1. Thu phí TKSD (tài khoản số đẹp) thành công
     * 2. Các giao dịch debit khác: rút tiền, mua ngoại tệ, thu phí dịch vụ
     *
     * @param request Thông tin notification từ LAPNET
     * @param customer Thông tin khách hàng
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param accountBalanceRequest Request kiểm tra balance
     * @param transactionType Loại giao dịch
     * @return TransactionEntryDTO chứa notification và receive transfer money DTO
     */
    private TransactionEntryDTO foreignCurrencyDebit(NotificationClientRequest request, Customer customer,
                                                     Instant transactionFinishTime, AccountBalanceRequest accountBalanceRequest,
                                                     CommonCode transactionType) {
        // Khai báo các DTO (sẽ được khởi tạo trong các trường hợp xử lý)
        NotificationDTO notificationDTO;
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO;

        // Tìm premium account number theo beneficiary account number và trạng thái PENDING
        PremiumAccNumber premiumAccNumber = this.premiumAccNumberRepository
                .findByPremiumAccNumberAndStatus(request.getBeneficiaryAccountNumber(), PremiumAccNumberStatus.PENDING.getValue());

        // Trường hợp 1: Thu phí TKSD thành công => chuyển trạng thái pending sang active
        if (Validator.isNotNull(premiumAccNumber) && Validator.equals(transactionType, CommonCode.SERVICE_FEE)) {
            // Cập nhật trạng thái premium account number từ PENDING sang ACTIVE
            PremiumAccNumber premiumAccNumberActive = this.updateStatusPremiumAccNumber(premiumAccNumber, request, transactionFinishTime);

            // Nếu không cập nhật được trạng thái thì return null
            if (Validator.isNull(premiumAccNumberActive)) {
                return null;
            }

            // Tạo notification DTO
            notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
            // Set title thông báo thu phí dịch vụ thành công
            notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_SERVICE_FEE_CHARGED_IS_SUCCESSFULLY));
            // Tạo receive transfer money DTO
            receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
            // Set account type là không phải tiết kiệm
            receiveTransferMoneyDTO.setAccountType(AccountTypeCif.NONSAVING.getStatus());
        } else {
            // Trường hợp 2: Các giao dịch debit khác (rút tiền, mua ngoại tệ, thu phí dịch vụ)
            // Gọi API T24 để lấy danh sách account balance của customer
            List<AccountBalanceDTO> accountBalanceDTOList =
                    this.apiGeeTransferService.checkAccountBalance(accountBalanceRequest);

            // Nếu không có account balance data từ T24 thì return null
            if (Validator.isNull(accountBalanceDTOList)) {
                return null;
            }

            // Tìm account balance khớp với beneficiary account number
            Optional<AccountBalanceDTO> accBalanceDTO = getAccountBalanceDTO(request, accountBalanceDTOList);

            // Nếu không tìm thấy account balance thì return null
            if (!accBalanceDTO.isPresent()) {
                return null;
            }

            // Kiểm tra currency trong request có khớp với account currency không
            if (!Validator.equals(request.getCurrency(), accBalanceDTO.get().getAccountCurrency())) {
                return null;
            }

            // Tạo notification và receive transfer money DTO
            notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
            receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
            // Set account type là không phải tiết kiệm
            receiveTransferMoneyDTO.setAccountType(AccountTypeCif.NONSAVING.getStatus());

            // Xác định title notification dựa trên transaction type
            switch (transactionType) {
                case CASH_WITHDRAWAL:
                    // Rút tiền mặt tại quầy
                    notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CASH_WITHDRAWAL_IS_SUCCESSFULLY));
                    break;
                case SERVICE_FEE:
                    // Thu phí dịch vụ
                    notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_SERVICE_FEE_CHARGED_IS_SUCCESSFULLY));
                    break;
                case LAK_EXCHANGE:
                case USD_EXCHANGE:
                case THB_EXCHANGE:
                    // Mua ngoại tệ (bán LAK/USD/THB)
                    notificationDTO.setTitle(Labels.getLabelsEn(LabelKey.LABEL_CURRENCY_SOLD_IS_SUCCESSFULLY));
                    break;
                default:
                    // Các trường hợp khác (không set title)
            }
        }

        // Trả về TransactionEntryDTO chứa cả notification và receive transfer money DTO
        return new TransactionEntryDTO(notificationDTO, receiveTransferMoneyDTO);
    }

    /**
     * Validate request để kiểm tra tính hợp lệ của notification request
     *
     * Method này kiểm tra các điều kiện:
     * 1. Transaction fee phải đúng format và >= 0
     * 2. Balance phải đúng format
     * 3. Transaction time không được trong tương lai
     * 4. Currency phải có trong danh sách currencies được hỗ trợ
     *
     * @param request Notification request cần validate
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param currencies Danh sách currencies được hỗ trợ
     * @return true nếu request invalid (cần skip), false nếu request valid
     */
    private boolean isInvalidRequest(NotificationClientRequest request, Instant transactionFinishTime, List<Currency> currencies) {
        return (Validator.isNotNull(request.getTransactionFee()) // Nếu có transaction fee
                && (!request.getTransactionFee().matches(ValidationConstraint.PATTERN.FEE_REGEX) // Fee không đúng format
                || Double.parseDouble(request.getTransactionFee()) < 0)) // Hoặc fee < 0
                || (Validator.isNotNull(request.getBalance()) // Nếu có balance
                && !request.getBalance().matches(ValidationConstraint.PATTERN.BALANCE_REGEX)) // Balance không đúng format
                || (Validator.isNotNull(transactionFinishTime) // Nếu có transaction time
                && transactionFinishTime.isAfter(Instant.now())) // Transaction time trong tương lai
                || currencies.stream().noneMatch(currency -> Validator.equals(currency.getCode(), request.getCurrency())); // Currency không được hỗ trợ
    }

    /**
     * Xử lý thông báo balance từ LAPNET client
     *
     * Method này xử lý thông báo về số dư tài khoản từ LAPNET:
     * 1. Validate client authentication (chỉ accept từ "mbbank")
     * 2. Parse danh sách notification requests
     * 3. Xác định loại giao dịch từ transaction type
     * 4. Tạo notification content phù hợp
     * 5. Lưu notification và transaction history
     * 6. Gửi push notification cho khách hàng
     *
     * Các loại giao dịch được xử lý:
     * - Chuyển tiền nội bộ và liên ngân hàng
     * - Mở/đóng tài khoản tiết kiệm
     * - Mua/bán ngoại tệ
     * - Thu phí dịch vụ
     *
     * @param notificationRequest Danh sách notification từ LAPNET
     * @return NotificationLapnetDTO Kết quả xử lý notification
     * @throws BadRequestAlertException Khi client không được authorize
     */
    @Override // Ghi đè method từ interface NotificationGatewayService
    @Transactional // Đảm bảo tất cả thao tác database trong method này được thực hiện trong một transaction
    public NotificationLapnetDTO getNotificationClientBalance(NotificationBalanceClientRequest notificationRequest) {
        // Khởi tạo các danh sách để lưu kết quả xử lý
        List<NotificationDTO> notificationDTOS = new ArrayList<>(); // Danh sách notification sẽ được tạo
        List<ReceiveTransferMoneyDTO> receiveTransferMoneyDTOS = new ArrayList<>(); // Danh sách lịch sử nhận tiền
        List<ReceiveTransferMoneyDTO> merchantHistories = new ArrayList<>(); // Danh sách lịch sử merchant
        NotificationLapnetDTO notificationLapnetDTO = new NotificationLapnetDTO(); // Response DTO
        Customer customer = null; // Biến lưu thông tin customer hiện tại

        // Lấy tên client từ Spring Security Context để xác thực
        String clientName = SecurityContextHolder.getContext().getAuthentication().getName();

        // Lấy danh sách các loại giao dịch từ bảng common
        List<Common> commons = this.commonRepository.findAllByCategory(CommonCategory.TRANSACTION_TYPE.name());
        // Lấy danh sách tiền tệ đang hoạt động
        List<Currency> currencies = this.currencyRepository.findAllByStatus(EntityStatus.ACTIVE.getStatus());
        // Lấy danh sách loại tài khoản tiết kiệm từ bảng common và chuyển thành list string
        List<String> savingCategory = this.commonRepository.findAllByCategory(CommonCategory.ACCOUNT_SAVING.name())
                .stream().map(Common::getValue).collect(Collectors.toList());

        // Extract danh sách CIF từ tất cả notification requests
        List<String> cifs = notificationRequest.getNotifications().stream().map(NotificationClientRequest::getCif)
                .collect(Collectors.toList());

        // Tạo formatter để parse thời gian theo pattern không có dấu gạch ngang
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.SHORT_TIMESTAMP_PATTERN_NON_DASH);

        // Khởi tạo danh sách customer
        List<Customer> customers = new ArrayList<>();
        // Nếu có danh sách CIF thì tìm customer tương ứng
        if (Validator.isNotNull(cifs)) {
            // Tìm tất cả customer theo CIF, trạng thái ACTIVE, đã được approval và không có reference ID
            customers = this.customerRepository.findAllByCifInAndStatusAndApprovalStatusAndReferenceIdIsNull(
                    cifs, EntityStatus.ACTIVE.getStatus(), CustomerApprovalStatus.APPROVAL.getStatus());
        }

        // Kiểm tra client authentication - chỉ accept từ "mbbank"
        if (clientName.equals("mbbank")) {
            // Duyệt qua từng notification request trong danh sách
            for (NotificationClientRequest request : notificationRequest.getNotifications()) {
                // Khai báo các biến để lưu kết quả xử lý cho từng request
                NotificationDTO notificationDTO; // DTO notification sẽ được tạo
                ReceiveTransferMoneyDTO receiveTransferMoneyDTO; // DTO lịch sử nhận tiền
                ReceiveTransferMoneyDTO merchantHistory; // DTO lịch sử merchant

                // Validate và parse thời gian giao dịch từ string sang Instant
                Instant transactionFinishTime = this.validateTransactionTime(request.getTransactionTime());
                // Xác định loại giao dịch từ transaction type trong request
                CommonCode transactionType = this.getTransactionType(request, commons);
                // Khởi tạo DTO để lưu kết quả xử lý giao dịch
                TransactionEntryDTO transactionEntry = new TransactionEntryDTO();

                // Tạo request để kiểm tra tài khoản tiết kiệm
                AccountSavingRequest accountSavingRequest = AccountSavingRequest.builder()
                        .cusAccNo(request.getBeneficiaryAccountNumber()) // Số tài khoản thụ hưởng
                        .build();

                // Xử lý giao dịch CREDIT (ghi có - tiền vào tài khoản)
                if (Validator.equals(request.getDrCr(), DrCrType.C.name())) {
                    // Nếu có CIF trong request thì xử lý các giao dịch liên quan đến customer
                    if (Validator.isNotNull(request.getCif())) {
                        // Tìm customer từ danh sách đã load trước đó
                        customer = getCustomer(customers, request);
                        // Tạo request để kiểm tra balance tài khoản
                        AccountBalanceRequest accountBalanceRequest = getAccountBalanceRequest(request.getCif());

                        // Xử lý giao dịch trả lãi tiết kiệm (đáo hạn không kỳ hạn, quay vòng gốc, quay vòng gốc + lãi, trả lãi tiền gửi)
                        if (Validator.equals(transactionType, CommonCode.CREDIT_PAY_INTEREST)) {
                            transactionEntry = this.creditPayInterest(request, transactionFinishTime,
                                    customer, savingCategory, transactionType);
                        }

                        // Xử lý giao dịch tất toán tiết kiệm cố định tự động
                        if (Validator.equals(transactionType, CommonCode.CREDIT_AUTO_CLOSE_FD)) {
                            transactionEntry = this.creditAutoCloseFd(request, transactionFinishTime,
                                    customer, accountBalanceRequest, transactionType);
                        }

                        // Xử lý giao dịch nạp tiền tại quầy (TKTT, TKSD, TKTK)
                        if (Validator.equals(transactionType, CommonCode.CASH_DEPOSIT)) {
                            transactionEntry = this.cashDeposit(request, customer, transactionFinishTime,
                                    accountBalanceRequest, accountSavingRequest, transactionType);
                        }

                        // Xử lý giao dịch mở tiết kiệm cố định tại quầy
                        if (Validator.equals(transactionType, CommonCode.FIXED_DEPOSIT_CREDIT)) {
                            transactionEntry = this.fixedDepositCredit(request, customer, transactionFinishTime,
                                    accountSavingRequest, transactionType, formatter);
                        }

                        // Xử lý giao dịch bán ngoại tệ, tất toán tiết kiệm không kỳ hạn, tích lũy tại quầy
                        if (Validator.equals(transactionType, CommonCode.LAK_EXCHANGE)
                                || Validator.equals(transactionType, CommonCode.THB_EXCHANGE)
                                || Validator.equals(transactionType, CommonCode.USD_EXCHANGE)
                                || Validator.equals(transactionType, CommonCode.CLOSE_NT)) {
                            transactionEntry = this.foreignCurrencyCredit(request, customer, transactionFinishTime,
                                    accountBalanceRequest, transactionType);
                        }

                        // Xử lý giao dịch thanh toán nội bộ (công tiền vào TKTK, mở tài khoản tiết kiệm tích lũy, tài khoản chi hộ)
                        if (Validator.equals(request.getTransactionType(), this.notificationProperties.getTransactionType().getInternalBank())) {
                            transactionEntry = this.internalCredit(request, customer, transactionFinishTime,
                                    accountSavingRequest, transactionType, commons);
                        }
                    }

                    // Xử lý giao dịch nhận tiền từ ngân hàng khác (Inter-bank transfer)
                    if (Validator.equals(request.getTransactionType(), this.notificationProperties.getTransactionType().getInterBank())) {
                        // Validate request - nếu invalid thì skip request này
                        if (this.isInvalidRequest(request, transactionFinishTime, currencies)) continue;

                        // Nếu có thông tin customer
                        if (Validator.isNotNull(customer)) {
                            // Validate thông tin customer - nếu invalid thì skip
                            if (this.validateCustomerInfo(request, customer)) continue;

                            // Khởi tạo biến để lưu thông tin account balance
                            Optional<AccountBalanceDTO> accountBalanceDTO = Optional.empty();

                            // Kiểm tra tài khoản merchant (tài khoản thụ hưởng) - chỉ lấy master merchant
                            Merchant merchant = this.merchantRepository
                                    .findByMerchantAccountNumberAndCustomerIdAndStatusNotAndParentIdNull(
                                            request.getBeneficiaryAccountNumber(), // Số tài khoản thụ hưởng
                                            customer.getCustomerId(), // ID customer
                                            EntityStatus.DELETED.getStatus()); // Loại trừ merchant đã xóa

                            // Nếu không phải tài khoản merchant
                            if (Validator.isNull(merchant)) {
                                // Kiểm tra tài khoản thụ hưởng (tài khoản thông thường trên app)
                                AccountBalanceRequest accountBalanceRequest = getAccountBalanceRequest(customer.getCif());

                                // Gọi API T24 để lấy danh sách tài khoản của customer
                                List<AccountBalanceDTO> accountBalanceDTOList =
                                        this.apiGeeTransferService.checkAccountBalance(accountBalanceRequest);

                                // Nếu có danh sách tài khoản
                                if (Validator.isNotNull(accountBalanceDTOList)) {
                                    // Tìm tài khoản khớp với currency và account number trong request
                                    accountBalanceDTO = accountBalanceDTOList.stream()
                                            .filter(account -> Validator.equals(account.getAccountCurrency(), request.getCurrency())
                                                    && Validator.equals(account.getAccountNumber(), request.getBeneficiaryAccountNumber()))
                                            .findFirst();
                                }
                            }

                            // Nếu tìm thấy merchant hoặc account balance hợp lệ
                            if (Validator.isNotNull(merchant) || accountBalanceDTO.isPresent()) {
                                // Tạo notification DTO cho giao dịch nhận tiền
                                notificationDTO = getNotificationDTO(request, transactionFinishTime, customer);
                                // Tạo receive transfer money DTO để lưu lịch sử
                                receiveTransferMoneyDTO = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                                // Set loại tài khoản là không phải tiết kiệm
                                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.NONSAVING.getStatus());

                                // Thêm vào danh sách kết quả
                                notificationDTOS.add(notificationDTO);
                                receiveTransferMoneyDTOS.add(receiveTransferMoneyDTO);
                            }
                        } else {
                            // Trường hợp không có customer (có thể là merchant transaction)
                            // Tìm merchant theo account number (không cần customer ID)
                            Merchant merchant = this.merchantRepository.findByMerchantAccountNumberAndStatusNotAndParentIdNull(
                                    request.getBeneficiaryAccountNumber(), EntityStatus.DELETED.getStatus());

                            // Nếu không phải merchant account thì tạo merchant history
                            if (Validator.isNull(merchant)) {
                                merchantHistory = getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                                merchantHistory.setAccountType(AccountTypeCif.NONSAVING.getStatus());
                                merchantHistories.add(merchantHistory);
                            }
                        }
                    }

                    // Xử lý kết quả từ transaction entry (các giao dịch đã xử lý ở trên)
                    List<NotificationDTO> finalNotificationDTOS = notificationDTOS;
                    Optional.ofNullable(transactionEntry)
                            // Chỉ xử lý nếu có đầy đủ notification và receive transfer money DTO
                            .filter(entry -> Validator.isNotNull(entry.getNotificationDTO()) && Validator.isNotNull(entry.getReceiveTransferMoneyDTO()))
                            .ifPresent(entry -> {
                                // Thêm notification và receive transfer money vào danh sách kết quả
                                finalNotificationDTOS.add(entry.getNotificationDTO());
                                receiveTransferMoneyDTOS.add(entry.getReceiveTransferMoneyDTO());
                            });

                // Xử lý giao dịch DEBIT (ghi nợ - tiền ra khỏi tài khoản)
                } else {
                    // Nếu có CIF trong request thì xử lý các giao dịch DEBIT liên quan đến customer
                    if (Validator.isNotNull(request.getCif())) {
                        // Tìm customer từ danh sách đã load trước đó
                        customer = getCustomer(customers, request);
                        // Tạo request để kiểm tra balance tài khoản
                        AccountBalanceRequest accountBalanceRequest = getAccountBalanceRequest(request.getCif());

                        // Xử lý giao dịch mở TKTK cố định tại quầy (trừ tiền để mở)
                        if (Validator.equals(transactionType, CommonCode.FIXED_DEPOSIT_DEBIT)) {
                            transactionEntry = this.fixedDepositDebit(request, transactionFinishTime, customer,
                                    accountBalanceRequest, formatter, transactionType);
                        }

                        // Xử lý giao dịch tất toán tự động TKTK cố định (trừ tiền để tất toán)
                        if (Validator.equals(transactionType, CommonCode.DEBIT_AUTO_CLOSE_FD)) {
                            transactionEntry = this.debitAutoCloseFd(request, transactionFinishTime, customer,
                                    accountSavingRequest, transactionType);
                        }

                        // Xử lý giao dịch trả lãi hợp đồng tiền gửi (TKTK + TKTT)
                        if (Validator.equals(transactionType, CommonCode.DEBIT_CONTRACT_INTEREST_PAY)) {
                            transactionEntry = this.debitContractInterestPay(request, customer, transactionFinishTime,
                                    accountBalanceRequest, transactionType, savingCategory);
                        }

                        // Xử lý giao dịch quay vòng gốc + lãi, trả lãi gửi KKH tất toán chủ động TKTK cố định trước hạn
                        if (Validator.equals(transactionType, CommonCode.DEBIT_PAY_INTEREST)) {
                            transactionEntry = this.debitPayInterest(request, customer, transactionFinishTime,
                                    accountSavingRequest, transactionType);
                        }

                        // Xử lý giao dịch tất toán tiết kiệm tích lũy tại quầy
                        if (Validator.equals(transactionType, CommonCode.DEBIT_CLOSE_ACC)) {
                            transactionEntry = this.debitCloseAcc(request, transactionFinishTime, customer, transactionType);
                        }

                        // Xử lý giao dịch trừ tiền thu phí SMS biến động số dư (internal debit)
                        if (Validator.equals(request.getTransactionType(), this.notificationProperties.getTransactionType().getInternalBank())) {
                            transactionEntry = this.internalDebit(request, customer, transactionFinishTime, transactionType);
                        }

                        // Xử lý giao dịch rút tiền mặt tại quầy, mua ngoại tệ, thu phí dịch vụ
                        if (Validator.equals(transactionType, CommonCode.CASH_WITHDRAWAL)
                                || Validator.equals(transactionType, CommonCode.LAK_EXCHANGE)
                                || Validator.equals(transactionType, CommonCode.USD_EXCHANGE)
                                || Validator.equals(transactionType, CommonCode.THB_EXCHANGE)
                                || Validator.equals(transactionType, CommonCode.SERVICE_FEE)) {
                            transactionEntry = this.foreignCurrencyDebit(request, customer, transactionFinishTime, accountBalanceRequest,
                                    transactionType);
                        }

                        // Kiểm tra xem transaction entry có được tạo thành công không
                        boolean isTransactionEntry = Optional.ofNullable(transactionEntry)
                                .map(TransactionEntryDTO::getNotificationDTO).isPresent() // Có notification DTO
                                || Optional.ofNullable(transactionEntry)
                                .map(TransactionEntryDTO::getReceiveTransferMoneyDTO).isPresent(); // Có receive transfer money DTO

                        // Nếu có transaction entry hợp lệ thì thêm vào danh sách kết quả
                        if (isTransactionEntry) {
                            notificationDTOS.add(transactionEntry.getNotificationDTO());
                            receiveTransferMoneyDTOS.add(transactionEntry.getReceiveTransferMoneyDTO());
                        }
                    }
                }
            } // Kết thúc loop xử lý từng notification request

            // Lưu tất cả notification vào database
            List<Notification> notificationSaves = this.notificationRepository.saveAll(this.notificationMapper.toEntity(notificationDTOS));
            // Lưu tất cả receive transfer money vào database
            List<ReceiveTransferMoney> transferMonies = this.receiveTransferMoneyRepository.saveAll(this.receiveTransferMoneyMapper.toEntity(receiveTransferMoneyDTOS));
            // Lưu tất cả merchant history vào database
            this.receiveTransferMoneyRepository.saveAll(receiveTransferMoneyMapper.toEntity(merchantHistories));

            // Tạo danh sách notification transaction DTO để liên kết notification với receive transfer money
            List<NotiTransactionDTO> notiTransactionDTOS = getNotiTransactionDTOS(notificationSaves, transferMonies);
            // Lưu tất cả notification transaction vào database
            this.notiTransactionRepository.saveAll(this.notiTransactionMapper.toEntity(notiTransactionDTOS));
            // Chuyển đổi notification entities thành DTOs để trả về
            notificationDTOS = this.notificationMapper.toDto(notificationSaves);
            // Gửi notification đến từng customer qua push notification/SMS
            notificationDTOS.forEach(this.sendService::sendNotification);
        } else {
            // Throw exception nếu client không được phép truy cập
            throw new BadRequestAlertException(ErrorCode.MSG101171);
        }

        // Set danh sách notification vào response DTO
        notificationLapnetDTO.setNotifications(notificationDTOS);
        // Set client message ID để tracking
        notificationLapnetDTO.setClientMessageId(DiagnosticContextUtil.getClientMessageId());
        // Trả về response chứa tất cả notification đã được xử lý
        return notificationLapnetDTO;
    }

    /**
     * Tạo danh sách NotiTransactionDTO để liên kết notification với receive transfer money
     *
     * @param notificationSaves Danh sách notification đã được lưu
     * @param transferMonies Danh sách receive transfer money đã được lưu
     * @return Danh sách NotiTransactionDTO
     */
    @NotNull
    private static List<NotiTransactionDTO> getNotiTransactionDTOS(List<Notification> notificationSaves, List<ReceiveTransferMoney> transferMonies) {
        // Khởi tạo danh sách kết quả
        List<NotiTransactionDTO> notiTransactionDTOS = new ArrayList<>();
        // Duyệt qua từng notification (số lượng notification = số lượng transfer money)
        for (int i = 0; i < notificationSaves.size(); i++) {
            // Tạo DTO để liên kết notification với receive transfer money
            NotiTransactionDTO notiTransaction = new NotiTransactionDTO();
            // Set notification ID
            notiTransaction.setNotificationId(notificationSaves.get(i).getNotificationId());
            // Set receive transfer money ID
            notiTransaction.setReceiveTransferMoneyId(transferMonies.get(i).getReceiveTransferMoneyId());
            // Set trạng thái active
            notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus());
            // Set transaction ID = 0 (không liên kết với transaction cụ thể)
            notiTransaction.setTransactionId(0L);
            // Thêm vào danh sách kết quả
            notiTransactionDTOS.add(notiTransaction);
        }
        return notiTransactionDTOS;
    }

    /**
     * Validate thông tin customer trong request
     *
     * @param request Notification request
     * @param customer Customer entity
     * @return true nếu invalid (cần skip), false nếu valid
     */
    private boolean validateCustomerInfo(NotificationClientRequest request, Customer customer) {
        // Kiểm tra sector value có khớp với customer sector ID không
        if (Validator.isNotNull(request.getSectorValue())
                && !Validator.equals(customer.getCustomerSectorId(), Integer.valueOf(request.getSectorValue()))) {
            return true; // Invalid - sector không khớp
        }

        // Kiểm tra phone number có khớp với username và đúng format không
        return Validator.isNotNull(request.getPhone()) &&
                (!Validator.equals(customer.getUsername(), request.getPhone()) // Username không khớp với phone
                        || !(request.getPhone().matches(ValidationConstraint.PATTERN.PHONE_NUMBER_REGEX))); // Phone format không đúng
    }

    /**
     * Validate currency theo loại giao dịch exchange
     *
     * @param request Notification request
     * @param transactionType Loại giao dịch
     * @return true nếu currency không khớp với loại exchange
     */
    private boolean validateCurrency(NotificationClientRequest request, CommonCode transactionType) {
        return (Validator.equals(transactionType, CommonCode.LAK_EXCHANGE) // Giao dịch exchange LAK
                && (!Validator.equals(request.getCurrency(), CurrencyType.LAK.name()))) // Nhưng currency không phải LAK
                || (Validator.equals(transactionType, CommonCode.USD_EXCHANGE) // Giao dịch exchange USD
                && (!Validator.equals(request.getCurrency(), CurrencyType.USD.name()))) // Nhưng currency không phải USD
                || (Validator.equals(transactionType, CommonCode.THB_EXCHANGE) // Giao dịch exchange THB
                && (!Validator.equals(request.getCurrency(), CurrencyType.THB.name()))); // Nhưng currency không phải THB
    }

    /**
     * Tạo hoặc cập nhật counter saving account từ T24 data
     * Method này sync dữ liệu tài khoản tiết kiệm từ T24 về local database
     *
     * @param customer Customer entity
     * @param formatter Date formatter để parse date
     * @param customerSavingAcc Số tài khoản tiết kiệm
     */
    private void extracted(Customer customer, DateTimeFormatter formatter, String customerSavingAcc) {
        // Kiểm tra counter saving account đã tồn tại trong local database chưa
        CounterSavingAccount counterSavingAcc = this.counterSavingAccountRepository
                .findBySavingAccountNumberAndCategoryIn(customerSavingAcc,
                        Arrays.asList(customerProperties.getCifTermMonthPayment(), // Category trả lãi hàng tháng
                                customerProperties.getCifTermPayInterest())); // Category trả lãi cuối kỳ

        // Nếu đã tồn tại thì không cần tạo mới
        if (Validator.isNotNull(counterSavingAcc)) {
            return;
        }

        // Tạo request để lấy thông tin saving account từ T24
        AccountSavingRequest accountSavingRequest = AccountSavingRequest.builder()
                .cusAccNo(customerSavingAcc) // Số tài khoản tiết kiệm
                .build();

        // Gọi API T24 để lấy thông tin saving account
        List<AccountSavingDTO> savingAccDtos = this.apiGeeTransferService
                .checkAccountSaving(accountSavingRequest);

        // Nếu không có data từ T24 thì return
        if (Validator.isNull(savingAccDtos)) {
            return;
        }

        // Tìm saving account có category phù hợp
        Optional<AccountSavingDTO> dto = savingAccDtos.stream()
                .filter(value -> (Validator.equals(value.getCategory(), customerProperties.getCifTermMonthPayment()) // Trả lãi hàng tháng
                        || Validator.equals(value.getCategory(), customerProperties.getCifTermPayInterest()))) // Trả lãi cuối kỳ
                .findFirst();

        // Nếu tìm thấy saving account phù hợp
        if (dto.isPresent()) {
            // Khởi tạo biến thời gian
            Instant startTimeIns = null;
            Instant enTimeIns = null;

            // Parse start time từ value date
            if (Validator.isNotNull(dto.get().getValueDate())) {
                LocalDate startTime = LocalDate.parse(dto.get().getValueDate(), formatter);
                startTimeIns = startTime.atStartOfDay(ZoneId.of(StringPool.UTC)).toInstant();
            }

            // Parse end time từ maturity date
            if (Validator.isNotNull(dto.get().getMaturityDate())) {
                LocalDate endTime = LocalDate.parse(dto.get().getValueDate(), formatter); // Note: Có thể là bug, nên dùng getMaturityDate()
                enTimeIns = endTime.atStartOfDay(ZoneId.of(StringPool.UTC)).toInstant();
            }

            // Tạo DTO cho counter saving account mới
            CounterSavingAccountDTO savingAccNew = CounterSavingAccountDTO.builder()
                    .savingAccountNumber(customerSavingAcc) // Số tài khoản tiết kiệm
                    .customerId(customer.getCustomerId()) // ID customer
                    .startTime(startTimeIns) // Thời gian bắt đầu
                    .settlementDueTime(enTimeIns) // Thời gian đáo hạn
                    .interestRate(Validator.isNotNull(dto.get().getInterestRate()) ? Double.valueOf(dto.get().getInterestRate()) : null) // Lãi suất
                    .savingAmount(Validator.isNotNull(dto.get().getWorkingBal()) ? Double.valueOf(dto.get().getWorkingBal()) : null) // Số tiền tiết kiệm
                    .category(dto.get().getCategory()) // Loại tài khoản tiết kiệm
                    .status(EntityStatus.ACTIVE.getStatus()) // Trạng thái active
                    .build();

            // Lưu counter saving account mới vào database
            this.counterSavingAccountRepository.save(this.counterSavingAccountMapper.toEntity(savingAccNew));
        }
    }

    /**
     * Lấy thông tin accumulated saving account
     *
     * @param accountSavingRequest Request chứa thông tin tài khoản
     * @return AccountSavingDTO của accumulated saving
     */
    private AccountSavingDTO getAccumulatedSaving(AccountSavingRequest accountSavingRequest) {
        return this.checkAccumulatedSaving(accountSavingRequest);
    }

    /**
     * Kiểm tra accumulated saving account từ T24
     *
     * @param accountSavingRequest Request chứa thông tin tài khoản
     * @return AccountSavingDTO của accumulated saving hoặc null
     */
    private AccountSavingDTO checkAccumulatedSaving(AccountSavingRequest accountSavingRequest) {
        // Gọi API T24 để lấy danh sách saving account
        List<AccountSavingDTO> accumulatedSavings = this.apiGeeTransferService
                .checkAccountSaving(accountSavingRequest);

        // Nếu có danh sách saving account từ T24
        if (Validator.isNotNull(accumulatedSavings)) {
            // Tìm accumulated saving account có category là smart saving
            Optional<AccountSavingDTO> accumulatedSaving = accumulatedSavings.stream()
                    .filter(item -> Validator.equals(item.getCategory(), customerProperties.getCifSmartSaving()))
                    .findFirst();

            // Nếu tìm thấy accumulated saving thì trả về
            if (accumulatedSaving.isPresent()) {
                return accumulatedSaving.get();
            }
        }
        // Không tìm thấy accumulated saving
        return null;
    }

    /**
     * Mở tài khoản tiết kiệm tích lũy (saving accumulation)
     * Method này tạo counter saving account từ data T24 cho tài khoản tiết kiệm tích lũy
     *
     * @param request Notification request
     * @param customer Customer entity
     * @param accumulatedSaving Data từ T24 về accumulated saving
     */
    private void openSavingAccumulation(NotificationClientRequest request, Customer customer, AccountSavingDTO accumulatedSaving) {
        // Kiểm tra counter saving account đã tồn tại chưa
        CounterSavingAccount counterSavingAcc = this.counterSavingAccountRepository
                .findBySavingAccountNumberAndStatus(request.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus());

        // Nếu chưa tồn tại thì tạo mới
        if (Validator.isNull(counterSavingAcc)) {
            // Khởi tạo biến thời gian
            Instant startTime = null;
            Instant settlementDueTime = null;
            // Tạo formatter để parse date từ T24
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.SHORT_TIMESTAMP_PATTERN_NON_DASH);

            // Parse start time từ value date
            if (Validator.isNotNull(accumulatedSaving.getValueDate())) {
                LocalDate valueDate = LocalDate.parse(accumulatedSaving.getValueDate(), formatter);
                startTime = valueDate.atStartOfDay(ZoneId.of(StringPool.UTC)).toInstant();
            }

            // Parse settlement due time từ maturity date
            if (Validator.isNotNull(accumulatedSaving.getMaturityDate())) {
                LocalDate maturityDate = LocalDate.parse(accumulatedSaving.getMaturityDate(), formatter);
                settlementDueTime = maturityDate.atStartOfDay(ZoneId.of(StringPool.UTC)).toInstant();
            }

            // Tạo DTO cho counter saving account mới
            CounterSavingAccountDTO dto = CounterSavingAccountDTO.builder()
                    .savingAccountNumber(request.getBeneficiaryAccountNumber()) // Số tài khoản tiết kiệm
                    .customerId(customer.getCustomerId()) // ID customer
                    .startTime(startTime) // Thời gian bắt đầu
                    .settlementDueTime(settlementDueTime) // Thời gian đáo hạn
                    .interestRate(Double.valueOf(accumulatedSaving.getInterestRate())) // Lãi suất
                    .savingAmount(Double.valueOf(accumulatedSaving.getWorkingBal())) // Số tiền tiết kiệm
                    .category(accumulatedSaving.getCategory()) // Loại tài khoản (smart saving)
                    .status(EntityStatus.ACTIVE.getStatus()) // Trạng thái active
                    .build();

            // Lưu counter saving account mới vào database
            this.counterSavingAccountRepository.save(this.counterSavingAccountMapper.toEntity(dto));
        }
    }

    /**
     * Tạo AccountBalanceRequest để gọi API T24 kiểm tra balance
     *
     * @param request Customer code (CIF)
     * @return AccountBalanceRequest
     */
    private static AccountBalanceRequest getAccountBalanceRequest(String request) {
        return AccountBalanceRequest.builder()
                .custCode(request) // Set customer code để query balance
                .build();
    }

    /**
     * Xử lý thông báo charge fee từ LAPNET client
     *
     * Method này xử lý thông báo về việc thu phí từ LAPNET:
     * 1. Validate client authentication (chỉ accept từ "mbbank")
     * 2. Xử lý các loại charge fee:
     *    - SMS balance charge fee (thu phí SMS biến động)
     *    - Premium account revert fee (hoàn phí số đẹp)
     * 3. Tạo notification cho khách hàng
     * 4. Lưu notification history
     * 5. Gửi thông báo đến khách hàng
     *
     * Business logic:
     * - SMS fee: Thu phí khi SMS balance thay đổi
     * - Premium account: Hoàn phí khi không thể cấp số đẹp
     *
     * @param notificationRequest Danh sách charge notification từ LAPNET
     * @return NotificationLapnetDTO Kết quả xử lý charge notification
     * @throws BadRequestAlertException Khi client không được authorize
     */
    @Override // Ghi đè method từ interface NotificationGatewayService
    public NotificationLapnetDTO getNotificationChargeClient(NotificationChargeClientRequest notificationRequest) {
        // Khởi tạo các danh sách để lưu kết quả xử lý
        List<NotificationDTO> notificationDTOS = new ArrayList<>(); // Danh sách notification sẽ được tạo
        List<NotificationHistory> notificationHistories = new ArrayList<>(); // Danh sách notification history
        NotificationLapnetDTO notificationLapnetDTO = new NotificationLapnetDTO(); // Response DTO
        Transaction transaction; // Biến lưu transaction (chưa được sử dụng)

        // Lấy tên client từ Spring Security Context để xác thực
        String clientName = SecurityContextHolder.getContext().getAuthentication().getName();

        // Kiểm tra client authentication - chỉ accept từ "mbbank"
        if (clientName.equals("mbbank")) {
            // Extract danh sách account number từ tất cả charge requests
            List<String> accountNumbers = (notificationRequest.getNotifications().stream()
                    .map(NotificationChargeRequest::getAccountNumber).collect(Collectors.toList()));

            // Tìm danh sách premium account number có trạng thái PENDING
            List<PremiumAccNumber> premiumAccNumbers = this.premiumAccNumberRepository
                    .findAllByPremiumAccNumberInAndStatus(accountNumbers, EntityStatus.PENDING.getStatus());

            // Tìm danh sách money account có trạng thái ACTIVE
            List<MoneyAccount> moneyAccounts = this.moneyAccountRepository
                    .findAllByMoneyAccountsAndStatus(accountNumbers, EntityStatus.ACTIVE.getStatus());

            // Duyệt qua từng charge notification request
            for (NotificationChargeRequest request : notificationRequest.getNotifications()) {
                // Validate và parse thời gian giao dịch từ string sang Instant
                Instant transactionFinishTime = this.validateTransactionTime(request.getTransactionTime());
                // Khởi tạo DTO notification cho request này
                NotificationDTO notificationDTO = new NotificationDTO();
                // Khởi tạo notification history cho request này
                NotificationHistory notificationHistory = new NotificationHistory();

                // Skip nếu thời gian giao dịch trong tương lai (invalid)
                if (Validator.isNotNull(transactionFinishTime) && transactionFinishTime.isAfter(Instant.now()))
                    continue;

                // Xử lý trường hợp thu phí SMS biến động số dư lỗi
                if (Validator.equals(request.getTransactionType(), NotificationTransactionType.FEE_SMS)) {
                    // Tìm money account tương ứng với account number trong request
                    MoneyAccount moneyAccount = moneyAccounts.stream()
                            .filter(m -> Validator.equals(m.getAccountNumber(), request.getAccountNumber()))
                            .findFirst().orElse(null);

                    // Nếu tìm thấy money account
                    if (Validator.isNotNull(moneyAccount)) {
                        // Tìm customer theo customer ID, trạng thái ACTIVE và đã được approval
                        Optional<Customer> customer = this.customerRepository.findByCustomerIdAndStatusAndApprovalStatus(
                                moneyAccount.getCustomerId(), EntityStatus.ACTIVE.getStatus(),
                                CustomerApprovalStatus.APPROVAL.getStatus());

                        // Nếu tìm thấy customer
                        if (customer.isPresent()) {
                            // Tìm SMS balance theo phone number, account number và trạng thái ACTIVE
                            SmsBalance smsBalance = this.smsBalanceRepository.findByPhoneNumberAndCustomerAccountNumberAndStatus(
                                    customer.get().getPhoneNumber(), moneyAccount.getAccountNumber(), EntityStatus.ACTIVE.getStatus()
                            );

                            // Nếu tìm thấy SMS balance
                            if (Validator.isNotNull(smsBalance)) {
                                // Set thời gian hủy SMS balance
                                smsBalance.setCancellationDate(transactionFinishTime);
                                // Set trạng thái SMS balance thành INACTIVE
                                smsBalance.setStatus(EntityStatus.INACTIVE.getStatus());

                                // Tạo notification cho SMS balance fee failed
                                notificationDTO = this.createNotificationSMSBalanceFeeFailed(request, customer.get().getCustomerId());
                                // Lưu history cho SMS balance fee failed
                                notificationHistory = this.saveHistorySMSBalanceFeeFailed(request, customer.get(), transactionFinishTime);

                                // Thêm vào danh sách kết quả
                                notificationDTOS.add(notificationDTO);
                                notificationHistories.add(notificationHistory);
                            }
                        }
                    }

                // Xử lý trường hợp thu phí tài khoản số đẹp lỗi (premium account revert)
                } else {
                    // Tìm premium account number tương ứng với account number trong request
                    PremiumAccNumber premiumAccNumber = premiumAccNumbers.stream()
                            .filter(pr -> Validator.equals(pr.getPremiumAccNumber(), request.getAccountNumber()))
                            .findFirst().orElse(null);

                    // Nếu không tìm thấy premium account number thì skip
                    if (Validator.isNull(premiumAccNumber)) continue;

                    // Tìm transaction liên quan đến premium account với trạng thái PENDING
                    transaction = this.transactionRepository
                            .findByTransferTransactionIdAndStatus(premiumAccNumber.getTransactionId(), EntityStatus.PENDING.getStatus());
                    // Nếu không tìm thấy transaction thì skip
                    if (Validator.isNull(transaction)) continue;

                    // Tìm customer theo customer ID, trạng thái ACTIVE và đã được approval
                    Customer customer = this.customerRepository.findByCustomerIdAndStatusAndApprovalStatus(premiumAccNumber.getCustomerId(),
                            EntityStatus.ACTIVE.getStatus(), CustomerApprovalStatus.APPROVAL.getStatus()).orElse(null);
                    // Nếu không tìm thấy customer thì skip
                    if (Validator.isNull(customer)) continue;

                    // Tạo notification cho premium account revert
                    notificationDTO = this.createNotificationPremiumAccountRevert(request, customer.getCustomerId());
                    // Lưu history cho premium account revert error
                    notificationHistory = this.saveHistoryPremiumAccountNumberRevertError(request, customer, transactionFinishTime, premiumAccNumber, transaction);

                    // Thêm vào danh sách kết quả
                    notificationDTOS.add(notificationDTO);
                    notificationHistories.add(notificationHistory);
                }
            } // Kết thúc loop xử lý từng charge request

            // Nếu có notification để lưu
            if (Validator.isNotNull(notificationDTOS)) {
                // Lưu tất cả notification vào database
                List<Notification> notificationSaves = this.notificationRepository.saveAll(this.notificationMapper.toEntity(notificationDTOS));
                // Lưu tất cả notification history vào database
                this.notificationHistoryRepository.saveAll(notificationHistories);
                // Chuyển đổi notification entities thành DTOs để trả về
                notificationDTOS = this.notificationMapper.toDto(notificationSaves);
            }
        } else {
            // Throw exception nếu client không được phép truy cập
            throw new BadRequestAlertException(ErrorCode.MSG101171);
        }

        // Set danh sách notification vào response DTO
        notificationLapnetDTO.setNotifications(notificationDTOS);
        // Set client message ID để tracking
        notificationLapnetDTO.setClientMessageId(DiagnosticContextUtil.getClientMessageId());
        // Trả về response chứa tất cả charge notification đã được xử lý
        return notificationLapnetDTO;
    }

    /**
     * Lấy AccountBalanceDTO từ danh sách theo beneficiary account number
     *
     * @param request Notification request
     * @param accountBalanceDTOS Danh sách account balance từ T24
     * @return Optional AccountBalanceDTO khớp với beneficiary account number
     */
    @NotNull
    private static Optional<AccountBalanceDTO> getAccountBalanceDTO(NotificationClientRequest request, List<AccountBalanceDTO> accountBalanceDTOS) {
        return accountBalanceDTOS.stream()
                .filter(accBalance -> Validator.equals(accBalance.getAccountNumber(), request.getBeneficiaryAccountNumber()))
                .findFirst();
    }

    /**
     * Lưu thông tin giao dịch ghi có thành công cho quản lý số lượng giao dịch
     * Method này phục vụ cho luồng bổ sung thêm cột giao dịch ghi có thành công
     *
     * @param request Notification request
     * @param commons Danh sách common transaction types
     * @param customer Customer entity
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param transactionType Loại giao dịch
     */
    private void saveCreditCustomerAccNumber(NotificationClientRequest request, List<Common> commons, Customer customer,
                                             Instant transactionFinishTime, CommonCode transactionType) {
        // Khởi tạo biến saving account
        SavingAccount savingAccount = null;
        // Lấy danh sách transaction type values từ commons
        List<String> transactionTypes = commons.stream().map(Common::getValue).collect(Collectors.toList());

        // Trường hợp: xử lý logic cho tất toán tài khoản tiết kiệm online
        if (request.getTransactionId().startsWith(this.notificationProperties.getClosurePrefix())) {
            // Extract saving account number từ transaction ID (bỏ 18 ký tự đầu)
            String savingAccNumber = request.getTransactionId().trim().substring(18);

            // Tìm saving account theo số tài khoản và loại trừ trạng thái DELETED
            savingAccount = this.savingAccountRepository
                    .findBySavingAccountNumberAndStatusNot(savingAccNumber, EntityStatus.DELETED.getStatus());
        }

        // Nếu có saving account hoặc transaction type không nằm trong danh sách commons
        if (Validator.isNotNull(savingAccount) || !transactionTypes.contains(request.getTransactionType())) {
            // Tạo request để kiểm tra account balance từ T24
            AccountBalanceRequest accountBalanceRequest = getAccountBalanceRequest(request.getCif());
            // Gọi API T24 để lấy danh sách account balance
            List<AccountBalanceDTO> accountBalanceDTOS = this.apiGeeTransferService.checkAccountBalance(accountBalanceRequest);
            // Tìm account balance khớp với beneficiary account number
            Optional<AccountBalanceDTO> accountBalanceDTO = getAccountBalanceDTO(request, accountBalanceDTOS);

            // Nếu tìm thấy account balance
            if (accountBalanceDTO.isPresent()) {
                // Tạo receive transfer money DTO
                ReceiveTransferMoneyDTO receiveTransferMoneyDTO = this.getReceiveTransferMoneyDTO(request, customer, transactionFinishTime, transactionType);
                // Set account type là không phải tiết kiệm
                receiveTransferMoneyDTO.setAccountType(AccountTypeCif.NONSAVING.getStatus());
                // Lưu receive transfer money vào database
                this.receiveTransferMoneyRepository.save(this.receiveTransferMoneyMapper.toEntity(receiveTransferMoneyDTO));
            }
        }
    }

    /**
     * Wrapper method để tạo NotificationDTO
     *
     * @param request Notification request
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param customer Customer entity
     * @return NotificationDTO
     */
    @NotNull
    private NotificationDTO getNotificationDTO(NotificationClientRequest request, Instant transactionFinishTime, Customer customer) {
        return this.createNotification(request, transactionFinishTime, customer.getCustomerId());
    }

    /**
     * Wrapper method để tạo ReceiveTransferMoneyDTO
     *
     * @param request Notification request
     * @param customer Customer entity
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param transactionType Loại giao dịch
     * @return ReceiveTransferMoneyDTO
     */
    @NotNull
    private ReceiveTransferMoneyDTO getReceiveTransferMoneyDTO(NotificationClientRequest request, Customer customer,
                                                               Instant transactionFinishTime, CommonCode transactionType) {
        return this.saveReceiveTransferMoney(request, customer, transactionFinishTime, transactionType);
    }

    /**
     * Lấy transaction type từ request và mapping với commons
     *
     * @param request Notification request
     * @param commons Danh sách common transaction types
     * @return CommonCode transaction type hoặc null nếu không tìm thấy
     */
    private CommonCode getTransactionType(NotificationClientRequest request, List<Common> commons) {
        // Tìm transaction type trong commons theo value
        Optional<String> transactionType = commons.stream()
                .filter(common -> Validator.equals(common.getValue(), request.getTransactionType()))
                .findFirst().map(Common::getCode);

        // Chuyển đổi string thành CommonCode enum
        return transactionType.map(CommonCode::valueOf).orElse(null);
    }

    /**
     * Tìm customer từ danh sách theo CIF
     *
     * @param customers Danh sách customer đã load
     * @param request Notification request chứa CIF
     * @return Customer entity
     * @throws BadRequestAlertException Nếu không tìm thấy customer với CIF
     */
    private Customer getCustomer(List<Customer> customers, NotificationClientRequest request) {
        return customers.stream()
                .filter(cus -> Validator.equals(cus.getCif(), request.getCif()))
                .findFirst().orElseThrow(() -> new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_CIF_DOES_NOT_EXISTED, new Object[]{}),
                        Customer.class.getSimpleName(), LabelKey.ERROR_CIF_DOES_NOT_EXISTED));
    }

    /**
     * Validate và chuyển đổi transaction time từ string sang Instant
     *
     * NOTE: Thời gian T24 truyền về là giờ GMT+7, cần convert sang UTC
     *
     * @param transactionTime Transaction time string từ T24
     * @return Instant UTC time
     */
    public Instant validateTransactionTime(String transactionTime) {
        // Tạo formatter để parse transaction time
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.LONG_DATE_PATTERN_DASH_S);
        // Parse string thành LocalDateTime
        LocalDateTime localDateTime = LocalDateTime.parse(transactionTime, formatter);
        // Convert sang GMT+7 timezone (Vietnam timezone)
        ZonedDateTime gmtPlus7Time = localDateTime.atZone(Labels.ZONE_ID_VN);
        // Convert sang UTC timezone
        ZonedDateTime utcTime = gmtPlus7Time.withZoneSameInstant(ZoneId.of(StringPool.UTC));
        // Trả về Instant UTC
        return utcTime.toInstant();
    }

    /**
     * Cập nhật trạng thái premium account number khi thanh toán thành công
     *
     * @param premiumAccNumber Premium account number entity
     * @param request Notification request
     * @param transactionTime Thời gian giao dịch
     * @return PremiumAccNumber đã được cập nhật hoặc null nếu không thành công
     */
    private PremiumAccNumber updateStatusPremiumAccNumber(PremiumAccNumber premiumAccNumber,
                                                          NotificationClientRequest request,
                                                          Instant transactionTime) {
        // Kiểm tra số tiền thanh toán có khớp với total price không
        if (Long.parseLong(request.getAmount()) == premiumAccNumber.getTotalPrice()) {
            // Tìm transaction liên quan với trạng thái WAITING
            Transaction transaction = this.transactionRepository
                    .findByTransferTransactionIdAndStatus(premiumAccNumber.getTransactionId(), EntityStatus.WAITING.getStatus());
            // Cập nhật trạng thái transaction thành ACTIVE
            transaction.setStatus(EntityStatus.ACTIVE.getStatus());
            // Lưu transaction với trạng thái mới
            this.transactionRepository.save(transaction);

            // Cập nhật trạng thái premium account number thành ACTIVE
            premiumAccNumber.setStatus(EntityStatus.ACTIVE.getStatus());
            // Set thời gian thanh toán TKSD thực tế
            premiumAccNumber.setPaymentDate(transactionTime);

            // Lưu và trả về premium account number đã cập nhật
            return this.premiumAccNumberRepository.save(premiumAccNumber);
        }
        // Trả về null nếu số tiền không khớp
        return null;
    }

    /**
     * Tạo notification cho giao dịch thay đổi số dư
     *
     * @param request Notification request từ LAPNET
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param customerId ID customer nhận notification
     * @return NotificationDTO đã được tạo
     */
    private NotificationDTO createNotification(NotificationClientRequest request, Instant
            transactionFinishTime, Long customerId) {
        // Tạo map chứa các giá trị để replace trong template
        Map<String, String> valuesAccount = new HashMap<>();

        // Set label cho reference ID
        valuesAccount.put(TemplateField.LABEL_REFERENCE_ID.name(), Labels.getLabelsEn(LabelKey.LABEL_REFERENCE_ID));

        // Set reference ID (transaction ID)
        valuesAccount.put(TemplateField.REFERENCE_ID.name(), request.getTransactionId());

        // Set payment account (beneficiary account number)
        valuesAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), request.getBeneficiaryAccountNumber());

        // Set label cho transaction amount
        valuesAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(), Labels.getLabelsEn(LabelKey.LABEL_AMOUNT_TRANSACTION));

        // Xử lý khác nhau cho DEBIT và CREDIT
        if (Validator.equals(request.getDrCr(), DrCrType.D.name())) {
            // DEBIT: Tiền ra khỏi tài khoản
            valuesAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(), Labels.getLabelsEn(LabelKey.LABEL_ACCOUNT_TYPE_PAYMENT_ACCOUNT));
            valuesAccount.put(TemplateField.TRANSACTION_AMOUNT.name(), StringPool.MINUS + StringUtil.formatMoney(request.getAmount()));
        } else {
            // CREDIT: Tiền vào tài khoản
            valuesAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(), Labels.getLabelsEn(LabelKey.LABEL_BENEFICIARY_ACCOUNT));
            valuesAccount.put(TemplateField.TRANSACTION_AMOUNT.name(), StringPool.PLUS + StringUtil.formatMoney(request.getAmount()));
        }

        // Set currency
        valuesAccount.put(TemplateField.CURRENCY.name(), request.getCurrency());

        // Set label cho transaction time
        valuesAccount.put(TemplateField.LABEL_TRANSACTION_TIME.name(), Labels.getLabelsEn(LabelKey.LABEL_TRANSACTION_DATE));

        // Set transaction time (format theo timezone mặc định)
        valuesAccount.put(TemplateField.TRANSACTION_TIME.name(), InstantUtil.formatStringLongDate(transactionFinishTime, Labels.getDefaultZoneId()));

        // Set label cho transaction content
        valuesAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(), Labels.getLabelsEn(LabelKey.LABEL_TRANSACTION_CONTENT));

        // Set message content
        valuesAccount.put(TemplateField.MESSAGE.name(), request.getMessage());

        // Lấy content template cho balance change notification
        ContentTemplate contentTemplate = this.contentTemplateRepository.findByTemplateCodeAndStatus(TemplateCode.BALANCE_CHANGE_RECEIVE_MONEY_V2.name(), EntityStatus.ACTIVE.getStatus());

        // Tạo notification DTO
        NotificationDTO notificationDTO = new NotificationDTO();
        notificationDTO.setNotificationStatus(NotificationStatus.PENDING); // Trạng thái pending
        notificationDTO.setEventId(0L); // Event ID = 0
        notificationDTO.setEndUserType(EndUserType.CUSTOMER); // Loại end user là customer
        notificationDTO.setPublishTime(transactionFinishTime.atZone(ZoneId.systemDefault()).toLocalDateTime()); // Thời gian publish
        notificationDTO.setTargetType(TargetType.USER); // Target type là user
        notificationDTO.setNotificationType(NotificationType.TRANSFER_TRANSACTION); // Loại notification là transfer transaction
        notificationDTO.setTitle(Labels.getLabelsEn(contentTemplate.getTitle())); // Title từ template
        notificationDTO.setContent(StringUtil.replaceMapValue(contentTemplate.getContent(), valuesAccount)); // Content với values đã replace
        notificationDTO.setDescription(contentTemplate.getDescription()); // Description từ template
        notificationDTO.setClassPk(customerId); // Customer ID

        // Gửi notification qua SendService
        this.sendService.sendNotification(notificationDTO);
        return notificationDTO;
    }

    /**
     * Tạo ReceiveTransferMoneyDTO để lưu lịch sử nhận tiền
     *
     * @param request Notification request từ LAPNET
     * @param customer Customer entity (có thể null)
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param transactionType Loại giao dịch
     * @return ReceiveTransferMoneyDTO đã được tạo
     */
    private ReceiveTransferMoneyDTO saveReceiveTransferMoney(NotificationClientRequest request, Customer
            customer, Instant transactionFinishTime, CommonCode transactionType) {
        // Khởi tạo DTO
        ReceiveTransferMoneyDTO receiveTransferMoneyDTO = new ReceiveTransferMoneyDTO();

        // Set thông tin customer nếu có
        if (Validator.isNotNull(customer)) {
            receiveTransferMoneyDTO.setCustomerAccountName(customer.getFullname()); // Tên customer
            receiveTransferMoneyDTO.setCustomerId(customer.getCustomerId()); // ID customer
        }
        receiveTransferMoneyDTO.setMessage(request.getMessage()); // Message từ request
        receiveTransferMoneyDTO.setStatus(EntityStatus.ACTIVE.getStatus()); // Trạng thái active

        // Định nghĩa các nhóm transaction type
        List<CommonCode> foreignCurrencies = Arrays.asList(CommonCode.USD_EXCHANGE, CommonCode.LAK_EXCHANGE, CommonCode.THB_EXCHANGE); // Giao dịch ngoại tệ
        List<CommonCode> savingAccounts = Arrays.asList(CommonCode.DEBIT_CLOSE_ACC, CommonCode.CREDIT_AUTO_CLOSE_FD,
                CommonCode.CREDIT_PAY_INTEREST, CommonCode.DEBIT_CONTRACT_INTEREST_PAY, CommonCode.FIXED_DEPOSIT_CREDIT,
                CommonCode.CLOSE_NT, CommonCode.FIXED_DEPOSIT_DEBIT, CommonCode.DEBIT_AUTO_CLOSE_FD, CommonCode.DEBIT_PAY_INTEREST); // Giao dịch tiết kiệm

        // Xác định transfer type dựa trên transaction type
        if (Validator.equals(transactionType, CommonCode.CASH_WITHDRAWAL)) {
            // Rút tiền mặt
            receiveTransferMoneyDTO.setTransferType(CommonCode.CASH_WITHDRAWAL.name());
        } else if (Validator.equals(transactionType, CommonCode.SERVICE_FEE)) {
            // Thu phí dịch vụ
            receiveTransferMoneyDTO.setTransferType(CommonCode.SERVICE_FEE.name());
        } else if (foreignCurrencies.contains(transactionType)) {
            // Giao dịch ngoại tệ
            receiveTransferMoneyDTO.setTransferType(EnumTransferType.FOREIGN_CURRENCY.name());
        } else if (savingAccounts.contains(transactionType)) {
            // Giao dịch tài khoản tiết kiệm
            receiveTransferMoneyDTO.setTransferType(EnumTransferType.SAVING_ACCOUNT.name());
        } else if (Validator.equals(request.getTransactionType(), this.notificationProperties.getTransactionType().getInternalBank())) {
            // Chuyển tiền nội bộ ngân hàng
            receiveTransferMoneyDTO.setTransferType(EnumTransferType.INTERNAL_BANK.name());
        } else if (Validator.equals(request.getTransactionType(), this.notificationProperties.getTransactionType().getInterBank())) {
            // Chuyển tiền liên ngân hàng
            receiveTransferMoneyDTO.setTransferType(EnumTransferType.INTER_BANK.name());
        } else if (Validator.equals(request.getTransactionType(), this.notificationProperties.getTransactionType().getCashInCounter())) {
            // Nạp tiền tại quầy
            receiveTransferMoneyDTO.setTransferType(EnumTransferType.CASH_IN_COUNTER.name());
        } else if (Validator.equals(transactionType, CommonCode.CHARGE_ERROR)) {
            // Lỗi thu phí tài khoản số đẹp
            receiveTransferMoneyDTO.setTransferType(EnumTransferType.CHARGE_PREMIUM_ACC_NUMBER_ERROR.name());
        }

        // Set các thông tin chi tiết giao dịch
        receiveTransferMoneyDTO.setCustomerAccountNumber(request.getBeneficiaryAccountNumber()); // Số tài khoản customer
        receiveTransferMoneyDTO.setTransactionAmount(Double.parseDouble(request.getAmount())); // Số tiền giao dịch
        receiveTransferMoneyDTO.setTransactionCurrency(request.getCurrency()); // Loại tiền tệ
        receiveTransferMoneyDTO.setTransactionId(request.getTransactionId()); // Transaction ID
        receiveTransferMoneyDTO.setOperate(Validator.equals(request.getDrCr(), DrCrType.C.name()) ? TransactionType.PLUS : TransactionType.MINUS); // Phép toán (+ hoặc -)
        receiveTransferMoneyDTO.setTransactionType(request.getTransactionType()); // Loại giao dịch
        receiveTransferMoneyDTO.setDrCr(request.getDrCr()); // Debit/Credit
        receiveTransferMoneyDTO.setPhone(request.getPhone()); // Số điện thoại
        receiveTransferMoneyDTO.setNatIdType(request.getNatIdType()); // Loại giấy tờ tùy thân
        receiveTransferMoneyDTO.setSectorValue(request.getSectorValue()); // Sector value
        receiveTransferMoneyDTO.setTransactionFinishTime(transactionFinishTime); // Thời gian hoàn thành giao dịch
        receiveTransferMoneyDTO.setBalance(request.getBalance()); // Số dư tài khoản
        receiveTransferMoneyDTO.setTransactionFee(Validator.isNotNull(request.getTransactionFee()) ? Double.parseDouble(request.getTransactionFee()) : 0); // Phí giao dịch

        return receiveTransferMoneyDTO;
    }

    /**
     * Tạo notification cho premium account revert error
     *
     * @param request Charge request
     * @param customerId ID customer nhận notification
     * @return NotificationDTO đã được tạo
     */
    private NotificationDTO createNotificationPremiumAccountRevert(NotificationChargeRequest request, Long customerId) {
        // Tạo map chứa các giá trị để replace trong template
        Map<String, String> valuesAccount = new HashMap<>();

        // Tạo message cho premium account revert (bao gồm account number)
        valuesAccount.put(TemplateField.MESSAGE.name(),
                Labels.getLabelsEn(LabelKey.LABEL_TEMPLATE_PREMIUM_ACCOUNT_REVERT_MESSAGE) // Message prefix
                        + StringPool.SPACE + request.getAccountNumber() + StringPool.SPACE // Account number
                        + Labels.getLabelsEn(LabelKey.LABEL_TEMPLATE_PREMIUM_ACCOUNT_REVERT_MESSAGE_ACC)); // Message suffix

        // Lấy content template cho premium account revert error
        ContentTemplate contentTemplate = this.contentTemplateRepository
                .findByTemplateCodeAndStatus(TemplateCode.PREMIUM_ACCOUNT_NUMBER_REVERT_ERROR.name(), EntityStatus.ACTIVE.getStatus());

        // Tạo notification DTO
        NotificationDTO notificationDTO = new NotificationDTO();
        notificationDTO.setNotificationStatus(NotificationStatus.PENDING); // Trạng thái pending
        notificationDTO.setEventId(0L); // Event ID = 0
        notificationDTO.setEndUserType(EndUserType.CUSTOMER); // Loại end user là customer
        notificationDTO.setPublishTime(DateUtil.convertToCurrentTimeZoneLA()); // Thời gian publish (timezone LA)
        notificationDTO.setTargetType(TargetType.USER); // Target type là user
        notificationDTO.setNotificationType(NotificationType.OTHER); // Loại notification là other
        notificationDTO.setTitle(Labels.getLabelsEn(contentTemplate.getTitle())); // Title từ template
        notificationDTO.setContent(StringUtil.replaceMapValue(contentTemplate.getContent(), valuesAccount)); // Content với values đã replace
        notificationDTO.setDescription(contentTemplate.getDescription()); // Description từ template
        notificationDTO.setClassPk(customerId); // Customer ID

        return notificationDTO;
    }

    /**
     * Tạo notification cho SMS balance fee failed
     *
     * @param request Charge request
     * @param customerId ID customer nhận notification
     * @return NotificationDTO đã được tạo
     */
    private NotificationDTO createNotificationSMSBalanceFeeFailed(NotificationChargeRequest request, Long customerId) {
        // Lấy content template cho SMS balance fee failed
        ContentTemplate contentTemplate = this.contentTemplateRepository.findByTemplateCodeAndStatus(TemplateCode.NOTIFICATION_SMS_BALANCE_FEE_FAILED.name(), EntityStatus.ACTIVE.getStatus());

        // Tạo notification DTO
        NotificationDTO notificationDTO = new NotificationDTO();
        notificationDTO.setNotificationStatus(NotificationStatus.PENDING); // Trạng thái pending
        notificationDTO.setEventId(0L); // Event ID = 0
        notificationDTO.setEndUserType(EndUserType.CUSTOMER); // Loại end user là customer
        notificationDTO.setPublishTime(DateUtil.convertToCurrentTimeZoneLA()); // Thời gian publish (timezone LA)
        notificationDTO.setTargetType(TargetType.USER); // Target type là user
        notificationDTO.setNotificationType(NotificationType.OTHER); // Loại notification là other
        notificationDTO.setTitle(Labels.getLabelsEn(contentTemplate.getTitle())); // Title từ template
        notificationDTO.setContent(Labels.getLabelsEn(contentTemplate.getContent(), new Object[]{request.getAccountNumber(), request.getAmount(), request.getTransactionTime()})); // Content với parameters
        notificationDTO.setDescription(contentTemplate.getDescription()); // Description từ template
        notificationDTO.setClassPk(customerId); // Customer ID
        return notificationDTO;
    }

    /**
     * Lưu notification history cho premium account number revert error
     *
     * @param request Charge request
     * @param customer Customer entity
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @param premiumAccNumber Premium account number entity
     * @param transaction Transaction entity
     * @return NotificationHistory đã được tạo
     */
    private NotificationHistory saveHistoryPremiumAccountNumberRevertError(NotificationChargeRequest request, Customer
            customer, Instant transactionFinishTime, PremiumAccNumber premiumAccNumber, Transaction transaction) {
        // Tạo notification history entity
        NotificationHistory notificationHistory = new NotificationHistory();
        notificationHistory.setTransferTransactionId(premiumAccNumber.getTransactionId()); // Transfer transaction ID
        notificationHistory.setTransferType(TransferType.PREMIUM_ACCOUNT_NUMBER); // Transfer type là premium account number
        notificationHistory.setCustomerId(customer.getCustomerId()); // Customer ID
        notificationHistory.setCustomerAccNumber(null); // Customer account number = null
        notificationHistory.setTarget(request.getAccountNumber()); // Target account number
        notificationHistory.setBeneficiaryAccountNumber(request.getAccountNumber()); // Beneficiary account number
        notificationHistory.setBankCode(null); // Bank code = null
        notificationHistory.setTransactionAmount(Long.valueOf(request.getAmount())); // Transaction amount
        notificationHistory.setBeneficiaryCustomerName(customer.getFullname()); // Beneficiary customer name
        // Set pay date nếu có payment date
        if (Validator.isNotNull(premiumAccNumber.getPaymentDate())) {
            notificationHistory.setPayDate(premiumAccNumber.getPaymentDate().atZone(ZoneId.systemDefault()).toLocalDate());
        }
        notificationHistory.setMessage(request.getMessage()); // Message
        notificationHistory.setTransactionStatus(TransactionStatus.SUCCESS); // Transaction status
        notificationHistory.setTransactionFee(transaction.getTransactionFee()); // Transaction fee
        notificationHistory.setTransactionId(transaction.getTransactionId()); // Transaction ID
        notificationHistory.setTransactionStartTime(premiumAccNumber.getPaymentDate()); // Thời gian bắt đầu giao dịch
        notificationHistory.setTransactionFinishTime(transactionFinishTime); // Thời gian kết thúc giao dịch
        notificationHistory.setTransactionCurrency(transaction.getTransactionCurrency()); // Loại tiền tệ giao dịch
        notificationHistory.setPhoneNumber(customer.getPhoneNumber()); // Số điện thoại customer
        notificationHistory.setBranchCode(this.customerProperties.getDefaultBranchCode()); // Mã chi nhánh mặc định
        notificationHistory.setTransactionType(TransactionType.MINUS); // Loại giao dịch là trừ tiền
        notificationHistory.setTotalAmount(Double.valueOf(request.getAmount())); // Tổng số tiền
        notificationHistory.setStatus(NotificationHistoryStatus.NO_CONTACT_YET.getStatus()); // Trạng thái chưa liên hệ
        notificationHistory.setNotificationType(NotificationHistoryEnum.PREMIUM_ACCOUNT_NUMBER_REVERT); // Loại notification history
        notificationHistory.setCif(customer.getCif()); // CIF customer
        // Set referral code nếu có
        if (Validator.isNotNull(premiumAccNumber.getReferralCode())) {
            notificationHistory.setReferralCode(premiumAccNumber.getReferralCode());
        }
        // Set transaction fee nếu có
        if (Validator.isNotNull(transaction.getTransactionFee())) {
            notificationHistory.setTransactionFee(transaction.getTransactionFee());
        }

        // Trả về notification history đã được tạo
        return notificationHistory;
    }

    /**
     * Lưu notification history cho SMS balance fee failed
     *
     * @param request Charge request
     * @param customer Customer entity
     * @param transactionFinishTime Thời gian hoàn thành giao dịch
     * @return NotificationHistory đã được tạo
     */
    private NotificationHistory saveHistorySMSBalanceFeeFailed(NotificationChargeRequest request, Customer
            customer, Instant transactionFinishTime) {
        // Tạo notification history entity
        NotificationHistory notificationHistory = new NotificationHistory();
        notificationHistory.setTransferType(TransferType.BILLING); // Transfer type là billing
        notificationHistory.setCustomerId(customer.getCustomerId()); // Customer ID
        notificationHistory.setCustomerAccNumber(null); // Customer account number = null
        notificationHistory.setTarget(request.getAccountNumber()); // Target account number
        notificationHistory.setBeneficiaryAccountNumber(request.getAccountNumber()); // Beneficiary account number
        notificationHistory.setBankCode(null); // Bank code = null
        notificationHistory.setTransactionAmount(Long.valueOf(request.getAmount())); // Transaction amount
        notificationHistory.setBeneficiaryCustomerName(customer.getFullname()); // Beneficiary customer name
        notificationHistory.setMessage(request.getMessage()); // Message
        notificationHistory.setTransactionStatus(TransactionStatus.SUCCESS); // Transaction status
        notificationHistory.setTransactionFinishTime(transactionFinishTime); // Transaction finish time
        notificationHistory.setPhoneNumber(customer.getPhoneNumber()); // Phone number
        notificationHistory.setBranchCode(this.customerProperties.getDefaultBranchCode()); // Branch code mặc định
        notificationHistory.setTransactionType(TransactionType.MINUS); // Transaction type là trừ tiền
        notificationHistory.setTotalAmount(Double.valueOf(request.getAmount())); // Total amount
        notificationHistory.setStatus(NotificationHistoryStatus.NO_CONTACT_YET.getStatus()); // Status chưa liên hệ
        notificationHistory.setNotificationType(NotificationHistoryEnum.SMS_BALANCE_CHARGE_FAIL); // Notification type
        notificationHistory.setCif(customer.getCif()); // CIF customer

        // Trả về notification history đã được tạo
        return notificationHistory;
    }
} // Kết thúc class NotificationGatewayServiceImpl
