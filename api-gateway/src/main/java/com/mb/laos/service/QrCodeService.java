package com.mb.laos.service;

import com.mb.laos.model.dto.MerchantDTO;
import com.mb.laos.model.dto.QrCodeDTO;
import com.mb.laos.model.search.QrSearch;
import com.mb.laos.request.QrRequest;
import com.mb.laos.request.QrVerifyRequest;
import com.mb.laos.response.QrCodeResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface QrCodeService {

    /**
     * generate or get QR code
     *
     * @param request
     * @return
     */
    QrCodeDTO getQrCodeV2(QrRequest request);

    /**
     * verify QR code
     *
     * @param request
     * @return
     */
    QrCodeResponse verifyQrCode(QrVerifyRequest request);

    /**
     * search QR code
     *
     * @param qrSearch
     * @param pageable
     * @return
     */
    Page<QrCodeDTO> search(QrSearch qrSearch, Pageable pageable);

    /**
     * Delete QR code dynamic
     *
     * @param request
     * @return
     */
    void delete(QrRequest request);

    /**
     * get Info QR code
     *
     * @param request
     * @return
     */
    QrCodeDTO detail(QrRequest request);

    /**
     * gen QR code merchant
     *
     * @param merchantDTO MerchantDTO
     * @return QrCodeDTO
     */
    QrCodeDTO genMerchantQrCodeV2(MerchantDTO merchantDTO, String currency);


    /**
     * deleteQrCodeByMerchant
     *
     * @param merchantCode String
     */
    void deleteQrCodeByMerchant(String merchantCode);
}
