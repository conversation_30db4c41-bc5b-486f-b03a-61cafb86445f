package com.mb.laos.service;

import com.mb.laos.gateway.request.UpdateVersionRequest;
import com.mb.laos.model.dto.VersionDTO;

public interface VersionGatewayService {


    /**
     * Trả về thông tin phiên bản mới nhất ứng với OS
     *
     * @param request
     * @return VersionDTO
     */
    VersionDTO getLastestVersion(VersionDTO request);

    /**
     * Cập nhật lịch sử update của thiết bị & update số lượt cài đặt version
     *
     * @param request
     * @return VersionDTO
     */
    VersionDTO updateVersion(UpdateVersionRequest request);

}
