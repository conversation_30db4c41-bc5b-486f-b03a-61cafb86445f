package com.mb.laos.service;

import com.mb.laos.api.request.MobilePaymentRequest;
import com.mb.laos.api.request.ValidateMobileNumberRequest;
import com.mb.laos.api.response.BestTelecomResponse;
import com.mb.laos.gateway.request.AccountCifRequest;
import com.mb.laos.model.dto.AccountCurrencyDTO;
import com.mb.laos.model.dto.AccountLoanDTO;
import com.mb.laos.model.dto.AccountSavingDTO;
import com.mb.laos.model.dto.MoneyAccountDTO;

import java.util.List;

public interface CustomerAccountService {
    /**
     * truy van so du tai khoan khach hang
     *
     * @return
     */
    List<MoneyAccountDTO> getCustomerAccountBalance();

    /**
     * truy van tài khoản ngoại tệ
     *
     * @return
     */
    List<AccountCurrencyDTO> getCustomerAccountCurrency();

    /**
     * truy van tài khoản tiết kiệm
     *
     * @return
     */
    List<AccountSavingDTO> getCustomerAccountSaving();

    /**
     * truy van tài khoản vay
     *
     * @return
     */
    List<AccountLoanDTO> getCustomerAccountLoan();

    /**
     * xem chi tiết tài khoản ngoại tệ
     *
     * @return
     */
    AccountCurrencyDTO getInfoCustomerAccountCurrency(AccountCifRequest accountCifRequest);

    /**
     * xem chi tiết tài khoản tiết kiệm
     *
     * @return
     */
    AccountSavingDTO getInfoCustomerAccountSaving(AccountCifRequest accountCifRequest);

    /**
     * xem chi tiết tài khoản vay
     *
     * @return
     */
    AccountLoanDTO getInfoCustomerAccountLoan(AccountCifRequest accountCifRequest);

}
