package com.mb.laos.service;

import com.mb.laos.gateway.response.SmsBalanceResponse;
import com.mb.laos.model.dto.SmsBalanceDTO;
import com.mb.laos.request.ConfirmOnOffSmsCustomerRequest;
import com.mb.laos.request.SmsBalanceRequest;

import java.util.List;

public interface SmsBalanceService {
    SmsBalanceDTO register(ConfirmOnOffSmsCustomerRequest request);

    SmsBalanceResponse requestRegister(SmsBalanceRequest request);

    SmsBalanceResponse requestCancel(SmsBalanceRequest request);

    SmsBalanceDTO cancel(ConfirmOnOffSmsCustomerRequest request);

    List<SmsBalanceDTO> list();

    SmsBalanceDTO getServiceFee();
}
