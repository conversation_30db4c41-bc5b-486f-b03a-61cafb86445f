package com.mb.laos.service;

import com.mb.laos.model.search.FileEntrySearch;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.mb.laos.model.dto.BankDTO;
import com.mb.laos.model.search.BankSearch;
import org.springframework.http.ResponseEntity;

public interface BankService {

    /**
     * tìm kiếm ngân hàng theo keyword
     *
     * @param bankSearch
     * @return
     */
    Page<BankDTO> search(BankSearch bankSearch, Pageable pageable);
    
    /**
     * @param params
     * @param pageable
     * @return
     */
    Page<BankDTO> simpleSearch(BankSearch params, Pageable pageable);

    /**
     * getIcon
     *
     * @param search FileEntrySearch
     * @return ResponseEntity<InputStreamResource>
     */
    ResponseEntity<InputStreamResource> getIcon(FileEntrySearch search);
}
