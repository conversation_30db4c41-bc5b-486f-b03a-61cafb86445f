package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.NoRollBackBadRequestAlertException;
import com.mb.laos.api.exception.UnauthorizedException;
import com.mb.laos.api.request.AccNumberVerifyRequest;
import com.mb.laos.api.request.AccountBalanceRequest;
import com.mb.laos.api.request.AccountSavingRequest;
import com.mb.laos.api.request.Amount;
import com.mb.laos.api.request.AuthenMethod;
import com.mb.laos.api.request.ClosedInterestT24Request;
import com.mb.laos.api.request.CreateInterestT24Request;
import com.mb.laos.api.request.InforInterestT24Request;
import com.mb.laos.api.request.InquiryTransactionStatusRequest;
import com.mb.laos.api.request.MakeTransferInteresT24Request;
import com.mb.laos.api.request.QuerySavingAccountT24Request;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.CustomerProperties;
import com.mb.laos.configuration.OtpProperties;
import com.mb.laos.enums.AccountTypeCif;
import com.mb.laos.enums.BranchCode;
import com.mb.laos.enums.CommonCategory;
import com.mb.laos.enums.CurrencyType;
import com.mb.laos.enums.EndUserType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.MaturityInstructionType;
import com.mb.laos.enums.NotificationStatus;
import com.mb.laos.enums.NotificationType;
import com.mb.laos.enums.OtpConfirmType;
import com.mb.laos.enums.OtpType;
import com.mb.laos.enums.SavingAccountType;
import com.mb.laos.enums.TargetType;
import com.mb.laos.enums.TemplateCode;
import com.mb.laos.enums.TemplateField;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.enums.TransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.gateway.request.ConfirmOtpSavingAccountRequest;
import com.mb.laos.gateway.request.InforInterestRequest;
import com.mb.laos.gateway.request.OtpSavingAccountRequest;
import com.mb.laos.gateway.request.QuerySavingAccountRequest;
import com.mb.laos.gateway.request.SearchSavingAccountRequest;
import com.mb.laos.gateway.response.CreateInterestResponse;
import com.mb.laos.gateway.response.OtpTransferConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferTransResponse;
import com.mb.laos.gateway.response.QuerySavingAccountResponse;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Common;
import com.mb.laos.model.ContentTemplate;
import com.mb.laos.model.Customer;
import com.mb.laos.model.DOTP;
import com.mb.laos.model.MoneyAccount;
import com.mb.laos.model.NotiTransaction;
import com.mb.laos.model.Notification;
import com.mb.laos.model.SavingAccount;
import com.mb.laos.model.SavingConfiguration;
import com.mb.laos.model.Transaction;
import com.mb.laos.model.dto.AccountBalanceDTO;
import com.mb.laos.model.dto.AccountSavingDTO;
import com.mb.laos.model.dto.ClosedInterestDTO;
import com.mb.laos.model.dto.InforInterestDTO;
import com.mb.laos.model.dto.InquiryTransactionStatusDTO;
import com.mb.laos.model.dto.IntRateDTO;
import com.mb.laos.model.dto.InterestDTO;
import com.mb.laos.model.dto.MakeTransferInterestDTO;
import com.mb.laos.model.dto.MoneyAccountDTO;
import com.mb.laos.model.dto.NotificationDTO;
import com.mb.laos.model.dto.ProductFeatureDTO;
import com.mb.laos.model.dto.QuerySavingAccountDTO;
import com.mb.laos.model.dto.SavingAccountDTO;
import com.mb.laos.model.dto.SavingAccountVerifyCacheDTO;
import com.mb.laos.model.dto.TotalMoneySavingAccountDTO;
import com.mb.laos.model.dto.TransactionDTO;
import com.mb.laos.model.dto.TransactionSavingAccountDTO;
import com.mb.laos.model.search.SavingAccountHistorySearch;
import com.mb.laos.repository.CommonRepository;
import com.mb.laos.repository.ContentTemplateRepository;
import com.mb.laos.repository.CustomerRepository;
import com.mb.laos.repository.DotpRepository;
import com.mb.laos.repository.MoneyAccountRepository;
import com.mb.laos.repository.NotiTransactionRepository;
import com.mb.laos.repository.NotificationRepository;
import com.mb.laos.repository.SavingAccountCacheRepository;
import com.mb.laos.repository.SavingAccountRepository;
import com.mb.laos.repository.SavingConfigurationRepository;
import com.mb.laos.repository.TransactionRepository;
import com.mb.laos.repository.TransactionSavingAccountRepository;
import com.mb.laos.security.util.GwSecurityUtils;
import com.mb.laos.service.ApiGeeSavingAccountService;
import com.mb.laos.service.ApiGeeTransferService;
import com.mb.laos.service.ConsumerOtpService;
import com.mb.laos.service.SavingAccountService;
import com.mb.laos.service.SendService;
import com.mb.laos.service.TransferService;
import com.mb.laos.service.mapper.MoneyAccountMapper;
import com.mb.laos.service.mapper.NotificationMapper;
import com.mb.laos.service.mapper.SavingAccountMapper;
import com.mb.laos.service.mapper.TransactionMapper;
import com.mb.laos.service.mapper.TransactionSavingAccountMapper;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.MD5Generator;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.TimeUtil;
import com.mb.laos.util.UUIDUtil;
import com.mb.laos.util.Validator;
import com.mb.laos.validator.ValidationConstraint;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service implementation cho quản lý tài khoản tiết kiệm
 *
 * Class này triển khai các chức năng chính của tài khoản tiết kiệm bao gồm:
 * - Tạo tài khoản tiết kiệm mới
 * - Gửi thêm tiền vào tài khoản tiết kiệm
 * - Tất toán tài khoản tiết kiệm
 * - Truy vấn thông tin và lịch sử giao dịch
 * - Quản lý thông báo và cập nhật số dư
 */
@Slf4j // Lombok annotation để tự động tạo logger
@Service // Spring annotation đánh dấu đây là service component
@Transactional // Spring annotation để quản lý transaction tự động
@RequiredArgsConstructor // Lombok annotation tự động tạo constructor với final fields
public class SavingAccountServiceImpl implements SavingAccountService {
    // Service để gọi API T24 cho các chức năng tài khoản tiết kiệm
    private final ApiGeeSavingAccountService apiGeeSavingAccountService;

    // Repository để quản lý thông tin DOTP (Dynamic OTP)
    private final DotpRepository dotpRepository;

    // Repository để quản lý dữ liệu tài khoản tiết kiệm
    private final SavingAccountRepository savingAccountRepository;

    // Mapper để chuyển đổi giữa entity và DTO cho tài khoản tiết kiệm
    private final SavingAccountMapper savingAccountMapper;

    // Repository để quản lý thông tin tài khoản tiền
    private final MoneyAccountRepository moneyAccountRepository;

    // Mapper để chuyển đổi giữa entity và DTO cho giao dịch
    private final TransactionMapper transactionMapper;

    // Repository để quản lý dữ liệu giao dịch
    private final TransactionRepository transactionRepository;

    // Service để xử lý OTP và xác thực
    private final ConsumerOtpService consumerOtpService;

    // Properties chứa cấu hình OTP
    private final OtpProperties otpProperties;

    // Service để gọi API T24 cho các chức năng chuyển tiền
    private final ApiGeeTransferService apiGeeTransferService;

    // Properties chứa cấu hình khách hàng
    private final CustomerProperties customerProperties;

    // Mapper để chuyển đổi giữa entity và DTO cho tài khoản tiền
    private final MoneyAccountMapper moneyAccountMapper;

    // Mapper để chuyển đổi giữa entity và DTO cho giao dịch tài khoản tiết kiệm
    private final TransactionSavingAccountMapper transactionSavingAccountMapper;

    // Repository để quản lý giao dịch tài khoản tiết kiệm
    private final TransactionSavingAccountRepository transactionSavingAccountRepository;

    // Mapper để chuyển đổi giữa entity và DTO cho thông báo
    private final NotificationMapper notificationMapper;

    // Repository để quản lý thông báo giao dịch
    private final NotiTransactionRepository notiTransactionRepository;

    // Repository để quản lý thông báo
    private final NotificationRepository notificationRepository;

    // Repository để quản lý template nội dung thông báo
    private final ContentTemplateRepository contentTemplateRepository;

    // Repository để quản lý dữ liệu common/master data
    private final CommonRepository commonRepository;

    // Service để xử lý các chức năng chuyển tiền
    private final TransferService transferService;

    // Repository để quản lý cache tài khoản tiết kiệm
    private final SavingAccountCacheRepository savingAccountCacheRepository;

    // Repository để quản lý thông tin khách hàng
    private final CustomerRepository customerRepository;

    // Repository để quản lý cấu hình tài khoản tiết kiệm
    private final SavingConfigurationRepository savingConfigurationRepository;

    // Service để gửi thông báo
    private final SendService sendService;


    @Override
    public List<IntRateDTO> getInforInterest(HttpServletRequest httpServletRequest, InforInterestRequest request) {
        this.getCustomerLogin();

        InforInterestDTO inforInterestDTO = this.verifyTenor(request.getSavingAccountType(), request.getCurrency(), request.getPeriod());

        List<Common> commons = this.commonRepository
                .findAllByCategoryAndStatus(CommonCategory.TENOR_PERIOD.toString(), EntityStatus.ACTIVE.getStatus());

        Boolean isCheck = inforInterestDTO.getLstIntRate().stream().allMatch(itemB -> commons.stream()
                .anyMatch(itemA -> itemB.getPeriod().endsWith(itemA.getCode())));

        if (!Validator.equals(isCheck, Boolean.TRUE)) {
            throw new BadRequestAlertException(ErrorCode.MSG101080);
        }

        inforInterestDTO.getLstIntRate().forEach(item -> {
            item.setMonth(StringUtil.extractNumber(item.getPeriod()));
            item.setTenorPeriod(StringUtil.extractCharacterStr(item.getPeriod()));
        });

        inforInterestDTO.getLstIntRate().forEach(item -> item.setPeriod(item.getPeriod().replace(StringUtil.extractCharacterStr(item.getPeriod()),
                this.getLabels(StringUtil.extractCharacterStr(item.getPeriod())))));

        return inforInterestDTO.getLstIntRate();
    }

    private String getLabels(String tenorPeriod) {
        Common common = this.commonRepository
                .findByCategoryAndCodeAndStatus(CommonCategory.TENOR_PERIOD.toString(), tenorPeriod, EntityStatus.ACTIVE.getStatus())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101080));
        return StringPool.SPACE + Labels.getLabels(common.getValue());
    }

    @Override
    public CreateInterestResponse closeInterest(HttpServletRequest httpServletRequest, ConfirmOtpSavingAccountRequest request) {
        Customer customer = this.getCustomerLogin();

        Transaction transaction = this.transactionRepository.findByTransactionIdAndCustomerId(
                request.getTransactionId(), customer.getCustomerId());

        SavingAccountVerifyCacheDTO savingAccountVerifyCacheDTO = this.getSavingAccountCache(transaction.getTransactionId());

        this.verifyTransaction(transaction);

        Optional<SavingAccount> savingAccount = this.savingAccountRepository.findBySavingAccountNumberAndCustomerIdAndStatus(
                savingAccountVerifyCacheDTO.getSavingAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus()
        );

        if (!savingAccount.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG101073);
        }

        String userId = customer.getCif() + customer.getIdCardNumber();
        DOTP dotp = this.checkRegisterDOTP(customer, savingAccountVerifyCacheDTO.getDeviceId());

        AuthenMethod authenMethod = AuthenMethod.builder()
                .otpValue(request.getOtpValue())
                .deviceId(savingAccountVerifyCacheDTO.getDeviceId())
                .token(dotp.getToken())
                .transData(MD5Generator.md5(request.getTransData()))
                .userId(userId)
                .build();

        ClosedInterestT24Request closedInterestT24Request = ClosedInterestT24Request.builder()
                .chanel("APP")
                .savingAccountNumber(savingAccountVerifyCacheDTO.getSavingAccountNumber())
                .receivePrincipalAccount(transaction.getBeneficiaryAccountNumber())
                .category(SavingAccountType.valueOf(transaction.getSavingAccountType()).getProductCategory())
                .subProduct(SavingAccountType.valueOf(transaction.getSavingAccountType()).getSubProduct())
                .cifT24(customer.getCif())
                .authenMethod(authenMethod)
                .build();

        ClosedInterestDTO response = this.consumerOtpService.verifyOtp(transaction.getTransactionId(),
                transaction.getPhoneNumber(), OtpType.SETTLEMENT, closedInterestT24Request,
                this.apiGeeSavingAccountService::closedInterest, OtpConfirmType.DOTP);

        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.FAILED)) {
            transaction.setTransactionStatus(TransactionStatus.FAILED);
            transaction.setTransactionFinishTime(Instant.now());
            this.transactionRepository.save_(transaction);

            if (Validator.isNotNull(response.getT24ErrorCode())) {
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            }

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023);
        } else if (Validator.equals(response.getTransactionStatus(), TransactionStatus.TIMEOUT)) {
            _log.info("Transaction has been timeout, {}", transaction.getTransactionId());

            transaction.setTransactionStatus(TransactionStatus.FAILED);
            this.transactionRepository.save_(transaction);

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
        }

        savingAccount.get().setStatus(EntityStatus.INACTIVE.getStatus());
        savingAccount.get().setAmtAfterSettlement(Long.valueOf(response.getInterestAmount()));
        savingAccount.get().setReceivingAccountNumber(transaction.getBeneficiaryAccountNumber());
        savingAccount.get().setMaturityDate(Instant.now());
        SavingAccount savingAcc = this.savingAccountRepository.save(savingAccount.get());

        transaction.setTransactionStatus(TransactionStatus.SUCCESS);
        transaction.setTransactionFinishTime(Instant.now());
        this.transactionRepository.save(transaction);

        TransactionSavingAccountDTO transactionSavingAccountDTO = new TransactionSavingAccountDTO();
        transactionSavingAccountDTO.setSavingAccountId(savingAccount.get().getSavingAccountId());
        transactionSavingAccountDTO.setStatus(EntityStatus.ACTIVE.getStatus());
        transactionSavingAccountDTO.setTransactionTime(Instant.now());
        transactionSavingAccountDTO.setTransferTransactionId(transaction.getTransferTransactionId());
        transactionSavingAccountDTO.setSavingAccountNumber(savingAccountVerifyCacheDTO.getSavingAccountNumber());
        transactionSavingAccountDTO.setDescription(transaction.getMessage());

        this.transactionSavingAccountRepository.save(this.transactionSavingAccountMapper.toEntity(transactionSavingAccountDTO));
        this.updateMoneyAccount(customer);
        this.savingAccountCacheRepository.evict(transaction.getTransactionId());

        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);
        this.createNotification(transactionDTO, customer, savingAcc);

        return CreateInterestResponse.builder()
                .savingAccountNumber(savingAccount.get().getSavingAccountNumber())
                .productName(savingAccount.get().getType().toString())
                .amount(Validator.isNotNull(transaction.getTransactionAmount()) ? String.valueOf(transaction.getTransactionAmount().longValue()) : String.valueOf(0L))
                .finalizationMethod(savingAccount.get().getFinalizationMethod())
                .beneficiaryAccountNumber(transaction.getBeneficiaryAccountNumber())
                .valueDate(String.valueOf(savingAccount.get().getStartTime()))
                .maturityDate(String.valueOf(savingAccount.get().getSettlementDueTime()))
                .interestAmount(response.getInterestAmount())
                .frequencyInterestPayment(Labels.getLabels(LabelKey.LABEL_END_OF_TERM))
                .createdDate(transaction.getTransactionFinishTime())
                .build();
    }

    private SavingAccountVerifyCacheDTO getSavingAccountCache(String transactionId) {
        SavingAccountVerifyCacheDTO savingAccountVerifyCacheDTO = this.savingAccountCacheRepository.get(transactionId);

        if (Validator.isNull(savingAccountVerifyCacheDTO)) {
            throw new BadRequestAlertException(ErrorCode.MSG101081);
        }

        return savingAccountVerifyCacheDTO;
    }

    @Override
    public OtpTransferConfirmResponse depositMore(HttpServletRequest httpServletRequest, ConfirmOtpSavingAccountRequest request) {
        Customer customer = this.getCustomerLogin();

        Transaction transaction = this.transactionRepository.findByTransactionIdAndCustomerId(request.getTransactionId(),
                customer.getCustomerId());

        SavingAccountType savingAccountType = SavingAccountType.valueOf(transaction.getSavingAccountType());
        this.verifyTransaction(transaction);
        SavingAccountVerifyCacheDTO savingAccountVerifyCacheDTO = this.getSavingAccountCache(transaction.getTransactionId());

        Optional<SavingAccount> savingAccount = this.savingAccountRepository.findBySavingAccountNumberAndCustomerIdAndStatus(
                savingAccountVerifyCacheDTO.getSavingAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus()
        );

        if (!savingAccount.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG101073);
        }

        String userId = customer.getCif() + customer.getIdCardNumber();
        DOTP dotp = this.checkRegisterDOTP(customer, savingAccountVerifyCacheDTO.getDeviceId());

        AuthenMethod authenMethod = AuthenMethod.builder()
                .otpValue(request.getOtpValue())
                .deviceId(savingAccountVerifyCacheDTO.getDeviceId())
                .token(dotp.getToken())
                .transData(MD5Generator.md5(request.getTransData()))
                .userId(userId)
                .build();

        MakeTransferInteresT24Request depositMoreRequest = MakeTransferInteresT24Request.builder()
                .cifT24(customer.getCif())
                .amount(Validator.isNotNull(transaction.getTransactionAmount()) ? String.valueOf(transaction.getTransactionAmount().longValue()) : String.valueOf(0L))
                .sourceAccount(transaction.getCustomerAccNumber())
                .changeAmount(String.valueOf(Math.round(transaction.getTransactionFee())))
                .savingAccountNo(savingAccountVerifyCacheDTO.getSavingAccountNumber())
                .branch(BranchCode.LA0010001.name())
                .category(savingAccountType.getProductCategory())
                .remark(transaction.getMessage())
                .amountCurrency(transaction.getTransactionCurrency())
                .authenMethod(authenMethod)
                .build();

        MakeTransferInterestDTO response = this.consumerOtpService.verifyOtp(transaction.getTransactionId(),
                transaction.getPhoneNumber(), OtpType.DEPOSIT_MORE, depositMoreRequest,
                this.apiGeeSavingAccountService::makeTransferInterest, OtpConfirmType.DOTP);

        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.FAILED)) {
            transaction.setTransactionStatus(TransactionStatus.FAILED);
            transaction.setTransactionFinishTime(Instant.now());
            this.transactionRepository.save_(transaction);

            if (Validator.isNotNull(response.getT24ErrorCode())) {
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            }

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023);
        } else if (Validator.equals(response.getTransactionStatus(), TransactionStatus.TIMEOUT)) {
            _log.info("Transaction has been timeout, {}", transaction.getTransactionId());

            InquiryTransactionStatusRequest transactionStatusRequest = InquiryTransactionStatusRequest.builder()
                    .transactionId(response.getReferenceNumber())
                    .build();

            InquiryTransactionStatusDTO inquiryTransactionStatusDTO = this.apiGeeTransferService
                    .inquiryTransactionStatus(transactionStatusRequest);

            if (Validator.isNull(inquiryTransactionStatusDTO)
                    || Validator.isNotNull(inquiryTransactionStatusDTO.getErrorCode())) {

                transaction.setTransactionStatus(TransactionStatus.FAILED);

                this.transactionRepository.save_(transaction);

                throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
            }
            response.setTransactionStatus(TransactionStatus.SUCCESS);
        }

        TransactionSavingAccountDTO transactionSavingAccountDTO = new TransactionSavingAccountDTO();
        transactionSavingAccountDTO.setSavingAccountId(savingAccount.get().getSavingAccountId());
        transactionSavingAccountDTO.setStatus(EntityStatus.ACTIVE.getStatus());
        transactionSavingAccountDTO.setTransactionCode(response.getT24ReferenceNumber());
        transactionSavingAccountDTO.setTransactionTime(Instant.now());
        transactionSavingAccountDTO.setTransferTransactionId(transaction.getTransferTransactionId());
        transactionSavingAccountDTO.setSavingAccountNumber(savingAccountVerifyCacheDTO.getSavingAccountNumber());
        transactionSavingAccountDTO.setDescription(transaction.getMessage());

        transaction.setTransactionCode(response.getT24ReferenceNumber());
        transaction.setTransactionFinishTime(Instant.now());
        transaction.setTransactionStatus(TransactionStatus.SUCCESS);
        this.transactionRepository.save(transaction);

        this.transactionSavingAccountRepository.save(this.transactionSavingAccountMapper.toEntity(transactionSavingAccountDTO));
        this.updateMoneyAccount(customer);
        this.changeAmountSavingAccount(savingAccount.get());
        this.savingAccountCacheRepository.evict(transaction.getTransactionId());

        TransactionDTO transDTO = this.transactionMapper.toDto(transaction);
        this.createNotification(transDTO, customer, savingAccount.get());

        return OtpTransferConfirmResponse.builder()
                .transactionTime(Instant.now())
                .transactionCode(response.getT24ReferenceNumber())
                .build();
    }

    public void changeAmountSavingAccount(SavingAccount savingAccount) {
        QuerySavingAccountT24Request querySavingAccount = QuerySavingAccountT24Request.builder()
                .cusAccNo(savingAccount.getSavingAccountNumber())
                .build();

        List<QuerySavingAccountDTO> querySavingAccountDTOS = this.apiGeeSavingAccountService.querySavingAccount(querySavingAccount);

        if (querySavingAccountDTOS.isEmpty()) {
            throw new BadRequestAlertException(ErrorCode.MSG101073);
        }

        QuerySavingAccountDTO querySavingAccountDTO = querySavingAccountDTOS.stream().findFirst().get();

        savingAccount.setSavingAmount(Long.valueOf(querySavingAccountDTO.getWorkingBal()));
        this.savingAccountRepository.save(savingAccount);
    }


    @Override
    public Page<TransactionSavingAccountDTO> history(HttpServletRequest httpServletRequest, SavingAccountHistorySearch request) {
        Customer customer = this.getCustomerLogin();


        // truy vấn lịch sử mở tài khoản và gửi thêm tiền
        request.setTransferTypes(Arrays.asList(TransferType.SAVING_ACCOUNT, TransferType.DEPOSIT_MORE));
        Pageable pageable = PageRequest.of(request.getPageIndex(), request.getPageSize());

        SavingAccount savingAccount = this.savingAccountRepository.findBySavingAccountNumberAndCustomerId(request.getSavingAccountNumber(), customer.getCustomerId())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101087));

        long totalElement = this.transactionSavingAccountRepository.count(request);

        if (totalElement == 0L) {
            return Page.empty();
        }

        List<TransactionSavingAccountDTO> transactionSavingAccountDTOS = this.transactionSavingAccountRepository.searchHistory(request, pageable);

        String savingAccountType = Validator.equals(SavingAccountType.FIXED, savingAccount.getType()) ? Labels.getLabels(LabelKey.LABEL_FIXED).toLowerCase()
                : Labels.getLabels(LabelKey.LABEL_ACCUMULATED).toLowerCase();

        String createInterestMessage = StringUtil.join(
                new String[]{customer.getFullname(), Labels.getLabels(LabelKey.MESSAGE_CREATE_INTEREST)
                        + StringPool.SPACE + savingAccountType}, StringPool.SPACE);

        transactionSavingAccountDTOS.stream().forEach(item -> {
            if (Validator.equals(TransferType.SAVING_ACCOUNT, item.getTransferType())) {
                item.setMessage(createInterestMessage);
            }
        });

        return new PageImpl<>(transactionSavingAccountDTOS, pageable, totalElement);
    }

    @Override
    public List<TotalMoneySavingAccountDTO> getTotalMoneySavingAccount(HttpServletRequest httpServletRequest) {
        Customer customer = this.getCustomerLogin();
        List<TotalMoneySavingAccountDTO> list = new ArrayList<>();
        Map<String, List<AccountSavingDTO>> accountSavingBranches = new HashMap<>();

        List<SavingAccount> savingAccounts = this.savingAccountRepository
                .findAllByCustomerIdAndStatus(customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

        AccountSavingRequest accountSavingRequest = AccountSavingRequest.builder()
                .cusId(customer.getCif())
                .build();

        List<AccountSavingDTO> accountSavingDTOS = this.apiGeeTransferService.checkAccountSaving(accountSavingRequest);

        accountSavingBranches = accountSavingDTOS.stream().collect(Collectors.groupingBy(AccountSavingDTO::getCurrency, Collectors.toList()));
        accountSavingDTOS.forEach(item -> item.setAmount(Validator.isNull(item.getWorkingBal()) ? 0L : Long.parseLong(item.getWorkingBal())));
        Long totalSavingAccountOnline = savingAccounts.stream().mapToLong(SavingAccount::getSavingAmount).reduce(0, Long::sum);

        list.add(new TotalMoneySavingAccountDTO(false, totalSavingAccountOnline, savingAccounts.size(), CurrencyType.LAK));

        List<String> savingAccountTypes = new ArrayList<>();
        savingAccountTypes.addAll(this.commonRepository
                .findAllByCategoryAndStatus(CommonCategory.SAVING_ACCOUNT.name(), EntityStatus.ACTIVE.getStatus())
                .stream().map(Common::getValue).collect(Collectors.toList()));

        accountSavingBranches.forEach((key, values) -> {
            long totalSavingAccount = values.stream().filter(value -> !savingAccountTypes.contains(value.getCategory())).count();
            long total = values.stream().filter(value -> !savingAccountTypes.contains(value.getCategory()))
                    .mapToLong(item -> Validator.isNull(item.getWorkingBal()) ? 0L : Long.parseLong(item.getWorkingBal()))
                    .reduce(0, Long::sum);

            list.add(new TotalMoneySavingAccountDTO(true, total, (int) totalSavingAccount, CurrencyType.valueOf(key)));
        });

        return list;
    }

    @Override
    public ProductFeatureDTO productFeature(HttpServletRequest httpServletRequest, SearchSavingAccountRequest request) {
        this.getCustomerLogin();

        return ProductFeatureDTO.builder()
                .depositMoreToSavingAccount(Validator.equals(SavingAccountType.FIXED, request.getType()) ? Boolean.FALSE : Boolean.TRUE)
                .allowSettlementDateToBeSet(Boolean.FALSE)
                .settlementBeforeDueDate(Boolean.TRUE)
                .partialWithdrawalBeforeDueDate(Boolean.FALSE)
                .build();
    }

    @Override
    @Transactional
    public void updateSavingAccount() {
        List<SavingAccount> savingAccounts = this.savingAccountRepository
                .findAllBySettlementDueTimeBeforeAndStatus(Instant.now(), EntityStatus.ACTIVE.getStatus());
        List<String> savingAccountClosed = new ArrayList<>();

        savingAccounts.forEach(item -> {
            QuerySavingAccountDTO response = null;
            QuerySavingAccountT24Request querySavingAccount = QuerySavingAccountT24Request.builder()
                    .category(String.valueOf(item.getType().getProductCategory()))
                    .cusAccNo(item.getSavingAccountNumber())
                    .build();

            String startTime = this.formatTime(item.getStartTime());
            String settlementDueTime = this.formatTime(item.getSettlementDueTime());

            List<QuerySavingAccountDTO> querySavingAccountDTOS = this.apiGeeSavingAccountService.querySavingAccount(querySavingAccount);

            if (Validator.isNull(querySavingAccountDTOS)) {
                savingAccountClosed.add(item.getSavingAccountNumber());
            } else {
                response = querySavingAccountDTOS.stream().findFirst().get();

                if (!Validator.equals(startTime, response.getValueDate())
                        || !Validator.equals(settlementDueTime, response.getMaturityDate())) {

                    SavingAccount savingAccountUpdate = this.savingAccountRepository
                            .findBySavingAccountNumberAndStatus(item.getSavingAccountNumber(), EntityStatus.ACTIVE.getStatus());

                    if (Validator.equals(item.getFinalizationMethod(), MaturityInstructionType.NO_ROTATION)) {
                        this.createOtherNotification(item, true); // thông báo biến động số dư
                        savingAccountUpdate.setStatus(EntityStatus.INACTIVE.getStatus());
                    } else if (Validator.equals(item.getFinalizationMethod(), MaturityInstructionType.ROOT_ROTATION)) {
                        this.createOtherNotification(item, true); // thông báo biến động số dư
                    } else {
                        this.createOtherNotification(item, false);// thống báo tiền lãi
                    }

                    savingAccountUpdate.setStartTime(this.parseTime(response.getValueDate()));
                    savingAccountUpdate.setSettlementDueTime(this.parseTime(response.getMaturityDate()));
                    savingAccountUpdate.setSavingAmount(Long.valueOf(response.getWorkingBal()));
                    savingAccountUpdate.setInterestAmtEnd(Long.valueOf(response.getInterestAmountEnd()));
                    savingAccountUpdate.setAmtAfterSettlement(Long.valueOf(response.getInterestAmountEnd()));

                    this.savingAccountRepository.save(savingAccountUpdate);
                    // cập nhật lại tiền của tài khoản
                    this.updateMoneyAccount(this.customerRepository.findByCustomerId(savingAccountUpdate.getCustomerId()));
                }
            }
        });

        List<SavingAccount> list = this.savingAccountRepository.findAllBySavingAccountNumberIn(savingAccountClosed);

        list.forEach(item -> {
            item.setStatus(EntityStatus.INACTIVE.getStatus());
            if (Validator.equals(item.getFinalizationMethod(), MaturityInstructionType.NO_ROTATION) ||
                    Validator.equals(item.getFinalizationMethod(), MaturityInstructionType.ROOT_ROTATION)) {
                this.createOtherNotification(item, true); // thông báo biến động số dư
            } else {
                this.createOtherNotification(item, false);// thống báo tiền lãi
            }
            // cập nhật lại tiền của tài khoản
            this.updateMoneyAccount(this.customerRepository.findByCustomerId(item.getCustomerId()));
        });

        this.savingAccountRepository.saveAll(list);
    }

    @Override
    public List<AccountSavingDTO> getCustomerAccountSavingBranch(HttpServletRequest httpServletRequest, Amount request) {
        Customer customer = this.getCustomerLogin();

        if (Validator.isNull(request.getCurrency())) {
            throw new BadRequestAlertException(ErrorCode.MSG101089);
        }

        List<Common> categoryCommon = commonRepository.findAllByCategory(CommonCategory.ACCOUNT_SAVING.name());

        List<String> categories = categoryCommon.stream().map(Common::getValue).collect(Collectors.toList());

        AccountSavingRequest accountSavingRequest = AccountSavingRequest.builder().cusId(customer.getCif())
                .accountType(AccountTypeCif.SAVING.name())
                .build();

        List<AccountSavingDTO> list = this.apiGeeTransferService
                .checkAccountSaving(accountSavingRequest).stream()
                .filter(item -> categories.contains(item.getCategory())).collect(Collectors.toList());

        if (Validator.isNull(list)) {
            return new ArrayList<>();
        }

        return list.stream().filter(item -> Validator.equals(item.getCurrency(), request.getCurrency())).collect(Collectors.toList());
    }

    private String formatTime(Instant time) {
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(time, ZoneId.of(StringPool.UTC));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TimeUtil.SHORT_TIMESTAMP_FORMAT);
        return formatter.format(zonedDateTime);
    }

    @Override
    public CreateInterestResponse createInterest(HttpServletRequest httpServletRequest, ConfirmOtpSavingAccountRequest request) {
        Customer customer = this.getCustomerLogin();

        Transaction transaction = this.transactionRepository.findByTransactionIdAndCustomerId(
                request.getTransactionId(), customer.getCustomerId());

        SavingAccountVerifyCacheDTO cache = this.getSavingAccountCache(request.getTransactionId());

        List<Common> commons = this.commonRepository.
                findAllByCategoryAndStatus(OtpType.SAVING_ACCOUNT.name(), EntityStatus.ACTIVE.getStatus());

        SavingAccountType savingAccountType = SavingAccountType.valueOf(String.valueOf(transaction.getSavingAccountType()));

        Optional<Common> common = commons.stream().filter(item -> item.getValue().equals(savingAccountType.getProductCategory().toString()))
                .collect(Collectors.toList()).stream().findAny();

        if (!common.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG101075);
        }

        this.verifyTransaction(transaction);

        String userId = customer.getCif() + customer.getIdCardNumber();
        DOTP dotp = this.checkRegisterDOTP(customer, cache.getDeviceId());

        InforInterestT24Request inforInterestT24Request = InforInterestT24Request.builder()
                .productCode(savingAccountType.getProductCode())
                .currency(transaction.getTransactionCurrency())
                .category(String.valueOf(savingAccountType.getProductCategory()))
                .subProduct(savingAccountType.getSubProduct())
                .build();

        InforInterestDTO inforInterestDTO = this.apiGeeSavingAccountService.getInforInterest(inforInterestT24Request);

        if (Validator.isNull(inforInterestDTO.getLstIntRate())) {
            throw new BadRequestAlertException(ErrorCode.MSG101076);
        }

        MaturityInstructionType maturityInstructionType = cache.getFinalizationMethod();
        Integer maturityInstruction = MaturityInstructionType.getMaturityInstructionType()
                .getOrDefault(maturityInstructionType, null);
        Integer tenor = StringUtil.extractNumber(cache.getTenor());
        String tenorPeriod = StringUtil.extractCharacterStr(cache.getTenor());

        Optional<IntRateDTO> intRateDTO = inforInterestDTO.getLstIntRate().stream()
                .filter(item -> item.getPeriod().equals(cache.getTenor())).collect(Collectors.toList()).stream().findFirst();

        if (!intRateDTO.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG101075);
        }

        AuthenMethod authenMethod = AuthenMethod.builder()
                .deviceId(cache.getDeviceId())
                .otpValue(request.getOtpValue())
                .token(dotp.getToken())
                .transData(MD5Generator.md5(request.getTransData()))
                .userId(userId)
                .build();

        CreateInterestT24Request interestT24Request = CreateInterestT24Request.builder()
                .channel("APP")
                .cifT24(customer.getCif())
                .productCategory(String.valueOf(savingAccountType.getProductCategory()))
                .amount(Validator.isNotNull(transaction.getTransactionAmount()) ? String.valueOf(transaction.getTransactionAmount().longValue()) : String.valueOf(0L))
                .amountCurrency(transaction.getTransactionCurrency())
                .sourceAccount(transaction.getCustomerAccNumber())
                .tenor(String.valueOf(tenor))
                .tenorPeriod(tenorPeriod)
                .maturityInstruction(String.valueOf(maturityInstruction))
                .beneficiaryAccount(transaction.getBeneficiaryAccountNumber())
                .fee(String.valueOf(Math.round(transaction.getTransactionFee()))) //@TODO T24 confirm ==> hiện tại truyền 0
                .subProduct(Validator.equals(SavingAccountType.FIXED.toString(), transaction.getSavingAccountType()) ? SavingAccountType.FIXED.getSubProduct() : null)
                .branch(BranchCode.LA0010001.name())
                .authenMethod(authenMethod)
                .build();

        InterestDTO response = this.consumerOtpService.verifyOtp(transaction.getTransactionId(),
                transaction.getPhoneNumber(), OtpType.SAVING_ACCOUNT, interestT24Request,
                this.apiGeeSavingAccountService::createInterest, OtpConfirmType.DOTP);

        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.FAILED)) {
            transaction.setTransactionStatus(TransactionStatus.FAILED);
            transaction.setTransactionFinishTime(Instant.now());
            this.transactionRepository.save_(transaction);

            if (Validator.isNotNull(response.getT24ErrorCode())) {
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            }

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023);
        } else if (Validator.equals(response.getTransactionStatus(), TransactionStatus.TIMEOUT)) {
            _log.info("Transaction has been timeout, {}", transaction.getTransactionId());

            transaction.setTransactionStatus(TransactionStatus.FAILED);
            this.transactionRepository.save_(transaction);

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
        }

        Instant startTime = this.parseTime(response.getValueDate());
        Instant settlementDueTime = this.parseTime(response.getMaturityDate());
        String sourceAccountNumber = transaction.getCustomerAccNumber();
        String beneficiaryAccountNumber = transaction.getBeneficiaryAccountNumber();
        String savingAccountNumber = response.getSavingAcctId();

        SavingAccountDTO savingAccountDTO = SavingAccountDTO.builder()
                .customerId(customer.getCustomerId())
                .accountNumber(sourceAccountNumber)
                .savingAccountNumber(savingAccountNumber)
                .cif(customer.getCif())
                .idCardNumber(customer.getIdCardNumber())
                .phoneNumber(customer.getUsername())
                .savingAmount(Validator.isNotNull(transaction.getTransactionAmount()) ? transaction.getTransactionAmount().longValue() : 0L)
                .type(savingAccountType)
                .startTime(startTime)
                .settlementDueTime(settlementDueTime)
                .tenor(tenor)
                .rmCode(cache.getRm())
                .interestRate(Double.valueOf(intRateDTO.get().getInterestRate()))
                .finalizationMethod(cache.getFinalizationMethod())
                .tenorPeriod(tenorPeriod)
                .receivingAccountNumber(beneficiaryAccountNumber)
                .status(EntityStatus.ACTIVE.getStatus())
                .build();

        SavingAccount savingAccount = this.savingAccountRepository.save(this.savingAccountMapper.toEntity(savingAccountDTO));

        try {
            String interestAmountEnd = this.getSavingAccount(savingAccount.getSavingAccountNumber(), null).getInterestAmountEnd();
            savingAccount.setInterestAmtEnd(Validator.isNull(interestAmountEnd) ? 0L : Long.parseLong(interestAmountEnd));
        } catch (Exception exception) {
            _log.info("No expected interest rate at the end of the period was found, {}", exception);
        }

        this.savingAccountRepository.save(savingAccount);

        transaction.setTransactionStatus(TransactionStatus.SUCCESS);
        transaction.setTransactionFinishTime(Instant.now());

        this.transactionRepository.save(transaction);
        this.updateMoneyAccount(customer);
        this.savingAccountCacheRepository.evict(transaction.getTransactionId());

        TransactionDTO trans = this.transactionMapper.toDto(transaction);
        this.createNotification(trans, customer, savingAccount);

        TransactionSavingAccountDTO dto = new TransactionSavingAccountDTO();
        dto.setSavingAccountId(savingAccount.getSavingAccountId());
        dto.setStatus(EntityStatus.ACTIVE.getStatus());
        dto.setTransferTransactionId(transaction.getTransferTransactionId());

        this.transactionSavingAccountRepository.save(this.transactionSavingAccountMapper.toEntity(dto));

        return CreateInterestResponse.builder()
                .productName(savingAccountType.toString())
                .savingAccountNumber(savingAccountNumber)
                .accountTransfer(sourceAccountNumber)
                .amount(Validator.isNotNull(transaction.getTransactionAmount()) ? String.valueOf(transaction.getTransactionAmount().longValue()) : String.valueOf(0L))
                .tenor(this.getTenor(cache.getTenor()))
                .createdDate(savingAccount.getCreatedDate())
                .valueDate(response.getValueDate())
                .maturityDate(response.getMaturityDate())
                .build();
    }

    private String getTenor(String tenor) {
        return StringUtil.extractNumber(tenor) + this.getLabels(StringUtil.extractCharacterStr(tenor));
    }

    private void verifyTransaction(Transaction transaction) {
        if (Validator.isNull(transaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }

        if (Validator.equals(transaction.getTransactionStatus(), TransactionStatus.SUCCESS)) {
            throw new BadRequestAlertException(ErrorCode.MSG1104);
        }
    }

    private DOTP checkRegisterDOTP(Customer customer, String deviceId) {
        Optional<DOTP> dotp = this.dotpRepository.findByDeviceIdAndCustomerIdAndStatus(deviceId,
                customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

        if (!dotp.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG100160);
        }

        return dotp.get();
    }

    private Instant parseTime(String inputDate) {
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(TimeUtil.SHORT_TIMESTAMP_FORMAT);

        LocalDate localDate = LocalDate.parse(inputDate, inputFormatter);
        LocalTime localTime = LocalTime.MIDNIGHT;
        LocalDateTime localDateTime = LocalDateTime.of(localDate, localTime);

        return localDateTime.atZone(ZoneId.systemDefault()).toInstant();
    }

    private MoneyAccount validateRequestSavingAccount(OtpSavingAccountRequest request, Long customerId) {
        if (Validator.isNull(request.getTenor())) {
            throw new BadRequestAlertException(ErrorCode.MSG101082);
        }
        if (Validator.isNull(request.getMaturityInstruction())) {
            throw new BadRequestAlertException(ErrorCode.MSG101083);
        }
        if (Validator.isNull(request.getBeneficiaryAccount())) {
            throw new BadRequestAlertException(ErrorCode.MSG101084);
        }

        if (Validator.isNull(request.getSourceAccount())) {
            throw new BadRequestAlertException(ErrorCode.MSG100173);
        }

        this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatusAndCurrency(
                request.getBeneficiaryAccount(), customerId, EntityStatus.ACTIVE.getStatus(), request.getAmountCurrency()
        ).orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1036));

        return this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatusAndCurrency(
                request.getSourceAccount(), customerId, EntityStatus.ACTIVE.getStatus(), request.getAmountCurrency()
        ).orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1151));
    }

    private SavingAccount validateRequestSettlement(OtpSavingAccountRequest request, Long customerId) {
        if (Validator.isNull(request.getBeneficiaryAccount())) {
            throw new BadRequestAlertException(ErrorCode.MSG101084);
        }
        if (Validator.isNull(request.getSavingAccountNumber())) {
            throw new BadRequestAlertException(ErrorCode.MSG101085);
        }

        this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatusAndCurrency(
                request.getBeneficiaryAccount(), customerId, EntityStatus.ACTIVE.getStatus(), request.getAmountCurrency()
        ).orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1036));


        return this.savingAccountRepository
                .findBySavingAccountNumberAndCustomerIdAndStatus(request.getSavingAccountNumber(), customerId, EntityStatus.ACTIVE.getStatus())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101073));
    }

    private MoneyAccount validateRequestDepositMore(OtpSavingAccountRequest request, Long customerId) {
        if (Validator.isNull(request.getSavingAccountNumber())) {
            throw new BadRequestAlertException(ErrorCode.MSG101085);
        }

        if (Validator.isNull(request.getSourceAccount())) {
            throw new BadRequestAlertException(ErrorCode.MSG100173);
        }

        this.savingAccountRepository
                .findBySavingAccountNumberAndCustomerIdAndStatus(request.getSavingAccountNumber(), customerId, EntityStatus.ACTIVE.getStatus())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101073));

        return this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatusAndCurrency(
                request.getSourceAccount(), customerId, EntityStatus.ACTIVE.getStatus(), request.getAmountCurrency()
        ).orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1151));
    }

    private InforInterestDTO verifyTenor(SavingAccountType savingAccountType, String currency, String period) {

        InforInterestT24Request inforInterestT24Request = InforInterestT24Request.builder()
                .productCode(savingAccountType.getProductCode())
                .period(period)
                .category(String.valueOf(savingAccountType.getProductCategory()))
                .currency(currency)
                .subProduct(savingAccountType.getSubProduct())
                .build();

        InforInterestDTO inforInterestDTO = this.apiGeeSavingAccountService.getInforInterest(inforInterestT24Request);

        List<SavingConfiguration> savingConfigurations = savingConfigurationRepository.findBySavingTypeAndStatus(savingAccountType, EntityStatus.ACTIVE.getStatus());

        if (Validator.isNull(inforInterestDTO)) {
            throw new BadRequestAlertException(ErrorCode.MSG101075);
        }

        //chi lay nhung ky han duoc cau hinh tren CMS
        inforInterestDTO.setLstIntRate(inforInterestDTO.getLstIntRate()
                .stream()
                .filter(i -> savingConfigurations.stream().anyMatch(s -> i.getPeriod().equals(s.getTenor() + s.getTenorPeriod())))
                .collect(Collectors.toList()));
        if (inforInterestDTO.getLstIntRate().isEmpty()) {
            throw new BadRequestAlertException(ErrorCode.MSG101075);
        }

        return inforInterestDTO;
    }

    private void verifyTimeDepositMore(Long customerId, String savingAccountNumber) {
        SavingAccount savingAccount = this.savingAccountRepository
                .findBySavingAccountNumberAndCustomerIdAndStatus(savingAccountNumber, customerId, EntityStatus.ACTIVE.getStatus())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101073));

        // tính đến cuối ngày dương lịch
        long daysBetween = ChronoUnit.DAYS.between(Instant.now(), savingAccount.getSettlementDueTime()) + 1;

        if (daysBetween <= 30) {
            throw new BadRequestAlertException(ErrorCode.MSG101086);
        }
    }

    private void verifyAccountWithT24(String sourceAccountNumber, String beneficiaryAccountNumber) {
        AccNumberVerifyRequest sourceAccountNumberVerify;
        AccNumberVerifyRequest beneficiaryAccountNumberVerify;

        if (Validator.isNotNull(sourceAccountNumber)) {
            sourceAccountNumberVerify = AccNumberVerifyRequest.builder()
                    .accountNumber(sourceAccountNumber)
                    .build();
            this.apiGeeTransferService.verifyAccNumber(sourceAccountNumberVerify);
        }
        if (Validator.isNotNull(beneficiaryAccountNumber)) {
            beneficiaryAccountNumberVerify = AccNumberVerifyRequest.builder()
                    .accountNumber(beneficiaryAccountNumber)
                    .build();
            this.apiGeeTransferService.verifyAccNumber(beneficiaryAccountNumberVerify);
        }
    }

    @Override
    public OtpTransferTransResponse requestOtpInterest(HttpServletRequest httpServletRequest, OtpSavingAccountRequest request) {
        Customer customer = this.getCustomerLogin();
        TransactionDTO transactionDTO = new TransactionDTO(request);
        String userId = customer.getCif() + customer.getIdCardNumber();
        SavingAccount savingAccount = null;
        Long transactionAmount;
        MoneyAccount sourceAccount = null;
        String tenor = null;
        String beneficiaryAccountNumber = null;

        if (Validator.equals(SavingAccountType.FIXED, request.getSavingAccountType())
                && Validator.equals(TransferType.DEPOSIT_MORE, request.getTransferType())) {
            throw new BadRequestAlertException(ErrorCode.MSG100041);
        }

        if (!Validator.equals(request.getAmountCurrency(), this.customerProperties.getDefaultCurrency())) {
            throw new BadRequestAlertException(ErrorCode.MSG100056);
        }

        DOTP dotp = this.checkRegisterDOTP(customer, request.getDeviceId());

        if (Validator.equals(TransferType.SAVING_ACCOUNT, request.getTransferType())
                || Validator.equals(TransferType.DEPOSIT_MORE, request.getTransferType())) {

            if (Validator.isNull(request.getAmount())) {
                throw new BadRequestAlertException(ErrorCode.MSG1138);
            }

            this.validateAmount(request.getAmount(), request.getSavingAccountType());
            transactionDTO.setTransactionType(TransactionType.MINUS);
        } else {
            transactionDTO.setTransactionType(TransactionType.PLUS);
        }

        if (Validator.equals(TransferType.SAVING_ACCOUNT, request.getTransferType())) {
            sourceAccount = this.validateRequestSavingAccount(request, customer.getCustomerId());
            this.verifyTenor(request.getSavingAccountType(), request.getAmountCurrency(), request.getTenor());
            transactionAmount = request.getAmount();
            tenor = request.getTenor();
            beneficiaryAccountNumber = request.getBeneficiaryAccount();
        } else if (Validator.equals(TransferType.SETTLEMENT, request.getTransferType())) {
            savingAccount = this.validateRequestSettlement(request, customer.getCustomerId());
            transactionAmount = savingAccount.getSavingAmount();
            beneficiaryAccountNumber = request.getBeneficiaryAccount();
        } else {
            sourceAccount = this.validateRequestDepositMore(request, customer.getCustomerId());
            transactionAmount = request.getAmount();
            this.verifyTimeDepositMore(customer.getCustomerId(), request.getSavingAccountNumber());
            beneficiaryAccountNumber = request.getSavingAccountNumber();
        }

        if (Validator.isNotNull(request.getSavingAccountNumber())) {
            SavingAccount savingAcc = this.savingAccountRepository
                    .findBySavingAccountNumberAndStatus(request.getSavingAccountNumber(), EntityStatus.ACTIVE.getStatus());
            if (!Validator.equals(request.getSavingAccountType(), savingAcc.getType())) {
                throw new BadRequestAlertException(ErrorCode.MSG101093);
            }
        }

        if (Validator.equals(TransactionType.MINUS, transactionDTO.getTransactionType())
                && sourceAccount.getAvailableAmount() <= request.getAmount()) {
            throw new BadRequestAlertException(ErrorCode.MSG1108);
        }

        String accountNumber = Validator.isNull(request.getSourceAccount()) ? request.getBeneficiaryAccount() : request.getSourceAccount();

        // verify account number with T24
        this.verifyAccountWithT24(request.getSourceAccount(), request.getBeneficiaryAccount());

        transactionDTO.setTransferType(request.getTransferType());
        transactionDTO.setCustomerId(customer.getCustomerId());
        transactionDTO.setPhoneNumber(customer.getPhoneNumber());
        transactionDTO.setTransactionId(UUIDUtil.generateUUID(20));
        transactionDTO.setTransactionAmount(Validator.isNotNull(transactionAmount) ? transactionAmount.doubleValue() : 0D);
        transactionDTO.setCustomerAccNumber(accountNumber);
        transactionDTO.setTarget(Validator.equals(request.getTransferType(), TransferType.SETTLEMENT) ? request.getSavingAccountNumber() : request.getSourceAccount());
        transactionDTO.setBeneficiaryAccountNumber(beneficiaryAccountNumber);

        if (Validator.equals(request.getTransferType(), TransferType.DEPOSIT_MORE)) {
            if (Validator.isNotNull(request.getRemark())) {
                transactionDTO.setMessage(request.getRemark());
            } else {
                throw new BadRequestAlertException(ErrorCode.MSG101077);
            }
        }

        this.consumerOtpService.requestOtp(transactionDTO.getTransactionId(),
                transactionDTO.getPhoneNumber(), OtpType.valueOf(request.getTransferType().name()));

        Transaction transaction = this.transactionRepository.save(this.transactionMapper.toEntity(transactionDTO));
        String transData = request.getDeviceId() + dotp.getToken() + userId;

        SavingAccountVerifyCacheDTO dto = SavingAccountVerifyCacheDTO.builder()
                .tenor(tenor)
                .transactionId(transaction.getTransactionId())
                .type(request.getTransferType())
                .deviceId(request.getDeviceId())
                .savingAccountNumber(request.getSavingAccountNumber())
                .finalizationMethod(request.getMaturityInstruction())
                .rm(request.getRm())
                .build();

        this.savingAccountCacheRepository.put(transaction.getTransactionId(), dto);

        try {
            // set tài khoản mặc định nếu gạt nút setting default account
            if (request.isSetDefaultAccount() && !Validator.equals(TransferType.SETTLEMENT, request.getTransferType())) {
                this.transferService.setDefaultAccountTransfer(customer.getCustomerId(), request.getSourceAccount());
            }
        } catch (Exception e) {
            _log.error("request Otp, after request OTP error:", e);
        }

        return OtpTransferTransResponse.builder()
                .requiredOtp(Boolean.TRUE)
                .transData(transData)
                .transactionId(transaction.getTransactionId())
                .transferTransactionId(transaction.getTransferTransactionId())
                .expiredTime(this.otpProperties.getDuration())
                .build();
    }

    private void validateAmount(Long amount, SavingAccountType savingAccountType) {
        Long minAmount = Validator.equals(SavingAccountType.FIXED, savingAccountType) ? ValidationConstraint.LENGTH.SAVING_ACCOUNT_FIXED
                : ValidationConstraint.LENGTH.SAVING_ACCOUNT_ACCUMULATED;

        if (amount < minAmount) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DEPOSIT_MIN,
                            new Object[]{minAmount}),
                    SavingAccount.class.getSimpleName(), LabelKey.ERROR_DEPOSIT_MIN);
        }
    }

    private QuerySavingAccountDTO getSavingAccount(String savingAccount, String category) {
        QuerySavingAccountT24Request querySavingAccount = QuerySavingAccountT24Request.builder()
                .cusAccNo(savingAccount)
                .category(category)
                .build();

        List<QuerySavingAccountDTO> querySavingAccountDTOS = this.apiGeeSavingAccountService.querySavingAccount(querySavingAccount);

        if (Validator.isNull(querySavingAccountDTOS)) {
            throw new BadRequestAlertException(ErrorCode.MSG101073);
        }

        return querySavingAccountDTOS.stream().findFirst().get();
    }

    @Override
    public QuerySavingAccountResponse querySavingAccount(HttpServletRequest httpServletRequest, QuerySavingAccountRequest request) {
        Customer customer = this.getCustomerLogin();

        Optional<SavingAccount> savingAccount = this.savingAccountRepository.findBySavingAccountNumberAndCustomerIdAndStatus(
                request.getSavingAccount(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus()
        );

        if (!savingAccount.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG101073);
        }

        QuerySavingAccountDTO querySavingAccountDTO = this.getSavingAccount(request.getSavingAccount(), request.getCategory());

        savingAccount.get().setInterestAmtEnd(Long.valueOf(querySavingAccountDTO.getInterestAmountEnd()));
        this.savingAccountRepository.save(savingAccount.get());

        Instant startingDate = this.parseTime(querySavingAccountDTO.getValueDate());
        Instant maturityDate = this.parseTime(querySavingAccountDTO.getMaturityDate());

        return QuerySavingAccountResponse.builder()
                .productName(querySavingAccountDTO.getProductName())
                .startingDate(startingDate)
                .maturityDate(maturityDate)
                .amount(querySavingAccountDTO.getWorkingBal())
                .savingAccountNumber(request.getSavingAccount())
                .tenor(this.getTenor(savingAccount.get().getTenor() + savingAccount.get().getTenorPeriod()))
                .interestAmountEnd(querySavingAccountDTO.getInterestAmountEnd())
                .finalizationMethod(savingAccount.get().getFinalizationMethod())
                .interestAmt(querySavingAccountDTO.getInterestAmt())
                .amount(String.valueOf(savingAccount.get().getSavingAmount()))
                .frequencyInterestPayment(Labels.getLabels(LabelKey.LABEL_END_OF_TERM))
                .fee((double) 0)
                .statusAcct(querySavingAccountDTO.getStatusAcct())
                .receivingAccountNumber(savingAccount.get().getReceivingAccountNumber())
                .build();
    }

    @Override
    public List<SavingAccountDTO> search(HttpServletRequest httpServletRequest, SearchSavingAccountRequest request) {
        Customer customer = this.getCustomerLogin();

        List<SavingAccount> savingAccounts = this.savingAccountRepository.findAllByCustomerIdAndTypeAndStatus(
                customer.getCustomerId(), request.getType(), EntityStatus.ACTIVE.getStatus()
        );

        if (Validator.isNull(savingAccounts)) {
            return Collections.emptyList();
        }

        List<SavingAccountDTO> list = this.savingAccountMapper.toDto(savingAccounts);
        Collections.sort(list, Comparator.comparingLong(SavingAccountDTO::getSavingAccountId).reversed());
        return list;
    }

    private void updateMoneyAccount(Customer customer) {
        AccountBalanceRequest accountBalanceRequest = AccountBalanceRequest.builder().custCode(customer.getCif())
                .build();

        List<AccountBalanceDTO> accountBalanceDTOS = this.apiGeeTransferService
                .checkAccountBalance(accountBalanceRequest);

        if (Validator.isNull(accountBalanceDTOS)) {
            throw new BadRequestAlertException(ErrorCode.MSG100029);
        }

        // compare với số tài khoản trong db và đồng bộ lại
        List<MoneyAccount> moneyAccounts = this.moneyAccountRepository
                .findByCustomerIdAndStatus(customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

        List<MoneyAccountDTO> moneyAccountDTOS = this.moneyAccountMapper.toDto(moneyAccounts);

        if (CollectionUtils.isEmpty(accountBalanceDTOS)) {
            moneyAccountDTOS = new ArrayList<>();
        } else {
            moneyAccountDTOS.forEach(MoneyAccountDTO::deleted);
        }

        List<String> displayCurrencies = Arrays.asList(this.customerProperties.getDisplayAccountCurrency());

        // dong bo tai khoan tren T24 voi cac tai khoan trong he thong
        for (AccountBalanceDTO accountBalanceDTO : accountBalanceDTOS) {
            String accountCurrency = accountBalanceDTO.getAccountCurrency();

            // check đơn vị tiền tệ của tài khoản, nếu ko nằm trong list hiển thị thì bỏ qua
            if (Validator.isNull(accountCurrency) || !displayCurrencies.contains(accountCurrency.toUpperCase())) {
                continue;
            }

            Optional<MoneyAccountDTO> optionalMoneyAccountDTO = moneyAccountDTOS.stream()
                    .filter(item -> Validator.equals(item.getAccountNumber(), accountBalanceDTO.getAccountNumber()))
                    .findFirst();

            // Neu tai khoan ben T24 da ton tai trong he thong => cap nhat, neu chua ton tai
            // => tao moi
            if (optionalMoneyAccountDTO.isPresent()) {
                MoneyAccountDTO moneyAccountDTO = optionalMoneyAccountDTO.get();

                moneyAccountDTO.unDeleted();
                moneyAccountDTO.update(accountBalanceDTO);
            } else {
                MoneyAccountDTO newMoneyAccount = new MoneyAccountDTO();

                newMoneyAccount.setStatus(EntityStatus.ACTIVE.getStatus());
                newMoneyAccount.setAccountNumber(accountBalanceDTO.getAccountNumber());
                newMoneyAccount.setAccountType(accountBalanceDTO.getAccountType());
                newMoneyAccount.setAvailableAmount(accountBalanceDTO.getAvailableBalance());
                newMoneyAccount.setCurrency(accountCurrency);
                newMoneyAccount.setCustomerId(customer.getCustomerId());
                newMoneyAccount.setCifNumber(customer.getCif());
                newMoneyAccount.setOpenDate(accountBalanceDTO.getOpenDate());

                moneyAccountDTOS.add(newMoneyAccount);
            }
        }

        this.moneyAccountRepository.saveAll(this.moneyAccountMapper.toEntity(moneyAccountDTOS));
    }

    private void createOtherNotification(SavingAccount savingAccount, boolean isExpired) {
        Map<String, String> valuesMapCreditAccount = new HashMap<>();
        String message = null;
        NotificationType notificationType;
        Long transactionAmount = 0L;

        String savingAccountType = Validator.equals(SavingAccountType.FIXED, savingAccount.getType()) ? LabelKey.LABEL_FIXED : LabelKey.LABEL_ACCUMULATED;

        //quay vòng gốc: thông báo khác (+ tiền lãi)
        //quay vòng gốc + lãi : thông báo khác ("tài khoản quay vòng gốc và lãi")
        // hết hạn tự động tất toán: tự động tất toán(+tiền lãi) , thông báo biến động số dư
        if (isExpired) {
            if (Validator.equals(savingAccount.getFinalizationMethod(), MaturityInstructionType.ROOT_ROTATION)) {
                message = Labels.getLabels(LabelKey.LABEL_PAY_INTEREST_END_OF_PERIOD) + StringPool.COMMA + StringPool.SPACE
                        + Labels.getLabels(LabelKey.LABEL_ROOT_ROTATION).toLowerCase() + StringPool.SPACE
                        + Labels.getLabels(LabelKey.LABEL_ACCOUNT_TYPE_SAVING_ACCOUNT).toLowerCase() + StringPool.SPACE
                        + Labels.getLabels(savingAccountType).toLowerCase()
                        + StringPool.SPACE + savingAccount.getSavingAccountNumber();
                transactionAmount = Validator.isNull(savingAccount.getInterestAmtEnd()) ? 0L : savingAccount.getInterestAmtEnd();
            } else {
                message = Labels.getLabels(LabelKey.LABEL_CLOSED_SAVING_ACCOUNT) + StringPool.SPACE
                        + Labels.getLabels(savingAccountType).toLowerCase() + StringPool.SPACE
                        + savingAccount.getSavingAccountNumber() + StringPool.COMMA + StringPool.SPACE
                        + Labels.getLabels(LabelKey.LABEL_PAYMENT_OF_PRINCIPAL_AND_INTEREST);
                transactionAmount = savingAccount.getSavingAmount() + savingAccount.getInterestAmtEnd();
            }
            notificationType = NotificationType.TRANSFER_TRANSACTION;
        } else {
            notificationType = NotificationType.OTHER;
            transactionAmount = null;
            message = Labels.getLabels(LabelKey.LABEL_ROTATION_OF_PRINCIPAL_AND_INTEREST) + StringPool.SPACE
                    + Labels.getLabels(LabelKey.LABEL_ACCOUNT_TYPE_SAVING_ACCOUNT).toLowerCase() + StringPool.SPACE
                    + Labels.getLabels(savingAccountType).toLowerCase() + StringPool.SPACE
                    + savingAccount.getSavingAccountNumber();
        }

        // Thông báo thanh toán lãi tiết kiệm
        valuesMapCreditAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(), Labels.getLabels(LabelKey.LABEL_BENEFICIARY_ACCOUNT));
        valuesMapCreditAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), savingAccount.getReceivingAccountNumber());// tài khoản tiết kiệm
        valuesMapCreditAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(), Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION));
        valuesMapCreditAccount.put(TemplateField.TRANSACTION_AMOUNT.name(), StringPool.PLUS + StringUtil.formatMoney(Validator.isNull(transactionAmount) ? 0L : transactionAmount));
        valuesMapCreditAccount.put((TemplateField.CURRENCY.name()), this.customerProperties.getDefaultCurrency());
        valuesMapCreditAccount.put((TemplateField.LABEL_TRANSACTION_TIME.name()), Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));
        valuesMapCreditAccount.put(TemplateField.TRANSACTION_TIME.name(), InstantUtil.formatStringLongDate(Instant.now(), Labels.getDefaultZoneId()));
        valuesMapCreditAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(), Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT));
        valuesMapCreditAccount.put(TemplateField.MESSAGE.name(), message);

        ContentTemplate templateReceiveMoney = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.CHANGE_PAY_SAVING_ACCOUNT.name(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(templateReceiveMoney)) {
            NotificationDTO notiForSender = new NotificationDTO();

            notiForSender.setNotificationStatus(NotificationStatus.PENDING);
            notiForSender.setNotificationType(notificationType);
            notiForSender.setTargetType(TargetType.USER);
            notiForSender.setEndUserType(EndUserType.CUSTOMER);

            // thông báo
            Optional<MoneyAccount> moneyAccount = this.moneyAccountRepository
                    .findByAccountNumberAndCustomerIdAndStatus(savingAccount.getReceivingAccountNumber(),
                            savingAccount.getCustomerId(), EntityStatus.ACTIVE.getStatus());

            if (!moneyAccount.isPresent()) {
                throw new BadRequestAlertException(ErrorCode.MSG1036);
            }

            List<NotiTransaction> notiTransactions = new ArrayList<>();
            Long beneficiaryCustomerId = moneyAccount.get().getCustomerId();

            notiForSender.setClassPk(beneficiaryCustomerId);

            notiForSender.setPublishTime(LocalDateTime.now());

            notiForSender.setTitle(Labels.getLabels(templateReceiveMoney.getTitle()));
            notiForSender.setContent(
                    StringUtil.replaceMapValue(templateReceiveMoney.getContent(), valuesMapCreditAccount));
            notiForSender.setDescription(templateReceiveMoney.getDescription());

            Notification notificationAfterSaved = this.notificationRepository
                    .save(this.notificationMapper.toEntity(notiForSender));

            // save notification transaction
            NotiTransaction notiTransaction = new NotiTransaction();

            notiTransaction.setNotificationId(notificationAfterSaved.getNotificationId());
            notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus());
            notiTransaction.setTransactionId(-2L); // @TODO setTransactionId mặc định cho thông báo

            notiTransactions.add(notiTransaction);
            this.notiTransactionRepository.saveAll(notiTransactions);
        }

    }

    private void createNotification(TransactionDTO transactionDTO, Customer customer, SavingAccount savingAccount) {
        Map<String, String> valuesMapDebitAccount = new HashMap<>();
        Map<String, String> valuesMapCreditAccount = new HashMap<>();

        String transactionFinishTime = transactionDTO.formatTransactionFinishTime();
        String transactionCode = Validator.isNull(transactionDTO.getTransactionCode()) ? transactionDTO.getTransactionId() : transactionDTO.getTransactionCode();

        String messageType = Validator.equals(SavingAccountType.FIXED, savingAccount.getType())
                ? Labels.getLabels(LabelKey.LABEL_FIXED).toLowerCase() : Labels.getLabels(LabelKey.LABEL_ACCUMULATED).toLowerCase();

        String createInterestMessage = StringUtil.join(
                new String[]{customer.getFullname(), Labels.getLabels(LabelKey.MESSAGE_CREATE_INTEREST).toLowerCase()
                        + StringPool.SPACE + messageType
                        + StringPool.SPACE + savingAccount.getSavingAccountNumber().toLowerCase()
                        + StringPool.SPACE + Labels.getLabels(LabelKey.MESSAGE_SUCCESSFUL).toLowerCase()}, StringPool.SPACE);

        String closedInterestMessage = StringUtil.join(
                new String[]{customer.getFullname(), Labels.getLabels(LabelKey.MESSAGE_CLOSED_INTEREST).toLowerCase()
                        + StringPool.SPACE + Labels.getLabels(LabelKey.LABEL_ACCOUNT_TYPE_SAVING_ACCOUNT).toLowerCase()
                        + StringPool.SPACE + messageType
                        + StringPool.SPACE + savingAccount.getSavingAccountNumber()
                        + StringPool.SPACE + Labels.getLabels(LabelKey.MESSAGE_SUCCESSFUL).toLowerCase()
                }, StringPool.SPACE);

        String message = Validator.equals(TransferType.SAVING_ACCOUNT, transactionDTO.getTransferType()) ? createInterestMessage : closedInterestMessage;

        Long getAmtAfterSettlement = Validator.isNull(savingAccount.getAmtAfterSettlement()) ? 0L : savingAccount.getAmtAfterSettlement();

        // transfer
        valuesMapDebitAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(), Labels.getLabels(LabelKey.LABEL_ACCOUNT_TYPE_PAYMENT_ACCOUNT));
        valuesMapDebitAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), transactionDTO.getCustomerAccNumber());
        valuesMapDebitAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(), Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION));
        valuesMapDebitAccount.put(TemplateField.TRANSACTION_AMOUNT.name(), StringPool.MINUS + StringUtil.formatMoney(transactionDTO.getTransactionAmount()));
        valuesMapDebitAccount.put((TemplateField.CURRENCY.name()), transactionDTO.getTransactionCurrency());
        valuesMapDebitAccount.put((TemplateField.LABEL_TRANSACTION_TIME.name()), Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));
        valuesMapDebitAccount.put(TemplateField.TRANSACTION_TIME.name(), transactionFinishTime);
        valuesMapDebitAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(), Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT));

        if (Validator.isNull(transactionDTO.getMessage())) {
            valuesMapDebitAccount.put(TemplateField.CONTENT_TRANSACTION.name(), message);
        } else {
            valuesMapDebitAccount.put(TemplateField.CONTENT_TRANSACTION.name(), transactionDTO.getMessage());
        }
        valuesMapDebitAccount.put((TemplateField.TRANSACTION_CODE.name()), transactionCode);

        // receive
        valuesMapCreditAccount.put(TemplateField.LABEL_REFERENCE_ID.name(), Labels.getLabels(LabelKey.LABEL_REFERENCE_ID));
        valuesMapCreditAccount.put(TemplateField.REFERENCE_ID.name(), transactionDTO.getTransactionId());
        valuesMapCreditAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(), Labels.getLabels(LabelKey.LABEL_BENEFICIARY_ACCOUNT));
        valuesMapCreditAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), savingAccount.getReceivingAccountNumber());// tài khoản thụ hưởng
        valuesMapCreditAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(), Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION));
        valuesMapCreditAccount.put(TemplateField.TRANSACTION_AMOUNT.name(), StringPool.PLUS + StringUtil.formatMoney(transactionDTO.getTransactionAmount() + getAmtAfterSettlement));
        valuesMapCreditAccount.put((TemplateField.CURRENCY.name()), transactionDTO.getTransactionCurrency());
        valuesMapCreditAccount.put((TemplateField.LABEL_TRANSACTION_TIME.name()), Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));
        valuesMapCreditAccount.put(TemplateField.TRANSACTION_TIME.name(), transactionFinishTime);
        valuesMapCreditAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(), Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT));

        if (Validator.isNull(transactionDTO.getMessage())) {
            valuesMapCreditAccount.put((TemplateField.MESSAGE.name()), message);
        } else {
            valuesMapCreditAccount.put((TemplateField.MESSAGE.name()), transactionDTO.getMessage());
        }
        valuesMapCreditAccount.put((TemplateField.TRANSACTION_CODE.name()), transactionCode);

        ContentTemplate templateTransferMoney = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.BALANCE_CHANGE_PAY_INSURANCE.name(), EntityStatus.ACTIVE.getStatus());

        NotificationDTO notiForReceiver = new NotificationDTO();
        NotificationDTO notiForSender = new NotificationDTO();

        if (Validator.isNotNull(templateTransferMoney) &&
                (Validator.equals(TransferType.SAVING_ACCOUNT, transactionDTO.getTransferType())
                        || Validator.equals(TransferType.DEPOSIT_MORE, transactionDTO.getTransferType()))) {

            notiForReceiver.setNotificationStatus(NotificationStatus.PENDING);
            notiForReceiver.setNotificationType(NotificationType.TRANSFER_TRANSACTION);
            notiForReceiver.setTargetType(TargetType.USER);
            notiForReceiver.setEndUserType(EndUserType.CUSTOMER);
            notiForReceiver.setClassPk(transactionDTO.getCustomerId());
            notiForReceiver.setPublishTime(LocalDateTime.now());

            notiForReceiver.setTitle(Labels.getLabels(templateTransferMoney.getTitle()));
            notiForReceiver
                    .setContent(StringUtil.replaceMapValue(templateTransferMoney.getContent(), valuesMapDebitAccount));
            notiForReceiver.setDescription(templateTransferMoney.getDescription());

            Notification notificationAfterSaved = this.notificationRepository
                    .save(this.notificationMapper.toEntity(notiForReceiver));
            notiForReceiver = this.notificationMapper.toDto(notificationAfterSaved);

            // save notification transaction
            NotiTransaction notiTransaction = new NotiTransaction();

            notiTransaction.setNotificationId(notificationAfterSaved.getNotificationId());
            notiTransaction.setTransactionId(transactionDTO.getTransferTransactionId());
            notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus());

            this.notiTransactionRepository.save(notiTransaction);
        }

        ContentTemplate templateReceiveMoney = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.BALANCE_CHANGE_RECEIVE_MONEY.name(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(templateReceiveMoney) && Validator.equals(TransferType.SETTLEMENT, transactionDTO.getTransferType())) {

            notiForSender.setNotificationStatus(NotificationStatus.PENDING);
            notiForSender.setNotificationType(NotificationType.TRANSFER_TRANSACTION);
            notiForSender.setTargetType(TargetType.USER);
            notiForSender.setEndUserType(EndUserType.CUSTOMER);

            // Neu giao dich la chuyen tien cho các tài khoản bên ngoài hệ thống => ko gửi
            // thông báo
            Optional<MoneyAccount> moneyAccount = this.moneyAccountRepository
                    .findByAccountNumberAndCustomerIdAndStatus(transactionDTO.getBeneficiaryAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

            if (!moneyAccount.isPresent()) {
                throw new BadRequestAlertException(ErrorCode.MSG1036);
            }

            List<NotiTransaction> notiTransactions = new ArrayList<>();
            Long beneficiaryCustomerId = moneyAccount.get().getCustomerId();

            notiForSender.setClassPk(beneficiaryCustomerId);

            notiForSender.setPublishTime(LocalDateTime.now());

            notiForSender.setTitle(Labels.getLabels(templateReceiveMoney.getTitle()));
            notiForSender.setContent(
                    StringUtil.replaceMapValue(templateReceiveMoney.getContent(), valuesMapCreditAccount));
            notiForSender.setDescription(templateReceiveMoney.getDescription());

            Notification notificationAfterSaved = this.notificationRepository
                    .save(this.notificationMapper.toEntity(notiForSender));
            notiForSender = this.notificationMapper.toDto(notificationAfterSaved);

            // save notification transaction
            NotiTransaction notiTransaction = new NotiTransaction();

            notiTransaction.setNotificationId(notificationAfterSaved.getNotificationId());
            notiTransaction.setTransactionId(transactionDTO.getTransferTransactionId());
            notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus());

            notiTransactions.add(notiTransaction);
            this.notiTransactionRepository.saveAll(notiTransactions);
        }

        if (Validator.equals(notiForSender.getNotificationType(), NotificationType.TRANSFER_TRANSACTION)) {
            this.sendService.sendNotification(notiForSender);
        }
        if (Validator.equals(notiForReceiver.getNotificationType(), NotificationType.TRANSFER_TRANSACTION)) {
            this.sendService.sendNotification(notiForReceiver);
        }
    }

    private Customer getCustomerLogin() {
        return GwSecurityUtils.getCustomerLogin()
                .orElseThrow(() -> new UnauthorizedException(ErrorCode.MSG1077));
    }
}
