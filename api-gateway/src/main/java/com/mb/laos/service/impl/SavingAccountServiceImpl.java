package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.NoRollBackBadRequestAlertException;
import com.mb.laos.api.exception.UnauthorizedException;
import com.mb.laos.api.request.AccNumberVerifyRequest;
import com.mb.laos.api.request.AccountBalanceRequest;
import com.mb.laos.api.request.AccountSavingRequest;
import com.mb.laos.api.request.Amount;
import com.mb.laos.api.request.AuthenMethod;
import com.mb.laos.api.request.ClosedInterestT24Request;
import com.mb.laos.api.request.CreateInterestT24Request;
import com.mb.laos.api.request.InforInterestT24Request;
import com.mb.laos.api.request.InquiryTransactionStatusRequest;
import com.mb.laos.api.request.MakeTransferInteresT24Request;
import com.mb.laos.api.request.QuerySavingAccountT24Request;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.CustomerProperties;
import com.mb.laos.configuration.OtpProperties;
import com.mb.laos.enums.AccountTypeCif;
import com.mb.laos.enums.BranchCode;
import com.mb.laos.enums.CommonCategory;
import com.mb.laos.enums.CurrencyType;
import com.mb.laos.enums.EndUserType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.MaturityInstructionType;
import com.mb.laos.enums.NotificationStatus;
import com.mb.laos.enums.NotificationType;
import com.mb.laos.enums.OtpConfirmType;
import com.mb.laos.enums.OtpType;
import com.mb.laos.enums.SavingAccountType;
import com.mb.laos.enums.TargetType;
import com.mb.laos.enums.TemplateCode;
import com.mb.laos.enums.TemplateField;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.enums.TransactionType;
import com.mb.laos.enums.TransferType;
import com.mb.laos.gateway.request.ConfirmOtpSavingAccountRequest;
import com.mb.laos.gateway.request.InforInterestRequest;
import com.mb.laos.gateway.request.OtpSavingAccountRequest;
import com.mb.laos.gateway.request.QuerySavingAccountRequest;
import com.mb.laos.gateway.request.SearchSavingAccountRequest;
import com.mb.laos.gateway.response.CreateInterestResponse;
import com.mb.laos.gateway.response.OtpTransferConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferTransResponse;
import com.mb.laos.gateway.response.QuerySavingAccountResponse;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Common;
import com.mb.laos.model.ContentTemplate;
import com.mb.laos.model.Customer;
import com.mb.laos.model.DOTP;
import com.mb.laos.model.MoneyAccount;
import com.mb.laos.model.NotiTransaction;
import com.mb.laos.model.Notification;
import com.mb.laos.model.SavingAccount;
import com.mb.laos.model.SavingConfiguration;
import com.mb.laos.model.Transaction;
import com.mb.laos.model.dto.AccountBalanceDTO;
import com.mb.laos.model.dto.AccountSavingDTO;
import com.mb.laos.model.dto.ClosedInterestDTO;
import com.mb.laos.model.dto.InforInterestDTO;
import com.mb.laos.model.dto.InquiryTransactionStatusDTO;
import com.mb.laos.model.dto.IntRateDTO;
import com.mb.laos.model.dto.InterestDTO;
import com.mb.laos.model.dto.MakeTransferInterestDTO;
import com.mb.laos.model.dto.MoneyAccountDTO;
import com.mb.laos.model.dto.NotificationDTO;
import com.mb.laos.model.dto.ProductFeatureDTO;
import com.mb.laos.model.dto.QuerySavingAccountDTO;
import com.mb.laos.model.dto.SavingAccountDTO;
import com.mb.laos.model.dto.SavingAccountVerifyCacheDTO;
import com.mb.laos.model.dto.TotalMoneySavingAccountDTO;
import com.mb.laos.model.dto.TransactionDTO;
import com.mb.laos.model.dto.TransactionSavingAccountDTO;
import com.mb.laos.model.search.SavingAccountHistorySearch;
import com.mb.laos.repository.CommonRepository;
import com.mb.laos.repository.ContentTemplateRepository;
import com.mb.laos.repository.CustomerRepository;
import com.mb.laos.repository.DotpRepository;
import com.mb.laos.repository.MoneyAccountRepository;
import com.mb.laos.repository.NotiTransactionRepository;
import com.mb.laos.repository.NotificationRepository;
import com.mb.laos.repository.SavingAccountCacheRepository;
import com.mb.laos.repository.SavingAccountRepository;
import com.mb.laos.repository.SavingConfigurationRepository;
import com.mb.laos.repository.TransactionRepository;
import com.mb.laos.repository.TransactionSavingAccountRepository;
import com.mb.laos.security.util.GwSecurityUtils;
import com.mb.laos.service.ApiGeeSavingAccountService;
import com.mb.laos.service.ApiGeeTransferService;
import com.mb.laos.service.ConsumerOtpService;
import com.mb.laos.service.SavingAccountService;
import com.mb.laos.service.SendService;
import com.mb.laos.service.TransferService;
import com.mb.laos.service.mapper.MoneyAccountMapper;
import com.mb.laos.service.mapper.NotificationMapper;
import com.mb.laos.service.mapper.SavingAccountMapper;
import com.mb.laos.service.mapper.TransactionMapper;
import com.mb.laos.service.mapper.TransactionSavingAccountMapper;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.MD5Generator;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.TimeUtil;
import com.mb.laos.util.UUIDUtil;
import com.mb.laos.util.Validator;
import com.mb.laos.validator.ValidationConstraint;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service implementation cho quản lý tài khoản tiết kiệm
 *
 * Class này triển khai các chức năng chính của tài khoản tiết kiệm bao gồm:
 * - Tạo tài khoản tiết kiệm mới
 * - Gửi thêm tiền vào tài khoản tiết kiệm
 * - Tất toán tài khoản tiết kiệm
 * - Truy vấn thông tin và lịch sử giao dịch
 * - Quản lý thông báo và cập nhật số dư
 */
@Slf4j // Lombok annotation để tự động tạo logger
@Service // Spring annotation đánh dấu đây là service component
@Transactional // Spring annotation để quản lý transaction tự động
@RequiredArgsConstructor // Lombok annotation tự động tạo constructor với final fields
public class SavingAccountServiceImpl implements SavingAccountService {
    // Service để gọi API T24 cho các chức năng tài khoản tiết kiệm
    private final ApiGeeSavingAccountService apiGeeSavingAccountService;

    // Repository để quản lý thông tin DOTP (Dynamic OTP)
    private final DotpRepository dotpRepository;

    // Repository để quản lý dữ liệu tài khoản tiết kiệm
    private final SavingAccountRepository savingAccountRepository;

    // Mapper để chuyển đổi giữa entity và DTO cho tài khoản tiết kiệm
    private final SavingAccountMapper savingAccountMapper;

    // Repository để quản lý thông tin tài khoản tiền
    private final MoneyAccountRepository moneyAccountRepository;

    // Mapper để chuyển đổi giữa entity và DTO cho giao dịch
    private final TransactionMapper transactionMapper;

    // Repository để quản lý dữ liệu giao dịch
    private final TransactionRepository transactionRepository;

    // Service để xử lý OTP và xác thực
    private final ConsumerOtpService consumerOtpService;

    // Properties chứa cấu hình OTP
    private final OtpProperties otpProperties;

    // Service để gọi API T24 cho các chức năng chuyển tiền
    private final ApiGeeTransferService apiGeeTransferService;

    // Properties chứa cấu hình khách hàng
    private final CustomerProperties customerProperties;

    // Mapper để chuyển đổi giữa entity và DTO cho tài khoản tiền
    private final MoneyAccountMapper moneyAccountMapper;

    // Mapper để chuyển đổi giữa entity và DTO cho giao dịch tài khoản tiết kiệm
    private final TransactionSavingAccountMapper transactionSavingAccountMapper;

    // Repository để quản lý giao dịch tài khoản tiết kiệm
    private final TransactionSavingAccountRepository transactionSavingAccountRepository;

    // Mapper để chuyển đổi giữa entity và DTO cho thông báo
    private final NotificationMapper notificationMapper;

    // Repository để quản lý thông báo giao dịch
    private final NotiTransactionRepository notiTransactionRepository;

    // Repository để quản lý thông báo
    private final NotificationRepository notificationRepository;

    // Repository để quản lý template nội dung thông báo
    private final ContentTemplateRepository contentTemplateRepository;

    // Repository để quản lý dữ liệu common/master data
    private final CommonRepository commonRepository;

    // Service để xử lý các chức năng chuyển tiền
    private final TransferService transferService;

    // Repository để quản lý cache tài khoản tiết kiệm
    private final SavingAccountCacheRepository savingAccountCacheRepository;

    // Repository để quản lý thông tin khách hàng
    private final CustomerRepository customerRepository;

    // Repository để quản lý cấu hình tài khoản tiết kiệm
    private final SavingConfigurationRepository savingConfigurationRepository;

    // Service để gửi thông báo
    private final SendService sendService;


    /**
     * Lấy thông tin lãi suất tài khoản tiết kiệm
     *
     * Method này thực hiện các bước sau:
     * 1. Xác thực người dùng đã đăng nhập
     * 2. Kiểm tra và xác thực kỳ hạn
     * 3. Lấy danh sách kỳ hạn được phép từ cấu hình
     * 4. Kiểm tra tính hợp lệ của kỳ hạn
     * 5. Xử lý và format dữ liệu trả về
     *
     * @param httpServletRequest HTTP request
     * @param request InforInterestRequest chứa thông tin loại tài khoản, tiền tệ và kỳ hạn
     * @return List<IntRateDTO> danh sách thông tin lãi suất theo kỳ hạn
     */
    @Override
    public List<IntRateDTO> getInforInterest(HttpServletRequest httpServletRequest, InforInterestRequest request) {
        // Xác thực người dùng đã đăng nhập
        this.getCustomerLogin();

        // Xác thực kỳ hạn và lấy thông tin lãi suất từ T24
        InforInterestDTO inforInterestDTO = this.verifyTenor(request.getSavingAccountType(), request.getCurrency(), request.getPeriod());

        // Lấy danh sách các kỳ hạn được phép từ cấu hình common
        List<Common> commons = this.commonRepository
                .findAllByCategoryAndStatus(CommonCategory.TENOR_PERIOD.toString(), EntityStatus.ACTIVE.getStatus());

        // Kiểm tra tất cả kỳ hạn trong danh sách có hợp lệ không (phải kết thúc bằng mã kỳ hạn được phép)
        Boolean isCheck = inforInterestDTO.getLstIntRate().stream().allMatch(itemB -> commons.stream()
                .anyMatch(itemA -> itemB.getPeriod().endsWith(itemA.getCode())));

        // Nếu có kỳ hạn không hợp lệ thì ném exception
        if (!Validator.equals(isCheck, Boolean.TRUE)) {
            throw new BadRequestAlertException(ErrorCode.MSG101080);
        }

        // Xử lý dữ liệu: tách số tháng và đơn vị kỳ hạn cho từng item
        inforInterestDTO.getLstIntRate().forEach(item -> {
            item.setMonth(StringUtil.extractNumber(item.getPeriod())); // Tách số tháng từ period
            item.setTenorPeriod(StringUtil.extractCharacterStr(item.getPeriod())); // Tách đơn vị kỳ hạn từ period
        });

        // Thay thế mã đơn vị kỳ hạn bằng label hiển thị cho người dùng
        inforInterestDTO.getLstIntRate().forEach(item -> item.setPeriod(item.getPeriod().replace(StringUtil.extractCharacterStr(item.getPeriod()),
                this.getLabels(StringUtil.extractCharacterStr(item.getPeriod())))));

        // Trả về danh sách thông tin lãi suất đã được xử lý
        return inforInterestDTO.getLstIntRate();
    }

    /**
     * Lấy label hiển thị cho đơn vị kỳ hạn
     *
     * Method này chuyển đổi mã đơn vị kỳ hạn (M, Y...) thành label hiển thị (tháng, năm...)
     *
     * @param tenorPeriod Mã đơn vị kỳ hạn cần chuyển đổi
     * @return String label hiển thị với khoảng trắng ở đầu
     */
    private String getLabels(String tenorPeriod) {
        // Tìm thông tin common theo category TENOR_PERIOD và code
        Common common = this.commonRepository
                .findByCategoryAndCodeAndStatus(CommonCategory.TENOR_PERIOD.toString(), tenorPeriod, EntityStatus.ACTIVE.getStatus())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101080)); // Ném exception nếu không tìm thấy

        // Trả về label với khoảng trắng ở đầu
        return StringPool.SPACE + Labels.getLabels(common.getValue());
    }

    /**
     * Tất toán tài khoản tiết kiệm
     *
     * Method này thực hiện tất toán tài khoản tiết kiệm trước hạn hoặc đúng hạn:
     * 1. Xác thực người dùng và giao dịch
     * 2. Kiểm tra tài khoản tiết kiệm tồn tại
     * 3. Xác thực OTP và DOTP
     * 4. Gọi API T24 để thực hiện tất toán
     * 5. Cập nhật trạng thái và tạo thông báo
     *
     * @param httpServletRequest HTTP request
     * @param request ConfirmOtpSavingAccountRequest chứa thông tin xác nhận OTP
     * @return CreateInterestResponse thông tin kết quả tất toán
     */
    @Override
    public CreateInterestResponse closeInterest(HttpServletRequest httpServletRequest, ConfirmOtpSavingAccountRequest request) {
        // Lấy thông tin khách hàng đã đăng nhập
        Customer customer = this.getCustomerLogin();

        // Tìm giao dịch theo transaction ID và customer ID
        Transaction transaction = this.transactionRepository.findByTransactionIdAndCustomerId(
                request.getTransactionId(), customer.getCustomerId());

        // Lấy thông tin cache của tài khoản tiết kiệm từ transaction ID
        SavingAccountVerifyCacheDTO savingAccountVerifyCacheDTO = this.getSavingAccountCache(transaction.getTransactionId());

        // Xác thực trạng thái giao dịch
        this.verifyTransaction(transaction);

        // Tìm tài khoản tiết kiệm theo số tài khoản, customer ID và trạng thái active
        Optional<SavingAccount> savingAccount = this.savingAccountRepository.findBySavingAccountNumberAndCustomerIdAndStatus(
                savingAccountVerifyCacheDTO.getSavingAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus()
        );

        // Kiểm tra tài khoản tiết kiệm có tồn tại không
        if (!savingAccount.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG101073); // Tài khoản tiết kiệm không tồn tại
        }

        // Tạo user ID từ CIF và số CMND
        String userId = customer.getCif() + customer.getIdCardNumber();

        // Kiểm tra DOTP đã được đăng ký cho device này chưa
        DOTP dotp = this.checkRegisterDOTP(customer, savingAccountVerifyCacheDTO.getDeviceId());

        // Tạo thông tin xác thực cho API T24
        AuthenMethod authenMethod = AuthenMethod.builder()
                .otpValue(request.getOtpValue()) // Giá trị OTP người dùng nhập
                .deviceId(savingAccountVerifyCacheDTO.getDeviceId()) // Device ID từ cache
                .token(dotp.getToken()) // Token DOTP
                .transData(MD5Generator.md5(request.getTransData())) // Mã hóa MD5 của transaction data
                .userId(userId) // User ID
                .build();

        // Tạo request để gọi API T24 tất toán tài khoản tiết kiệm
        ClosedInterestT24Request closedInterestT24Request = ClosedInterestT24Request.builder()
                .chanel("APP") // Kênh giao dịch là mobile app
                .savingAccountNumber(savingAccountVerifyCacheDTO.getSavingAccountNumber()) // Số tài khoản tiết kiệm cần tất toán
                .receivePrincipalAccount(transaction.getBeneficiaryAccountNumber()) // Tài khoản nhận tiền gốc và lãi
                .category(SavingAccountType.valueOf(transaction.getSavingAccountType()).getProductCategory()) // Loại sản phẩm tiết kiệm
                .subProduct(SavingAccountType.valueOf(transaction.getSavingAccountType()).getSubProduct()) // Sản phẩm con
                .cifT24(customer.getCif()) // Mã CIF khách hàng trong T24
                .authenMethod(authenMethod) // Thông tin xác thực
                .build();

        // Gọi service xác thực OTP và thực hiện tất toán qua T24
        ClosedInterestDTO response = this.consumerOtpService.verifyOtp(transaction.getTransactionId(),
                transaction.getPhoneNumber(), OtpType.SETTLEMENT, closedInterestT24Request,
                this.apiGeeSavingAccountService::closedInterest, OtpConfirmType.DOTP);

        // Xử lý kết quả trả về từ T24
        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.FAILED)) {
            // Nếu giao dịch thất bại
            transaction.setTransactionStatus(TransactionStatus.FAILED); // Cập nhật trạng thái giao dịch thành FAILED
            transaction.setTransactionFinishTime(Instant.now()); // Cập nhật thời gian kết thúc giao dịch
            this.transactionRepository.save_(transaction); // Lưu giao dịch

            // Kiểm tra có mã lỗi từ T24 không
            if (Validator.isNotNull(response.getT24ErrorCode())) {
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode()); // Ném exception với mã lỗi T24
            }

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023); // Ném exception lỗi chung
        } else if (Validator.equals(response.getTransactionStatus(), TransactionStatus.TIMEOUT)) {
            // Nếu giao dịch timeout
            _log.info("Transaction has been timeout, {}", transaction.getTransactionId()); // Log thông tin timeout

            transaction.setTransactionStatus(TransactionStatus.FAILED); // Cập nhật trạng thái thành FAILED
            this.transactionRepository.save_(transaction); // Lưu giao dịch

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162); // Ném exception timeout
        }

        // Cập nhật thông tin tài khoản tiết kiệm sau khi tất toán thành công
        savingAccount.get().setStatus(EntityStatus.INACTIVE.getStatus()); // Đặt trạng thái tài khoản thành INACTIVE
        savingAccount.get().setAmtAfterSettlement(Long.valueOf(response.getInterestAmount())); // Cập nhật số tiền sau tất toán
        savingAccount.get().setReceivingAccountNumber(transaction.getBeneficiaryAccountNumber()); // Cập nhật tài khoản nhận tiền
        savingAccount.get().setMaturityDate(Instant.now()); // Cập nhật ngày đáo hạn thực tế
        SavingAccount savingAcc = this.savingAccountRepository.save(savingAccount.get()); // Lưu thông tin tài khoản tiết kiệm

        // Cập nhật trạng thái giao dịch thành công
        transaction.setTransactionStatus(TransactionStatus.SUCCESS); // Đặt trạng thái giao dịch thành SUCCESS
        transaction.setTransactionFinishTime(Instant.now()); // Cập nhật thời gian hoàn thành giao dịch
        this.transactionRepository.save(transaction); // Lưu thông tin giao dịch

        // Tạo bản ghi giao dịch tài khoản tiết kiệm
        TransactionSavingAccountDTO transactionSavingAccountDTO = new TransactionSavingAccountDTO();
        transactionSavingAccountDTO.setSavingAccountId(savingAccount.get().getSavingAccountId()); // ID tài khoản tiết kiệm
        transactionSavingAccountDTO.setStatus(EntityStatus.ACTIVE.getStatus()); // Trạng thái bản ghi
        transactionSavingAccountDTO.setTransactionTime(Instant.now()); // Thời gian giao dịch
        transactionSavingAccountDTO.setTransferTransactionId(transaction.getTransferTransactionId()); // ID giao dịch chuyển tiền
        transactionSavingAccountDTO.setSavingAccountNumber(savingAccountVerifyCacheDTO.getSavingAccountNumber()); // Số tài khoản tiết kiệm
        transactionSavingAccountDTO.setDescription(transaction.getMessage()); // Mô tả giao dịch

        // Lưu bản ghi giao dịch tài khoản tiết kiệm
        this.transactionSavingAccountRepository.save(this.transactionSavingAccountMapper.toEntity(transactionSavingAccountDTO));

        // Cập nhật số dư tài khoản tiền của khách hàng
        this.updateMoneyAccount(customer);

        // Xóa cache tài khoản tiết kiệm
        this.savingAccountCacheRepository.evict(transaction.getTransactionId());

        // Tạo thông báo cho khách hàng
        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);
        this.createNotification(transactionDTO, customer, savingAcc);

        // Tạo response trả về cho client
        return CreateInterestResponse.builder()
                .savingAccountNumber(savingAccount.get().getSavingAccountNumber()) // Số tài khoản tiết kiệm
                .productName(savingAccount.get().getType().toString()) // Tên sản phẩm tiết kiệm
                .amount(Validator.isNotNull(transaction.getTransactionAmount()) ? String.valueOf(transaction.getTransactionAmount().longValue()) : String.valueOf(0L)) // Số tiền gốc
                .finalizationMethod(savingAccount.get().getFinalizationMethod()) // Phương thức tất toán
                .beneficiaryAccountNumber(transaction.getBeneficiaryAccountNumber()) // Tài khoản nhận tiền
                .valueDate(String.valueOf(savingAccount.get().getStartTime())) // Ngày hiệu lực
                .maturityDate(String.valueOf(savingAccount.get().getSettlementDueTime())) // Ngày đáo hạn
                .interestAmount(response.getInterestAmount()) // Số tiền lãi
                .frequencyInterestPayment(Labels.getLabels(LabelKey.LABEL_END_OF_TERM)) // Tần suất trả lãi
                .createdDate(transaction.getTransactionFinishTime()) // Ngày tạo
                .build();
    }

    /**
     * Lấy thông tin cache tài khoản tiết kiệm từ transaction ID
     *
     * Method này lấy thông tin cache đã được lưu trước đó khi tạo OTP
     * Cache chứa các thông tin cần thiết để xác thực và thực hiện giao dịch
     *
     * @param transactionId ID giao dịch để lấy cache
     * @return SavingAccountVerifyCacheDTO thông tin cache tài khoản tiết kiệm
     * @throws BadRequestAlertException nếu không tìm thấy cache
     */
    private SavingAccountVerifyCacheDTO getSavingAccountCache(String transactionId) {
        // Lấy thông tin cache từ repository theo transaction ID
        SavingAccountVerifyCacheDTO savingAccountVerifyCacheDTO = this.savingAccountCacheRepository.get(transactionId);

        // Kiểm tra cache có tồn tại không
        if (Validator.isNull(savingAccountVerifyCacheDTO)) {
            throw new BadRequestAlertException(ErrorCode.MSG101081); // Cache không tồn tại hoặc đã hết hạn
        }

        // Trả về thông tin cache
        return savingAccountVerifyCacheDTO;
    }

    /**
     * Gửi thêm tiền vào tài khoản tiết kiệm
     *
     * Method này thực hiện gửi thêm tiền vào tài khoản tiết kiệm đã có:
     * 1. Xác thực người dùng và giao dịch
     * 2. Kiểm tra tài khoản tiết kiệm tồn tại
     * 3. Xác thực OTP và DOTP
     * 4. Gọi API T24 để thực hiện gửi thêm tiền
     * 5. Cập nhật số dư và tạo thông báo
     *
     * @param httpServletRequest HTTP request
     * @param request ConfirmOtpSavingAccountRequest chứa thông tin xác nhận OTP
     * @return OtpTransferConfirmResponse thông tin kết quả gửi thêm tiền
     */
    @Override
    public OtpTransferConfirmResponse depositMore(HttpServletRequest httpServletRequest, ConfirmOtpSavingAccountRequest request) {
        // Lấy thông tin khách hàng đã đăng nhập
        Customer customer = this.getCustomerLogin();

        // Tìm giao dịch theo transaction ID và customer ID
        Transaction transaction = this.transactionRepository.findByTransactionIdAndCustomerId(request.getTransactionId(),
                customer.getCustomerId());

        // Lấy loại tài khoản tiết kiệm từ giao dịch
        SavingAccountType savingAccountType = SavingAccountType.valueOf(transaction.getSavingAccountType());

        // Xác thực trạng thái giao dịch
        this.verifyTransaction(transaction);

        // Lấy thông tin cache của tài khoản tiết kiệm
        SavingAccountVerifyCacheDTO savingAccountVerifyCacheDTO = this.getSavingAccountCache(transaction.getTransactionId());

        // Tìm tài khoản tiết kiệm theo số tài khoản, customer ID và trạng thái active
        Optional<SavingAccount> savingAccount = this.savingAccountRepository.findBySavingAccountNumberAndCustomerIdAndStatus(
                savingAccountVerifyCacheDTO.getSavingAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus()
        );

        // Kiểm tra tài khoản tiết kiệm có tồn tại không
        if (!savingAccount.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG101073); // Tài khoản tiết kiệm không tồn tại
        }

        // Tạo user ID từ CIF và số CMND
        String userId = customer.getCif() + customer.getIdCardNumber();

        // Kiểm tra DOTP đã được đăng ký cho device này chưa
        DOTP dotp = this.checkRegisterDOTP(customer, savingAccountVerifyCacheDTO.getDeviceId());

        // Tạo thông tin xác thực cho API T24
        AuthenMethod authenMethod = AuthenMethod.builder()
                .otpValue(request.getOtpValue()) // Giá trị OTP người dùng nhập
                .deviceId(savingAccountVerifyCacheDTO.getDeviceId()) // Device ID từ cache
                .token(dotp.getToken()) // Token DOTP
                .transData(MD5Generator.md5(request.getTransData())) // Mã hóa MD5 của transaction data
                .userId(userId) // User ID
                .build();

        // Tạo request để gọi API T24 gửi thêm tiền vào tài khoản tiết kiệm
        MakeTransferInteresT24Request depositMoreRequest = MakeTransferInteresT24Request.builder()
                .cifT24(customer.getCif()) // Mã CIF khách hàng trong T24
                .amount(Validator.isNotNull(transaction.getTransactionAmount()) ? String.valueOf(transaction.getTransactionAmount().longValue()) : String.valueOf(0L)) // Số tiền gửi thêm
                .sourceAccount(transaction.getCustomerAccNumber()) // Tài khoản nguồn (trừ tiền)
                .changeAmount(String.valueOf(Math.round(transaction.getTransactionFee()))) // Phí giao dịch
                .savingAccountNo(savingAccountVerifyCacheDTO.getSavingAccountNumber()) // Số tài khoản tiết kiệm đích
                .branch(BranchCode.LA0010001.name()) // Mã chi nhánh
                .category(savingAccountType.getProductCategory()) // Loại sản phẩm tiết kiệm
                .remark(transaction.getMessage()) // Ghi chú giao dịch
                .amountCurrency(transaction.getTransactionCurrency()) // Đơn vị tiền tệ
                .authenMethod(authenMethod) // Thông tin xác thực
                .build();

        // Gọi service xác thực OTP và thực hiện gửi thêm tiền qua T24
        MakeTransferInterestDTO response = this.consumerOtpService.verifyOtp(transaction.getTransactionId(),
                transaction.getPhoneNumber(), OtpType.DEPOSIT_MORE, depositMoreRequest,
                this.apiGeeSavingAccountService::makeTransferInterest, OtpConfirmType.DOTP);

        // Xử lý kết quả trả về từ T24
        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.FAILED)) {
            // Nếu giao dịch thất bại
            transaction.setTransactionStatus(TransactionStatus.FAILED); // Cập nhật trạng thái giao dịch thành FAILED
            transaction.setTransactionFinishTime(Instant.now()); // Cập nhật thời gian kết thúc giao dịch
            this.transactionRepository.save_(transaction); // Lưu giao dịch

            // Kiểm tra có mã lỗi từ T24 không
            if (Validator.isNotNull(response.getT24ErrorCode())) {
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode()); // Ném exception với mã lỗi T24
            }

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023); // Ném exception lỗi chung
        } else if (Validator.equals(response.getTransactionStatus(), TransactionStatus.TIMEOUT)) {
            // Nếu giao dịch timeout
            _log.info("Transaction has been timeout, {}", transaction.getTransactionId()); // Log thông tin timeout

            // Tạo request để kiểm tra trạng thái giao dịch
            InquiryTransactionStatusRequest transactionStatusRequest = InquiryTransactionStatusRequest.builder()
                    .transactionId(response.getReferenceNumber()) // Reference number từ T24
                    .build();

            // Gọi API kiểm tra trạng thái giao dịch
            InquiryTransactionStatusDTO inquiryTransactionStatusDTO = this.apiGeeTransferService
                    .inquiryTransactionStatus(transactionStatusRequest);

            // Kiểm tra kết quả inquiry
            if (Validator.isNull(inquiryTransactionStatusDTO)
                    || Validator.isNotNull(inquiryTransactionStatusDTO.getErrorCode())) {
                // Nếu inquiry thất bại hoặc có lỗi
                transaction.setTransactionStatus(TransactionStatus.FAILED); // Đặt trạng thái giao dịch thành FAILED

                this.transactionRepository.save_(transaction); // Lưu giao dịch

                throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162); // Ném exception timeout
            }
            // Nếu inquiry thành công, đặt trạng thái response thành SUCCESS
            response.setTransactionStatus(TransactionStatus.SUCCESS);
        }

        // Tạo bản ghi giao dịch tài khoản tiết kiệm
        TransactionSavingAccountDTO transactionSavingAccountDTO = new TransactionSavingAccountDTO();
        transactionSavingAccountDTO.setSavingAccountId(savingAccount.get().getSavingAccountId()); // ID tài khoản tiết kiệm
        transactionSavingAccountDTO.setStatus(EntityStatus.ACTIVE.getStatus()); // Trạng thái bản ghi
        transactionSavingAccountDTO.setTransactionCode(response.getT24ReferenceNumber()); // Mã giao dịch từ T24
        transactionSavingAccountDTO.setTransactionTime(Instant.now()); // Thời gian giao dịch
        transactionSavingAccountDTO.setTransferTransactionId(transaction.getTransferTransactionId()); // ID giao dịch chuyển tiền
        transactionSavingAccountDTO.setSavingAccountNumber(savingAccountVerifyCacheDTO.getSavingAccountNumber()); // Số tài khoản tiết kiệm
        transactionSavingAccountDTO.setDescription(transaction.getMessage()); // Mô tả giao dịch

        // Cập nhật thông tin giao dịch chính
        transaction.setTransactionCode(response.getT24ReferenceNumber()); // Cập nhật mã giao dịch từ T24
        transaction.setTransactionFinishTime(Instant.now()); // Cập nhật thời gian hoàn thành
        transaction.setTransactionStatus(TransactionStatus.SUCCESS); // Đặt trạng thái thành công
        this.transactionRepository.save(transaction); // Lưu giao dịch

        // Lưu bản ghi giao dịch tài khoản tiết kiệm
        this.transactionSavingAccountRepository.save(this.transactionSavingAccountMapper.toEntity(transactionSavingAccountDTO));

        // Cập nhật số dư tài khoản tiền của khách hàng
        this.updateMoneyAccount(customer);

        // Cập nhật số dư tài khoản tiết kiệm từ T24
        this.changeAmountSavingAccount(savingAccount.get());

        // Xóa cache tài khoản tiết kiệm
        this.savingAccountCacheRepository.evict(transaction.getTransactionId());

        // Tạo thông báo cho khách hàng
        TransactionDTO transDTO = this.transactionMapper.toDto(transaction);
        this.createNotification(transDTO, customer, savingAccount.get());

        // Tạo response trả về cho client
        return OtpTransferConfirmResponse.builder()
                .transactionTime(Instant.now()) // Thời gian giao dịch
                .transactionCode(response.getT24ReferenceNumber()) // Mã giao dịch từ T24
                .build();
    }

    /**
     * Cập nhật số dư tài khoản tiết kiệm từ T24
     *
     * Method này đồng bộ số dư tài khoản tiết kiệm từ hệ thống T24
     * về database local sau khi có giao dịch gửi thêm tiền
     *
     * @param savingAccount Tài khoản tiết kiệm cần cập nhật số dư
     * @throws BadRequestAlertException nếu không tìm thấy tài khoản trong T24
     */
    public void changeAmountSavingAccount(SavingAccount savingAccount) {
        // Tạo request để truy vấn thông tin tài khoản tiết kiệm từ T24
        QuerySavingAccountT24Request querySavingAccount = QuerySavingAccountT24Request.builder()
                .cusAccNo(savingAccount.getSavingAccountNumber()) // Số tài khoản tiết kiệm
                .build();

        // Gọi API T24 để lấy thông tin tài khoản tiết kiệm
        List<QuerySavingAccountDTO> querySavingAccountDTOS = this.apiGeeSavingAccountService.querySavingAccount(querySavingAccount);

        // Kiểm tra có dữ liệu trả về không
        if (querySavingAccountDTOS.isEmpty()) {
            throw new BadRequestAlertException(ErrorCode.MSG101073); // Không tìm thấy tài khoản tiết kiệm
        }

        // Lấy thông tin tài khoản đầu tiên từ danh sách kết quả
        QuerySavingAccountDTO querySavingAccountDTO = querySavingAccountDTOS.stream().findFirst().get();

        // Cập nhật số dư tài khoản tiết kiệm từ T24
        savingAccount.setSavingAmount(Long.valueOf(querySavingAccountDTO.getWorkingBal()));

        // Lưu thông tin tài khoản đã cập nhật
        this.savingAccountRepository.save(savingAccount);
    }


    /**
     * Lấy lịch sử giao dịch tài khoản tiết kiệm
     *
     * Method này truy vấn lịch sử các giao dịch liên quan đến tài khoản tiết kiệm:
     * - Giao dịch mở tài khoản tiết kiệm
     * - Giao dịch gửi thêm tiền vào tài khoản tiết kiệm
     * Kết quả được phân trang và format message phù hợp
     *
     * @param httpServletRequest HTTP request
     * @param request SavingAccountHistorySearch chứa điều kiện tìm kiếm và phân trang
     * @return Page<TransactionSavingAccountDTO> danh sách lịch sử giao dịch có phân trang
     */
    @Override
    public Page<TransactionSavingAccountDTO> history(HttpServletRequest httpServletRequest, SavingAccountHistorySearch request) {
        // Lấy thông tin khách hàng đã đăng nhập
        Customer customer = this.getCustomerLogin();

        // Thiết lập các loại giao dịch cần truy vấn: mở tài khoản và gửi thêm tiền
        request.setTransferTypes(Arrays.asList(TransferType.SAVING_ACCOUNT, TransferType.DEPOSIT_MORE));

        // Tạo đối tượng phân trang từ request
        Pageable pageable = PageRequest.of(request.getPageIndex(), request.getPageSize());

        // Tìm tài khoản tiết kiệm theo số tài khoản và customer ID
        SavingAccount savingAccount = this.savingAccountRepository.findBySavingAccountNumberAndCustomerId(request.getSavingAccountNumber(), customer.getCustomerId())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101087)); // Ném exception nếu không tìm thấy

        // Đếm tổng số bản ghi thỏa mãn điều kiện
        long totalElement = this.transactionSavingAccountRepository.count(request);

        // Nếu không có dữ liệu thì trả về page rỗng
        if (totalElement == 0L) {
            return Page.empty();
        }

        // Truy vấn danh sách giao dịch với phân trang
        List<TransactionSavingAccountDTO> transactionSavingAccountDTOS = this.transactionSavingAccountRepository.searchHistory(request, pageable);

        // Xác định loại tài khoản tiết kiệm để tạo message phù hợp
        String savingAccountType = Validator.equals(SavingAccountType.FIXED, savingAccount.getType()) ? Labels.getLabels(LabelKey.LABEL_FIXED).toLowerCase()
                : Labels.getLabels(LabelKey.LABEL_ACCUMULATED).toLowerCase();

        // Tạo message cho giao dịch mở tài khoản tiết kiệm
        String createInterestMessage = StringUtil.join(
                new String[]{customer.getFullname(), Labels.getLabels(LabelKey.MESSAGE_CREATE_INTEREST)
                        + StringPool.SPACE + savingAccountType}, StringPool.SPACE);

        // Cập nhật message cho các giao dịch mở tài khoản tiết kiệm
        transactionSavingAccountDTOS.stream().forEach(item -> {
            if (Validator.equals(TransferType.SAVING_ACCOUNT, item.getTransferType())) {
                item.setMessage(createInterestMessage); // Đặt message cho giao dịch mở tài khoản
            }
        });

        // Trả về kết quả với phân trang
        return new PageImpl<>(transactionSavingAccountDTOS, pageable, totalElement);
    }

    /**
     * Lấy tổng số tiền trong các tài khoản tiết kiệm
     *
     * Method này tính toán tổng số tiền trong tài khoản tiết kiệm theo từng loại tiền tệ:
     * - Tài khoản tiết kiệm online (được tạo qua app)
     * - Tài khoản tiết kiệm tại chi nhánh (được tạo tại quầy)
     * Kết quả được nhóm theo đơn vị tiền tệ
     *
     * @param httpServletRequest HTTP request
     * @return List<TotalMoneySavingAccountDTO> danh sách tổng tiền theo từng loại tiền tệ
     */
    @Override
    public List<TotalMoneySavingAccountDTO> getTotalMoneySavingAccount(HttpServletRequest httpServletRequest) {
        // Lấy thông tin khách hàng đã đăng nhập
        Customer customer = this.getCustomerLogin();

        // Khởi tạo danh sách kết quả và map nhóm theo tiền tệ
        List<TotalMoneySavingAccountDTO> list = new ArrayList<>();
        Map<String, List<AccountSavingDTO>> accountSavingBranches = new HashMap<>();

        // Lấy danh sách tài khoản tiết kiệm online của khách hàng
        List<SavingAccount> savingAccounts = this.savingAccountRepository
                .findAllByCustomerIdAndStatus(customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

        // Tạo request để lấy tài khoản tiết kiệm từ T24 (bao gồm cả tài khoản tại chi nhánh)
        AccountSavingRequest accountSavingRequest = AccountSavingRequest.builder()
                .cusId(customer.getCif()) // Mã CIF khách hàng
                .build();

        // Gọi API T24 để lấy danh sách tài khoản tiết kiệm
        List<AccountSavingDTO> accountSavingDTOS = this.apiGeeTransferService.checkAccountSaving(accountSavingRequest);

        // Nhóm tài khoản tiết kiệm theo đơn vị tiền tệ
        accountSavingBranches = accountSavingDTOS.stream().collect(Collectors.groupingBy(AccountSavingDTO::getCurrency, Collectors.toList()));

        // Cập nhật số tiền cho từng tài khoản (chuyển từ string sang long)
        accountSavingDTOS.forEach(item -> item.setAmount(Validator.isNull(item.getWorkingBal()) ? 0L : Long.parseLong(item.getWorkingBal())));

        // Tính tổng số tiền trong tài khoản tiết kiệm online
        Long totalSavingAccountOnline = savingAccounts.stream().mapToLong(SavingAccount::getSavingAmount).reduce(0, Long::sum);

        // Thêm thông tin tài khoản tiết kiệm online (LAK) vào danh sách kết quả
        list.add(new TotalMoneySavingAccountDTO(false, totalSavingAccountOnline, savingAccounts.size(), CurrencyType.LAK));

        // Lấy danh sách loại tài khoản tiết kiệm được cấu hình
        List<String> savingAccountTypes = new ArrayList<>();
        savingAccountTypes.addAll(this.commonRepository
                .findAllByCategoryAndStatus(CommonCategory.SAVING_ACCOUNT.name(), EntityStatus.ACTIVE.getStatus())
                .stream().map(Common::getValue).collect(Collectors.toList()));

        // Xử lý tài khoản tiết kiệm tại chi nhánh theo từng loại tiền tệ
        accountSavingBranches.forEach((key, values) -> {
            // Đếm số lượng tài khoản không thuộc loại được cấu hình (tài khoản tại chi nhánh)
            long totalSavingAccount = values.stream().filter(value -> !savingAccountTypes.contains(value.getCategory())).count();

            // Tính tổng số tiền trong các tài khoản tại chi nhánh
            long total = values.stream().filter(value -> !savingAccountTypes.contains(value.getCategory()))
                    .mapToLong(item -> Validator.isNull(item.getWorkingBal()) ? 0L : Long.parseLong(item.getWorkingBal()))
                    .reduce(0, Long::sum);

            // Thêm thông tin tài khoản tại chi nhánh vào danh sách kết quả
            list.add(new TotalMoneySavingAccountDTO(true, total, (int) totalSavingAccount, CurrencyType.valueOf(key)));
        });

        // Trả về danh sách tổng tiền theo từng loại tiền tệ
        return list;
    }

    /**
     * Lấy thông tin tính năng sản phẩm tiết kiệm
     *
     * Method này trả về các tính năng được hỗ trợ cho từng loại sản phẩm tiết kiệm:
     * - Có thể gửi thêm tiền hay không
     * - Có thể thiết lập ngày tất toán hay không
     * - Có thể tất toán trước hạn hay không
     * - Có thể rút một phần trước hạn hay không
     *
     * @param httpServletRequest HTTP request
     * @param request SearchSavingAccountRequest chứa loại tài khoản tiết kiệm
     * @return ProductFeatureDTO thông tin tính năng sản phẩm
     */
    @Override
    public ProductFeatureDTO productFeature(HttpServletRequest httpServletRequest, SearchSavingAccountRequest request) {
        // Xác thực người dùng đã đăng nhập
        this.getCustomerLogin();

        // Trả về thông tin tính năng sản phẩm
        return ProductFeatureDTO.builder()
                .depositMoreToSavingAccount(Validator.equals(SavingAccountType.FIXED, request.getType()) ? Boolean.FALSE : Boolean.TRUE) // Tài khoản có kỳ hạn cố định không cho phép gửi thêm tiền
                .allowSettlementDateToBeSet(Boolean.FALSE) // Không cho phép thiết lập ngày tất toán tùy ý
                .settlementBeforeDueDate(Boolean.TRUE) // Cho phép tất toán trước hạn
                .partialWithdrawalBeforeDueDate(Boolean.FALSE) // Không cho phép rút một phần trước hạn
                .build();
    }

    /**
     * Cập nhật thông tin tài khoản tiết kiệm từ T24
     *
     * Method này được chạy định kỳ để đồng bộ thông tin tài khoản tiết kiệm:
     * - Kiểm tra các tài khoản đã đến hạn tất toán
     * - Đồng bộ thông tin từ T24 về database local
     * - Xử lý tự động tái tục hoặc tất toán theo cấu hình
     * - Tạo thông báo cho khách hàng
     * - Cập nhật trạng thái tài khoản
     */
    @Override
    @Transactional // Đảm bảo tính toàn vẹn dữ liệu trong quá trình cập nhật
    public void updateSavingAccount() {
        // Lấy danh sách tài khoản tiết kiệm đã đến hạn tất toán
        List<SavingAccount> savingAccounts = this.savingAccountRepository
                .findAllBySettlementDueTimeBeforeAndStatus(Instant.now(), EntityStatus.ACTIVE.getStatus());

        // Danh sách tài khoản đã bị đóng trong T24
        List<String> savingAccountClosed = new ArrayList<>();

        // Xử lý từng tài khoản tiết kiệm
        savingAccounts.forEach(item -> {
            QuerySavingAccountDTO response = null;

            // Tạo request để truy vấn thông tin tài khoản từ T24
            QuerySavingAccountT24Request querySavingAccount = QuerySavingAccountT24Request.builder()
                    .category(String.valueOf(item.getType().getProductCategory())) // Loại sản phẩm
                    .cusAccNo(item.getSavingAccountNumber()) // Số tài khoản tiết kiệm
                    .build();

            // Format thời gian để so sánh với T24
            String startTime = this.formatTime(item.getStartTime()); // Ngày hiệu lực hiện tại
            String settlementDueTime = this.formatTime(item.getSettlementDueTime()); // Ngày đáo hạn hiện tại

            // Gọi API T24 để lấy thông tin tài khoản
            List<QuerySavingAccountDTO> querySavingAccountDTOS = this.apiGeeSavingAccountService.querySavingAccount(querySavingAccount);

            // Kiểm tra tài khoản có tồn tại trong T24 không
            if (Validator.isNull(querySavingAccountDTOS)) {
                // Nếu không tồn tại, thêm vào danh sách tài khoản đã đóng
                savingAccountClosed.add(item.getSavingAccountNumber());
            } else {
                // Lấy thông tin tài khoản từ T24
                response = querySavingAccountDTOS.stream().findFirst().get();

                // So sánh thời gian để kiểm tra có thay đổi không
                if (!Validator.equals(startTime, response.getValueDate())
                        || !Validator.equals(settlementDueTime, response.getMaturityDate())) {

                    // Lấy thông tin tài khoản cần cập nhật
                    SavingAccount savingAccountUpdate = this.savingAccountRepository
                            .findBySavingAccountNumberAndStatus(item.getSavingAccountNumber(), EntityStatus.ACTIVE.getStatus());

                    // Xử lý theo phương thức tất toán
                    if (Validator.equals(item.getFinalizationMethod(), MaturityInstructionType.NO_ROTATION)) {
                        // Không tái tục - tạo thông báo biến động số dư và đóng tài khoản
                        this.createOtherNotification(item, true);
                        savingAccountUpdate.setStatus(EntityStatus.INACTIVE.getStatus());
                    } else if (Validator.equals(item.getFinalizationMethod(), MaturityInstructionType.ROOT_ROTATION)) {
                        // Tái tục gốc - tạo thông báo biến động số dư
                        this.createOtherNotification(item, true);
                    } else {
                        // Tái tục gốc + lãi - tạo thông báo tiền lãi
                        this.createOtherNotification(item, false);
                    }

                    // Cập nhật thông tin tài khoản từ T24
                    savingAccountUpdate.setStartTime(this.parseTime(response.getValueDate())); // Ngày hiệu lực mới
                    savingAccountUpdate.setSettlementDueTime(this.parseTime(response.getMaturityDate())); // Ngày đáo hạn mới
                    savingAccountUpdate.setSavingAmount(Long.valueOf(response.getWorkingBal())); // Số dư hiện tại
                    savingAccountUpdate.setInterestAmtEnd(Long.valueOf(response.getInterestAmountEnd())); // Tiền lãi cuối kỳ
                    savingAccountUpdate.setAmtAfterSettlement(Long.valueOf(response.getInterestAmountEnd())); // Số tiền sau tất toán

                    // Lưu thông tin tài khoản đã cập nhật
                    this.savingAccountRepository.save(savingAccountUpdate);

                    // Cập nhật lại số dư tài khoản tiền của khách hàng
                    this.updateMoneyAccount(this.customerRepository.findByCustomerId(savingAccountUpdate.getCustomerId()));
                }
            }
        });

        // Lấy danh sách tài khoản đã bị đóng trong T24
        List<SavingAccount> list = this.savingAccountRepository.findAllBySavingAccountNumberIn(savingAccountClosed);

        // Xử lý các tài khoản đã bị đóng
        list.forEach(item -> {
            // Đặt trạng thái tài khoản thành INACTIVE
            item.setStatus(EntityStatus.INACTIVE.getStatus());

            // Tạo thông báo cho khách hàng theo phương thức tất toán
            if (Validator.equals(item.getFinalizationMethod(), MaturityInstructionType.NO_ROTATION) ||
                    Validator.equals(item.getFinalizationMethod(), MaturityInstructionType.ROOT_ROTATION)) {
                this.createOtherNotification(item, true); // Thông báo biến động số dư
            } else {
                this.createOtherNotification(item, false); // Thông báo tiền lãi
            }

            // Cập nhật lại số dư tài khoản tiền của khách hàng
            this.updateMoneyAccount(this.customerRepository.findByCustomerId(item.getCustomerId()));
        });

        // Lưu tất cả thay đổi vào database
        this.savingAccountRepository.saveAll(list);
    }

    /**
     * Lấy danh sách tài khoản tiết kiệm tại chi nhánh theo loại tiền tệ
     *
     * Method này lấy danh sách tài khoản tiết kiệm được tạo tại chi nhánh (không phải online)
     * theo loại tiền tệ cụ thể. Chỉ lấy những tài khoản thuộc các category được cấu hình.
     *
     * @param httpServletRequest HTTP request
     * @param request Amount chứa thông tin loại tiền tệ cần lọc
     * @return List<AccountSavingDTO> danh sách tài khoản tiết kiệm tại chi nhánh
     */
    @Override
    public List<AccountSavingDTO> getCustomerAccountSavingBranch(HttpServletRequest httpServletRequest, Amount request) {
        // Lấy thông tin khách hàng đã đăng nhập
        Customer customer = this.getCustomerLogin();

        // Kiểm tra loại tiền tệ có được cung cấp không
        if (Validator.isNull(request.getCurrency())) {
            throw new BadRequestAlertException(ErrorCode.MSG101089); // Thiếu thông tin loại tiền tệ
        }

        // Lấy danh sách category tài khoản tiết kiệm được cấu hình
        List<Common> categoryCommon = commonRepository.findAllByCategory(CommonCategory.ACCOUNT_SAVING.name());

        // Chuyển đổi thành danh sách string các category value
        List<String> categories = categoryCommon.stream().map(Common::getValue).collect(Collectors.toList());

        // Tạo request để lấy tài khoản tiết kiệm từ T24
        AccountSavingRequest accountSavingRequest = AccountSavingRequest.builder().cusId(customer.getCif()) // Mã CIF khách hàng
                .accountType(AccountTypeCif.SAVING.name()) // Loại tài khoản là SAVING
                .build();

        // Gọi API T24 và lọc theo category được cấu hình
        List<AccountSavingDTO> list = this.apiGeeTransferService
                .checkAccountSaving(accountSavingRequest).stream()
                .filter(item -> categories.contains(item.getCategory())).collect(Collectors.toList()); // Chỉ lấy tài khoản thuộc category được cấu hình

        // Kiểm tra có dữ liệu không
        if (Validator.isNull(list)) {
            return new ArrayList<>(); // Trả về danh sách rỗng
        }

        // Lọc theo loại tiền tệ và trả về kết quả
        return list.stream().filter(item -> Validator.equals(item.getCurrency(), request.getCurrency())).collect(Collectors.toList());
    }

    /**
     * Format thời gian từ Instant sang string theo định dạng ngắn
     *
     * Method này chuyển đổi thời gian từ Instant sang string để so sánh với T24
     * Sử dụng timezone UTC và định dạng ngắn (yyyyMMdd)
     *
     * @param time Instant cần format
     * @return String thời gian đã được format
     */
    private String formatTime(Instant time) {
        // Chuyển đổi Instant sang ZonedDateTime với timezone UTC
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(time, ZoneId.of(StringPool.UTC));

        // Tạo formatter với pattern ngắn
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TimeUtil.SHORT_TIMESTAMP_FORMAT);

        // Format và trả về string
        return formatter.format(zonedDateTime);
    }

    /**
     * Tạo tài khoản tiết kiệm mới
     *
     * Method này thực hiện tạo tài khoản tiết kiệm mới sau khi xác thực OTP:
     * 1. Xác thực người dùng và giao dịch
     * 2. Kiểm tra cấu hình sản phẩm
     * 3. Lấy thông tin lãi suất và kỳ hạn
     * 4. Gọi API T24 để tạo tài khoản
     * 5. Lưu thông tin và tạo thông báo
     *
     * @param httpServletRequest HTTP request
     * @param request ConfirmOtpSavingAccountRequest chứa thông tin xác nhận OTP
     * @return CreateInterestResponse thông tin tài khoản tiết kiệm đã tạo
     */
    @Override
    public CreateInterestResponse createInterest(HttpServletRequest httpServletRequest, ConfirmOtpSavingAccountRequest request) {
        // Lấy thông tin khách hàng đã đăng nhập
        Customer customer = this.getCustomerLogin();

        // Tìm giao dịch theo transaction ID và customer ID
        Transaction transaction = this.transactionRepository.findByTransactionIdAndCustomerId(
                request.getTransactionId(), customer.getCustomerId());

        // Lấy thông tin cache đã lưu khi tạo OTP
        SavingAccountVerifyCacheDTO cache = this.getSavingAccountCache(request.getTransactionId());

        // Lấy danh sách cấu hình cho loại OTP tài khoản tiết kiệm
        List<Common> commons = this.commonRepository.
                findAllByCategoryAndStatus(OtpType.SAVING_ACCOUNT.name(), EntityStatus.ACTIVE.getStatus());

        // Lấy loại tài khoản tiết kiệm từ giao dịch
        SavingAccountType savingAccountType = SavingAccountType.valueOf(String.valueOf(transaction.getSavingAccountType()));

        // Tìm cấu hình tương ứng với loại sản phẩm
        Optional<Common> common = commons.stream().filter(item -> item.getValue().equals(savingAccountType.getProductCategory().toString()))
                .collect(Collectors.toList()).stream().findAny();

        // Kiểm tra có cấu hình cho loại sản phẩm này không
        if (!common.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG101075); // Không có cấu hình cho loại sản phẩm này
        }

        // Xác thực trạng thái giao dịch
        this.verifyTransaction(transaction);

        // Tạo user ID từ CIF và số CMND
        String userId = customer.getCif() + customer.getIdCardNumber();

        // Kiểm tra DOTP đã được đăng ký cho device này chưa
        DOTP dotp = this.checkRegisterDOTP(customer, cache.getDeviceId());

        // Tạo request để lấy thông tin lãi suất từ T24
        InforInterestT24Request inforInterestT24Request = InforInterestT24Request.builder()
                .productCode(savingAccountType.getProductCode()) // Mã sản phẩm
                .currency(transaction.getTransactionCurrency()) // Loại tiền tệ
                .category(String.valueOf(savingAccountType.getProductCategory())) // Loại sản phẩm
                .subProduct(savingAccountType.getSubProduct()) // Sản phẩm con
                .build();

        // Gọi API T24 để lấy thông tin lãi suất
        InforInterestDTO inforInterestDTO = this.apiGeeSavingAccountService.getInforInterest(inforInterestT24Request);

        // Kiểm tra có thông tin lãi suất không
        if (Validator.isNull(inforInterestDTO.getLstIntRate())) {
            throw new BadRequestAlertException(ErrorCode.MSG101076); // Không có thông tin lãi suất
        }

        // Lấy phương thức tất toán từ cache
        MaturityInstructionType maturityInstructionType = cache.getFinalizationMethod();

        // Chuyển đổi phương thức tất toán sang mã số
        Integer maturityInstruction = MaturityInstructionType.getMaturityInstructionType()
                .getOrDefault(maturityInstructionType, null);

        // Tách số tháng và đơn vị kỳ hạn từ cache
        Integer tenor = StringUtil.extractNumber(cache.getTenor()); // Số tháng/năm
        String tenorPeriod = StringUtil.extractCharacterStr(cache.getTenor()); // Đơn vị (M/Y)

        // Tìm thông tin lãi suất tương ứng với kỳ hạn đã chọn
        Optional<IntRateDTO> intRateDTO = inforInterestDTO.getLstIntRate().stream()
                .filter(item -> item.getPeriod().equals(cache.getTenor())).collect(Collectors.toList()).stream().findFirst();

        // Kiểm tra có lãi suất cho kỳ hạn này không
        if (!intRateDTO.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG101075); // Không có lãi suất cho kỳ hạn này
        }

        // Tạo thông tin xác thực cho API T24
        AuthenMethod authenMethod = AuthenMethod.builder()
                .deviceId(cache.getDeviceId()) // Device ID từ cache
                .otpValue(request.getOtpValue()) // Giá trị OTP người dùng nhập
                .token(dotp.getToken()) // Token DOTP
                .transData(MD5Generator.md5(request.getTransData())) // Mã hóa MD5 của transaction data
                .userId(userId) // User ID
                .build();

        // Tạo request để gọi API T24 tạo tài khoản tiết kiệm
        CreateInterestT24Request interestT24Request = CreateInterestT24Request.builder()
                .channel("APP") // Kênh giao dịch là mobile app
                .cifT24(customer.getCif()) // Mã CIF khách hàng trong T24
                .productCategory(String.valueOf(savingAccountType.getProductCategory())) // Loại sản phẩm
                .amount(Validator.isNotNull(transaction.getTransactionAmount()) ? String.valueOf(transaction.getTransactionAmount().longValue()) : String.valueOf(0L)) // Số tiền gửi ban đầu
                .amountCurrency(transaction.getTransactionCurrency()) // Đơn vị tiền tệ
                .sourceAccount(transaction.getCustomerAccNumber()) // Tài khoản nguồn (trừ tiền)
                .tenor(String.valueOf(tenor)) // Số kỳ hạn
                .tenorPeriod(tenorPeriod) // Đơn vị kỳ hạn
                .maturityInstruction(String.valueOf(maturityInstruction)) // Phương thức tất toán
                .beneficiaryAccount(transaction.getBeneficiaryAccountNumber()) // Tài khoản thụ hưởng
                .fee(String.valueOf(Math.round(transaction.getTransactionFee()))) // Phí giao dịch (hiện tại = 0)
                .subProduct(Validator.equals(SavingAccountType.FIXED.toString(), transaction.getSavingAccountType()) ? SavingAccountType.FIXED.getSubProduct() : null) // Sản phẩm con (chỉ cho loại FIXED)
                .branch(BranchCode.LA0010001.name()) // Mã chi nhánh
                .authenMethod(authenMethod) // Thông tin xác thực
                .build();

        // Gọi service xác thực OTP và tạo tài khoản tiết kiệm qua T24
        InterestDTO response = this.consumerOtpService.verifyOtp(transaction.getTransactionId(),
                transaction.getPhoneNumber(), OtpType.SAVING_ACCOUNT, interestT24Request,
                this.apiGeeSavingAccountService::createInterest, OtpConfirmType.DOTP);

        // Xử lý kết quả trả về từ T24
        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.FAILED)) {
            // Nếu giao dịch thất bại
            transaction.setTransactionStatus(TransactionStatus.FAILED); // Cập nhật trạng thái giao dịch thành FAILED
            transaction.setTransactionFinishTime(Instant.now()); // Cập nhật thời gian kết thúc giao dịch
            this.transactionRepository.save_(transaction); // Lưu giao dịch

            // Kiểm tra có mã lỗi từ T24 không
            if (Validator.isNotNull(response.getT24ErrorCode())) {
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode()); // Ném exception với mã lỗi T24
            }

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023); // Ném exception lỗi chung
        } else if (Validator.equals(response.getTransactionStatus(), TransactionStatus.TIMEOUT)) {
            // Nếu giao dịch timeout
            _log.info("Transaction has been timeout, {}", transaction.getTransactionId()); // Log thông tin timeout

            transaction.setTransactionStatus(TransactionStatus.FAILED); // Cập nhật trạng thái thành FAILED
            this.transactionRepository.save_(transaction); // Lưu giao dịch

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162); // Ném exception timeout
        }

        // Lấy thông tin thời gian từ response T24
        Instant startTime = this.parseTime(response.getValueDate()); // Ngày hiệu lực
        Instant settlementDueTime = this.parseTime(response.getMaturityDate()); // Ngày đáo hạn
        String sourceAccountNumber = transaction.getCustomerAccNumber(); // Số tài khoản nguồn
        String beneficiaryAccountNumber = transaction.getBeneficiaryAccountNumber(); // Số tài khoản thụ hưởng
        String savingAccountNumber = response.getSavingAcctId(); // Số tài khoản tiết kiệm từ T24

        // Tạo DTO để lưu thông tin tài khoản tiết kiệm
        SavingAccountDTO savingAccountDTO = SavingAccountDTO.builder()
                .customerId(customer.getCustomerId()) // ID khách hàng
                .accountNumber(sourceAccountNumber) // Số tài khoản nguồn
                .savingAccountNumber(savingAccountNumber) // Số tài khoản tiết kiệm
                .cif(customer.getCif()) // Mã CIF khách hàng
                .idCardNumber(customer.getIdCardNumber()) // Số CMND
                .phoneNumber(customer.getUsername()) // Số điện thoại
                .savingAmount(Validator.isNotNull(transaction.getTransactionAmount()) ? transaction.getTransactionAmount().longValue() : 0L) // Số tiền gửi ban đầu
                .type(savingAccountType) // Loại tài khoản tiết kiệm
                .startTime(startTime) // Ngày hiệu lực
                .settlementDueTime(settlementDueTime) // Ngày đáo hạn
                .tenor(tenor) // Kỳ hạn
                .rmCode(cache.getRm()) // Mã RM
                .interestRate(Double.valueOf(intRateDTO.get().getInterestRate())) // Lãi suất
                .finalizationMethod(cache.getFinalizationMethod()) // Phương thức tất toán
                .tenorPeriod(tenorPeriod) // Đơn vị kỳ hạn
                .receivingAccountNumber(beneficiaryAccountNumber) // Tài khoản nhận tiền khi tất toán
                .status(EntityStatus.ACTIVE.getStatus()) // Trạng thái active
                .build();

        // Lưu thông tin tài khoản tiết kiệm vào database
        SavingAccount savingAccount = this.savingAccountRepository.save(this.savingAccountMapper.toEntity(savingAccountDTO));

        // Cố gắng lấy và cập nhật số tiền lãi dự kiến cuối kỳ
        try {
            String interestAmountEnd = this.getSavingAccount(savingAccount.getSavingAccountNumber(), null).getInterestAmountEnd();
            savingAccount.setInterestAmtEnd(Validator.isNull(interestAmountEnd) ? 0L : Long.parseLong(interestAmountEnd)); // Cập nhật số tiền lãi cuối kỳ
        } catch (Exception exception) {
            _log.info("No expected interest rate at the end of the period was found, {}", exception); // Log nếu không lấy được thông tin lãi cuối kỳ
        }

        // Lưu lại thông tin tài khoản tiết kiệm đã cập nhật
        this.savingAccountRepository.save(savingAccount);

        // Cập nhật trạng thái giao dịch thành công
        transaction.setTransactionStatus(TransactionStatus.SUCCESS); // Đặt trạng thái thành SUCCESS
        transaction.setTransactionFinishTime(Instant.now()); // Cập nhật thời gian hoàn thành

        // Lưu giao dịch và cập nhật các thông tin liên quan
        this.transactionRepository.save(transaction); // Lưu giao dịch
        this.updateMoneyAccount(customer); // Cập nhật số dư tài khoản tiền của khách hàng
        this.savingAccountCacheRepository.evict(transaction.getTransactionId()); // Xóa cache

        // Tạo thông báo cho khách hàng
        TransactionDTO trans = this.transactionMapper.toDto(transaction);
        this.createNotification(trans, customer, savingAccount);

        // Tạo bản ghi giao dịch tài khoản tiết kiệm
        TransactionSavingAccountDTO dto = new TransactionSavingAccountDTO();
        dto.setSavingAccountId(savingAccount.getSavingAccountId()); // ID tài khoản tiết kiệm
        dto.setStatus(EntityStatus.ACTIVE.getStatus()); // Trạng thái bản ghi
        dto.setTransferTransactionId(transaction.getTransferTransactionId()); // ID giao dịch chuyển tiền

        // Lưu bản ghi giao dịch tài khoản tiết kiệm
        this.transactionSavingAccountRepository.save(this.transactionSavingAccountMapper.toEntity(dto));

        // Tạo response trả về cho client
        return CreateInterestResponse.builder()
                .productName(savingAccountType.toString()) // Tên sản phẩm
                .savingAccountNumber(savingAccountNumber) // Số tài khoản tiết kiệm
                .accountTransfer(sourceAccountNumber) // Tài khoản chuyển tiền
                .amount(Validator.isNotNull(transaction.getTransactionAmount()) ? String.valueOf(transaction.getTransactionAmount().longValue()) : String.valueOf(0L)) // Số tiền
                .tenor(this.getTenor(cache.getTenor())) // Kỳ hạn hiển thị
                .createdDate(savingAccount.getCreatedDate()) // Ngày tạo
                .valueDate(response.getValueDate()) // Ngày hiệu lực
                .maturityDate(response.getMaturityDate()) // Ngày đáo hạn
                .build();
    }

    /**
     * Lấy chuỗi kỳ hạn hiển thị cho người dùng
     *
     * Method này chuyển đổi mã kỳ hạn (ví dụ: "12M") thành chuỗi hiển thị
     * có ý nghĩa cho người dùng (ví dụ: "12 tháng")
     *
     * @param tenor Mã kỳ hạn cần chuyển đổi
     * @return String chuỗi kỳ hạn đã được format để hiển thị
     */
    private String getTenor(String tenor) {
        // Tách số và chữ từ tenor, sau đó kết hợp với label tương ứng
        return StringUtil.extractNumber(tenor) + this.getLabels(StringUtil.extractCharacterStr(tenor));
    }

    /**
     * Xác thực trạng thái giao dịch
     *
     * Method này kiểm tra tính hợp lệ của giao dịch trước khi xử lý:
     * - Giao dịch phải tồn tại
     * - Giao dịch chưa được xử lý thành công trước đó
     *
     * @param transaction Giao dịch cần xác thực
     * @throws BadRequestAlertException nếu giao dịch không hợp lệ
     */
    private void verifyTransaction(Transaction transaction) {
        // Kiểm tra giao dịch có tồn tại không
        if (Validator.isNull(transaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048); // Giao dịch không tồn tại
        }

        // Kiểm tra giao dịch đã được xử lý thành công chưa
        if (Validator.equals(transaction.getTransactionStatus(), TransactionStatus.SUCCESS)) {
            throw new BadRequestAlertException(ErrorCode.MSG1104); // Giao dịch đã được xử lý thành công
        }
    }

    /**
     * Kiểm tra DOTP đã được đăng ký cho thiết bị
     *
     * Method này xác thực DOTP (Dynamic OTP) đã được đăng ký và kích hoạt
     * cho thiết bị của khách hàng hay chưa
     *
     * @param customer Thông tin khách hàng
     * @param deviceId ID thiết bị cần kiểm tra
     * @return DOTP thông tin DOTP đã đăng ký
     * @throws BadRequestAlertException nếu DOTP chưa được đăng ký
     */
    private DOTP checkRegisterDOTP(Customer customer, String deviceId) {
        // Tìm DOTP theo device ID, customer ID và trạng thái active
        Optional<DOTP> dotp = this.dotpRepository.findByDeviceIdAndCustomerIdAndStatus(deviceId,
                customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

        // Kiểm tra DOTP có tồn tại không
        if (!dotp.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG100160); // DOTP chưa được đăng ký cho thiết bị này
        }

        // Trả về thông tin DOTP
        return dotp.get();
    }

    /**
     * Parse string thời gian thành Instant
     *
     * Method này chuyển đổi string thời gian từ T24 thành Instant
     * Sử dụng định dạng ngắn và đặt thời gian là đầu ngày (00:00:00)
     *
     * @param inputDate String ngày tháng cần parse
     * @return Instant đối tượng thời gian đã parse
     */
    private Instant parseTime(String inputDate) {
        // Tạo formatter với pattern ngắn để parse string
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(TimeUtil.SHORT_TIMESTAMP_FORMAT);

        // Parse string thành LocalDate
        LocalDate localDate = LocalDate.parse(inputDate, inputFormatter);

        // Đặt thời gian là đầu ngày (00:00:00)
        LocalTime localTime = LocalTime.MIDNIGHT;

        // Kết hợp ngày và giờ thành LocalDateTime
        LocalDateTime localDateTime = LocalDateTime.of(localDate, localTime);

        // Chuyển đổi sang Instant với timezone hệ thống
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant();
    }

    /**
     * Xác thực request tạo tài khoản tiết kiệm
     *
     * Method này kiểm tra tính hợp lệ của request tạo tài khoản tiết kiệm mới:
     * - Kiểm tra các trường bắt buộc
     * - Xác thực tài khoản nguồn và đích tồn tại
     * - Kiểm tra quyền sở hữu tài khoản
     *
     * @param request OtpSavingAccountRequest chứa thông tin tạo tài khoản tiết kiệm
     * @param customerId ID khách hàng
     * @return MoneyAccount tài khoản nguồn đã được xác thực
     * @throws BadRequestAlertException nếu dữ liệu không hợp lệ
     */
    private MoneyAccount validateRequestSavingAccount(OtpSavingAccountRequest request, Long customerId) {
        // Kiểm tra kỳ hạn có được cung cấp không
        if (Validator.isNull(request.getTenor())) {
            throw new BadRequestAlertException(ErrorCode.MSG101082); // Thiếu thông tin kỳ hạn
        }

        // Kiểm tra phương thức tất toán có được cung cấp không
        if (Validator.isNull(request.getMaturityInstruction())) {
            throw new BadRequestAlertException(ErrorCode.MSG101083); // Thiếu thông tin phương thức tất toán
        }

        // Kiểm tra tài khoản thụ hưởng có được cung cấp không
        if (Validator.isNull(request.getBeneficiaryAccount())) {
            throw new BadRequestAlertException(ErrorCode.MSG101084); // Thiếu thông tin tài khoản thụ hưởng
        }

        // Kiểm tra tài khoản nguồn có được cung cấp không
        if (Validator.isNull(request.getSourceAccount())) {
            throw new BadRequestAlertException(ErrorCode.MSG100173); // Thiếu thông tin tài khoản nguồn
        }

        // Xác thực tài khoản thụ hưởng tồn tại và thuộc về khách hàng
        this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatusAndCurrency(
                request.getBeneficiaryAccount(), customerId, EntityStatus.ACTIVE.getStatus(), request.getAmountCurrency()
        ).orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1036)); // Tài khoản thụ hưởng không hợp lệ

        // Xác thực tài khoản nguồn tồn tại và thuộc về khách hàng, sau đó trả về
        return this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatusAndCurrency(
                request.getSourceAccount(), customerId, EntityStatus.ACTIVE.getStatus(), request.getAmountCurrency()
        ).orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1151)); // Tài khoản nguồn không hợp lệ
    }

    /**
     * Xác thực request tất toán tài khoản tiết kiệm
     *
     * Method này kiểm tra tính hợp lệ của request tất toán tài khoản tiết kiệm:
     * - Kiểm tra tài khoản thụ hưởng được cung cấp
     * - Kiểm tra số tài khoản tiết kiệm được cung cấp
     * - Xác thực tài khoản tồn tại và thuộc về khách hàng
     *
     * @param request OtpSavingAccountRequest chứa thông tin tất toán
     * @param customerId ID khách hàng
     * @return SavingAccount tài khoản tiết kiệm đã được xác thực
     * @throws BadRequestAlertException nếu dữ liệu không hợp lệ
     */
    private SavingAccount validateRequestSettlement(OtpSavingAccountRequest request, Long customerId) {
        // Kiểm tra tài khoản thụ hưởng có được cung cấp không
        if (Validator.isNull(request.getBeneficiaryAccount())) {
            throw new BadRequestAlertException(ErrorCode.MSG101084); // Thiếu thông tin tài khoản thụ hưởng
        }

        // Kiểm tra số tài khoản tiết kiệm có được cung cấp không
        if (Validator.isNull(request.getSavingAccountNumber())) {
            throw new BadRequestAlertException(ErrorCode.MSG101085); // Thiếu thông tin số tài khoản tiết kiệm
        }

        // Xác thực tài khoản thụ hưởng tồn tại và thuộc về khách hàng
        this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatusAndCurrency(
                request.getBeneficiaryAccount(), customerId, EntityStatus.ACTIVE.getStatus(), request.getAmountCurrency()
        ).orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1036)); // Tài khoản thụ hưởng không hợp lệ

        // Xác thực tài khoản tiết kiệm tồn tại và thuộc về khách hàng, sau đó trả về
        return this.savingAccountRepository
                .findBySavingAccountNumberAndCustomerIdAndStatus(request.getSavingAccountNumber(), customerId, EntityStatus.ACTIVE.getStatus())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101073)); // Tài khoản tiết kiệm không tồn tại
    }

    /**
     * Xác thực request gửi thêm tiền vào tài khoản tiết kiệm
     *
     * Method này kiểm tra tính hợp lệ của request gửi thêm tiền:
     * - Kiểm tra số tài khoản tiết kiệm được cung cấp
     * - Kiểm tra tài khoản nguồn được cung cấp
     * - Xác thực tài khoản tồn tại và thuộc về khách hàng
     *
     * @param request OtpSavingAccountRequest chứa thông tin gửi thêm tiền
     * @param customerId ID khách hàng
     * @return MoneyAccount tài khoản nguồn đã được xác thực
     * @throws BadRequestAlertException nếu dữ liệu không hợp lệ
     */
    private MoneyAccount validateRequestDepositMore(OtpSavingAccountRequest request, Long customerId) {
        // Kiểm tra số tài khoản tiết kiệm có được cung cấp không
        if (Validator.isNull(request.getSavingAccountNumber())) {
            throw new BadRequestAlertException(ErrorCode.MSG101085); // Thiếu thông tin số tài khoản tiết kiệm
        }

        // Kiểm tra tài khoản nguồn có được cung cấp không
        if (Validator.isNull(request.getSourceAccount())) {
            throw new BadRequestAlertException(ErrorCode.MSG100173); // Thiếu thông tin tài khoản nguồn
        }

        // Xác thực tài khoản tiết kiệm tồn tại và thuộc về khách hàng
        this.savingAccountRepository
                .findBySavingAccountNumberAndCustomerIdAndStatus(request.getSavingAccountNumber(), customerId, EntityStatus.ACTIVE.getStatus())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101073)); // Tài khoản tiết kiệm không tồn tại

        // Xác thực tài khoản nguồn tồn tại và thuộc về khách hàng, sau đó trả về
        return this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatusAndCurrency(
                request.getSourceAccount(), customerId, EntityStatus.ACTIVE.getStatus(), request.getAmountCurrency()
        ).orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1151)); // Tài khoản nguồn không hợp lệ
    }

    /**
     * Xác thực kỳ hạn và lấy thông tin lãi suất
     *
     * Method này kiểm tra kỳ hạn có hợp lệ không và lấy thông tin lãi suất từ T24:
     * - Gọi API T24 để lấy thông tin lãi suất
     * - Lọc theo cấu hình kỳ hạn được phép trên CMS
     * - Kiểm tra có dữ liệu hợp lệ không
     *
     * @param savingAccountType Loại tài khoản tiết kiệm
     * @param currency Loại tiền tệ
     * @param period Kỳ hạn cần kiểm tra
     * @return InforInterestDTO thông tin lãi suất đã được lọc
     * @throws BadRequestAlertException nếu không có dữ liệu hợp lệ
     */
    private InforInterestDTO verifyTenor(SavingAccountType savingAccountType, String currency, String period) {
        // Tạo request để lấy thông tin lãi suất từ T24
        InforInterestT24Request inforInterestT24Request = InforInterestT24Request.builder()
                .productCode(savingAccountType.getProductCode()) // Mã sản phẩm
                .period(period) // Kỳ hạn
                .category(String.valueOf(savingAccountType.getProductCategory())) // Loại sản phẩm
                .currency(currency) // Loại tiền tệ
                .subProduct(savingAccountType.getSubProduct()) // Sản phẩm con
                .build();

        // Gọi API T24 để lấy thông tin lãi suất
        InforInterestDTO inforInterestDTO = this.apiGeeSavingAccountService.getInforInterest(inforInterestT24Request);

        // Lấy cấu hình kỳ hạn được phép cho loại tài khoản tiết kiệm này
        List<SavingConfiguration> savingConfigurations = savingConfigurationRepository.findBySavingTypeAndStatus(savingAccountType, EntityStatus.ACTIVE.getStatus());

        // Kiểm tra có dữ liệu trả về từ T24 không
        if (Validator.isNull(inforInterestDTO)) {
            throw new BadRequestAlertException(ErrorCode.MSG101075); // Không có thông tin lãi suất
        }

        // Chỉ lấy những kỳ hạn được cấu hình trên CMS
        inforInterestDTO.setLstIntRate(inforInterestDTO.getLstIntRate()
                .stream()
                .filter(i -> savingConfigurations.stream().anyMatch(s -> i.getPeriod().equals(s.getTenor() + s.getTenorPeriod()))) // Lọc theo cấu hình CMS
                .collect(Collectors.toList()));

        // Kiểm tra sau khi lọc có còn dữ liệu không
        if (inforInterestDTO.getLstIntRate().isEmpty()) {
            throw new BadRequestAlertException(ErrorCode.MSG101075); // Không có kỳ hạn hợp lệ
        }

        // Trả về thông tin lãi suất đã được lọc
        return inforInterestDTO;
    }

    /**
     * Xác thực thời gian cho phép gửi thêm tiền
     *
     * Method này kiểm tra tài khoản tiết kiệm có đủ điều kiện để gửi thêm tiền không:
     * - Tài khoản phải tồn tại và đang hoạt động
     * - Thời gian còn lại đến ngày đáo hạn phải > 30 ngày
     *
     * @param customerId ID khách hàng
     * @param savingAccountNumber Số tài khoản tiết kiệm
     * @throws BadRequestAlertException nếu không đủ điều kiện gửi thêm tiền
     */
    private void verifyTimeDepositMore(Long customerId, String savingAccountNumber) {
        // Tìm tài khoản tiết kiệm theo số tài khoản, customer ID và trạng thái active
        SavingAccount savingAccount = this.savingAccountRepository
                .findBySavingAccountNumberAndCustomerIdAndStatus(savingAccountNumber, customerId, EntityStatus.ACTIVE.getStatus())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101073)); // Tài khoản tiết kiệm không tồn tại

        // Tính số ngày còn lại đến ngày đáo hạn (tính đến cuối ngày dương lịch)
        long daysBetween = ChronoUnit.DAYS.between(Instant.now(), savingAccount.getSettlementDueTime()) + 1;

        // Kiểm tra thời gian còn lại phải > 30 ngày mới cho phép gửi thêm tiền
        if (daysBetween <= 30) {
            throw new BadRequestAlertException(ErrorCode.MSG101086); // Không thể gửi thêm tiền khi gần đến hạn tất toán
        }
    }

    /**
     * Xác thực tài khoản với hệ thống T24
     *
     * Method này kiểm tra tính hợp lệ của tài khoản nguồn và tài khoản thụ hưởng
     * bằng cách gọi API T24 để xác thực số tài khoản có tồn tại và hoạt động không
     *
     * @param sourceAccountNumber Số tài khoản nguồn cần xác thực
     * @param beneficiaryAccountNumber Số tài khoản thụ hưởng cần xác thực
     * @throws BadRequestAlertException nếu tài khoản không hợp lệ
     */
    private void verifyAccountWithT24(String sourceAccountNumber, String beneficiaryAccountNumber) {
        AccNumberVerifyRequest sourceAccountNumberVerify;
        AccNumberVerifyRequest beneficiaryAccountNumberVerify;

        // Xác thực tài khoản nguồn nếu được cung cấp
        if (Validator.isNotNull(sourceAccountNumber)) {
            // Tạo request xác thực tài khoản nguồn
            sourceAccountNumberVerify = AccNumberVerifyRequest.builder()
                    .accountNumber(sourceAccountNumber) // Số tài khoản nguồn
                    .build();

            // Gọi API T24 để xác thực tài khoản nguồn
            this.apiGeeTransferService.verifyAccNumber(sourceAccountNumberVerify);
        }

        // Xác thực tài khoản thụ hưởng nếu được cung cấp
        if (Validator.isNotNull(beneficiaryAccountNumber)) {
            // Tạo request xác thực tài khoản thụ hưởng
            beneficiaryAccountNumberVerify = AccNumberVerifyRequest.builder()
                    .accountNumber(beneficiaryAccountNumber) // Số tài khoản thụ hưởng
                    .build();

            // Gọi API T24 để xác thực tài khoản thụ hưởng
            this.apiGeeTransferService.verifyAccNumber(beneficiaryAccountNumberVerify);
        }
    }

    /**
     * Tạo OTP cho các giao dịch tài khoản tiết kiệm
     *
     * Method này xử lý tạo OTP cho các loại giao dịch tài khoản tiết kiệm:
     * - Tạo tài khoản tiết kiệm mới (SAVING_ACCOUNT)
     * - Gửi thêm tiền vào tài khoản tiết kiệm (DEPOSIT_MORE)
     * - Tất toán tài khoản tiết kiệm (SETTLEMENT)
     *
     * @param httpServletRequest HTTP request
     * @param request OtpSavingAccountRequest chứa thông tin giao dịch
     * @return OtpTransferTransResponse thông tin OTP và transaction data
     */
    @Override
    public OtpTransferTransResponse requestOtpInterest(HttpServletRequest httpServletRequest, OtpSavingAccountRequest request) {
        // Lấy thông tin khách hàng đã đăng nhập
        Customer customer = this.getCustomerLogin();

        // Tạo DTO giao dịch từ request
        TransactionDTO transactionDTO = new TransactionDTO(request);

        // Tạo user ID từ CIF và số CMND
        String userId = customer.getCif() + customer.getIdCardNumber();

        // Khởi tạo các biến cần thiết
        SavingAccount savingAccount = null; // Tài khoản tiết kiệm (cho trường hợp tất toán)
        Long transactionAmount; // Số tiền giao dịch
        MoneyAccount sourceAccount = null; // Tài khoản nguồn
        String tenor = null; // Kỳ hạn
        String beneficiaryAccountNumber = null; // Số tài khoản thụ hưởng

        // Kiểm tra ràng buộc: tài khoản có kỳ hạn cố định không cho phép gửi thêm tiền
        if (Validator.equals(SavingAccountType.FIXED, request.getSavingAccountType())
                && Validator.equals(TransferType.DEPOSIT_MORE, request.getTransferType())) {
            throw new BadRequestAlertException(ErrorCode.MSG100041); // Tài khoản FIXED không cho phép gửi thêm tiền
        }

        // Kiểm tra đơn vị tiền tệ phải là đơn vị mặc định
        if (!Validator.equals(request.getAmountCurrency(), this.customerProperties.getDefaultCurrency())) {
            throw new BadRequestAlertException(ErrorCode.MSG100056); // Chỉ hỗ trợ đơn vị tiền tệ mặc định
        }

        // Kiểm tra DOTP đã được đăng ký cho thiết bị này chưa
        DOTP dotp = this.checkRegisterDOTP(customer, request.getDeviceId());

        // Xác định loại giao dịch và xử lý tương ứng
        if (Validator.equals(TransferType.SAVING_ACCOUNT, request.getTransferType())
                || Validator.equals(TransferType.DEPOSIT_MORE, request.getTransferType())) {
            // Giao dịch tạo tài khoản tiết kiệm hoặc gửi thêm tiền (trừ tiền từ tài khoản nguồn)

            // Kiểm tra số tiền có được cung cấp không
            if (Validator.isNull(request.getAmount())) {
                throw new BadRequestAlertException(ErrorCode.MSG1138); // Thiếu thông tin số tiền
            }

            // Xác thực số tiền tối thiểu theo loại tài khoản tiết kiệm
            this.validateAmount(request.getAmount(), request.getSavingAccountType());

            // Đặt loại giao dịch là MINUS (trừ tiền)
            transactionDTO.setTransactionType(TransactionType.MINUS);
        } else {
            // Giao dịch tất toán (cộng tiền vào tài khoản thụ hưởng)
            transactionDTO.setTransactionType(TransactionType.PLUS);
        }

        // Xử lý theo từng loại giao dịch cụ thể
        if (Validator.equals(TransferType.SAVING_ACCOUNT, request.getTransferType())) {
            // Giao dịch tạo tài khoản tiết kiệm mới
            sourceAccount = this.validateRequestSavingAccount(request, customer.getCustomerId()); // Xác thực request tạo tài khoản
            this.verifyTenor(request.getSavingAccountType(), request.getAmountCurrency(), request.getTenor()); // Xác thực kỳ hạn
            transactionAmount = request.getAmount(); // Số tiền gửi ban đầu
            tenor = request.getTenor(); // Kỳ hạn
            beneficiaryAccountNumber = request.getBeneficiaryAccount(); // Tài khoản thụ hưởng khi tất toán
        } else if (Validator.equals(TransferType.SETTLEMENT, request.getTransferType())) {
            // Giao dịch tất toán tài khoản tiết kiệm
            savingAccount = this.validateRequestSettlement(request, customer.getCustomerId()); // Xác thực request tất toán
            transactionAmount = savingAccount.getSavingAmount(); // Số tiền trong tài khoản tiết kiệm
            beneficiaryAccountNumber = request.getBeneficiaryAccount(); // Tài khoản nhận tiền sau tất toán
        } else {
            // Giao dịch gửi thêm tiền vào tài khoản tiết kiệm
            sourceAccount = this.validateRequestDepositMore(request, customer.getCustomerId()); // Xác thực request gửi thêm tiền
            transactionAmount = request.getAmount(); // Số tiền gửi thêm
            this.verifyTimeDepositMore(customer.getCustomerId(), request.getSavingAccountNumber()); // Kiểm tra thời gian cho phép gửi thêm
            beneficiaryAccountNumber = request.getSavingAccountNumber(); // Tài khoản tiết kiệm đích
        }

        // Kiểm tra tính nhất quán của loại tài khoản tiết kiệm (nếu có số tài khoản)
        if (Validator.isNotNull(request.getSavingAccountNumber())) {
            SavingAccount savingAcc = this.savingAccountRepository
                    .findBySavingAccountNumberAndStatus(request.getSavingAccountNumber(), EntityStatus.ACTIVE.getStatus());

            // Kiểm tra loại tài khoản tiết kiệm trong request có khớp với database không
            if (!Validator.equals(request.getSavingAccountType(), savingAcc.getType())) {
                throw new BadRequestAlertException(ErrorCode.MSG101093); // Loại tài khoản tiết kiệm không khớp
            }
        }

        // Kiểm tra số dư tài khoản đủ để thực hiện giao dịch (cho giao dịch trừ tiền)
        if (Validator.equals(TransactionType.MINUS, transactionDTO.getTransactionType())
                && sourceAccount.getAvailableAmount() <= request.getAmount()) {
            throw new BadRequestAlertException(ErrorCode.MSG1108); // Số dư tài khoản không đủ
        }

        String accountNumber = Validator.isNull(request.getSourceAccount()) ? request.getBeneficiaryAccount() : request.getSourceAccount();

        // verify account number with T24
        this.verifyAccountWithT24(request.getSourceAccount(), request.getBeneficiaryAccount());

        transactionDTO.setTransferType(request.getTransferType());
        transactionDTO.setCustomerId(customer.getCustomerId());
        transactionDTO.setPhoneNumber(customer.getPhoneNumber());
        transactionDTO.setTransactionId(UUIDUtil.generateUUID(20));
        transactionDTO.setTransactionAmount(Validator.isNotNull(transactionAmount) ? transactionAmount.doubleValue() : 0D);
        transactionDTO.setCustomerAccNumber(accountNumber);
        transactionDTO.setTarget(Validator.equals(request.getTransferType(), TransferType.SETTLEMENT) ? request.getSavingAccountNumber() : request.getSourceAccount());
        transactionDTO.setBeneficiaryAccountNumber(beneficiaryAccountNumber);

        if (Validator.equals(request.getTransferType(), TransferType.DEPOSIT_MORE)) {
            if (Validator.isNotNull(request.getRemark())) {
                transactionDTO.setMessage(request.getRemark());
            } else {
                throw new BadRequestAlertException(ErrorCode.MSG101077);
            }
        }

        this.consumerOtpService.requestOtp(transactionDTO.getTransactionId(),
                transactionDTO.getPhoneNumber(), OtpType.valueOf(request.getTransferType().name()));

        Transaction transaction = this.transactionRepository.save(this.transactionMapper.toEntity(transactionDTO));
        String transData = request.getDeviceId() + dotp.getToken() + userId;

        SavingAccountVerifyCacheDTO dto = SavingAccountVerifyCacheDTO.builder()
                .tenor(tenor)
                .transactionId(transaction.getTransactionId())
                .type(request.getTransferType())
                .deviceId(request.getDeviceId())
                .savingAccountNumber(request.getSavingAccountNumber())
                .finalizationMethod(request.getMaturityInstruction())
                .rm(request.getRm())
                .build();

        this.savingAccountCacheRepository.put(transaction.getTransactionId(), dto);

        try {
            // set tài khoản mặc định nếu gạt nút setting default account
            if (request.isSetDefaultAccount() && !Validator.equals(TransferType.SETTLEMENT, request.getTransferType())) {
                this.transferService.setDefaultAccountTransfer(customer.getCustomerId(), request.getSourceAccount());
            }
        } catch (Exception e) {
            _log.error("request Otp, after request OTP error:", e);
        }

        return OtpTransferTransResponse.builder()
                .requiredOtp(Boolean.TRUE)
                .transData(transData)
                .transactionId(transaction.getTransactionId())
                .transferTransactionId(transaction.getTransferTransactionId())
                .expiredTime(this.otpProperties.getDuration())
                .build();
    }

    private void validateAmount(Long amount, SavingAccountType savingAccountType) {
        Long minAmount = Validator.equals(SavingAccountType.FIXED, savingAccountType) ? ValidationConstraint.LENGTH.SAVING_ACCOUNT_FIXED
                : ValidationConstraint.LENGTH.SAVING_ACCOUNT_ACCUMULATED;

        if (amount < minAmount) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DEPOSIT_MIN,
                            new Object[]{minAmount}),
                    SavingAccount.class.getSimpleName(), LabelKey.ERROR_DEPOSIT_MIN);
        }
    }

    private QuerySavingAccountDTO getSavingAccount(String savingAccount, String category) {
        QuerySavingAccountT24Request querySavingAccount = QuerySavingAccountT24Request.builder()
                .cusAccNo(savingAccount)
                .category(category)
                .build();

        List<QuerySavingAccountDTO> querySavingAccountDTOS = this.apiGeeSavingAccountService.querySavingAccount(querySavingAccount);

        if (Validator.isNull(querySavingAccountDTOS)) {
            throw new BadRequestAlertException(ErrorCode.MSG101073);
        }

        return querySavingAccountDTOS.stream().findFirst().get();
    }

    @Override
    public QuerySavingAccountResponse querySavingAccount(HttpServletRequest httpServletRequest, QuerySavingAccountRequest request) {
        Customer customer = this.getCustomerLogin();

        Optional<SavingAccount> savingAccount = this.savingAccountRepository.findBySavingAccountNumberAndCustomerIdAndStatus(
                request.getSavingAccount(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus()
        );

        if (!savingAccount.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG101073);
        }

        QuerySavingAccountDTO querySavingAccountDTO = this.getSavingAccount(request.getSavingAccount(), request.getCategory());

        savingAccount.get().setInterestAmtEnd(Long.valueOf(querySavingAccountDTO.getInterestAmountEnd()));
        this.savingAccountRepository.save(savingAccount.get());

        Instant startingDate = this.parseTime(querySavingAccountDTO.getValueDate());
        Instant maturityDate = this.parseTime(querySavingAccountDTO.getMaturityDate());

        return QuerySavingAccountResponse.builder()
                .productName(querySavingAccountDTO.getProductName())
                .startingDate(startingDate)
                .maturityDate(maturityDate)
                .amount(querySavingAccountDTO.getWorkingBal())
                .savingAccountNumber(request.getSavingAccount())
                .tenor(this.getTenor(savingAccount.get().getTenor() + savingAccount.get().getTenorPeriod()))
                .interestAmountEnd(querySavingAccountDTO.getInterestAmountEnd())
                .finalizationMethod(savingAccount.get().getFinalizationMethod())
                .interestAmt(querySavingAccountDTO.getInterestAmt())
                .amount(String.valueOf(savingAccount.get().getSavingAmount()))
                .frequencyInterestPayment(Labels.getLabels(LabelKey.LABEL_END_OF_TERM))
                .fee((double) 0)
                .statusAcct(querySavingAccountDTO.getStatusAcct())
                .receivingAccountNumber(savingAccount.get().getReceivingAccountNumber())
                .build();
    }

    /**
     * Tìm kiếm tài khoản tiết kiệm theo loại
     *
     * Method này tìm kiếm danh sách tài khoản tiết kiệm của khách hàng
     * theo loại tài khoản cụ thể và sắp xếp theo thứ tự mới nhất
     *
     * @param httpServletRequest HTTP request
     * @param request SearchSavingAccountRequest chứa điều kiện tìm kiếm
     * @return List<SavingAccountDTO> danh sách tài khoản tiết kiệm đã sắp xếp
     */
    @Override
    public List<SavingAccountDTO> search(HttpServletRequest httpServletRequest, SearchSavingAccountRequest request) {
        // Lấy thông tin khách hàng đã đăng nhập
        Customer customer = this.getCustomerLogin();

        // Tìm kiếm tài khoản tiết kiệm theo customer ID, loại tài khoản và trạng thái active
        List<SavingAccount> savingAccounts = this.savingAccountRepository.findAllByCustomerIdAndTypeAndStatus(
                customer.getCustomerId(), request.getType(), EntityStatus.ACTIVE.getStatus()
        );

        // Kiểm tra có dữ liệu không
        if (Validator.isNull(savingAccounts)) {
            return Collections.emptyList(); // Trả về danh sách rỗng
        }

        // Chuyển đổi entity sang DTO
        List<SavingAccountDTO> list = this.savingAccountMapper.toDto(savingAccounts);

        // Sắp xếp theo ID giảm dần (mới nhất trước)
        Collections.sort(list, Comparator.comparingLong(SavingAccountDTO::getSavingAccountId).reversed());

        // Trả về danh sách đã sắp xếp
        return list;
    }

    private void updateMoneyAccount(Customer customer) {
        AccountBalanceRequest accountBalanceRequest = AccountBalanceRequest.builder().custCode(customer.getCif())
                .build();

        List<AccountBalanceDTO> accountBalanceDTOS = this.apiGeeTransferService
                .checkAccountBalance(accountBalanceRequest);

        if (Validator.isNull(accountBalanceDTOS)) {
            throw new BadRequestAlertException(ErrorCode.MSG100029);
        }

        // compare với số tài khoản trong db và đồng bộ lại
        List<MoneyAccount> moneyAccounts = this.moneyAccountRepository
                .findByCustomerIdAndStatus(customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

        List<MoneyAccountDTO> moneyAccountDTOS = this.moneyAccountMapper.toDto(moneyAccounts);

        if (CollectionUtils.isEmpty(accountBalanceDTOS)) {
            moneyAccountDTOS = new ArrayList<>();
        } else {
            moneyAccountDTOS.forEach(MoneyAccountDTO::deleted);
        }

        List<String> displayCurrencies = Arrays.asList(this.customerProperties.getDisplayAccountCurrency());

        // dong bo tai khoan tren T24 voi cac tai khoan trong he thong
        for (AccountBalanceDTO accountBalanceDTO : accountBalanceDTOS) {
            String accountCurrency = accountBalanceDTO.getAccountCurrency();

            // check đơn vị tiền tệ của tài khoản, nếu ko nằm trong list hiển thị thì bỏ qua
            if (Validator.isNull(accountCurrency) || !displayCurrencies.contains(accountCurrency.toUpperCase())) {
                continue;
            }

            Optional<MoneyAccountDTO> optionalMoneyAccountDTO = moneyAccountDTOS.stream()
                    .filter(item -> Validator.equals(item.getAccountNumber(), accountBalanceDTO.getAccountNumber()))
                    .findFirst();

            // Neu tai khoan ben T24 da ton tai trong he thong => cap nhat, neu chua ton tai
            // => tao moi
            if (optionalMoneyAccountDTO.isPresent()) {
                MoneyAccountDTO moneyAccountDTO = optionalMoneyAccountDTO.get();

                moneyAccountDTO.unDeleted();
                moneyAccountDTO.update(accountBalanceDTO);
            } else {
                MoneyAccountDTO newMoneyAccount = new MoneyAccountDTO();

                newMoneyAccount.setStatus(EntityStatus.ACTIVE.getStatus());
                newMoneyAccount.setAccountNumber(accountBalanceDTO.getAccountNumber());
                newMoneyAccount.setAccountType(accountBalanceDTO.getAccountType());
                newMoneyAccount.setAvailableAmount(accountBalanceDTO.getAvailableBalance());
                newMoneyAccount.setCurrency(accountCurrency);
                newMoneyAccount.setCustomerId(customer.getCustomerId());
                newMoneyAccount.setCifNumber(customer.getCif());
                newMoneyAccount.setOpenDate(accountBalanceDTO.getOpenDate());

                moneyAccountDTOS.add(newMoneyAccount);
            }
        }

        this.moneyAccountRepository.saveAll(this.moneyAccountMapper.toEntity(moneyAccountDTOS));
    }

    private void createOtherNotification(SavingAccount savingAccount, boolean isExpired) {
        Map<String, String> valuesMapCreditAccount = new HashMap<>();
        String message = null;
        NotificationType notificationType;
        Long transactionAmount = 0L;

        String savingAccountType = Validator.equals(SavingAccountType.FIXED, savingAccount.getType()) ? LabelKey.LABEL_FIXED : LabelKey.LABEL_ACCUMULATED;

        //quay vòng gốc: thông báo khác (+ tiền lãi)
        //quay vòng gốc + lãi : thông báo khác ("tài khoản quay vòng gốc và lãi")
        // hết hạn tự động tất toán: tự động tất toán(+tiền lãi) , thông báo biến động số dư
        if (isExpired) {
            if (Validator.equals(savingAccount.getFinalizationMethod(), MaturityInstructionType.ROOT_ROTATION)) {
                message = Labels.getLabels(LabelKey.LABEL_PAY_INTEREST_END_OF_PERIOD) + StringPool.COMMA + StringPool.SPACE
                        + Labels.getLabels(LabelKey.LABEL_ROOT_ROTATION).toLowerCase() + StringPool.SPACE
                        + Labels.getLabels(LabelKey.LABEL_ACCOUNT_TYPE_SAVING_ACCOUNT).toLowerCase() + StringPool.SPACE
                        + Labels.getLabels(savingAccountType).toLowerCase()
                        + StringPool.SPACE + savingAccount.getSavingAccountNumber();
                transactionAmount = Validator.isNull(savingAccount.getInterestAmtEnd()) ? 0L : savingAccount.getInterestAmtEnd();
            } else {
                message = Labels.getLabels(LabelKey.LABEL_CLOSED_SAVING_ACCOUNT) + StringPool.SPACE
                        + Labels.getLabels(savingAccountType).toLowerCase() + StringPool.SPACE
                        + savingAccount.getSavingAccountNumber() + StringPool.COMMA + StringPool.SPACE
                        + Labels.getLabels(LabelKey.LABEL_PAYMENT_OF_PRINCIPAL_AND_INTEREST);
                transactionAmount = savingAccount.getSavingAmount() + savingAccount.getInterestAmtEnd();
            }
            notificationType = NotificationType.TRANSFER_TRANSACTION;
        } else {
            notificationType = NotificationType.OTHER;
            transactionAmount = null;
            message = Labels.getLabels(LabelKey.LABEL_ROTATION_OF_PRINCIPAL_AND_INTEREST) + StringPool.SPACE
                    + Labels.getLabels(LabelKey.LABEL_ACCOUNT_TYPE_SAVING_ACCOUNT).toLowerCase() + StringPool.SPACE
                    + Labels.getLabels(savingAccountType).toLowerCase() + StringPool.SPACE
                    + savingAccount.getSavingAccountNumber();
        }

        // Thông báo thanh toán lãi tiết kiệm
        valuesMapCreditAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(), Labels.getLabels(LabelKey.LABEL_BENEFICIARY_ACCOUNT));
        valuesMapCreditAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), savingAccount.getReceivingAccountNumber());// tài khoản tiết kiệm
        valuesMapCreditAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(), Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION));
        valuesMapCreditAccount.put(TemplateField.TRANSACTION_AMOUNT.name(), StringPool.PLUS + StringUtil.formatMoney(Validator.isNull(transactionAmount) ? 0L : transactionAmount));
        valuesMapCreditAccount.put((TemplateField.CURRENCY.name()), this.customerProperties.getDefaultCurrency());
        valuesMapCreditAccount.put((TemplateField.LABEL_TRANSACTION_TIME.name()), Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));
        valuesMapCreditAccount.put(TemplateField.TRANSACTION_TIME.name(), InstantUtil.formatStringLongDate(Instant.now(), Labels.getDefaultZoneId()));
        valuesMapCreditAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(), Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT));
        valuesMapCreditAccount.put(TemplateField.MESSAGE.name(), message);

        ContentTemplate templateReceiveMoney = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.CHANGE_PAY_SAVING_ACCOUNT.name(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(templateReceiveMoney)) {
            NotificationDTO notiForSender = new NotificationDTO();

            notiForSender.setNotificationStatus(NotificationStatus.PENDING);
            notiForSender.setNotificationType(notificationType);
            notiForSender.setTargetType(TargetType.USER);
            notiForSender.setEndUserType(EndUserType.CUSTOMER);

            // thông báo
            Optional<MoneyAccount> moneyAccount = this.moneyAccountRepository
                    .findByAccountNumberAndCustomerIdAndStatus(savingAccount.getReceivingAccountNumber(),
                            savingAccount.getCustomerId(), EntityStatus.ACTIVE.getStatus());

            if (!moneyAccount.isPresent()) {
                throw new BadRequestAlertException(ErrorCode.MSG1036);
            }

            List<NotiTransaction> notiTransactions = new ArrayList<>();
            Long beneficiaryCustomerId = moneyAccount.get().getCustomerId();

            notiForSender.setClassPk(beneficiaryCustomerId);

            notiForSender.setPublishTime(LocalDateTime.now());

            notiForSender.setTitle(Labels.getLabels(templateReceiveMoney.getTitle()));
            notiForSender.setContent(
                    StringUtil.replaceMapValue(templateReceiveMoney.getContent(), valuesMapCreditAccount));
            notiForSender.setDescription(templateReceiveMoney.getDescription());

            Notification notificationAfterSaved = this.notificationRepository
                    .save(this.notificationMapper.toEntity(notiForSender));

            // save notification transaction
            NotiTransaction notiTransaction = new NotiTransaction();

            notiTransaction.setNotificationId(notificationAfterSaved.getNotificationId());
            notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus());
            notiTransaction.setTransactionId(-2L); // @TODO setTransactionId mặc định cho thông báo

            notiTransactions.add(notiTransaction);
            this.notiTransactionRepository.saveAll(notiTransactions);
        }

    }

    private void createNotification(TransactionDTO transactionDTO, Customer customer, SavingAccount savingAccount) {
        Map<String, String> valuesMapDebitAccount = new HashMap<>();
        Map<String, String> valuesMapCreditAccount = new HashMap<>();

        String transactionFinishTime = transactionDTO.formatTransactionFinishTime();
        String transactionCode = Validator.isNull(transactionDTO.getTransactionCode()) ? transactionDTO.getTransactionId() : transactionDTO.getTransactionCode();

        String messageType = Validator.equals(SavingAccountType.FIXED, savingAccount.getType())
                ? Labels.getLabels(LabelKey.LABEL_FIXED).toLowerCase() : Labels.getLabels(LabelKey.LABEL_ACCUMULATED).toLowerCase();

        String createInterestMessage = StringUtil.join(
                new String[]{customer.getFullname(), Labels.getLabels(LabelKey.MESSAGE_CREATE_INTEREST).toLowerCase()
                        + StringPool.SPACE + messageType
                        + StringPool.SPACE + savingAccount.getSavingAccountNumber().toLowerCase()
                        + StringPool.SPACE + Labels.getLabels(LabelKey.MESSAGE_SUCCESSFUL).toLowerCase()}, StringPool.SPACE);

        String closedInterestMessage = StringUtil.join(
                new String[]{customer.getFullname(), Labels.getLabels(LabelKey.MESSAGE_CLOSED_INTEREST).toLowerCase()
                        + StringPool.SPACE + Labels.getLabels(LabelKey.LABEL_ACCOUNT_TYPE_SAVING_ACCOUNT).toLowerCase()
                        + StringPool.SPACE + messageType
                        + StringPool.SPACE + savingAccount.getSavingAccountNumber()
                        + StringPool.SPACE + Labels.getLabels(LabelKey.MESSAGE_SUCCESSFUL).toLowerCase()
                }, StringPool.SPACE);

        String message = Validator.equals(TransferType.SAVING_ACCOUNT, transactionDTO.getTransferType()) ? createInterestMessage : closedInterestMessage;

        Long getAmtAfterSettlement = Validator.isNull(savingAccount.getAmtAfterSettlement()) ? 0L : savingAccount.getAmtAfterSettlement();

        // transfer
        valuesMapDebitAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(), Labels.getLabels(LabelKey.LABEL_ACCOUNT_TYPE_PAYMENT_ACCOUNT));
        valuesMapDebitAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), transactionDTO.getCustomerAccNumber());
        valuesMapDebitAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(), Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION));
        valuesMapDebitAccount.put(TemplateField.TRANSACTION_AMOUNT.name(), StringPool.MINUS + StringUtil.formatMoney(transactionDTO.getTransactionAmount()));
        valuesMapDebitAccount.put((TemplateField.CURRENCY.name()), transactionDTO.getTransactionCurrency());
        valuesMapDebitAccount.put((TemplateField.LABEL_TRANSACTION_TIME.name()), Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));
        valuesMapDebitAccount.put(TemplateField.TRANSACTION_TIME.name(), transactionFinishTime);
        valuesMapDebitAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(), Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT));

        if (Validator.isNull(transactionDTO.getMessage())) {
            valuesMapDebitAccount.put(TemplateField.CONTENT_TRANSACTION.name(), message);
        } else {
            valuesMapDebitAccount.put(TemplateField.CONTENT_TRANSACTION.name(), transactionDTO.getMessage());
        }
        valuesMapDebitAccount.put((TemplateField.TRANSACTION_CODE.name()), transactionCode);

        // receive
        valuesMapCreditAccount.put(TemplateField.LABEL_REFERENCE_ID.name(), Labels.getLabels(LabelKey.LABEL_REFERENCE_ID));
        valuesMapCreditAccount.put(TemplateField.REFERENCE_ID.name(), transactionDTO.getTransactionId());
        valuesMapCreditAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(), Labels.getLabels(LabelKey.LABEL_BENEFICIARY_ACCOUNT));
        valuesMapCreditAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), savingAccount.getReceivingAccountNumber());// tài khoản thụ hưởng
        valuesMapCreditAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(), Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION));
        valuesMapCreditAccount.put(TemplateField.TRANSACTION_AMOUNT.name(), StringPool.PLUS + StringUtil.formatMoney(transactionDTO.getTransactionAmount() + getAmtAfterSettlement));
        valuesMapCreditAccount.put((TemplateField.CURRENCY.name()), transactionDTO.getTransactionCurrency());
        valuesMapCreditAccount.put((TemplateField.LABEL_TRANSACTION_TIME.name()), Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));
        valuesMapCreditAccount.put(TemplateField.TRANSACTION_TIME.name(), transactionFinishTime);
        valuesMapCreditAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(), Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT));

        if (Validator.isNull(transactionDTO.getMessage())) {
            valuesMapCreditAccount.put((TemplateField.MESSAGE.name()), message);
        } else {
            valuesMapCreditAccount.put((TemplateField.MESSAGE.name()), transactionDTO.getMessage());
        }
        valuesMapCreditAccount.put((TemplateField.TRANSACTION_CODE.name()), transactionCode);

        ContentTemplate templateTransferMoney = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.BALANCE_CHANGE_PAY_INSURANCE.name(), EntityStatus.ACTIVE.getStatus());

        NotificationDTO notiForReceiver = new NotificationDTO();
        NotificationDTO notiForSender = new NotificationDTO();

        if (Validator.isNotNull(templateTransferMoney) &&
                (Validator.equals(TransferType.SAVING_ACCOUNT, transactionDTO.getTransferType())
                        || Validator.equals(TransferType.DEPOSIT_MORE, transactionDTO.getTransferType()))) {

            notiForReceiver.setNotificationStatus(NotificationStatus.PENDING);
            notiForReceiver.setNotificationType(NotificationType.TRANSFER_TRANSACTION);
            notiForReceiver.setTargetType(TargetType.USER);
            notiForReceiver.setEndUserType(EndUserType.CUSTOMER);
            notiForReceiver.setClassPk(transactionDTO.getCustomerId());
            notiForReceiver.setPublishTime(LocalDateTime.now());

            notiForReceiver.setTitle(Labels.getLabels(templateTransferMoney.getTitle()));
            notiForReceiver
                    .setContent(StringUtil.replaceMapValue(templateTransferMoney.getContent(), valuesMapDebitAccount));
            notiForReceiver.setDescription(templateTransferMoney.getDescription());

            Notification notificationAfterSaved = this.notificationRepository
                    .save(this.notificationMapper.toEntity(notiForReceiver));
            notiForReceiver = this.notificationMapper.toDto(notificationAfterSaved);

            // save notification transaction
            NotiTransaction notiTransaction = new NotiTransaction();

            notiTransaction.setNotificationId(notificationAfterSaved.getNotificationId());
            notiTransaction.setTransactionId(transactionDTO.getTransferTransactionId());
            notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus());

            this.notiTransactionRepository.save(notiTransaction);
        }

        ContentTemplate templateReceiveMoney = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.BALANCE_CHANGE_RECEIVE_MONEY.name(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(templateReceiveMoney) && Validator.equals(TransferType.SETTLEMENT, transactionDTO.getTransferType())) {

            notiForSender.setNotificationStatus(NotificationStatus.PENDING);
            notiForSender.setNotificationType(NotificationType.TRANSFER_TRANSACTION);
            notiForSender.setTargetType(TargetType.USER);
            notiForSender.setEndUserType(EndUserType.CUSTOMER);

            // Neu giao dich la chuyen tien cho các tài khoản bên ngoài hệ thống => ko gửi
            // thông báo
            Optional<MoneyAccount> moneyAccount = this.moneyAccountRepository
                    .findByAccountNumberAndCustomerIdAndStatus(transactionDTO.getBeneficiaryAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

            if (!moneyAccount.isPresent()) {
                throw new BadRequestAlertException(ErrorCode.MSG1036);
            }

            List<NotiTransaction> notiTransactions = new ArrayList<>();
            Long beneficiaryCustomerId = moneyAccount.get().getCustomerId();

            notiForSender.setClassPk(beneficiaryCustomerId);

            notiForSender.setPublishTime(LocalDateTime.now());

            notiForSender.setTitle(Labels.getLabels(templateReceiveMoney.getTitle()));
            notiForSender.setContent(
                    StringUtil.replaceMapValue(templateReceiveMoney.getContent(), valuesMapCreditAccount));
            notiForSender.setDescription(templateReceiveMoney.getDescription());

            Notification notificationAfterSaved = this.notificationRepository
                    .save(this.notificationMapper.toEntity(notiForSender));
            notiForSender = this.notificationMapper.toDto(notificationAfterSaved);

            // save notification transaction
            NotiTransaction notiTransaction = new NotiTransaction();

            notiTransaction.setNotificationId(notificationAfterSaved.getNotificationId());
            notiTransaction.setTransactionId(transactionDTO.getTransferTransactionId());
            notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus());

            notiTransactions.add(notiTransaction);
            this.notiTransactionRepository.saveAll(notiTransactions);
        }

        if (Validator.equals(notiForSender.getNotificationType(), NotificationType.TRANSFER_TRANSACTION)) {
            this.sendService.sendNotification(notiForSender);
        }
        if (Validator.equals(notiForReceiver.getNotificationType(), NotificationType.TRANSFER_TRANSACTION)) {
            this.sendService.sendNotification(notiForReceiver);
        }
    }

    private Customer getCustomerLogin() {
        return GwSecurityUtils.getCustomerLogin()
                .orElseThrow(() -> new UnauthorizedException(ErrorCode.MSG1077));
    }
}
