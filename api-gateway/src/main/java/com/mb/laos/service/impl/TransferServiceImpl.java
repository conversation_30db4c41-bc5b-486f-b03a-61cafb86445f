// Package chứa các service implementation củ<PERSON> hệ thống MB Laos
package com.mb.laos.service.impl;

// Import các exception classes để xử lý lỗi
import com.mb.laos.api.exception.BadRequestAlertException; // Exception cho các lỗi bad request
import com.mb.laos.api.exception.NoRollBackBadRequestAlertException; // Exception không rollback transaction
import com.mb.laos.api.exception.UnauthorizedException; // Exception cho lỗi unauthorized

// Import các request classes cho API calls
import com.mb.laos.api.request.AccNumberVerifyRequest; // Request xác thực số tài khoản
import com.mb.laos.api.request.AccountBalanceRequest; // Request kiểm tra số dư tài khoản
import com.mb.laos.api.request.AccountMoney; // Request thông tin tài khoản tiền
import com.mb.laos.api.request.AddInfoList; // Request danh sách thông tin bổ sung
import com.mb.laos.api.request.Amount; // Request thông tin số tiền giao dịch
import com.mb.laos.api.request.ApiUnitelRequest; // Request API Unitel
import com.mb.laos.api.request.AuthenMethod; // Request phương thức xác thực
import com.mb.laos.api.request.CashoutTransferRequest; // Request rút tiền mặt
import com.mb.laos.api.request.ChargeInfo; // Request thông tin phí giao dịch
import com.mb.laos.api.request.CheckTransactionMmoneyRequest; // Request kiểm tra giao dịch Mmoney
import com.mb.laos.api.request.CreditBank; // Request thông tin ngân hàng nhận
import com.mb.laos.api.request.DebitMoneyTransferRequest; // Request chuyển tiền debit
import com.mb.laos.api.request.DepositMoneyTransferRequest; // Request gửi tiền
import com.mb.laos.api.request.ETLPaymentTopupWSRequest; // Request nạp tiền ETL
import com.mb.laos.api.request.InquiryTransactionStatusRequest; // Request truy vấn trạng thái giao dịch
import com.mb.laos.api.request.MobilePaymentRequest; // Request thanh toán di động
import com.mb.laos.api.request.OtpTransferRequest; // Request OTP chuyển tiền
import com.mb.laos.api.request.OtpVerifyTransferRequest; // Request xác thực OTP chuyển tiền
import com.mb.laos.api.request.PaymentInternetLtcRequest; // Request thanh toán internet LTC
import com.mb.laos.api.request.PaymentPostpaidLtcRequest; // Request thanh toán trả sau LTC
import com.mb.laos.api.request.PaymentPstnLtcRequest; // Request thanh toán PSTN LTC
import com.mb.laos.api.request.RevertTransactionRequest; // Request hoàn tác giao dịch
import com.mb.laos.api.request.TransactionHistoryMBRequest; // Request lịch sử giao dịch MB
import com.mb.laos.api.request.TransferAccountRequest; // Request chuyển khoản
import com.mb.laos.api.request.ValidateMobileNumberRequest; // Request validate số điện thoại
import com.mb.laos.api.request.VerifyInternetLtcMmoneyRequest; // Request xác thực internet LTC Mmoney
import com.mb.laos.api.request.VerifyPhoneNumberRequest; // Request xác thực số điện thoại
import com.mb.laos.api.request.VerifyPostPaidLtcMmoneyRequest; // Request xác thực trả sau LTC Mmoney
import com.mb.laos.api.request.VerifyPstnMmoneyPstnRequest; // Request xác thực PSTN Mmoney
// Import các response classes để trả về kết quả API
import com.mb.laos.api.response.BestTelecomResponse; // Response từ Best Telecom
import com.mb.laos.api.response.CheckBalanceLtcResponse; // Response kiểm tra số dư LTC
import com.mb.laos.api.response.CheckTransactionMmoneyResponse; // Response kiểm tra giao dịch Mmoney
import com.mb.laos.api.response.PaymentInternetLtcResponse; // Response thanh toán internet LTC
import com.mb.laos.api.response.PaymentPostPaidLtcResponse; // Response thanh toán trả sau LTC
import com.mb.laos.api.response.PaymentPstnLtcResponse; // Response thanh toán PSTN LTC
import com.mb.laos.api.response.QueryDebtUnitelResponse; // Response truy vấn nợ Unitel
import com.mb.laos.api.response.QueryEtlResponse; // Response truy vấn ETL
import com.mb.laos.api.response.TopupEtlResponse; // Response nạp tiền ETL
import com.mb.laos.api.response.TopupUnitelResponse; // Response nạp tiền Unitel
import com.mb.laos.api.response.TransferAccountResponse; // Response chuyển khoản
import com.mb.laos.api.response.VerifyInternetLtcResponse; // Response xác thực internet LTC
import com.mb.laos.api.response.VerifyPostPaidLtcResponse; // Response xác thực trả sau LTC
import com.mb.laos.api.response.VerifyPstnLtcResponse; // Response xác thực PSTN LTC
import com.mb.laos.api.response.VerifyUmoneyAccountResponse; // Response xác thực tài khoản Umoney
// Import các utility classes và constants
import com.mb.laos.api.util.ApiConstants; // Constants cho API
import com.mb.laos.api.util.ErrorCode; // Mã lỗi hệ thống

// Import các configuration properties
import com.mb.laos.configuration.ConstantProperties; // Properties hằng số
import com.mb.laos.configuration.ConsumerProperties; // Properties consumer
import com.mb.laos.configuration.CustomerProperties; // Properties khách hàng
import com.mb.laos.configuration.InternationalPaymentProperties; // Properties thanh toán quốc tế
import com.mb.laos.configuration.MBApiConstantProperties; // Properties API MB
import com.mb.laos.configuration.OtpProperties; // Properties OTP
import com.mb.laos.configuration.QrProperties; // Properties QR Code
import com.mb.laos.configuration.TransferTransProperties; // Properties chuyển tiền
// Import các enum classes để định nghĩa constants
import com.mb.laos.enums.AccountType; // Enum loại tài khoản
import com.mb.laos.enums.BillingType; // Enum loại hóa đơn
import com.mb.laos.enums.Channel; // Enum kênh giao dịch
import com.mb.laos.enums.CommonCategory; // Enum danh mục chung
import com.mb.laos.enums.CurrencyType; // Enum loại tiền tệ
import com.mb.laos.enums.EndUserType; // Enum loại người dùng cuối
import com.mb.laos.enums.EntityStatus; // Enum trạng thái entity
import com.mb.laos.enums.FromAccountType; // Enum loại tài khoản nguồn
import com.mb.laos.enums.InternationalQuery; // Enum truy vấn quốc tế
import com.mb.laos.enums.MasterMerchantType; // Enum loại merchant chính
import com.mb.laos.enums.MerchantCode; // Enum mã merchant
import com.mb.laos.enums.MmoneyServiceName; // Enum tên dịch vụ Mmoney
import com.mb.laos.enums.NationCode; // Enum mã quốc gia
import com.mb.laos.enums.NotificationStatus; // Enum trạng thái thông báo
import com.mb.laos.enums.NotificationType; // Enum loại thông báo
import com.mb.laos.enums.OtpConfirmType; // Enum loại xác thực OTP
import com.mb.laos.enums.OtpType; // Enum loại OTP
import com.mb.laos.enums.Sector; // Enum lĩnh vực
import com.mb.laos.enums.T24TransferType; // Enum loại chuyển tiền T24
import com.mb.laos.enums.TargetType; // Enum loại đích
import com.mb.laos.enums.TelcoType; // Enum nhà mạng viễn thông
import com.mb.laos.enums.TemplateCode; // Enum mã template
import com.mb.laos.enums.TemplateField; // Enum trường template
import com.mb.laos.enums.TransactionFeeTypeCode; // Enum mã loại phí giao dịch
import com.mb.laos.enums.TransactionStatus; // Enum trạng thái giao dịch
import com.mb.laos.enums.TransactionType; // Enum loại giao dịch
import com.mb.laos.enums.TransferTransactionType; // Enum loại giao dịch chuyển tiền
import com.mb.laos.enums.TransferType; // Enum loại chuyển tiền
import com.mb.laos.enums.UmoneyConfigurationType; // Enum loại cấu hình Umoney
// Import các gateway request classes cho internal API calls
import com.mb.laos.gateway.request.BeneficiaryConfirmRequest; // Request xác nhận người thụ hưởng
import com.mb.laos.gateway.request.BillingHistoryRequest; // Request lịch sử hóa đơn
import com.mb.laos.gateway.request.BillingRequest; // Request hóa đơn
import com.mb.laos.gateway.request.BillingV2Request; // Request hóa đơn phiên bản 2
import com.mb.laos.gateway.request.CashoutRequest; // Request rút tiền mặt
import com.mb.laos.gateway.request.ConfirmOtpBillingRequest; // Request xác nhận OTP hóa đơn
import com.mb.laos.gateway.request.ConfirmOtpBillingV2Request; // Request xác nhận OTP hóa đơn V2
import com.mb.laos.gateway.request.DebitMoneyRequest; // Request vay tiền
import com.mb.laos.gateway.request.DepositMoneyRequest; // Request gửi tiền
import com.mb.laos.gateway.request.FeeTransactionRequest; // Request phí giao dịch
import com.mb.laos.gateway.request.InfoQrMerchantRequest; // Request thông tin QR merchant
import com.mb.laos.gateway.request.OtpBillingRequest; // Request OTP hóa đơn
import com.mb.laos.gateway.request.OtpBillingV2Request; // Request OTP hóa đơn V2
import com.mb.laos.gateway.request.OtpCashInRequest; // Request OTP nạp tiền
import com.mb.laos.gateway.request.OtpQrBillRequest; // Request OTP QR hóa đơn
import com.mb.laos.gateway.request.OtpTransferConfirmRequest; // Request xác nhận OTP chuyển tiền
import com.mb.laos.gateway.request.OtpTransferInsuranceRequest; // Request OTP chuyển tiền bảo hiểm
import com.mb.laos.gateway.request.OtpTransferTransRequest; // Request OTP chuyển tiền trans
import com.mb.laos.gateway.request.ResendOtpRequest; // Request gửi lại OTP
import com.mb.laos.gateway.request.TransactionHistoryRequest; // Request lịch sử giao dịch
// Import các gateway response classes cho internal API responses
import com.mb.laos.gateway.response.BeneficiaryInfoResponse; // Response thông tin người thụ hưởng
import com.mb.laos.gateway.response.BillingHistoryResponse; // Response lịch sử hóa đơn
import com.mb.laos.gateway.response.BillingHistoryV2Response; // Response lịch sử hóa đơn V2
import com.mb.laos.gateway.response.BillingResponse; // Response hóa đơn
import com.mb.laos.gateway.response.CashInHistoryResponse; // Response lịch sử nạp tiền
import com.mb.laos.gateway.response.CashoutResponse; // Response rút tiền mặt
import com.mb.laos.gateway.response.DebitMoneyResponse; // Response vay tiền
import com.mb.laos.gateway.response.DepositMoneyResponse; // Response gửi tiền
import com.mb.laos.gateway.response.FeeTransactionResponse; // Response phí giao dịch
import com.mb.laos.gateway.response.InfoQrMerchantResponse; // Response thông tin QR merchant
import com.mb.laos.gateway.response.MBOtpTransConfirmResponse; // Response xác nhận OTP MB
import com.mb.laos.gateway.response.OtpTransferConfirmResponse; // Response xác nhận OTP chuyển tiền
import com.mb.laos.gateway.response.OtpTransferTransResponse; // Response OTP chuyển tiền trans
// Import các message classes cho đa ngôn ngữ
import com.mb.laos.messages.LabelKey; // Key cho label đa ngôn ngữ
import com.mb.laos.messages.Labels; // Labels đa ngôn ngữ

// Import các model entities
import com.mb.laos.model.Bank; // Entity ngân hàng
import com.mb.laos.model.CashoutAccount; // Entity tài khoản rút tiền
import com.mb.laos.model.Common; // Entity dữ liệu chung
import com.mb.laos.model.ContentTemplate; // Entity template nội dung
import com.mb.laos.model.Currency; // Entity tiền tệ
import com.mb.laos.model.Customer; // Entity khách hàng
import com.mb.laos.model.CustomerEkyc; // Entity eKYC khách hàng
import com.mb.laos.model.DOTP; // Entity DOTP (Dynamic OTP)
import com.mb.laos.model.DebitAccount; // Entity tài khoản debit
import com.mb.laos.model.Fee; // Entity phí
import com.mb.laos.model.InternationalPayment; // Entity thanh toán quốc tế
import com.mb.laos.model.Merchant; // Entity merchant
import com.mb.laos.model.MoneyAccount; // Entity tài khoản tiền
import com.mb.laos.model.NotiTransaction; // Entity thông báo giao dịch
import com.mb.laos.model.Notification; // Entity thông báo
import com.mb.laos.model.Telco; // Entity nhà mạng viễn thông
import com.mb.laos.model.Transaction; // Entity giao dịch
import com.mb.laos.model.TransactionClient; // Entity giao dịch client
import com.mb.laos.model.TransactionMerchant; // Entity giao dịch merchant
// Import các DTO classes cho data transfer
import com.mb.laos.model.dto.AccNumberVerifyDTO; // DTO xác thực số tài khoản
import com.mb.laos.model.dto.AccountBalanceDTO; // DTO số dư tài khoản
import com.mb.laos.model.dto.InquiryTransactionStatusDTO; // DTO truy vấn trạng thái giao dịch
import com.mb.laos.model.dto.InternationalPaymentDTO; // DTO thanh toán quốc tế
import com.mb.laos.model.dto.MoneyAccountDTO; // DTO tài khoản tiền
import com.mb.laos.model.dto.NotificationDTO; // DTO thông báo
import com.mb.laos.model.dto.ReqFundTransferDTO; // DTO request chuyển tiền
import com.mb.laos.model.dto.TransactionDTO; // DTO giao dịch
import com.mb.laos.model.dto.TransactionHistoryDTO; // DTO lịch sử giao dịch
import com.mb.laos.model.dto.VerifyFundTransferDTO; // DTO xác thực chuyển tiền
// Import các search classes cho tìm kiếm
import com.mb.laos.model.search.BillingHistoryV2Search; // Search lịch sử hóa đơn V2
import com.mb.laos.model.search.CashInHistorySearch; // Search lịch sử nạp tiền
import com.mb.laos.model.search.TransactionSearchRequest; // Search request giao dịch

// Import các repository classes cho database access
import com.mb.laos.repository.BankRepository; // Repository ngân hàng
import com.mb.laos.repository.CashoutAccountRepository; // Repository tài khoản rút tiền
import com.mb.laos.repository.CommonRepository; // Repository dữ liệu chung
import com.mb.laos.repository.ContentTemplateRepository; // Repository template nội dung
import com.mb.laos.repository.CustomerEkycRepository; // Repository eKYC khách hàng
import com.mb.laos.repository.CustomerRepository; // Repository khách hàng
import com.mb.laos.repository.DebitAccountRepository; // Repository tài khoản debit
import com.mb.laos.repository.DotpRepository; // Repository DOTP
import com.mb.laos.repository.InternationalPaymentRepository; // Repository thanh toán quốc tế
import com.mb.laos.repository.MerchantRepository; // Repository merchant
import com.mb.laos.repository.MoneyAccountRepository; // Repository tài khoản tiền
import com.mb.laos.repository.NotiTransactionRepository; // Repository thông báo giao dịch
import com.mb.laos.repository.NotificationRepository; // Repository thông báo
import com.mb.laos.repository.TelcoRepository; // Repository nhà mạng viễn thông
import com.mb.laos.repository.TransactionClientRepository; // Repository giao dịch client
import com.mb.laos.repository.TransactionMerchantRepository; // Repository giao dịch merchant
import com.mb.laos.repository.TransactionRepository; // Repository giao dịch
// Import các request/response classes khác
import com.mb.laos.request.QrVerifyRequest; // Request xác thực QR
import com.mb.laos.response.QrCodeResponse; // Response QR Code

// Import các security utilities
import com.mb.laos.security.util.GwSecurityUtils; // Utilities bảo mật gateway
import com.mb.laos.security.util.SecurityConstants; // Constants bảo mật
// Import các service interfaces và implementations
import com.mb.laos.service.ApiBestTelecomService; // Service API Best Telecom
import com.mb.laos.service.ApiETLService; // Service API ETL
import com.mb.laos.service.ApiGeeTransferService; // Service API Gee Transfer
import com.mb.laos.service.ApiLtcService; // Service API LTC
import com.mb.laos.service.ApiMmoneyService; // Service API Mmoney
import com.mb.laos.service.ApiUnitelService; // Service API Unitel
import com.mb.laos.service.BeneficiaryService; // Service người thụ hưởng
import com.mb.laos.service.ConsumerOtpService; // Service consumer OTP
import com.mb.laos.service.DotpService; // Service DOTP
import com.mb.laos.service.EwalletService; // Service ví điện tử
import com.mb.laos.service.MmoneyEncryptService; // Service mã hóa Mmoney
import com.mb.laos.service.QrCodeService; // Service QR Code
import com.mb.laos.service.SendService; // Service gửi
import com.mb.laos.service.TransactionService; // Service giao dịch
import com.mb.laos.service.TransferService; // Service chuyển tiền
// Import các mapper classes cho chuyển đổi Entity/DTO
import com.mb.laos.service.mapper.InternationalPaymentMapper; // Mapper thanh toán quốc tế
import com.mb.laos.service.mapper.MoneyAccountMapper; // Mapper tài khoản tiền
import com.mb.laos.service.mapper.NotificationMapper; // Mapper thông báo
import com.mb.laos.service.mapper.TransactionMapper; // Mapper giao dịch

// Import các utility classes
import com.mb.laos.util.DateUtil; // Utility xử lý ngày tháng
import com.mb.laos.util.GetterUtil; // Utility getter
import com.mb.laos.util.InstantUtil; // Utility xử lý Instant
import com.mb.laos.util.LocalDateUtil; // Utility xử lý LocalDate
import com.mb.laos.util.MD5Generator; // Utility tạo MD5 hash
import com.mb.laos.util.NumberUtil; // Utility xử lý số
import com.mb.laos.util.QueryUtil; // Utility xử lý query
import com.mb.laos.util.RandomGenerator; // Utility tạo số ngẫu nhiên
import com.mb.laos.util.StringPool; // Pool các string constants
import com.mb.laos.util.StringUtil; // Utility xử lý string
import com.mb.laos.util.UUIDUtil; // Utility tạo UUID
import com.mb.laos.util.Validator; // Utility validation
// Import validator
import com.mb.laos.validator.ValidationConstraint; // Constraint validation

// Import Lombok annotations
import lombok.RequiredArgsConstructor; // Annotation tự động tạo constructor
import lombok.extern.slf4j.Slf4j; // Annotation tự động tạo logger

// Import các annotations và utilities khác
import org.jetbrains.annotations.NotNull; // Annotation NotNull
import org.springframework.beans.BeanUtils; // Utility copy properties
import org.springframework.data.domain.Page; // Interface phân trang
import org.springframework.data.domain.PageImpl; // Implementation phân trang
import org.springframework.data.domain.PageRequest; // Request phân trang
import org.springframework.data.domain.Pageable; // Interface pageable
import org.springframework.security.core.context.SecurityContextHolder; // Holder security context
import org.springframework.stereotype.Service; // Annotation đánh dấu service
import org.springframework.transaction.annotation.Transactional; // Annotation transaction

// Import Java standard libraries
import javax.servlet.http.HttpServletRequest; // HTTP servlet request
import java.text.DecimalFormat; // Format số thập phân
import java.time.Instant; // Thời điểm tức thì
import java.time.LocalDate; // Ngày địa phương
import java.time.LocalDateTime; // Ngày giờ địa phương
import java.time.Period; // Khoảng thời gian
import java.time.ZoneId; // ID múi giờ
import java.time.ZoneOffset; // Offset múi giờ
import java.time.format.DateTimeFormatter; // Formatter ngày giờ
import java.time.temporal.ChronoUnit; // Đơn vị thời gian
import java.util.ArrayList; // Danh sách động
import java.util.Arrays; // Utility cho mảng
import java.util.Collections; // Utility cho collection
import java.util.HashMap; // Map hash
import java.util.HashSet; // Set hash
import java.util.LinkedHashSet; // Set linked hash
import java.util.List; // Interface danh sách
import java.util.Map; // Interface map
import java.util.Objects; // Utility cho object
import java.util.Optional; // Container optional
import java.util.Set; // Interface set
import java.util.stream.Collectors; // Collector cho stream

/**
 * Service Implementation xử lý tất cả các loại giao dịch chuyển tiền và thanh toán
 *
 * Đây là service phức tạp nhất của hệ thống với hơn 3000 dòng code, xử lý:
 *
 * 💰 CHUYỂN TIỀN:
 * - Chuyển tiền nội bộ (cùng MB Bank)
 * - Chuyển tiền LAPNET (liên ngân hàng trong Lào)
 * - Chuyển tiền quốc tế (Cambodia, Vietnam...)
 * - Chuyển tiền ngoại tệ (USD, THB...)
 *
 * 💳 THANH TOÁN:
 * - Thanh toán QR Code (merchant, LAPNET, quốc tế)
 * - Thanh toán hóa đơn (điện, nước, internet, TV)
 * - Nạp tiền điện thoại (Unitel, LTC, ETL)
 * - Thanh toán bảo hiểm
 * - Nạp tiền ví điện tử (Umoney, Mmoney)
 *
 * 🏦 DỊCH VỤ NGÂN HÀNG:
 * - Rút tiền mặt (cashout)
 * - Gửi tiền tiết kiệm (deposit)
 * - Vay tiền (debit)
 *
 * 🔄 TÍCH HỢP HỆ THỐNG:
 * - T24 Core Banking (tạo OTP, xác thực giao dịch)
 * - LAPNET (chuyển tiền liên ngân hàng)
 * - Telco APIs (Unitel, LTC, ETL)
 * - Umoney/Mmoney APIs
 * - International Payment Gateway
 *
 * 📊 QUẢN LÝ GIAO DỊCH:
 * - Tạo và lưu transaction
 * - Kiểm tra hạn mức
 * - Tính phí giao dịch
 * - Xử lý tỷ giá ngoại tệ
 * - Log và audit trail
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2023
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class TransferServiceImpl implements TransferService {

    // ==================== CORE REPOSITORIES ====================

    /** Repository chính cho thao tác với khách hàng */
    private final CustomerRepository customerRepository;

    /** Mapper chuyển đổi Transaction Entity/DTO */
    private final TransactionMapper transactionMapper;

    /** Repository quản lý giao dịch */
    private final TransactionRepository transactionRepository;

    /** Repository quản lý tài khoản tiền */
    private final MoneyAccountRepository moneyAccountRepository;

    /** Mapper chuyển đổi MoneyAccount Entity/DTO */
    private final MoneyAccountMapper moneyAccountMapper;

    // ==================== EXTERNAL SERVICES ====================

    /** Service tích hợp ApiGee cho chuyển tiền */
    private final ApiGeeTransferService apiGeeTransferService;

    // ==================== CONFIGURATION PROPERTIES ====================

    /** Properties cấu hình API MB */
    private final MBApiConstantProperties mbApiConstantProperties;

    /** Properties cấu hình OTP */
    private final OtpProperties otpProperties;

    /** Properties cấu hình khách hàng */
    private final CustomerProperties customerProperties;

    // ==================== MASTER DATA REPOSITORIES ====================

    /** Repository thông tin ngân hàng */
    private final BankRepository bankRepository;

    /** Repository thông tin merchant */
    private final MerchantRepository merchantRepository;

    /** Service xử lý giao dịch */
    private final TransactionService transactionService;

    /** Repository eKYC khách hàng */
    private final CustomerEkycRepository customerEkycRepository;

    /** Repository template nội dung */
    private final ContentTemplateRepository contentTemplateRepository;

    /** Repository thông báo */
    private final NotificationRepository notificationRepository;

    /** Repository thông báo giao dịch */
    private final NotiTransactionRepository notiTransactionRepository;

    /** Mapper chuyển đổi Notification Entity/DTO */
    private final NotificationMapper notificationMapper;

    /** Repository giao dịch merchant */
    private final TransactionMerchantRepository transactionMerchantRepository;

    /** Service API LTC */
    private final ApiLtcService apiLtcService;

    /** Properties cấu hình consumer */
    private final ConsumerProperties consumerProperties;

    /** Service consumer OTP */
    private final ConsumerOtpService consumerOtpService;

    /** Service ví điện tử */
    private final EwalletService ewalletService;

    /** Service người thụ hưởng */
    private final BeneficiaryService beneficiaryService;

    /** Service DOTP */
    private final DotpService dotpService;

    /** Properties cấu hình QR */
    private final QrProperties qrProperties;

    /** Properties cấu hình chuyển tiền trans */
    private final TransferTransProperties transferTransProperties;

    /** Service QR Code */
    private final QrCodeService qrCodeService;

    /** Repository nhà mạng viễn thông */
    private final TelcoRepository telcoRepository;

    /** Service API Unitel */
    private final ApiUnitelService apiUnitelService;

    /** Service API ETL */
    private final ApiETLService apiETLService;

    /** Service API Best Telecom */
    private final ApiBestTelecomService apiBestTelecomService;

    /** Properties hằng số */
    private final ConstantProperties constantProperties;

    /** Repository giao dịch client */
    private final TransactionClientRepository transactionClientRepository;

    /** Repository tài khoản rút tiền */
    private final CashoutAccountRepository cashoutAccountRepository;

    /** Repository tài khoản debit */
    private final DebitAccountRepository debitAccountRepository;

    /** Service API Mmoney */
    private final ApiMmoneyService apiMmoneyService;

    /** Service mã hóa Mmoney */
    private final MmoneyEncryptService mmoneyEncryptService;

    /** Service gửi */
    private final SendService sendService;

    /** Repository dữ liệu chung */
    private final CommonRepository commonRepository;

    /** Implementation service QR Code */
    private final QrCodeServiceImpl qrCodeServiceImpl;

    /** Properties thanh toán quốc tế */
    private final InternationalPaymentProperties internationalPaymentProperties;

    /** Repository DOTP */
    private final DotpRepository dotpRepository;

    /** Implementation service phí */
    private final FeeServiceImpl feeService;

    /** Repository thanh toán quốc tế */
    private final InternationalPaymentRepository internationalPaymentRepository;

    /** Mapper chuyển đổi InternationalPayment Entity/DTO */
    private final InternationalPaymentMapper internationalPaymentMapper;

    /**
     * Xác nhận thông tin người nhận tiền
     *
     * Method này là bước đầu tiên trong luồng chuyển tiền, dùng để:
     * - Xác thực số tài khoản/số điện thoại người nhận
     * - Lấy thông tin tên người nhận từ hệ thống
     * - Kiểm tra trạng thái tài khoản (active, blocked...)
     * - Phân biệt chuyển tiền nội bộ vs liên ngân hàng
     *
     * Luồng xử lý:
     * 1. Validate mã ngân hàng tồn tại và active
     * 2. Phân biệt loại chuyển tiền:
     *    - Nếu bankCode = MB: Gọi API nội bộ T24
     *    - Nếu bankCode khác: Gọi API LAPNET liên ngân hàng
     * 3. Trả về thông tin người nhận để hiển thị xác nhận
     *
     * @param request Thông tin người nhận (bankCode, accountNumber)
     * @return BeneficiaryInfoResponse Thông tin chi tiết người nhận
     * @throws BadRequestAlertException Khi mã ngân hàng không hợp lệ
     */
    @Override
    public BeneficiaryInfoResponse confirmBeneficiary(BeneficiaryConfirmRequest request) {
        // Kiểm tra mã ngân hàng có tồn tại và đang hoạt động
        List<Bank> bank = this.bankRepository.findByBankCodeAndStatus(request.getBankCode(), EntityStatus.ACTIVE.getStatus());
        if (bank.isEmpty()) {
            throw new BadRequestAlertException(ErrorCode.MSG100162);
        }

        // Phân biệt chuyển tiền nội bộ vs liên ngân hàng dựa vào bank code
        // Logic routing: BankCode == MB ? call truy vấn nội bộ : call truy vấn LAPNET
        if (Validator.equals(this.mbApiConstantProperties.getInBanKTransfer().getChannel(), request.getBankCode())) {

            // CHUYỂN TIỀN NỘI BỘ - cùng MB Bank
            // Gọi API T24 trực tiếp, không qua LAPNET network
            // Ưu điểm: nhanh, rẻ, real-time
            return this.internalBankMB(request.getBeneficiaryAccountNumber());

        } else {

            // CHUYỂN TIỀN LIÊN NGÂN HÀNG - qua LAPNET
            // Gọi API LAPNET để verify account ở ngân hàng khác
            // Có thể có phí liên ngân hàng và delay network
            return this.interbank(request);
        }
    }

    /**
     * Xác thực OTP và thực hiện chuyển tiền MB Bank
     *
     * Method này xử lý xác thực OTP và thực hiện chuyển tiền thông qua hệ thống T24:
     * 1. Lấy thông tin khách hàng đang đăng nhập
     * 2. Chuẩn bị thông tin tài khoản ghi nợ (debit account)
     * 3. Chuẩn bị thông tin tài khoản có (credit account)
     * 4. Chuẩn bị thông tin số tiền giao dịch
     * 5. Tạo request xác thực OTP gửi đến T24
     * 6. Gọi service xác thực OTP và thực hiện chuyển tiền
     * 7. Trả về kết quả giao dịch
     *
     * @param transaction DTO chứa thông tin giao dịch
     * @param otp Mã OTP từ khách hàng
     * @param deviceId ID thiết bị (cho DOTP)
     * @return MBOtpTransConfirmResponse Kết quả xác thực và chuyển tiền
     */
    @Override
    public MBOtpTransConfirmResponse confirmFundTransferMB(TransactionDTO transaction, String otp, String deviceId) {
        // Lấy thông tin khách hàng đang đăng nhập
        Customer customer = getCustomerLogin();

        // Chuẩn bị thông tin tài khoản ghi nợ (tài khoản trừ tiền)
        AccountMoney debitAccount = new AccountMoney();

        // Set số tài khoản khách hàng
        debitAccount.setAccountNumber(transaction.getCustomerAccNumber());
        // Set tên chủ tài khoản
        debitAccount.setAccountName(customer.getFullname());
        // Set loại tài khoản từ config
        debitAccount.setAccountType(this.mbApiConstantProperties.getInBanKTransfer().getAccountType());
        // Set loại tiền tệ giao dịch
        debitAccount.setAccountCurrency(transaction.getTransactionCurrency());

        // Chuẩn bị thông tin tài khoản có (tài khoản nhận tiền)
        AccountMoney creditAccount = new AccountMoney();

        // Kiểm tra loại giao dịch để set thông tin tài khoản nhận phù hợp
        if (TransferTransactionType.getQrLapnetTransaction().contains(transaction.getType())) {
            // Nếu là giao dịch QR LAPNET, set loại tài khoản QR
            creditAccount.setAccountType(this.mbApiConstantProperties.getOtpGen().getQrType());
            // Set mã QR làm số tài khoản
            creditAccount.setAccountNumber(transaction.getQrCodeValue());
        } else {
            // Nếu là giao dịch thường, set loại tài khoản bình thường
            creditAccount.setAccountType(this.mbApiConstantProperties.getInBanKTransfer().getAccountType());
            // Set số tài khoản người thụ hưởng
            creditAccount.setAccountNumber(transaction.getBeneficiaryAccountNumber());
        }
        // Set loại tiền tệ cho tài khoản nhận
        creditAccount.setAccountCurrency(transaction.getTransactionCurrency());

        // Chuẩn bị thông tin số tiền giao dịch
        Amount amount = new Amount();

        // Lấy số tiền giao dịch từ transaction service
        double amountTransaction = this.transactionService.getAmountTransaction(transaction);

        // Format số tiền và set vào amount object
        amount.setAmount(Validator.isNotNull(amountTransaction) ? StringUtil.formatNumberDouble((long) amountTransaction) : String.valueOf(0L));
        // Set loại tiền tệ từ config
        amount.setCurrency(this.mbApiConstantProperties.getInBanKTransfer().getCurrency());

        // Tạo request gửi đến hệ thống T24
        // Tạo user ID từ CIF và số CMND/CCCD
        String userId = customer.getCif() + customer.getIdCardNumber();
        // Tạo object phương thức xác thực
        AuthenMethod authenMethod = AuthenMethod.builder()
                .otpValue(otp) // Mã OTP
                .type(transaction.getOtpType().name() != null ? transaction.getOtpType() : OtpConfirmType.SMS) // Loại OTP
                .userId(userId) // User ID
                .transData(MD5Generator.md5(transaction.getTransData())) // Hash MD5 của transaction data
                .deviceId(deviceId) // ID thiết bị
                .build();
        // Nếu là DOTP, thêm token vào authentication method
        if (Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP)) {
            authenMethod.setToken(dotpService.getCustomerDevice(deviceId).getToken());
        }
        // Tạo request xác thực OTP chuyển tiền
        OtpVerifyTransferRequest otpVerifyTransferRequest = OtpVerifyTransferRequest.builder()
                .otpValue(otp) // Mã OTP
                .transferType(transaction.getT24TransferType()) // Loại chuyển tiền T24
                .channel(transaction.getT24Channel()) // Kênh giao dịch T24
                .serviceType(transaction.getT24ServiceType()) // Loại dịch vụ T24
                .requestId(transaction.getTransactionId()) // ID request
                .remark(transaction.getMessage()) // Ghi chú giao dịch
                .branchCode(transaction.getBranchCode()) // Mã chi nhánh
                .creditAccount(creditAccount) // Tài khoản nhận
                .debitAccount(debitAccount) // Tài khoản trừ
                .amount(amount) // Số tiền
                .authenMethod(authenMethod) // Phương thức xác thực
                .build();

        // Bổ sung thêm thông tin vào request
        this.enrichVerifyTransferRequest(otpVerifyTransferRequest, transaction);

        // Gọi service xác thực OTP và thực hiện chuyển tiền
        VerifyFundTransferDTO reqFundTransferDTO = this.consumerOtpService.verifyOtp(transaction.getTransactionId(),
                transaction.getPhoneNumber(), OtpType.TRANSFER, otpVerifyTransferRequest,
                this.apiGeeTransferService::verifyFundTransfer,
                Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP) ? OtpConfirmType.DOTP : OtpConfirmType.SMS);

        // Tạo và trả về response
        return MBOtpTransConfirmResponse.builder()
                .t24ReferenceNumber(reqFundTransferDTO.getT24ReferenceNumber()) // Số tham chiếu T24
                .referenceNumber(reqFundTransferDTO.getReferenceNumber()) // Số tham chiếu
                .transactionStatus(reqFundTransferDTO.getTransactionStatus()) // Trạng thái giao dịch
                .transactionId(reqFundTransferDTO.getTransactionId()) // ID giao dịch
                .t24ErrorCode(reqFundTransferDTO.getT24ErrorCode()) // Mã lỗi T24 (nếu có)
                .build();
    }

    /**
     * Xác thực OTP và thanh toán hóa đơn - Phiên bản 1 (DEPRECATED)
     *
     * ⚠️ Method này đã lỗi thời, vui lòng sử dụng confirmOtpBilling(ConfirmOtpBillingV2Request) thay thế.
     *
     * @deprecated Sử dụng confirmOtpBilling(ConfirmOtpBillingV2Request) thay thế
     * @param request Thông tin xác thực OTP V1
     * @param httpServletRequest HTTP request
     * @return OtpTransferConfirmResponse Kết quả thanh toán
     */
    @Override
    public OtpTransferConfirmResponse confirmOtpBilling(ConfirmOtpBillingRequest request, HttpServletRequest httpServletRequest) {
        // Lấy thông tin khách hàng đang đăng nhập
        Customer customer = this.getCustomerLogin();

        // Tìm giao dịch theo transaction ID và customer ID
        Transaction transaction = this.transactionRepository
                .findByTransactionIdAndCustomerId(request.getTransactionId(), customer.getCustomerId());

        // Kiểm tra giao dịch có tồn tại không
        if (Validator.isNull(transaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }

        // Kiểm tra trạng thái hóa đơn
        // Tạo request kiểm tra hóa đơn với target (mã hóa đơn)
        BillingRequest billingRequest = new BillingRequest(transaction.getTarget());
        // Gọi lại API kiểm tra số tiền cần thanh toán
        BillingResponse billing = this.verifyBillingCode(billingRequest);

        // Kiểm tra lại số tiền hóa đơn và số tiền tạo giao dịch có trùng khớp không
        if (!Validator.equals(Long.valueOf(transaction.getTransactionAmount().longValue()), billing.getBalance())) {
            throw new BadRequestAlertException(ErrorCode.MSG1164);
        }

        // Chuyển đổi Transaction entity sang DTO
        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);

        // Set thông tin T24 cho thanh toán hóa đơn
        transactionDTO.setT24TransferType(this.mbApiConstantProperties.getBillingPayment().getTransferType());
        transactionDTO.setT24Channel(this.mbApiConstantProperties.getBillingPayment().getChannel());
        transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getBillingPayment().getServiceType());

        // Thực hiện xác nhận chuyển tiền đến merchant
        OtpTransferConfirmResponse response = this.confirmTransferToMerchant(transactionDTO, request.getOtp(), request.getDeviceId());

        // Tạo thông báo cho khách hàng
        this.transactionService.createNotification(transactionDTO);

        return response;
    }

    /**
     * Xác thực OTP và thanh toán hóa đơn - Phiên bản 2 (Khuyến nghị)
     *
     * Method này hoàn tất thanh toán hóa đơn sau khi xác thực OTP:
     * 1. Validate OTP từ khách hàng
     * 2. Kiểm tra transaction tồn tại và trạng thái PENDING
     * 3. Gọi T24 để verify OTP và trừ tiền
     * 4. Gọi API nhà cung cấp để thanh toán hóa đơn:
     *    - EDL: payBill API
     *    - Nam Papa: payWaterBill API
     *    - LTC/Unitel/ETL: payUtilityBill API
     * 5. Xử lý kết quả:
     *    - Thành công: cập nhật transaction = SUCCESS
     *    - Thất bại: revert transaction, hoàn tiền
     * 6. Gửi thông báo kết quả cho khách hàng
     *
     * Error handling:
     * - Bill already paid: Hóa đơn đã được thanh toán
     * - Bill expired: Hóa đơn đã quá hạn
     * - Provider API fail: Revert transaction
     * - Network timeout: Retry mechanism
     *
     * @param request Thông tin xác thực OTP V2
     * @param httpServletRequest HTTP request
     * @return OtpTransferConfirmResponse Kết quả thanh toán
     * @throws BadRequestAlertException Khi có lỗi validation hoặc thanh toán thất bại
     */
    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    @Override
    public OtpTransferConfirmResponse confirmOtpBilling(ConfirmOtpBillingV2Request request,
                                                        HttpServletRequest httpServletRequest) {

        // Lấy thông tin khách hàng đang đăng nhập
        Customer customer = this.getCustomerLogin();

        // Tìm giao dịch theo transaction ID và customer ID
        Transaction transaction = this.transactionRepository
                .findByTransactionIdAndCustomerId(request.getTransactionId(), customer.getCustomerId());

        // Kiểm tra giao dịch có tồn tại không
        if (Validator.isNull(transaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }
        // Kiểm tra giao dịch đã thành công chưa (tránh thanh toán trùng lặp)
        if (Validator.equals(transaction.getTransactionStatus(), TransactionStatus.SUCCESS)) {
            throw new BadRequestAlertException(ErrorCode.MSG1104);
        }
        // Lấy thông tin nhà mạng từ giao dịch
        TelcoType telco = this.getTelcoByTransaction(transaction);

        // Gọi lại API kiểm tra số tiền cần thanh toán
        // Tạo request V2 với đầy đủ thông tin: target, telco, account, billing type
        BillingV2Request billingRequest = new BillingV2Request(transaction.getTarget(), telco.name(),
                transaction.getCustomerAccNumber(), BillingType.valueOf(transaction.getBillingType()).getType());
        BillingResponse billing = this.verifyBillingCode(billingRequest);

        // Kiểm tra lại số tiền hóa đơn và số tiền tạo giao dịch có trùng khớp không
        if (!Validator.equals(Long.valueOf(transaction.getTransactionAmount().longValue()), billing.getBalance())) {
            throw new BadRequestAlertException(ErrorCode.MSG1164);
        }

        // Chuyển đổi Transaction entity sang DTO
        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);

        // Set thông tin T24 cho thanh toán hóa đơn
        transactionDTO.setT24TransferType(this.mbApiConstantProperties.getBillingPayment().getTransferType());
        transactionDTO.setT24Channel(this.mbApiConstantProperties.getBillingPayment().getChannel());
        transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getBillingPayment().getServiceType());

        // Thực hiện xác nhận chuyển tiền đến merchant
        OtpTransferConfirmResponse response = this.confirmTransferToMerchant(transactionDTO, request.getOtp(), request.getDeviceId());

        // Tạo thông báo cho khách hàng
        this.transactionService.createNotification(transactionDTO);

        return response;
    }

    /**
     * Xác thực OTP và thực hiện nạp tiền (Cash In)
     *
     * Method này xử lý xác thực OTP và thực hiện giao dịch nạp tiền:
     * 1. Lấy thông tin khách hàng đang đăng nhập
     * 2. Tìm giao dịch theo transaction ID
     * 3. Validate giao dịch tồn tại
     * 4. Chuẩn bị thông tin T24 cho cash in
     * 5. Thực hiện xác nhận chuyển tiền đến merchant
     * 6. Gửi thông báo cho khách hàng
     *
     * @param request Request chứa OTP và transaction ID
     * @param httpServletRequest HTTP servlet request
     * @return OtpTransferConfirmResponse Kết quả xác thực OTP và nạp tiền
     * @throws BadRequestAlertException Khi giao dịch không tồn tại
     */
    @Override
    public OtpTransferConfirmResponse confirmOtpCashIn(OtpTransferConfirmRequest request,
                                                       HttpServletRequest httpServletRequest) {

        // Lấy thông tin khách hàng đang đăng nhập
        Customer customer = this.getCustomerLogin();

        // Tìm giao dịch theo transaction ID và customer ID
        Transaction transaction = this.transactionRepository
                .findByTransactionIdAndCustomerId(request.getTransactionId(), customer.getCustomerId());

        // Kiểm tra giao dịch có tồn tại không
        if (Validator.isNull(transaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }

        // Chuyển đổi Transaction entity sang DTO
        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);

        // Set thông tin T24 cho cash in transfer
        transactionDTO.setT24TransferType(this.mbApiConstantProperties.getCashInTransferApi().getTransferType());
        transactionDTO.setT24Channel(this.mbApiConstantProperties.getCashInTransferApi().getChannel());
        transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getCashInTransferApi().getServiceType());

        // Thực hiện xác nhận chuyển tiền đến merchant
        OtpTransferConfirmResponse response = this.confirmTransferToMerchant(transactionDTO, request.getOtp(), request.getDeviceId());

        // Gửi thông báo cho khách hàng
        this.transactionService.createNotification(transactionDTO);

        return response;
    }

    /**
     * Xác thực OTP và thực hiện thanh toán QR Code
     *
     * Method này xử lý xác thực OTP và thực hiện thanh toán QR Code:
     * 1. Lấy thông tin khách hàng đang đăng nhập
     * 2. Tìm giao dịch theo transaction ID
     * 3. Validate giao dịch tồn tại
     * 4. Chuẩn bị thông tin T24 cho QR payment
     * 5. Thực hiện xác nhận chuyển tiền đến merchant
     * 6. Gửi thông báo cho khách hàng
     *
     * @param request Request chứa OTP và transaction ID
     * @param httpServletRequest HTTP servlet request
     * @return OtpTransferConfirmResponse Kết quả xác thực OTP và thanh toán QR
     * @throws BadRequestAlertException Khi giao dịch không tồn tại
     */
    @Override
    public OtpTransferConfirmResponse confirmOtpQrPayment(OtpTransferConfirmRequest request,
                                                          HttpServletRequest httpServletRequest) {

        // Lấy thông tin khách hàng đang đăng nhập
        Customer customer = this.getCustomerLogin();

        // Tìm giao dịch theo transaction ID và customer ID
        Transaction transaction = this.transactionRepository
                .findByTransactionIdAndCustomerId(request.getTransactionId(), customer.getCustomerId());

        // Kiểm tra giao dịch có tồn tại không
        if (Validator.isNull(transaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }

        // Chuyển đổi Transaction entity sang DTO
        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);

        // Set thông tin T24 cho QR payment
        transactionDTO.setT24TransferType(this.mbApiConstantProperties.getQrPayment().getTransferType());
        transactionDTO.setT24Channel(this.mbApiConstantProperties.getQrPayment().getChannel());
        transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getQrPayment().getServiceType());

        // Thực hiện xác nhận chuyển tiền đến merchant
        OtpTransferConfirmResponse response = this.confirmTransferToMerchant(transactionDTO, request.getOtp(), request.getDeviceId());

        // Tạo thông báo cho khách hàng
        this.transactionService.createNotification(transactionDTO);

        return response;
    }


    /**
     * Xác thực OTP và thực hiện chuyển tiền
     *
     * Đây là method cuối cùng trong luồng chuyển tiền, thực hiện:
     * 1. Validate OTP từ khách hàng
     * 2. Kiểm tra transaction tồn tại và thuộc về khách hàng
     * 3. Validate trạng thái transaction (PENDING)
     * 4. Gọi T24 để thực hiện chuyển tiền thực tế
     * 5. Cập nhật trạng thái transaction (SUCCESS/FAILED)
     * 6. Gửi thông báo cho khách hàng
     * 7. Lưu beneficiary (nếu có yêu cầu)
     * 8. Set tài khoản mặc định (nếu có yêu cầu)
     *
     * Các loại chuyển tiền được xử lý:
     * - Chuyển tiền nội bộ (cùng MB Bank)
     * - Chuyển tiền LAPNET (liên ngân hàng)
     * - Chuyển tiền quốc tế (Cambodia, Vietnam...)
     * - Thanh toán QR Code
     *
     * Error handling:
     * - OTP sai: báo lỗi và tăng counter
     * - Insufficient funds: báo lỗi số dư không đủ
     * - Network error: retry mechanism
     * - T24 error: rollback transaction
     *
     * @param request Chứa OTP và transaction ID
     * @param httpServletRequest HTTP request để lấy thông tin bổ sung
     * @return OtpTransferConfirmResponse Kết quả chuyển tiền
     * @throws BadRequestAlertException Khi có lỗi validation hoặc chuyển tiền thất bại
     */
    @Override
    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    public OtpTransferConfirmResponse confirmOtpTransfer(OtpTransferConfirmRequest request,
                                                         HttpServletRequest httpServletRequest) {
        // Lấy thông tin khách hàng đang đăng nhập
        Customer customer = this.getCustomerLogin();

        // Tìm giao dịch theo transaction ID và customer ID
        Transaction transaction = this.transactionRepository
                .findByTransactionIdAndCustomerId(request.getTransactionId(), customer.getCustomerId());

        // Tìm thông tin thanh toán quốc tế (nếu có)
        Optional<InternationalPayment> interPayment = this.internationalPaymentRepository
                .findByTransferTransactionId(transaction.getTransferTransactionId());

        // Xác thực giao dịch (kiểm tra tồn tại, trạng thái, hạn mức)
        verifyTransaction(transaction);

        // Chuyển đổi Transaction entity sang DTO
        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);

        // Phân loại giao dịch tiền và set thông tin T24 channel
        this.setT24Channel(transaction, transactionDTO);

        // Set tài khoản nhận từ target
        transactionDTO.setBeneficiaryAccountNumber(transactionDTO.getTarget());
        MBOtpTransConfirmResponse response;
        // Xác nhận OTP dựa trên loại giao dịch
        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(transactionDTO.getBankCode())
                && !Validator.equals(transactionDTO.getTransactionCurrency(), customerProperties.getDefaultCurrency())) {
            // Chuyển khoản nội bộ ngoại tệ
            response = this.confirmFundTransferMBCurrencyInternal(transactionDTO, request.getOtp(), request.getDeviceId());
        } else {
            // Kiểm tra loại giao dịch để gọi method phù hợp
            response = Validator.equals(transaction.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT) && interPayment.isPresent() ?
                    // Thanh toán QR quốc tế
                    this.confirmFundTransferInterPayment(transactionDTO, request.getOtp(), request.getDeviceId(), interPayment.get())
                    // Chuyển tiền thông thường
                    : this.confirmFundTransferMB(transactionDTO, request.getOtp(), request.getDeviceId());
        }

        // Lấy mã tham chiếu T24 từ response
        String transactionCode = response.getT24ReferenceNumber();

        // Xử lý trường hợp giao dịch timeout
        if (Validator.equals(TransactionStatus.TIMEOUT, response.getTransactionStatus())) {
            _log.info("Transaction has been timeout, {}", transactionDTO.getTransactionId());

            // Tạo request truy vấn trạng thái giao dịch
            InquiryTransactionStatusRequest transactionStatusRequest = InquiryTransactionStatusRequest.builder()
                    .transactionId(response.getReferenceNumber())
                    .build();

            // Gọi API truy vấn trạng thái giao dịch
            InquiryTransactionStatusDTO inquiryTransactionStatusDTO = this.apiGeeTransferService
                    .inquiryTransactionStatus(transactionStatusRequest);

            // Kiểm tra kết quả truy vấn
            if (Validator.isNull(inquiryTransactionStatusDTO)
                    || Validator.isNotNull(inquiryTransactionStatusDTO.getErrorCode())) {
                throw new BadRequestAlertException(ErrorCode.MSG1023);
            } else {
                // Cập nhật transaction code từ kết quả truy vấn
                transactionCode = inquiryTransactionStatusDTO.getT24ReferenceNumber();
            }
        }

        // Xử lý trường hợp giao dịch thất bại
        if (Validator.equals(TransactionStatus.FAILED, response.getTransactionStatus())) {
            // Set trạng thái giao dịch thất bại
            transactionDTO.setTransactionStatus(TransactionStatus.FAILED);
            transactionDTO.setTransactionFinishTime(Instant.now());

            // Lưu giao dịch thất bại vào database
            Transaction trans = this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));
            // Cập nhật thông tin thanh toán quốc tế (nếu có)
            this.updateInterPayment(interPayment, this.transactionMapper.toDto(trans));

            // Throw exception với mã lỗi từ T24 (nếu có)
            if (Validator.isNotNull(response.getT24ErrorCode())) {
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            }

            // Throw exception mặc định
            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023);
        }

        // Cập nhật trạng thái giao dịch thành công
        transaction.setTransactionStatus(TransactionStatus.SUCCESS);
        transaction.setTransactionFinishTime(Instant.now());
        transaction.setTransactionCode(transactionCode);

        // Lưu giao dịch thành công vào database
        this.transactionRepository.save_(transaction);

        // Chuyển đổi sang DTO để xử lý tiếp
        TransactionDTO transDTO = this.transactionMapper.toDto(transaction);
        // Cập nhật thông tin thanh toán quốc tế (nếu có)
        this.updateInterPayment(interPayment, transDTO);

        // Gửi thông báo cho khách hàng
        this.createNotification(transDTO, customer.getCustomerId());

        // Tạo và trả về response
        return OtpTransferConfirmResponse.builder()
                .transactionTime(Instant.now()) // Thời gian giao dịch
                .isSuccess(!StringUtil.isBlank(transactionCode)) // Trạng thái thành công
                .transactionCode(transactionCode) // Mã giao dịch
                .transactionFee(transaction.getTransactionFee()) // Phí giao dịch
                .transactionId(transDTO.getTransactionId()) // ID giao dịch
                .build();
    }

    /**
     * Set thông tin T24 channel cho giao dịch
     *
     * Method này phân loại và set thông tin T24 dựa trên loại giao dịch:
     * - Nếu là chuyển tiền liên ngân hàng (LAPNET): set LAPNET config
     * - Nếu là chuyển tiền nội bộ: set internal config
     * - Nếu là QR Code nội bộ: set QR service type
     *
     * @param transaction Entity giao dịch
     * @param transactionDTO DTO giao dịch để set thông tin
     */
    private void setT24Channel(Transaction transaction, TransactionDTO transactionDTO) {
        // Kiểm tra có phải chuyển tiền nội bộ MB Bank không
        if (!Validator.equals(transaction.getBankCode(),
                this.mbApiConstantProperties.getInBanKTransfer().getChannel())) {
            // CHUYỂN TIỀN LIÊN NGÂN HÀNG (LAPNET)
            transactionDTO.setT24TransferType(this.mbApiConstantProperties.getLapnetTransfer().getTransferType());
            transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getLapnetTransfer().getServiceType());
            transactionDTO.setT24Channel(this.mbApiConstantProperties.getLapnetTransfer().getChannel());
        } else {
            // CHUYỂN TIỀN NỘI BỘ MB BANK
            transactionDTO.setT24TransferType(this.mbApiConstantProperties.getInBanKTransfer().getTransferType());
            // Phân loại chuyển khoản bằng QR Code
            if (Validator.equals(transactionDTO.getType(), TransferTransactionType.QR_CODE_INTERNAL)) {
                // QR Code nội bộ - sử dụng service type đặc biệt cho QR
                transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getInBanKTransfer().getServiceTypeQr());
            } else {
                // Chuyển tiền thông thường - sử dụng service type mặc định
                transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getInBanKTransfer().getServiceType());
            }
            transactionDTO.setT24Channel(this.mbApiConstantProperties.getInBanKTransfer().getChannel());
        }
    }

    /**
     * Cập nhật thông tin thanh toán quốc tế
     *
     * Method này cập nhật thông tin thanh toán quốc tế cho giao dịch QR Code quốc tế:
     * 1. Kiểm tra loại giao dịch có phải QR Code quốc tế không
     * 2. Validate thông tin thanh toán quốc tế tồn tại
     * 3. Copy thông tin từ transaction DTO
     * 4. Lưu cập nhật vào database
     *
     * @param interPayment Optional chứa thông tin thanh toán quốc tế
     * @param transDTO DTO giao dịch chứa thông tin cần cập nhật
     * @throws BadRequestAlertException Khi không tìm thấy thông tin thanh toán quốc tế
     */
    private void updateInterPayment(Optional<InternationalPayment> interPayment, TransactionDTO transDTO) {
        // Kiểm tra có phải giao dịch QR Code quốc tế không
        if (Validator.equals(transDTO.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)) {
            // Validate thông tin thanh toán quốc tế tồn tại
            if (!interPayment.isPresent()) {
                throw new BadRequestAlertException(ErrorCode.MSG1048);
            }

            // Chuyển đổi entity sang DTO
            InternationalPaymentDTO dto = this.internationalPaymentMapper.toDto(interPayment.get());
            // Copy thông tin từ transaction DTO sang international payment DTO
            BeanUtils.copyProperties(transDTO, dto);
            // Lưu cập nhật vào database
            this.internationalPaymentRepository.save(this.internationalPaymentMapper.toEntity(dto));
        }
    }

    /**
     * Xác thực OTP và thực hiện chuyển tiền thanh toán quốc tế
     *
     * Method này xử lý thanh toán QR Code quốc tế với các tính năng:
     * - Chuyển đổi tiền tệ theo tỷ giá
     * - Tính phí giao dịch quốc tế
     * - Xử lý thông tin quốc gia đích
     * - Hỗ trợ DOTP authentication
     *
     * @param transaction DTO giao dịch
     * @param otp Mã OTP
     * @param deviceId ID thiết bị
     * @param interPayment Thông tin thanh toán quốc tế
     * @return MBOtpTransConfirmResponse Kết quả xác thực
     */
    private MBOtpTransConfirmResponse confirmFundTransferInterPayment(TransactionDTO transaction, String otp, String deviceId, InternationalPayment interPayment) {
        // Lấy thông tin khách hàng đang đăng nhập
        Customer customer = getCustomerLogin();
        AuthenMethod authenMethod = null;

        // Lấy thông tin quốc gia đích từ loại tiền tệ
        Common common = getCommon(interPayment.getBeneficiaryCurrency());

        // Chuẩn bị thông tin tài khoản ghi nợ (tài khoản trừ tiền)
        AccountMoney debitAccount = new AccountMoney();

        debitAccount.setAccountNumber(interPayment.getCustomerAccNumber());
        debitAccount.setAccountName(customer.getFullname());
        debitAccount.setAccountType(this.mbApiConstantProperties.getInBanKTransfer().getAccountType());
        debitAccount.setAccountCurrency(interPayment.getTransactionCurrency());

        // Chuẩn bị thông tin tài khoản có (tài khoản nhận tiền)
        AccountMoney creditAccount = new AccountMoney();
        creditAccount.setAccountName(interPayment.getBeneficiaryCustomerName());
        creditAccount.setAccountType(AccountType.QR.name());
        creditAccount.setAccountCurrency(CurrencyType.LAK.name());
        creditAccount.setAccountNumber(interPayment.getQrCodeValue());

        // Chuẩn bị thông tin số tiền giao dịch
        Amount amount = new Amount();
        // Set số tiền giao dịch (đã quy đổi sang LAK)
        amount.setAmount(String.valueOf(interPayment.getTransactionAmount().longValue()));
        amount.setCurrency(this.mbApiConstantProperties.getInBanKTransfer().getCurrency());

        // Tạo thông tin thanh toán quốc tế (bao gồm tỷ giá, số tiền gốc)
        Amount.InternationalPaymentInfo internationalPaymentInfo = Amount.InternationalPaymentInfo.builder()
                .toNation(common.getValue()) // Quốc gia đích
                .originalAmount(String.valueOf(interPayment.getForeignAmount().longValue())) // Số tiền gốc (ngoại tệ)
                .exchangeRate(interPayment.getExchangeRate().toString()) // Tỷ giá quy đổi
                .exchangeCurrency(interPayment.getBeneficiaryCurrency()) // Loại tiền tệ đích
                .build();
        amount.setInternationalPaymentInfo(internationalPaymentInfo);

        // Chuẩn bị thông tin phí giao dịch
        ChargeInfo chargeInfo = new ChargeInfo();
        chargeInfo.setAmount(String.valueOf((long) transaction.getTransactionFee())); // Phí giao dịch (LAK)
        chargeInfo.setCurrency(CurrencyType.LAK.name());
        chargeInfo.setAccountNumber(transaction.getCustomerAccNumber());
        // Tạo thông tin phí thanh toán quốc tế
        ChargeInfo.InternationalPaymentFeeInfo internationalPaymentFeeInfo = ChargeInfo.InternationalPaymentFeeInfo.builder()
                .toNation(common.getValue()) // Quốc gia đích
                .originalFeeAmount(String.valueOf(interPayment.getForeignFee().longValue())) // Phí gốc (ngoại tệ)
                .exchangeRate(interPayment.getExchangeRate().toString()) // Tỷ giá quy đổi
                .exchangeCurrency(interPayment.getBeneficiaryCurrency()) // Loại tiền tệ đích
                .build();
        chargeInfo.setInternationalPaymentFeeInfo(internationalPaymentFeeInfo);

        // Chuẩn bị thông tin ngân hàng nhận
        CreditBank creditBank = new CreditBank();
        creditBank.setCode(interPayment.getPaymentSystem()); // Mã hệ thống thanh toán
        creditBank.setName(interPayment.getPaymentSystem()); // Tên hệ thống thanh toán

        // Xử lý authentication method cho DOTP (nếu sử dụng)
        if (Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP)) {
            // Tạo user ID từ CIF và số CMND/CCCD
            String userId = customer.getCif() + customer.getIdCardNumber();

            // Tìm thông tin DOTP device của khách hàng
            Optional<DOTP> dotp = this.dotpRepository
                    .findByDeviceIdAndCustomerIdAndStatus(deviceId, customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

            // Kiểm tra DOTP device có tồn tại và active không
            if (!dotp.isPresent()) {
                throw new BadRequestAlertException(ErrorCode.MSG100155);
            }

            // Tạo authentication method cho DOTP
            authenMethod = AuthenMethod.builder()
                    .otpValue(otp) // Mã OTP
                    .type(OtpConfirmType.DOTP) // Loại xác thực DOTP
                    .token(dotp.get().getToken()) // Token DOTP
                    .userId(userId) // User ID
                    .transData(MD5Generator.md5(transaction.getTransData())) // Hash transaction data
                    .deviceId(deviceId) // ID thiết bị
                    .build();
        }

        // Tạo request xác thực OTP cho thanh toán quốc tế
        OtpVerifyTransferRequest otpVerifyTransferRequest = OtpVerifyTransferRequest.builder()
                .otpValue(otp) // Mã OTP
                .requestId(transaction.getTransactionId()) // ID giao dịch
                .transferType(T24TransferType.INTERNATIONAL_PAYMENT.name()) // Loại chuyển tiền quốc tế
                .channel(Channel.LAPNET.name()) // Kênh LAPNET
                .serviceType(this.mbApiConstantProperties.getLapnetTransfer().getServiceType()) // Service type
                .remark(transaction.getMessage()) // Ghi chú giao dịch
                .branchCode(transaction.getBranchCode()) // Mã chi nhánh
                .debitAccount(debitAccount) // Tài khoản trừ tiền
                .creditAccount(creditAccount) // Tài khoản nhận tiền
                .amount(amount) // Thông tin số tiền
                .creditBank(creditBank) // Thông tin ngân hàng nhận
                .chargeInfo(chargeInfo) // Thông tin phí
                .authenMethod(authenMethod) // Phương thức xác thực
                .build();

        // Gọi service xác thực OTP và thực hiện chuyển tiền
        // Phân biệt API call dựa trên loại OTP (DOTP vs SMS)
        VerifyFundTransferDTO reqFundTransferDTO = this.consumerOtpService.verifyOtp(transaction.getTransactionId(),
                transaction.getPhoneNumber(), OtpType.TRANSFER, otpVerifyTransferRequest,
                // Nếu là DOTP: gọi verifyFundTransfer, nếu là SMS: gọi verifyFundTransferOtherCurrency
                Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP) ? this.apiGeeTransferService::verifyFundTransfer :
                        this.apiGeeTransferService::verifyFundTransferOtherCurrency,
                Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP) ? OtpConfirmType.DOTP : OtpConfirmType.SMS);

        // Tạo và trả về response
        return MBOtpTransConfirmResponse.builder()
                .t24ReferenceNumber(reqFundTransferDTO.getT24ReferenceNumber()) // Số tham chiếu T24
                .referenceNumber(reqFundTransferDTO.getReferenceNumber()) // Số tham chiếu
                .transactionStatus(reqFundTransferDTO.getTransactionStatus()) // Trạng thái giao dịch
                .transactionId(reqFundTransferDTO.getTransactionId()) // ID giao dịch
                .t24ErrorCode(reqFundTransferDTO.getT24ErrorCode()) // Mã lỗi T24 (nếu có)
                .build();
    }

    /**
     * Xác thực giao dịch trước khi thực hiện
     *
     * Method này thực hiện các bước validation quan trọng:
     * 1. Kiểm tra giao dịch có tồn tại không
     * 2. Kiểm tra giao dịch đã thành công chưa (tránh duplicate)
     * 3. Xác định số tiền cần kiểm tra hạn mức
     * 4. Kiểm tra hạn mức giao dịch của khách hàng
     *
     * @param transaction Entity giao dịch cần xác thực
     * @throws BadRequestAlertException Khi giao dịch không hợp lệ hoặc vượt hạn mức
     */
    public void verifyTransaction(Transaction transaction) {
        // Kiểm tra giao dịch có tồn tại không
        if (Validator.isNull(transaction)) {
            throw new BadRequestAlertException(ErrorCode.MSG1048);
        }

        // Kiểm tra giao dịch đã thành công chưa (tránh thực hiện lại)
        if (Validator.equals(transaction.getTransactionStatus(), TransactionStatus.SUCCESS)) {
            throw new BadRequestAlertException(ErrorCode.MSG1104);
        }

        // Xác định số tiền cần kiểm tra hạn mức
        // Với giao dịch QR quốc tế: sử dụng foreign amount
        // Với giao dịch khác: sử dụng transaction amount
        double amount = Validator.equals(transaction.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)
                ? transaction.getForeignAmount() : transaction.getTransactionAmount();

        // Kiểm tra hạn mức giao dịch của khách hàng
        this.transactionService.checkTransactionLimit(String.valueOf(transaction.getTransferType()), transaction.getCustomerId(),
                transaction.getCustomerAccNumber(), amount, transaction.getType(), transaction);
    }

    /**
     * Xử lý rút tiền mặt (Cashout) cho client
     *
     * Method này xử lý rút tiền từ tài khoản client (Umoney) sang tài khoản khách hàng:
     * 1. Verify thông tin tài khoản nhận
     * 2. Xác định tài khoản nguồn (cashout account)
     * 3. Phân loại loại chuyển tiền (nội bộ vs liên ngân hàng)
     * 4. Thực hiện giao dịch qua T24
     * 5. Cập nhật số dư tài khoản cashout
     * 6. Lưu log giao dịch client
     *
     * @param request HTTP servlet request
     * @param cashoutRequest Thông tin rút tiền
     * @return CashoutResponse Kết quả rút tiền
     * @throws BadRequestAlertException Khi có lỗi validation hoặc giao dịch thất bại
     */
    @Override
    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    public CashoutResponse cashout(HttpServletRequest request, CashoutRequest cashoutRequest) {
        // Lấy request call verify T24 để kiểm tra tên tài khoản khớp với dữ liệu truyền vào
        BeneficiaryConfirmRequest beneficiaryConfirmRequest = new BeneficiaryConfirmRequest();
        beneficiaryConfirmRequest.setBeneficiaryAccountNumber(cashoutRequest.getCreditAccount().getAccountNumber());
        // Phân biệt loại tài khoản: QR Code hoặc Account Number
        if (Validator.equals(cashoutRequest.getCreditAccount().getAccountType(), AccountType.QR.name())) {
            beneficiaryConfirmRequest.setType(AccountType.QR);
        } else {
            beneficiaryConfirmRequest.setType(AccountType.ACCOUNT);
        }
        beneficiaryConfirmRequest.setBankCode(cashoutRequest.getCreditBank());
        // Gọi API verify thông tin người nhận
        BeneficiaryInfoResponse beneficiaryInfoResponse = this.clientConfirmBeneficiary(beneficiaryConfirmRequest);
        // Kiểm tra tên tài khoản có khớp không
        if (!Validator.equals(StringUtil.trim(beneficiaryInfoResponse.getBeneficiaryName()), StringUtil.trim(cashoutRequest.getCreditAccount().getAccountName()))) {
            throw new BadRequestAlertException(ErrorCode.MSG101032);
        }

        // Lấy tên client từ security context
        String clientName = SecurityContextHolder.getContext().getAuthentication().getName();
        // Tạo request chuyển tiền cashout
        CashoutTransferRequest cashoutTransferRequest = new CashoutTransferRequest();
        cashoutTransferRequest.setServiceType(this.mbApiConstantProperties.getCashout().getServiceType());
        cashoutTransferRequest.setTransferType(this.mbApiConstantProperties.getCashout().getTransferType());
        // Tài khoản nguồn (sẽ được set sau)
        AccountMoney debitAccount = new AccountMoney();
        // Entity lưu thông tin giao dịch client
        TransactionClient transactionClient = new TransactionClient();

        // Khởi tạo biến cho cashout account
        CashoutAccount cashoutAccount = null;
        List<CashoutAccount> cashoutAccounts = null;

        // Xử lý cho client Umoney
        if (clientName.equals("umoney")) {
            // Lấy danh sách tài khoản cashout active (không bị xóa)
            cashoutAccounts = this.cashoutAccountRepository.findAllByStatusIsNotOrderByIdDesc(EntityStatus.DELETED.getStatus());

            // Chọn tài khoản cashout phù hợp (manual hoặc auto)
            cashoutAccount = this.getCashoutAccountManualOrAuto(cashoutRequest, cashoutAccounts);
            // Set thông tin tài khoản nguồn từ cashout account
            debitAccount.setAccountNumber(cashoutAccount.getAccountNumber());
            debitAccount.setAccountName(cashoutAccount.getAccountName());
            debitAccount.setAccountType(cashoutAccount.getAccountType());
            debitAccount.setAccountCurrency(cashoutAccount.getAccountCurrency());

            // Loại bỏ account đã chọn khỏi danh sách (để xử lý sau)
            cashoutAccounts.remove(cashoutAccount);
        }
        // Phân loại loại chuyển tiền dựa trên mã ngân hàng đích
        if (this.mbApiConstantProperties.getCashout().getMbCodes().contains(cashoutRequest.getCreditBank())) {
            // CHUYỂN TIỀN NỘI BỘ MB BANK
            if (cashoutRequest.getCreditAccount().getAccountType().equals(AccountType.ACCOUNT.name())) {
                // Chuyển tiền nội bộ bằng số tài khoản
                transactionClient.setType(TransferTransactionType.INTERNAL_BANK);
            } else {
                // Chuyển tiền nội bộ bằng QR Code
                transactionClient.setType(TransferTransactionType.QR_CODE_INTERNAL);
                transactionClient.setQrCodeValue(cashoutRequest.getCreditAccount().getAccountNumber());
            }
            // Set channel cho client
            if (clientName.equals("umoney")) {
                cashoutTransferRequest.setChannel("UMONEY");
            } else {
                // Chỉ hỗ trợ Umoney client
                throw new BadRequestAlertException(ErrorCode.MSG1005);
            }
        } else {
            // CHUYỂN TIỀN LIÊN NGÂN HÀNG - qua LAPNET
            cashoutTransferRequest.setChannel(this.mbApiConstantProperties.getCashout().getChannelLapnet());
            if (cashoutRequest.getCreditAccount().getAccountType().equals(AccountType.ACCOUNT.name())) {
                // Chuyển tiền liên ngân hàng bằng số tài khoản
                transactionClient.setType(TransferTransactionType.INTER_BANK);
            } else {
                // Chuyển tiền liên ngân hàng bằng QR Code
                transactionClient.setType(TransferTransactionType.QR_CODE_LAPNET_TRANSFER);
                transactionClient.setQrCodeValue(cashoutRequest.getCreditAccount().getAccountNumber());
            }
        }
        // Chuẩn bị thông tin request chuyển tiền
        cashoutTransferRequest.setAmount(cashoutRequest.getAmount()); // Số tiền
        cashoutTransferRequest.setDebitAccount(debitAccount); // Tài khoản nguồn
        cashoutTransferRequest.setCreditAccount(cashoutRequest.getCreditAccount()); // Tài khoản đích
        cashoutTransferRequest.setBranchCode(this.customerProperties.getDefaultBranchCode()); // Mã chi nhánh
        cashoutTransferRequest.setCreditBank(new CreditBank(cashoutRequest.getCreditBank())); // Ngân hàng đích
        cashoutTransferRequest.setChargeInfo(cashoutRequest.getChargeInfo()); // Thông tin phí
        cashoutTransferRequest.setRemark(cashoutRequest.getRemark()); // Ghi chú

        // Chuẩn bị thông tin transaction client để lưu log
        transactionClient.setTransferType(TransferType.CASH_OUT); // Loại giao dịch: rút tiền
        transactionClient.setClientName(clientName); // Tên client
        transactionClient.setClientAccountName(debitAccount.getAccountName()); // Tên tài khoản client
        transactionClient.setClientAccountNumber(debitAccount.getAccountNumber()); // Số tài khoản client
        transactionClient.setTarget(cashoutTransferRequest.getCreditAccount().getAccountNumber()); // Tài khoản đích
        transactionClient.setTransactionStartTime(Instant.now()); // Thời gian bắt đầu
        transactionClient.setTransactionFinishTime(Instant.now()); // Thời gian kết thúc
        // Tính phí giao dịch (nếu có)
        transactionClient.setTransactionFee(cashoutTransferRequest.getChargeInfo() != null ? Double.parseDouble(cashoutTransferRequest.getChargeInfo().getAmount()) : 0);
        transactionClient.setTransactionAmount(Double.parseDouble(cashoutTransferRequest.getAmount().getAmount())); // Số tiền giao dịch
        transactionClient.setTransactionCurrency(cashoutTransferRequest.getAmount().getCurrency()); // Loại tiền tệ
        transactionClient.setBranchCode(cashoutTransferRequest.getBranchCode()); // Mã chi nhánh
        transactionClient.setBankCode(cashoutTransferRequest.getCreditBank().getCode()); // Mã ngân hàng đích
        transactionClient.setBeneficiaryAccountNumber(cashoutTransferRequest.getCreditAccount().getAccountNumber()); // Số tài khoản người nhận
        transactionClient.setBeneficiaryCustomerName(cashoutTransferRequest.getCreditAccount().getAccountName()); // Tên người nhận
        transactionClient.setMessage(cashoutTransferRequest.getRemark()); // Ghi chú
        transactionClient.setClientMessageId(request.getHeader(SecurityConstants.Header.CLIENT_MESSAGE_ID)); // Client message ID
        // Set API Gee transaction ID
        if (Validator.isNotNull(beneficiaryInfoResponse) && Validator.isNotNull(beneficiaryInfoResponse.getReferenceNumber())) {
            // Sử dụng reference number từ beneficiary response
            cashoutTransferRequest.setApiGeeTransactionId(beneficiaryInfoResponse.getReferenceNumber());
        } else {
            // Sử dụng transaction ID từ header
            cashoutTransferRequest.setApiGeeTransactionId(request.getHeader(ApiConstants.HttpHeaders.TRANSACTION_ID));
        }

        // Thực hiện giao dịch cashout qua API Gee Transfer Service
        VerifyFundTransferDTO response = this.apiGeeTransferService.cashout(cashoutTransferRequest);
        // Set trạng thái giao dịch từ response
        transactionClient.setTransactionStatus(response.getTransactionStatus());

        // Xử lý trường hợp giao dịch thất bại
        if (!response.getTransactionStatus().equals(TransactionStatus.SUCCESS)) {
            if (response.getT24ErrorCode() != null) {
                // Có mã lỗi T24 cụ thể
                transactionClient.setTransactionId(response.getTransactionId());
                transactionClient.setFailureCause(response.getT24ErrorCode().getErrorCode().getKey());
                transactionClientRepository.save(transactionClient);
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            } else {
                // Lỗi chung
                transactionClient.setTransactionId(response.getTransactionId());
                transactionClientRepository.save(transactionClient);
                throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023);
            }
        }

        // Giao dịch thành công - lưu thông tin
        transactionClient.setTransactionId(response.getReferenceNumber()); // Reference number
        transactionClient.setTransactionCode(response.getT24ReferenceNumber()); // T24 reference number
        transactionClientRepository.save(transactionClient); // Lưu vào database

        // Cập nhật thông tin cashout account (nếu có)
        if (Validator.isNotNull(cashoutAccount)) {
            // Cộng số tiền đã chuyển vào tổng amount transferred
            this.cashoutAccountRepository.addTransferedAmount(Long.parseLong(cashoutRequest.getAmount().getAmount()), cashoutAccount.getId());

            // Xử lý cho loại cấu hình AUTO
            if (Validator.equals(cashoutAccount.getConfigurationType(), UmoneyConfigurationType.AUTO.name())) {
                // Inactive tất cả các account khác
                cashoutAccounts.forEach(item -> item.setStatus(EntityStatus.INACTIVE.getStatus()));
                // Active account hiện tại
                cashoutAccount.setStatus(EntityStatus.ACTIVE.getStatus());
            }
        }

        // Tạo và trả về response
        return CashoutResponse.builder()
                .referenceNumber(response.getReferenceNumber()) // Số tham chiếu
                .t24ReferenceNumber(response.getT24ReferenceNumber()) // Số tham chiếu T24
                .transactionStatus(response.getTransactionStatus()) // Trạng thái giao dịch
                .transactionId(transactionClient.getTransactionId()) // ID giao dịch client
                .build();
    }

    /**
     * Xử lý vay tiền (Debit Money) cho client
     *
     * Method này xử lý vay tiền từ tài khoản debit sang tài khoản khách hàng:
     * 1. Validate thông tin client và debit account
     * 2. Kiểm tra hạn mức giao dịch
     * 3. Validate mã giao dịch hoạch toán (description)
     * 4. Thực hiện giao dịch vay tiền qua T24
     * 5. Cập nhật số dư tài khoản debit
     * 6. Lưu log giao dịch client
     *
     * @param request HTTP servlet request
     * @param debitMoneyRequest Thông tin vay tiền
     * @return DebitMoneyResponse Kết quả vay tiền
     * @throws BadRequestAlertException Khi có lỗi validation hoặc giao dịch thất bại
     */
    // @TODO MBLAOS23P2-4058: Chuyển tiền Umoney sang Lapnet
    @Override
    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    public DebitMoneyResponse debitMoney(HttpServletRequest request, DebitMoneyRequest debitMoneyRequest) {
        // Lấy tên client từ security context
        String clientName = SecurityContextHolder.getContext().getAuthentication().getName();
        // Validate thông tin request
        this.validateDebitTransferUmoney(debitMoneyRequest);

        // Khởi tạo biến debit account
        Optional<DebitAccount> debitAccount = null;

        // Xử lý cho client Umoney
        if (clientName.equals("umoney")) {
            // Tìm debit account active theo số tài khoản
            debitAccount = this.debitAccountRepository
                    .findByAccountNumberAndStatus(debitMoneyRequest.getDebitAccount().getAccountNumber(), EntityStatus.ACTIVE.getStatus());
        }

        // Validate mã giao dịch hoạch toán (bắt buộc phải có description)
        if (Validator.isNull(debitMoneyRequest.getChargeInfo().getDescription())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DESCRIPTION_IS_REQUIRED,
                            new Object[]{}),
                    TransactionClient.class.getSimpleName(), LabelKey.ERROR_DESCRIPTION_IS_REQUIRED);
        }

        // Validate debit account
        if (!debitAccount.isPresent()) {
            // Tài khoản debit không tồn tại
            throw new BadRequestAlertException(ErrorCode.MSG101276);
        } else if (!Validator.equals(debitAccount.get().getAccountName(), debitMoneyRequest.getDebitAccount().getAccountName())) {
            // Tên tài khoản không khớp
            throw new BadRequestAlertException(ErrorCode.MSG101032);
        } else if (Double.parseDouble(debitMoneyRequest.getAmount().getAmount()) > debitAccount.get().getTransactionLimit()) {
            // Vượt hạn mức giao dịch
            throw new BadRequestAlertException(ErrorCode.MSG1106);
        }

        // Chuẩn bị thông tin transaction client để lưu log
        TransactionClient transactionClient = new TransactionClient();
        transactionClient.setTransferType(TransferType.DEBIT); // Loại giao dịch: vay tiền
        transactionClient.setBankCode(debitMoneyRequest.getCreditBank().getCode()); // Mã ngân hàng đích
        transactionClient.setBankName(debitMoneyRequest.getCreditBank().getName()); // Tên ngân hàng đích
        transactionClient.setMessage(debitMoneyRequest.getRemark()); // Ghi chú
        transactionClient.setClientName(clientName); // Tên client
        transactionClient.setClientAccountName(debitMoneyRequest.getCreditAccount().getAccountName()); // Tên tài khoản client
        transactionClient.setClientAccountNumber(debitMoneyRequest.getCreditAccount().getAccountNumber()); // Số tài khoản client
        transactionClient.setTarget(debitMoneyRequest.getCreditAccount().getAccountNumber()); // Tài khoản đích

        // Thông tin tài khoản người thụ hưởng (debit account)
        transactionClient.setBeneficiaryAccountNumber(debitMoneyRequest.getDebitAccount().getAccountNumber());
        transactionClient.setBeneficiaryCustomerName(debitMoneyRequest.getDebitAccount().getAccountName());

        // Thông tin thời gian và số tiền
        transactionClient.setTransactionStartTime(Instant.now()); // Thời gian bắt đầu
        transactionClient.setTransactionFinishTime(Instant.now()); // Thời gian kết thúc
        // Tính phí giao dịch (nếu có)
        transactionClient.setTransactionFee(debitMoneyRequest.getChargeInfo() != null ? Double.parseDouble(debitMoneyRequest.getChargeInfo().getAmount()) : 0);
        transactionClient.setTransactionAmount(Double.parseDouble(debitMoneyRequest.getAmount().getAmount())); // Số tiền giao dịch
        transactionClient.setTransactionCurrency(debitMoneyRequest.getAmount().getCurrency()); // Loại tiền tệ
        transactionClient.setBranchCode(this.customerProperties.getDefaultBranchCode()); // Mã chi nhánh
        transactionClient.setDescription(debitMoneyRequest.getChargeInfo().getDescription()); // Mô tả giao dịch hoạch toán

        // Thông tin bổ sung từ request
        transactionClient.setClientMessageId(request.getHeader(SecurityConstants.Header.CLIENT_MESSAGE_ID)); // Client message ID
        transactionClient.setFromMember(debitMoneyRequest.getFromMember()); // Từ member
        transactionClient.setFromUser(debitMoneyRequest.getFromUser()); // Từ user
        transactionClient.setFromUserFullName(debitMoneyRequest.getFromUserFullName()); // Tên đầy đủ user
        transactionClient.setFromAccount(debitMoneyRequest.getFromAccount()); // Từ tài khoản

        // Phân loại loại giao dịch dựa trên account type
        if (debitMoneyRequest.getCreditAccount().getAccountType().equals(AccountType.ACCOUNT.name())) {
            // Chuyển tiền nội bộ bằng số tài khoản
            transactionClient.setType(TransferTransactionType.INTERNAL_BANK);
        } else {
            // Chuyển tiền nội bộ bằng QR Code
            transactionClient.setType(TransferTransactionType.QR_CODE_INTERNAL);
            transactionClient.setQrCodeValue(debitMoneyRequest.getCreditAccount().getAccountNumber());
        }

        // Chuẩn bị request cho API debit money
        DebitMoneyTransferRequest debitMoneyTransferRequest = new DebitMoneyTransferRequest();
        debitMoneyTransferRequest.setAmount(debitMoneyRequest.getAmount()); // Số tiền
        debitMoneyTransferRequest.setChargeInfo(debitMoneyRequest.getChargeInfo()); // Thông tin phí
        debitMoneyTransferRequest.setCreditBank(debitMoneyRequest.getCreditBank()); // Ngân hàng đích
        debitMoneyTransferRequest.setCreditAccount(debitMoneyRequest.getCreditAccount()); // Tài khoản đích
        debitMoneyTransferRequest.setDebitAccount(debitMoneyRequest.getDebitAccount()); // Tài khoản nguồn
        debitMoneyTransferRequest.setRemark(debitMoneyRequest.getRemark()); // Ghi chú
        debitMoneyTransferRequest.setBranchCode(this.customerProperties.getDefaultBranchCode()); // Mã chi nhánh
        debitMoneyTransferRequest.setServiceType(this.mbApiConstantProperties.getDepositDebit().getServiceTypeDebit()); // Service type cho debit
        debitMoneyTransferRequest.setChannel(this.mbApiConstantProperties.getDepositDebit().getChannel()); // Channel
        debitMoneyTransferRequest.setTransferType(this.mbApiConstantProperties.getDepositDebit().getTransferType()); // Transfer type
        debitMoneyTransferRequest.setApiGeeTransactionId(request.getHeader(ApiConstants.HttpHeaders.TRANSACTION_ID)); // Transaction ID
        // Thực hiện giao dịch debit money qua API Gee Transfer Service
        VerifyFundTransferDTO response = this.apiGeeTransferService.debitMoney(debitMoneyTransferRequest);
        // Set trạng thái giao dịch từ response
        transactionClient.setTransactionStatus(response.getTransactionStatus());

        // Xử lý trường hợp giao dịch thất bại
        if (!response.getTransactionStatus().equals(TransactionStatus.SUCCESS)) {
            if (response.getT24ErrorCode() != null) {
                // Có mã lỗi T24 cụ thể
                transactionClient.setTransactionId(response.getTransactionId());
                transactionClient.setFailureCause(response.getT24ErrorCode().getErrorCode().getKey());
                transactionClientRepository.save(transactionClient);
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            } else {
                // Lỗi chung
                transactionClient.setTransactionId(response.getTransactionId());
                transactionClientRepository.save(transactionClient);
                throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023);
            }
        }

        // Giao dịch thành công - lưu thông tin
        transactionClient.setTransactionId(response.getReferenceNumber()); // Reference number
        transactionClient.setTransactionCode(response.getT24ReferenceNumber()); // T24 reference number
        transactionClientRepository.save(transactionClient); // Lưu vào database

        // Cập nhật số dư tài khoản debit (nếu có)
        if (debitAccount.isPresent()) {
            // Cộng số tiền đã chuyển vào tổng amount transferred
            this.debitAccountRepository.addTransferedAmount(Long.parseLong(debitMoneyRequest.getAmount().getAmount()), debitAccount.get().getId());
        }

        // Tạo và trả về response
        return DebitMoneyResponse.builder()
                .referenceNumber(response.getReferenceNumber()) // Số tham chiếu
                .t24ReferenceNumber(response.getT24ReferenceNumber()) // Số tham chiếu T24
                .transactionStatus(response.getTransactionStatus()) // Trạng thái giao dịch
                .transactionId(transactionClient.getTransactionId()) // ID giao dịch client
                .build();
    }

    /**
     * Xử lý gửi tiền tiết kiệm (Deposit Money) cho client
     *
     * Method này xử lý gửi tiền từ tài khoản khách hàng vào tài khoản tiết kiệm:
     * 1. Validate thông tin client và debit account
     * 2. Kiểm tra hạn mức giao dịch
     * 3. Thực hiện giao dịch gửi tiền qua T24
     * 4. Cập nhật số dư tài khoản debit
     * 5. Lưu log giao dịch client
     *
     * @param request HTTP servlet request
     * @param depositMoneyRequest Thông tin gửi tiền
     * @return DepositMoneyResponse Kết quả gửi tiền
     * @throws BadRequestAlertException Khi có lỗi validation hoặc giao dịch thất bại
     */
    @Override
    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    public DepositMoneyResponse depositMoney(HttpServletRequest request, DepositMoneyRequest depositMoneyRequest) {
        // Lấy tên client từ security context
        String clientName = SecurityContextHolder.getContext().getAuthentication().getName();
        // Validate thông tin request
        this.validateDepositTransferUmoney(depositMoneyRequest);

        // Khởi tạo biến debit account
        Optional<DebitAccount> debitAccount = null;
        // Xử lý cho client Umoney
        if (clientName.equals("umoney")) {
            // Tìm debit account active theo số tài khoản credit (tài khoản tiết kiệm)
            debitAccount = this.debitAccountRepository.findByAccountNumberAndStatus(
                    depositMoneyRequest.getCreditAccount().getAccountNumber(), EntityStatus.ACTIVE.getStatus());
        }

        // Validate debit account
        if (!debitAccount.isPresent()) {
            // Tài khoản debit không tồn tại
            throw new BadRequestAlertException(ErrorCode.MSG101276);
        } else if (!Validator.equals(debitAccount.get().getAccountName(), depositMoneyRequest.getCreditAccount().getAccountName())) {
            // Tên tài khoản không khớp
            throw new BadRequestAlertException(ErrorCode.MSG101032);
        } else if (Double.parseDouble(depositMoneyRequest.getAmount().getAmount()) > debitAccount.get().getTransactionLimit()) {
            // Vượt hạn mức giao dịch
            throw new BadRequestAlertException(ErrorCode.MSG1106);
        }

        // Chuẩn bị thông tin transaction client để lưu log
        TransactionClient transactionClient = new TransactionClient();
        transactionClient.setTransferType(TransferType.DEPOSIT); // Loại giao dịch: gửi tiền
        transactionClient.setBankCode(depositMoneyRequest.getCreditBank().getCode()); // Mã ngân hàng đích
        transactionClient.setBankName(depositMoneyRequest.getCreditBank().getName()); // Tên ngân hàng đích
        transactionClient.setMessage(depositMoneyRequest.getRemark()); // Ghi chú

        // Thông tin client
        transactionClient.setClientName(clientName); // Tên client
        transactionClient.setClientAccountName(depositMoneyRequest.getDebitAccount().getAccountName()); // Tên tài khoản client
        transactionClient.setClientAccountNumber(depositMoneyRequest.getDebitAccount().getAccountNumber()); // Số tài khoản client
        transactionClient.setTarget(depositMoneyRequest.getDebitAccount().getAccountNumber()); // Tài khoản đích

        // Thông tin tài khoản người thụ hưởng (credit account - tài khoản tiết kiệm)
        transactionClient.setBeneficiaryAccountNumber(depositMoneyRequest.getCreditAccount().getAccountNumber());
        transactionClient.setBeneficiaryCustomerName(depositMoneyRequest.getCreditAccount().getAccountName());

        // Thông tin thời gian và số tiền
        transactionClient.setTransactionStartTime(Instant.now()); // Thời gian bắt đầu
        transactionClient.setTransactionFinishTime(Instant.now()); // Thời gian kết thúc
        // Tính phí giao dịch (nếu có)
        transactionClient.setTransactionFee(depositMoneyRequest.getChargeInfo() != null ? Double.parseDouble(depositMoneyRequest.getChargeInfo().getAmount()) : 0);
        transactionClient.setTransactionAmount(Double.parseDouble(depositMoneyRequest.getAmount().getAmount())); // Số tiền giao dịch
        transactionClient.setTransactionCurrency(depositMoneyRequest.getAmount().getCurrency()); // Loại tiền tệ
        transactionClient.setBranchCode(this.customerProperties.getDefaultBranchCode()); // Mã chi nhánh
        transactionClient.setDescription(depositMoneyRequest.getChargeInfo().getDescription()); // Mô tả giao dịch

        // Thông tin bổ sung từ request
        transactionClient.setFromMember(depositMoneyRequest.getFromMember()); // Từ member
        transactionClient.setFromUser(depositMoneyRequest.getFromUser()); // Từ user
        transactionClient.setFromUserFullName(depositMoneyRequest.getFromUserFullName()); // Tên đầy đủ user
        transactionClient.setFromAccount(depositMoneyRequest.getFromAccount()); // Từ tài khoản
        transactionClient.setClientMessageId(request.getHeader(SecurityConstants.Header.CLIENT_MESSAGE_ID)); // Client message ID

        // Phân loại loại giao dịch dựa trên account type
        if (depositMoneyRequest.getCreditAccount().getAccountType().equals(AccountType.ACCOUNT.name())) {
            // Gửi tiền nội bộ bằng số tài khoản
            transactionClient.setType(TransferTransactionType.INTERNAL_BANK);
        } else {
            // Gửi tiền nội bộ bằng QR Code
            transactionClient.setType(TransferTransactionType.QR_CODE_INTERNAL);
            transactionClient.setQrCodeValue(depositMoneyRequest.getCreditAccount().getAccountNumber());
        }

        // Chuẩn bị request cho API deposit money
        DepositMoneyTransferRequest depositMoneyTransferRequest = new DepositMoneyTransferRequest();
        depositMoneyTransferRequest.setAmount(depositMoneyRequest.getAmount()); // Số tiền
        depositMoneyTransferRequest.setChargeInfo(depositMoneyRequest.getChargeInfo()); // Thông tin phí
        depositMoneyTransferRequest.setCreditBank(depositMoneyRequest.getCreditBank()); // Ngân hàng đích
        depositMoneyTransferRequest.setCreditAccount(depositMoneyRequest.getCreditAccount()); // Tài khoản đích (tiết kiệm)
        depositMoneyTransferRequest.setDebitAccount(depositMoneyRequest.getDebitAccount()); // Tài khoản nguồn
        depositMoneyTransferRequest.setRemark(depositMoneyRequest.getRemark()); // Ghi chú
        depositMoneyTransferRequest.setBranchCode(this.customerProperties.getDefaultBranchCode()); // Mã chi nhánh
        depositMoneyTransferRequest.setServiceType(this.mbApiConstantProperties.getDepositDebit().getServiceTypeDeposit()); // Service type cho deposit
        depositMoneyTransferRequest.setChannel(this.mbApiConstantProperties.getDepositDebit().getChannel()); // Channel
        depositMoneyTransferRequest.setTransferType(this.mbApiConstantProperties.getDepositDebit().getTransferType()); // Transfer type
        depositMoneyTransferRequest.setApiGeeTransactionId(request.getHeader(ApiConstants.HttpHeaders.TRANSACTION_ID)); // Transaction ID

        // Thực hiện giao dịch deposit money qua API Gee Transfer Service
        VerifyFundTransferDTO response = this.apiGeeTransferService.depositMoney(depositMoneyTransferRequest);
        // Set trạng thái giao dịch từ response
        transactionClient.setTransactionStatus(response.getTransactionStatus());

        // Xử lý trường hợp giao dịch thất bại
        if (!response.getTransactionStatus().equals(TransactionStatus.SUCCESS)) {
            if (response.getT24ErrorCode() != null) {
                // Có mã lỗi T24 cụ thể
                transactionClient.setTransactionId(response.getTransactionId());
                transactionClient.setFailureCause(response.getT24ErrorCode().getErrorCode().getKey());
                transactionClientRepository.save(transactionClient);
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            } else {
                // Lỗi chung
                transactionClient.setTransactionId(response.getTransactionId());
                transactionClientRepository.save(transactionClient);
                throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023);
            }
        }

        // Giao dịch thành công - lưu thông tin
        transactionClient.setTransactionId(response.getReferenceNumber()); // Reference number
        transactionClient.setTransactionCode(response.getT24ReferenceNumber()); // T24 reference number
        transactionClientRepository.save(transactionClient); // Lưu vào database

        // Cập nhật số dư tài khoản debit (nếu có) - cộng thêm số tiền đã gửi
        if (debitAccount.isPresent()) {
            // Cộng số tiền đã gửi vào tổng amount (khác với debit là trừ)
            this.debitAccountRepository.addedAmount(Long.parseLong(depositMoneyRequest.getAmount().getAmount()), debitAccount.get().getId());
        }

        // Tạo và trả về response
        return DepositMoneyResponse.builder()
                .referenceNumber(response.getReferenceNumber()) // Số tham chiếu
                .t24ReferenceNumber(response.getT24ReferenceNumber()) // Số tham chiếu T24
                .transactionStatus(response.getTransactionStatus()) // Trạng thái giao dịch
                .transactionId(transactionClient.getTransactionId()) // ID giao dịch client
                .build();
    }

    /**
     * Chọn tài khoản cashout phù hợp (Manual hoặc Auto)
     *
     * Method này xử lý logic chọn tài khoản cashout dựa trên cấu hình:
     *
     * MANUAL MODE:
     * - Sử dụng tài khoản active đầu tiên
     * - Kiểm tra hạn mức giao dịch
     * - Admin tự quản lý số dư
     *
     * AUTO MODE:
     * - Tự động chọn tài khoản có đủ số dư
     * - Kiểm tra số dư thực tế từ T24
     * - Load balancing giữa các tài khoản
     * - Tự động switch khi hết số dư
     *
     * @param cashoutRequest Request chứa thông tin rút tiền
     * @param cashoutAccounts Danh sách tài khoản cashout available
     * @return CashoutAccount Tài khoản được chọn để thực hiện giao dịch
     * @throws BadRequestAlertException Khi không có tài khoản phù hợp hoặc không đủ số dư
     */
    private CashoutAccount getCashoutAccountManualOrAuto(CashoutRequest cashoutRequest, List<CashoutAccount> cashoutAccounts) {

        // Validate số tiền giao dịch
        if (Validator.isNull(cashoutRequest.getAmount().getAmount())) {
            throw new BadRequestAlertException(ErrorCode.MSG1138);
        }

        // Tìm tài khoản cashout active đầu tiên
        CashoutAccount cashoutAccount = cashoutAccounts.stream()
                .filter(item -> Validator.equals(EntityStatus.ACTIVE.getStatus(), item.getStatus()))
                .findFirst()
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101054));

        // Kiểm tra loại cấu hình: AUTO hay MANUAL
        if (Validator.equals(UmoneyConfigurationType.AUTO.name(), cashoutAccount.getConfigurationType())) {
            // ==================== AUTO MODE ====================
            // Tự động chọn tài khoản có đủ số dư và chưa vượt hạn mức

            // Lọc các tài khoản chưa vượt hạn mức giao dịch
            // Điều kiện: amountTransferred + amount <= transactionLimit
            cashoutAccounts = cashoutAccounts.stream()
                    .filter(item -> item.getAmountTransferred() + Long.parseLong(cashoutRequest.getAmount().getAmount()) <= item.getTransactionLimit())
                    .sorted((i1, i2) -> i2.getStatus() - i1.getStatus()) // Sắp xếp theo status (active trước)
                    .collect(Collectors.toList());

            // Lấy danh sách số tài khoản để kiểm tra số dư
            List<String> cashoutAccountNumbers = cashoutAccounts.stream()
                    .map(CashoutAccount::getAccountNumber)
                    .collect(Collectors.toList());

            // Lấy danh sách CIF để gọi API kiểm tra số dư (loại bỏ null)
            List<String> cifCashoutAccounts = cashoutAccounts.stream()
                    .map(CashoutAccount::getCif)
                    .filter(Validator::isNotNull)
                    .collect(Collectors.toList());

            // Set để track các CIF đã kiểm tra (tránh duplicate check)
            Set<String> usedCif = new HashSet<>();

            // Flag để đánh dấu đã tìm được tài khoản có đủ số dư
            boolean checkAvailableBalance = false;

            // Duyệt qua từng CIF để kiểm tra số dư tài khoản
            for (String cif : cifCashoutAccounts) {
                // Skip nếu CIF đã được kiểm tra rồi
                if (usedCif.contains(cif)) {
                    continue;
                }
                // Đánh dấu CIF đã kiểm tra
                usedCif.add(cif);

                // Tạo request kiểm tra số dư tài khoản theo CIF
                AccountBalanceRequest accountBalanceRequest = AccountBalanceRequest.builder()
                        .custCode(cif)
                        .build();

                // Gọi API T24 để lấy số dư tất cả tài khoản của CIF
                List<AccountBalanceDTO> accountBalanceDTOList = this.apiGeeTransferService
                        .checkAccountBalance(accountBalanceRequest);

                // Validate response từ T24
                if (Validator.isNull(accountBalanceDTOList)) {
                    throw new BadRequestAlertException(ErrorCode.MSG100029);
                }

                // Lọc chỉ lấy các tài khoản cashout (trong danh sách cashoutAccountNumbers)
                accountBalanceDTOList = accountBalanceDTOList.stream()
                        .filter(item -> cashoutAccountNumbers.contains(item.getAccountNumber()))
                        .collect(Collectors.toList());

                // Duyệt qua từng tài khoản cashout để tìm tài khoản có đủ số dư
                for (String accountNumber : cashoutAccountNumbers) {
                    // Tìm thông tin số dư của tài khoản hiện tại
                    Optional<AccountBalanceDTO> accountBalanceDTOOptional = accountBalanceDTOList.stream()
                            .filter(a -> a.getAccountNumber().equals(accountNumber))
                            .findFirst();

                    if (accountBalanceDTOOptional.isPresent()) {
                        AccountBalanceDTO accountBalanceDTO = accountBalanceDTOOptional.get();
                        // Kiểm tra điều kiện số dư:
                        // 1. Available balance không null
                        // 2. Available balance > 0
                        // 3. Available balance >= số tiền cần rút
                        if (Validator.isNotNull(accountBalanceDTO.getAvailableBalance())
                                && Long.parseLong(accountBalanceDTO.getAvailableBalance()) > 0
                                && Long.parseLong(accountBalanceDTO.getAvailableBalance()) >= Long.parseLong(cashoutRequest.getAmount().getAmount())) {
                            // Tìm được tài khoản phù hợp - set làm cashout account
                            cashoutAccount = cashoutAccounts.stream()
                                    .filter(item -> Validator.equals(item.getAccountNumber(), accountBalanceDTO.getAccountNumber()))
                                    .findFirst().get();
                            checkAvailableBalance = true;
                            break; // Thoát khỏi vòng lặp tài khoản
                        }
                    }
                }

                // Nếu đã tìm được tài khoản phù hợp thì thoát khỏi vòng lặp CIF
                if (checkAvailableBalance) {
                    break;
                }
            }

            // Kiểm tra kết quả tìm kiếm tài khoản
            if (!checkAvailableBalance) {
                // Không tìm được tài khoản nào có đủ số dư
                throw new BadRequestAlertException(ErrorCode.MSG101096);
            }
        } else {
            // ==================== MANUAL MODE ====================
            // Chỉ kiểm tra hạn mức giao dịch, không kiểm tra số dư thực tế
            // Admin tự quản lý và đảm bảo số dư đủ

            if (Long.parseLong(cashoutRequest.getAmount().getAmount()) > cashoutAccount.getTransactionLimit()) {
                // Vượt hạn mức giao dịch cho phép
                throw new BadRequestAlertException(ErrorCode.MSG1158);
            }
        }

        // Trả về tài khoản cashout đã được chọn
        return cashoutAccount;
    }

    /**
     * Xác thực thông tin người thụ hưởng cho client (Umoney)
     *
     * Method này xác định cách thức verify thông tin người nhận dựa trên:
     * - Loại ngân hàng (MB Bank nội bộ vs ngân hàng khác)
     * - Loại tài khoản (Account Number vs QR Code)
     *
     * Luồng xử lý:
     * 1. Kiểm tra bank code có phải MB Bank không
     * 2. Nếu là MB Bank + Account Number: gọi internal API
     * 3. Nếu là MB Bank + QR Code: gọi LAPNET API (vì QR theo chuẩn LAPNET)
     * 4. Nếu là ngân hàng khác: gọi LAPNET API
     *
     * @param request Thông tin người thụ hưởng cần verify
     * @return BeneficiaryInfoResponse Thông tin người thụ hưởng đã verify
     */
    @Override
    public BeneficiaryInfoResponse clientConfirmBeneficiary(BeneficiaryConfirmRequest request) {
        // Kiểm tra bank code có trong danh sách MB Bank codes không
        if (this.mbApiConstantProperties.getCashout().getMbCodes().contains(request.getBankCode())) {
            // TRƯỜNG HỢP MB BANK

            // Trường hợp quét QR của MB thì cũng gọi sang LAPNET để truy vấn
            // Do QR Code theo chuẩn LAPNET (không phải internal MB)
            if (request.getType().equals(AccountType.QR)) {
                return this.getCustomerInterBankInfo(request);
            }
            // Trường hợp Account Number nội bộ MB - gọi internal API
            return this.internalBankMB(request.getBeneficiaryAccountNumber());
        } else {
            // TRƯỜNG HỢP NGÂN HÀNG KHÁC - gọi LAPNET API
            return this.getCustomerInterBankInfo(request);
        }
    }

    /**
     * Lấy lịch sử giao dịch của khách hàng từ T24 Core Banking
     *
     * Method này gọi API T24 để lấy lịch sử giao dịch:
     * 1. Validate tài khoản thuộc về khách hàng
     * 2. Validate khoảng thời gian (tối đa 3 tháng)
     * 3. Gọi API T24 để lấy raw transaction history
     * 4. Enrich thông tin bổ sung (tên ngân hàng, tên người nhận...)
     * 5. Format ngày tháng cho frontend
     * 6. Reverse list để hiển thị giao dịch mới nhất trước
     *
     * Thông tin được enrich:
     * - Tên ngân hàng nhận (từ bank code)
     * - Tên người nhận (từ internal database)
     * - Loại giao dịch (PLUS/MINUS)
     * - Phí giao dịch
     * - Nội dung chi tiết
     *
     * @param request Chứa số tài khoản và khoảng thời gian
     * @return List<TransactionHistoryDTO> Danh sách lịch sử giao dịch
     * @throws BadRequestAlertException Khi tài khoản không hợp lệ hoặc khoảng thời gian quá dài
     */
    @Override
    public List<TransactionHistoryDTO> getHistory(TransactionHistoryRequest request) {
        Customer customer = this.getCustomerLogin();

        // check account number of customer
        MoneyAccount moneyAccountOfCustomer = this.moneyAccountRepository
                .findByAccountNumberAndCustomerIdAndStatus(request.getAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1038));

        if (!Validator.equals(moneyAccountOfCustomer.getCustomerId(), customer.getCustomerId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1038);
        }

        // yyyyMMdd
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern(DateUtil.SHORT_TIMESTAMP_PATTERN_NON_DASH);

        if (request.getFromDate().isAfter(LocalDate.now()) || request.getToDate().isAfter(LocalDate.now())) {
            throw new BadRequestAlertException(ErrorCode.MSG1060);
        }

        if (request.getFromDate().isAfter(request.getToDate())) {
            throw new BadRequestAlertException(ErrorCode.MSG1058);
        }

        if (ChronoUnit.DAYS.between(request.getFromDate(), request.getToDate()) > this.customerProperties
                .getExceedDate() - 1) {
            throw new BadRequestAlertException(ErrorCode.MSG1096);
        }

        String fromDate = request.getFromDate().format(formatters);
        String toDate = request.getToDate().format(formatters);

        TransactionHistoryMBRequest mbRequest = TransactionHistoryMBRequest.builder()
                .accountNumber(request.getAccountNumber())
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        List<TransactionHistoryDTO> transactionHistoryDTOS =
                this.apiGeeTransferService.getTransactionHistory(mbRequest);

        // format transactionDate
        transactionHistoryDTOS.forEach(t -> {
            String transactionDate = t.getTransactionDate();

            if (Validator.isNotNull(transactionDate) && transactionDate.matches(DateUtil.LONG_DATE_NO_DASH_REGEX)) {
                String td = LocalDateUtil.changeFormat(transactionDate, DateUtil.LONG_DATE_NO_DASH,
                        DateUtil.LONG_DATE_PATTERN);

                if (Validator.isNotNull(td)) {
                    t.setTransactionDate(td);
                }
            }
        });

        // reverse list
        Collections.reverse(transactionHistoryDTOS);

        return transactionHistoryDTOS;
    }

    /**
     * Lấy lịch sử thanh toán hóa đơn - Phiên bản 1 (DEPRECATED)
     *
     * ⚠️ Method này đã lỗi thời, vui lòng sử dụng getHistoryBillingV2() thay thế.
     *
     * Method này lấy lịch sử thanh toán hóa đơn của khách hàng:
     * 1. Lấy thông tin khách hàng đang đăng nhập
     * 2. Tạo pageable để phân trang
     * 3. Query database lấy billing transactions
     * 4. Convert sang response format
     * 5. Set default currency nếu null
     *
     * @deprecated Sử dụng getHistoryBillingV2() thay thế
     * @param request Request chứa thông tin phân trang
     * @return List<BillingHistoryResponse> Danh sách lịch sử thanh toán hóa đơn
     */
    @Override
    public List<BillingHistoryResponse> getHistoryBilling(BillingHistoryRequest request) {
        // Lấy thông tin khách hàng đang đăng nhập
        Customer customer = this.getCustomerLogin();

        // Tạo pageable cho phân trang
        Pageable pageable = PageRequest.of(request.getPageIndex(), request.getPageSize());

        // Query database lấy lịch sử billing transactions của khách hàng
        List<Transaction> transactions = this.transactionRepository.getBillingHistories(customer.getCustomerId(),
                pageable);

        // Khởi tạo danh sách response
        List<BillingHistoryResponse> responses = new ArrayList<>();

        // Convert từng transaction sang response format
        transactions.forEach(transaction -> {
            // Tạo billing history response với thông tin cơ bản
            BillingHistoryResponse billingHistoryResponse = BillingHistoryResponse.builder()
                    .billingCode(transaction.getTarget()) // Mã hóa đơn
                    .transactionAmount(Validator.isNotNull(transaction.getTransactionAmount()) ?
                            transaction.getTransactionAmount().longValue() : 0L) // Số tiền thanh toán
                    .transactionCurrency(transaction.getTransactionCurrency()) // Loại tiền tệ
                    .build();
            // Set default currency nếu transaction currency null
            if (Validator.isNull(transaction.getTransactionCurrency())) {
                billingHistoryResponse.setTransactionCurrency(this.customerProperties.getDefaultCurrency());
            }
            // Thêm vào danh sách response
            responses.add(billingHistoryResponse);
        });

        return responses;
    }

    /**
     * Lấy lịch sử thanh toán hóa đơn - Phiên bản 2 (Khuyến nghị)
     *
     * Method này lấy lịch sử thanh toán hóa đơn với thông tin chi tiết hơn V1:
     * 1. Validate billing type hợp lệ
     * 2. Lấy thông tin khách hàng đang đăng nhập
     * 3. Query database với filter theo billing type
     * 4. Enrich thông tin telco (nhà mạng)
     * 5. Trả về unique billing codes (dùng LinkedHashSet)
     *
     * Cải tiến so với V1:
     * - Hỗ trợ filter theo billing type
     * - Hiển thị tên nhà mạng
     * - Loại bỏ duplicate billing codes
     * - Sắp xếp theo thứ tự thời gian
     *
     * @param request Search criteria bao gồm billing type và pagination
     * @return Set<BillingHistoryV2Response> Danh sách unique billing codes với telco info
     * @throws BadRequestAlertException Khi billing type không hợp lệ
     */
    @Override
    public Set<BillingHistoryV2Response> getHistoryBillingV2(BillingHistoryV2Search request) {

        // Validate billing type từ request
        BillingType billingType = BillingType.getBillingType(request.getBillingType());

        // Kiểm tra billing type có hợp lệ không
        if (Validator.isNull(billingType)) {
            throw new BadRequestAlertException(ErrorCode.MSG1169);
        }

        // Lấy thông tin khách hàng đang đăng nhập
        Customer customer = this.getCustomerLogin();

        // Tạo pageable cho phân trang
        Pageable pageable = PageRequest.of(request.getPageIndex(), request.getPageSize());

        // Query database với filter theo billing type và customer
        List<Transaction> transactions = this.transactionRepository.getBillingHistoriesV2(request, pageable, customer.getCustomerId());

        // Sử dụng LinkedHashSet để maintain order và loại bỏ duplicate
        Set<BillingHistoryV2Response> responses = new LinkedHashSet<>();

        // Convert từng transaction sang response format
        transactions.forEach(transaction -> {

            // Xác định telco type từ transaction history
            TelcoType telcoType = this.getTelcoByTransactionHistory(transaction);

            // Tìm thông tin telco trong database
            Optional<Telco> telco = telcoRepository.findByTelcoCodeAndStatus(telcoType.name(), EntityStatus.ACTIVE.getStatus());

            // Chỉ thêm vào response nếu tìm được telco info
            if (telco.isPresent()) {
                BillingHistoryV2Response billingHistoryResponse = BillingHistoryV2Response.builder()
                        .billingCode(transaction.getTarget()) // Mã hóa đơn
                        .telco(telco.get().getTelcoName()) // Tên nhà mạng
                        .build();

                // Thêm vào set (tự động loại bỏ duplicate)
                responses.add(billingHistoryResponse);
            }
        });

        return responses;
    }

    /**
     * Lấy thông tin merchant từ QR Code
     *
     * Method này được gọi khi khách hàng quét QR Code merchant để lấy thông tin:
     * 1. Tìm merchant theo merchant ID từ QR Code
     * 2. Validate merchant tồn tại và đang hoạt động
     * 3. Chỉ lấy child merchant (có parentId), không phải master merchant
     * 4. Trả về thông tin cơ bản để hiển thị trước khi thanh toán
     *
     * Thông tin trả về:
     * - Tên merchant (để khách hàng xác nhận)
     * - Bank code (ngân hàng của merchant)
     * - Account number (tài khoản nhận tiền)
     *
     * @param request Chứa merchant ID từ QR Code
     * @return InfoQrMerchantResponse Thông tin merchant để hiển thị
     * @throws BadRequestAlertException Khi merchant không tồn tại hoặc không hoạt động
     */
    @Override
    public InfoQrMerchantResponse getInfoQrMerchant(InfoQrMerchantRequest request) {

        // Tìm thông tin merchant theo merchant code từ QR
        // Điều kiện: merchant phải active và là child merchant (có parentId)
        Merchant merchant = this.merchantRepository.findByMerchantCodeAndStatusAndParentIdIsNotNull(request.getMerchantId(),
                EntityStatus.ACTIVE.getStatus());

        // Validate merchant tồn tại
        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        // TODO: Kiểm tra merchantId có phải của backend app sinh ra hay không
        // Nghiệp vụ merchant validation chưa được implement đầy đủ

        // Trả về thông tin merchant cơ bản
        return InfoQrMerchantResponse.builder()
                .merchantName(merchant.getMerchantName()) // Tên merchant hiển thị
                .bankCode(merchant.getMerchantBankCode()) // Mã ngân hàng của merchant
                .accountNumber(merchant.getMerchantAccountNumber()) // Số tài khoản nhận tiền
                .build();
    }

    /**
     * Tạo OTP cho thanh toán hóa đơn - Phiên bản 2 (Khuyến nghị)
     *
     * Method này xử lý thanh toán các loại hóa đơn:
     * - Điện lực (EDL): Hóa đơn tiền điện
     * - Nam Papa: Hóa đơn tiền nước
     * - LTC: Internet, TV, điện thoại cố định
     * - Unitel: Internet, TV
     * - ETL: Internet, TV
     * - Bảo hiểm: Các loại bảo hiểm
     *
     * Luồng xử lý:
     * 1. Validate bill code và telco code
     * 2. Gọi API nhà cung cấp để verify hóa đơn
     * 3. Lấy thông tin số tiền cần thanh toán
     * 4. Tạo transaction record
     * 5. Gọi T24 để tạo OTP
     * 6. Lưu thông tin billing để confirm sau
     *
     * @param request Thông tin thanh toán hóa đơn V2
     * @param httpServletRequest HTTP request
     * @return OtpTransferTransResponse Chứa OTP info và transaction ID
     * @throws BadRequestAlertException Khi bill code không hợp lệ hoặc đã thanh toán
     */
    @Override
    public OtpTransferTransResponse requestOtpBilling(OtpBillingV2Request request,
                                                      HttpServletRequest httpServletRequest) {

        Customer customer = this.getCustomerLogin();

        BillingV2Request billingRequest = new BillingV2Request(request.getBillCode(), request.getTelcoCode(), request.getAccountNumber(), request.getBillingType());
        // gọi lại api check số tiền cần thanh toán
        BillingResponse billing = this.verifyBillingCode(billingRequest);

        // tạo giao dịch chuyển tiền
        TransactionDTO transactionDTO = new TransactionDTO(request);

        transactionDTO.setPhoneNumber(customer.getPhoneNumber());
        transactionDTO.setTransactionCurrency(this.customerProperties.getDefaultCurrency());
        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode());
        transactionDTO.setMessage(StringUtil.join(new String[]{request.getTelcoCode(), request.getBillCode()}, StringPool.SPACE));
        transactionDTO.setConfigurationFeeType(billing.getConfigurationFeeType());
        transactionDTO.setTransactionAmount(Validator.isNotNull(billing.getBalance()) ? billing.getBalance().doubleValue() : 0D);
        transactionDTO.setTransactionFee(billing.getTransactionFee());
        transactionDTO.setDiscount(billing.getTransactionDiscount());
        transactionDTO.setDiscountFixed(billing.getDiscountFixed());

        return this.makeTransferToMerchant(transactionDTO, request.getTelcoCode(), customer, request.isSetDefaultAccount());
    }

    /**
     * Tạo OTP cho thanh toán hóa đơn - Phiên bản 1 (DEPRECATED)
     *
     * ⚠️ Method này đã lỗi thời, vui lòng sử dụng requestOtpBilling(OtpBillingV2Request) thay thế.
     *
     * Phiên bản cũ chỉ hỗ trợ thanh toán hóa đơn cơ bản với bill code,
     * không có thông tin telco code và billing type chi tiết.
     *
     * @deprecated Sử dụng requestOtpBilling(OtpBillingV2Request) thay thế
     * @param request Thông tin thanh toán hóa đơn V1
     * @param httpServletRequest HTTP request
     * @return OtpTransferTransResponse Chứa OTP info và transaction ID
     */
    @Override
    public OtpTransferTransResponse requestOtpBilling(OtpBillingRequest request, HttpServletRequest httpServletRequest) {
        Customer customer = this.getCustomerLogin();

        BillingRequest billingRequest = new BillingRequest(request.getBillCode());
        // gọi lại api check số tiền cần thanh toán
        BillingResponse billing = this.verifyBillingCode(billingRequest);

        // tạo giao dịch chuyển tiền
        TransactionDTO transactionDTO = new TransactionDTO(request);

        transactionDTO.setPhoneNumber(customer.getPhoneNumber());
        transactionDTO.setTransactionCurrency(this.customerProperties.getDefaultCurrency());
        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode());
        transactionDTO.setMessage(StringUtil.join(new String[]{
                billing.getNetworkType(), request.getBillCode()}, StringPool.SPACE));
        transactionDTO.setTransactionAmount(Validator.isNotNull(billing.getBalance()) ? billing.getBalance().doubleValue() : 0L);

        if (BillingType.getTransactionFeeCodeByType(request.getType()) != null) {
            FeeTransactionResponse feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                    TransactionFeeTypeCode.valueOf(BillingType.getTransactionFeeCodeByType(request.getType())),
                    Validator.isNotNull(transactionDTO.getTransactionAmount()) ? transactionDTO.getTransactionAmount().longValue() : 0L, this.customerProperties.getDefaultCurrency());

            transactionDTO.setTransactionFee(feeTransactionResponse.getFee());
            transactionDTO.setDiscount(feeTransactionResponse.getDiscount());
            transactionDTO.setConfigurationFeeType(feeTransactionResponse.getConfigurationFeeType());
        }
        return this.makeTransferToMerchant(transactionDTO, billing.getNetworkType(), customer, request.isSetDefaultAccount());
    }

    /**
     * Tạo OTP cho nạp tiền ví điện tử (Cash In)
     *
     * Method này xử lý nạp tiền từ tài khoản MB Bank vào ví điện tử Umoney:
     * 1. Verify tài khoản Umoney tồn tại và lấy tên chủ tài khoản
     * 2. Validate số tiền giao dịch hợp lệ (>= 0)
     * 3. Tính phí giao dịch cho E-Wallet recharge
     * 4. Tạo transaction DTO với thông tin đầy đủ
     * 5. Gọi makeTransferToMerchant để tạo OTP và transaction
     *
     * Luồng tiền: MB Bank Account → Umoney Wallet
     * Merchant: UMONEY (ví điện tử đối tác)
     * Fee Type: E_WALLET_RECHARGE
     *
     * @param request Thông tin nạp tiền (account, amount, message...)
     * @param httpServletRequest HTTP request
     * @return OtpTransferTransResponse Chứa OTP info và transaction ID
     * @throws BadRequestAlertException Khi số tiền âm hoặc tài khoản Umoney không hợp lệ
     */
    @Override
    public OtpTransferTransResponse requestOtpCashIn(OtpCashInRequest request,
                                                     HttpServletRequest httpServletRequest) {

        // Verify tài khoản Umoney có tồn tại không
        String umoneyAccountName = null;
        VerifyUmoneyAccountResponse umoneyAccountResponse = this.ewalletService.verifyCashAccount(request.getEwalletAccountNumber());

        // Lấy tên chủ tài khoản Umoney nếu verify thành công
        if (Validator.isNotNull(umoneyAccountResponse)) {
            umoneyAccountName = umoneyAccountResponse.getAccountName();
        }

        // Validate số tiền giao dịch phải >= 0
        if (request.getAmount() < 0) {
            throw new BadRequestAlertException(ErrorCode.MSG1111);
        }

        // Tạo transaction DTO từ request và tên tài khoản Umoney
        TransactionDTO transactionDTO = new TransactionDTO(request, umoneyAccountName);

        // Tính phí giao dịch cho E-Wallet recharge
        FeeTransactionResponse feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                TransactionFeeTypeCode.E_WALLET_RECHARGE, // Loại phí nạp ví điện tử
                Validator.isNotNull(transactionDTO.getTransactionAmount()) ? transactionDTO.getTransactionAmount().longValue() : 0L,
                this.customerProperties.getDefaultCurrency());

        // Set thông tin phí vào transaction DTO
        transactionDTO.setTransactionFee(feeTransactionResponse.getFee());
        transactionDTO.setDiscount(feeTransactionResponse.getDiscount());
        transactionDTO.setConfigurationFeeType(feeTransactionResponse.getConfigurationFeeType());

        // Lấy thông tin khách hàng đang đăng nhập
        Customer customer = this.getCustomerLogin();

        // Set thông tin bổ sung vào transaction DTO
        transactionDTO.setPhoneNumber(customer.getPhoneNumber()); // Số điện thoại để gửi OTP

        transactionDTO.setTransactionCurrency(this.customerProperties.getDefaultCurrency()); // Tiền tệ mặc định (LAK)

        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode()); // Mã chi nhánh

        // Tạo message mô tả giao dịch: "[Số tài khoản] [Message từ user]"
        transactionDTO.setMessage(StringUtil.join(new String[]{
                request.getAccountNumber(), request.getMessage()
        }, StringPool.SPACE));

        // Gọi method chính để tạo OTP và transaction
        // Merchant: UMONEY (ví điện tử)
        return this.makeTransferToMerchant(transactionDTO, MerchantCode.UMONEY.name(), customer, request.isSetDefaultAccount());
    }

    /**
     * Tạo OTP cho thanh toán QR Code merchant nội bộ
     *
     * Method này xử lý thanh toán QR Code tại các merchant đối tác của MB Bank:
     * 1. Validate thông tin đầu vào (amount, merchant)
     * 2. Tìm thông tin merchant theo merchant code
     * 3. Tính phí giao dịch cho merchant billing
     * 4. Tạo transaction DTO với thông tin đầy đủ
     * 5. Gọi makeTransferToMerchant để xử lý chuyển tiền
     *
     * Đây là luồng thanh toán QR Code nội bộ (không qua LAPNET):
     * - Khách hàng quét QR Code tại merchant
     * - Tiền được chuyển từ tài khoản khách hàng → tài khoản merchant
     * - Merchant nhận tiền ngay lập tức (real-time)
     *
     * @param request Thông tin thanh toán QR Code (amount, merchantId, account...)
     * @param httpServletRequest HTTP request để lấy thông tin bổ sung
     * @return OtpTransferTransResponse Chứa OTP info và transaction ID
     * @throws BadRequestAlertException Khi có lỗi validation hoặc merchant không tồn tại
     */
    @Override
    public OtpTransferTransResponse requestOtpQrPayment(OtpQrBillRequest request,
                                                        HttpServletRequest httpServletRequest) {
        // Validate số tiền giao dịch phải >= 0
        // Không cho phép thanh toán với số tiền âm
        if (request.getAmount() < 0) {
            throw new BadRequestAlertException(ErrorCode.MSG1111);
        }

        // Lấy ra merchant theo merchant code, chỉ lấy merchant con (child merchant)
        // Điều kiện: parentIdIsNotNull đảm bảo đây là merchant con, không phải master merchant
        // Chỉ merchant con mới có thể nhận thanh toán trực tiếp
        Merchant merchant = this.merchantRepository.findByMerchantCodeAndStatusAndParentIdIsNotNull(request.getMerchantId(),
                EntityStatus.ACTIVE.getStatus());

        // Kiểm tra merchant có tồn tại và đang hoạt động không
        // Nếu không tìm thấy merchant thì throw exception
        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        // Tạo giao dịch chuyển tiền từ request và merchant ID
        // Constructor này sẽ copy các thông tin cơ bản từ request
        TransactionDTO transactionDTO = new TransactionDTO(request, request.getMerchantId());

        // Tính phí giao dịch cho merchant billing (thanh toán QR Code nội bộ)
        // TransactionFeeTypeCode.INTERNAL_QR_BILLING: Loại phí cho QR Code nội bộ
        // merchant.getMerchantId(): ID merchant để tính phí theo cấu hình riêng
        // transactionAmount: Số tiền giao dịch để tính phí theo tỷ lệ %
        // defaultCurrency: Tiền tệ mặc định (LAK) để lấy cấu hình phí
        FeeTransactionResponse feeTransactionResponse = this.transactionService.getFeeOfMerchantTransaction(
                TransactionFeeTypeCode.INTERNAL_QR_BILLING, merchant.getMerchantId(),
                Validator.isNotNull(transactionDTO.getTransactionAmount()) ? transactionDTO.getTransactionAmount().longValue() : 0L,
                this.customerProperties.getDefaultCurrency());

        // Set thông tin phí giao dịch vào transaction DTO
        transactionDTO.setTransactionFee(feeTransactionResponse.getFee());        // Phí giao dịch
        transactionDTO.setDiscount(feeTransactionResponse.getDiscount());          // Giảm giá (nếu có)
        transactionDTO.setConfigurationFeeType(feeTransactionResponse.getConfigurationFeeType()); // Loại cấu hình phí

        // Lấy thông tin khách hàng đã đăng nhập từ security context
        Customer customer = this.getCustomerLogin();

        // Tạo message mô tả giao dịch để hiển thị trong lịch sử
        // Format: "[Tên khách hàng] [Thanh toán QR Code] [Mã merchant]"
        // Ví dụ: "Nguyen Van A Thanh toán QR Code MERCHANT001"
        transactionDTO.setMessage(StringUtil.join(
                new String[]{
                        customer.getFullname(),                           // Tên đầy đủ khách hàng
                        Labels.getLabels(LabelKey.MESSAGE_QR_PAYMENT),   // Label "Thanh toán QR Code"
                        request.getMerchantId()                          // Mã merchant
                }, StringPool.SPACE));                                   // Nối bằng dấu cách

        // Set số điện thoại khách hàng vào transaction (để gửi SMS thông báo)
        transactionDTO.setPhoneNumber(customer.getPhoneNumber());

        // Set mã chi nhánh mặc định (MB Laos hiện tại chỉ có 1 chi nhánh)
        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode());

        // Set số tài khoản người nhận (merchant account number)
        // Đây là tài khoản sẽ nhận tiền từ giao dịch thanh toán
        transactionDTO.setBeneficiaryAccountNumber(merchant.getMerchantAccountNumber());

        // Set loại giao dịch là QR Code merchant nội bộ
        // Phân biệt với các loại khác như LAPNET, international...
        transactionDTO.setType(TransferTransactionType.QR_CODE_INTERNAL_MERCHANT);

        // Gọi method chính để thực hiện chuyển tiền đến merchant
        // Truyền vào: transaction info, merchant code, customer info, có set default account không
        return this.makeTransferToMerchant(transactionDTO, merchant.getMerchantCode(), customer, request.isSetDefaultAccount());
    }

    /**
     * Xác thực số dư cho giao dịch thanh toán quốc tế
     *
     * Method này kiểm tra số dư tài khoản cho giao dịch QR Code quốc tế:
     * 1. Chỉ áp dụng cho QR_CODE_INTERNATIONAL_MERCHANT
     * 2. Lấy số dư hiện tại từ T24
     * 3. Tính toán số tiền cần trừ (bao gồm phí và tỷ giá)
     * 4. Kiểm tra số dư đủ để thực hiện giao dịch
     * 5. Throw exception nếu số dư không đủ
     *
     * @param transaction Thông tin giao dịch
     * @param customer Thông tin khách hàng
     * @param rate Tỷ giá quy đổi
     * @param internationalPayment Thông tin thanh toán quốc tế
     * @throws BadRequestAlertException Khi số dư không đủ
     */
    public void verifyBalance(Transaction transaction, Customer customer, double rate, InternationalPayment internationalPayment) {
        // Chỉ áp dụng cho giao dịch QR Code merchant quốc tế
        if (!Validator.equals(transaction.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)) {
            return;
        }

        // Tạo request kiểm tra số dư tài khoản theo CIF
        AccountBalanceRequest accountBalanceRequest = AccountBalanceRequest.builder()
                .custCode(customer.getCif())
                .build();

        // Gọi API T24 để lấy số dư tất cả tài khoản của khách hàng
        List<AccountBalanceDTO> accountBalanceDTOList = this.apiGeeTransferService
                .checkAccountBalance(accountBalanceRequest);

        // Validate response từ T24
        if (Validator.isNull(accountBalanceDTOList)) {
            throw new BadRequestAlertException(ErrorCode.MSG100100);
        }

        // Lấy số dư available của tài khoản giao dịch
        String amount = accountBalanceDTOList.stream()
                .filter(item -> Validator.equals(item.getAccountNumber(), transaction.getCustomerAccNumber()))
                .map(AccountBalanceDTO::getAvailableBalance)
                .findFirst()
                .orElse(StringPool.NUMBER_0);

        // Quy đổi số dư LAK sang ngoại tệ theo tỷ giá
        // Ví dụ: 1,000,000 LAK / 4000 (rate) = 250 KHR
        double amountForeign = Math.round(Double.parseDouble(amount) / rate);

        // Kiểm tra số dư ngoại tệ có đủ để thực hiện giao dịch không
        if (amountForeign < transaction.getForeignAmount()) {
            // Số dư không đủ - set trạng thái FAILED cho cả transaction và international payment
            transaction.setTransactionStatus(TransactionStatus.FAILED);
            internationalPayment.setTransactionStatus(TransactionStatus.FAILED);

            // Lưu trạng thái FAILED vào database để tracking
            this.transactionRepository.save(transaction);
            this.internationalPaymentRepository.save(internationalPayment);

            // Throw exception không rollback để giữ lại log FAILED
            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1108);
        }
    }

    /**
     * Tạo OTP cho giao dịch chuyển tiền
     *
     * Đây là method phức tạp nhất của service, xử lý tất cả loại chuyển tiền:
     * - Chuyển tiền nội bộ (cùng MB Bank)
     * - Chuyển tiền LAPNET (liên ngân hàng)
     * - Chuyển tiền quốc tế (Cambodia, Vietnam...)
     * - Chuyển tiền ngoại tệ (USD, THB...)
     * - Thanh toán QR Code
     *
     * Luồng xử lý chính:
     * 1. Validate thông tin đầu vào
     * 2. Xác định loại giao dịch và tỷ giá (nếu có)
     * 3. Kiểm tra thông tin người nhận
     * 4. Tính phí giao dịch
     * 5. Kiểm tra hạn mức và số dư
     * 6. Tạo transaction record
     * 7. Gọi T24 để tạo OTP
     * 8. Lưu thông tin beneficiary (nếu có)
     * 9. Set tài khoản mặc định (nếu có)
     *
     * @param request Thông tin chuyển tiền từ client
     * @param httpServletRequest HTTP request để lấy thông tin bổ sung
     * @return OtpTransferTransResponse Chứa OTP info và transaction ID
     * @throws BadRequestAlertException Khi có lỗi validation hoặc business logic
     */
    @Override
    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    public OtpTransferTransResponse requestOtpTransfer(OtpTransferTransRequest request,
                                                       HttpServletRequest httpServletRequest) {
        // Lấy thông tin khách hàng đã đăng nhập
        Customer customer = this.getCustomerLogin();
        double rate = 0L; // Tỷ giá cho chuyển tiền quốc tế

        // Validate thông tin đầu vào
        this.validateTransferTrans(request);

        // Xử lý đặc biệt cho QR Code nội bộ
        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())
                && Validator.equals(request.getType(), AccountType.QR)) {
            request.setBeneficiaryQrValue(request.getBeneficiaryAccountNumber());
        }

        // tạo giao dịch chuyển tiền
        TransactionDTO transactionDTO = new TransactionDTO(request);

        transactionDTO.setPhoneNumber(customer.getPhoneNumber());
        // tạm thời lấy default branch code vì bên MB Laos hiện tại chỉ có 1 chi nhánh duy nhất
        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode());

        // kiem tra so tai khoan ghi no va so tai khoan ghi co
        // tài khoản ghi nợ
        MoneyAccount moneyAccountOfCustomer = this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(
                request.getCustomerAccNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG1038));

        MoneyAccount moneyAccountOfBenefit = this.moneyAccountRepository.findByAccountNumberAndStatus(
                request.getBeneficiaryAccountNumber(), EntityStatus.ACTIVE.getStatus()).stream().findFirst().orElse(null);

        this.checkTransactionLimitCustomerAndValidateMoneyAccount(moneyAccountOfCustomer, request, customer, null, moneyAccountOfBenefit);

        transactionDTO.setCustomerId(customer.getCustomerId());

        // Gen message
        if (Validator.isNull(request.getRemark())) {
            transactionDTO.setMessage(StringUtil.join(
                    new String[]{
                            customer.getFullname(), Labels.getLabels(LabelKey.MESSAGE_TRANSFER_MONEY)
                    }, StringPool.SPACE));
        }
        // check ekyc và thời gian hết hạn đổi mật khẩu
        CustomerEkyc ekyc = this.customerEkycRepository.findByCustomerIdAndStatusNot(customer.getCustomerId(), EntityStatus.DELETED.getStatus());

        boolean ekycRequired = Validator.isNull(ekyc) && customer.isEkycRequired();

        // Do bỏ rule hết hạn thay đổi mật khẩu. Nên không check pwExpiredTime
        if (ekycRequired) {
            throw new BadRequestAlertException(ErrorCode.MSG101094);
        }

        FeeTransactionResponse feeTransactionResponse;

        // tài khoản ghi có
        // kiem tra neu la chuyen khoan lapnet thi khong check tai khoan nhan
        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())) {
            // Lấy tiền phí giao dịch nội bộ từ cấu hình
            // Phí nội bộ thường thấp hơn hoặc miễn phí so với liên ngân hàng
            feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                    TransactionFeeTypeCode.TRANSFER_INTERNAL_BANK,  // Loại phí chuyển tiền nội bộ
                    // Convert amount sang long để tính phí
                    Validator.isNotNull(transactionDTO.getTransactionAmount()) ?
                            transactionDTO.getTransactionAmount().longValue() : 0L,
                    // Currency của tài khoản người gửi
                    moneyAccountOfCustomer.getCurrency());

            MoneyAccount creditAccount = this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(
                            request.getBeneficiaryAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus())
                    .orElse(null);

            this.checkTransactionLimitCustomerAndValidateMoneyAccount(moneyAccountOfCustomer, request, customer, feeTransactionResponse, null);

            Customer beneficiaryCustomer =
                    Objects.nonNull(creditAccount)
                            ? this.customerRepository.findByCustomerIdAndReferenceIdIsNull(
                            creditAccount.getCustomerId())
                            : null;

            if (Objects.isNull(beneficiaryCustomer)) {
                _log.info("BeneficiaryCustomer with account number {} has not any MBLAOS user yet", request.getBeneficiaryAccountNumber());
                // nếu khách hàng hưởng thụ chưa có tài khoản mb laos thì gọi sang bên t24 check tk
                BeneficiaryInfoResponse beneficiaryInfo = this.internalBankMB(request.getBeneficiaryAccountNumber());

                transactionDTO.setBeneficiaryCustomerName(beneficiaryInfo.getBeneficiaryName());
            } else {
                transactionDTO.setBeneficiaryCustomerId(beneficiaryCustomer.getCustomerId());
                transactionDTO.setBeneficiaryCustomerName(beneficiaryCustomer.getFullname());
            }
            // Set transaction type cho chuyển tiền nội bộ MB Bank
            // Phân biệt QR Code nội bộ vs Account transfer nội bộ
            transactionDTO.setType(Validator.equals(request.getType(), AccountType.QR) ?
                    TransferTransactionType.QR_CODE_INTERNAL :      // QR Code nội bộ MB Bank
                    TransferTransactionType.INTERNAL_BANK);         // Account transfer nội bộ MB Bank
        } else {
            //bo sung tinh phi QR lapnet
            AccountType type = request.getType();

            // call sang T24 de check tai khoan nhan
            AccNumberVerifyRequest accNumberVerifyRequest = AccNumberVerifyRequest.builder()
                    .toAccount(AccountType.QR.equals(type) ? request.getBeneficiaryQrValue() : request.getBeneficiaryAccountNumber())
                    .toMember(request.getBeneficiaryBankCode())
                    .toType(AccountType.QR.equals(type) ? this.mbApiConstantProperties.getOtpGen().getQrType() : this.mbApiConstantProperties.getOtpGen().getAccountType())
                    .fromUserFullName(customer.getFullname())
                    .fromAccountType(FromAccountType.PERSONAL.name())
                    .internationalQuery(InternationalQuery.N.name())
                    .fromAccount(request.getCustomerAccNumber())
                    .fromUser(customer.getFullname())
                    .build();

            this.isInterPayment(request, type, accNumberVerifyRequest, transactionDTO);

            AccNumberVerifyDTO accNumberVerifyDTO = this.apiGeeTransferService.verifyAccNumber(accNumberVerifyRequest);
            if (Validator.isNull(accNumberVerifyDTO) || Validator.isNull(accNumberVerifyDTO.getAccountName())) {
                throw new BadRequestAlertException(ErrorCode.MSG1036);
            }

            rate = Validator.isNotNull(accNumberVerifyDTO.getCrossBorderExchangeRate()) ?
                    Double.parseDouble(accNumberVerifyDTO.getCrossBorderExchangeRate().getRate()) : 0L;

            transactionDTO.setTransactionAmount(rate > 0 ? Math.round(request.getAmount() * rate) : request.getAmount());
            transactionDTO.setTransactionId(accNumberVerifyDTO.getReferenceNumber());
            transactionDTO.setQrCodeValue(request.getBeneficiaryQrValue());

            if (AccountType.QR.equals(type)) {
                //thanh toan merchant lapnet
                //mien phi neu trong thang:ut giao dich < feeFreeTransaction, tong tien < feeFreeAmount
                //khong thoa man thi lay phi trong cau hinh CMS
                feeTransactionResponse = getLapnetTransactionFee(request, transactionDTO, accNumberVerifyDTO, moneyAccountOfCustomer);
            } else {

                // CHUYỂN TIỀN LIÊN NGÂN HÀNG (không phải QR Code)
                // Set transaction type cho account transfer qua LAPNET
                transactionDTO.setType(TransferTransactionType.INTER_BANK);

                // Tính phí chuyển tiền liên ngân hàng
                // Sử dụng fee type TRANSFER_INTER_BANK (phí cao hơn internal)
                feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                        TransactionFeeTypeCode.TRANSFER_INTER_BANK,    // Loại phí liên ngân hàng
                        // Convert amount sang long để tính phí
                        Validator.isNotNull(transactionDTO.getTransactionAmount()) ?
                                transactionDTO.getTransactionAmount().longValue() : 0L,
                        // Currency của tài khoản người gửi
                        moneyAccountOfCustomer.getCurrency());
            }
        }

        if (Validator.isNotNull(feeTransactionResponse)) {
            transactionDTO.setTransactionFee(feeTransactionResponse.getFee());
            transactionDTO.setDiscount(feeTransactionResponse.getDiscount());
            transactionDTO.setConfigurationFeeType(feeTransactionResponse.getConfigurationFeeType());
        }

        if (Validator.equals(request.getCustomerCurrency(), customerProperties.getDefaultCurrency())
                && Validator.equals(request.getBeneficiaryCurrency(), customerProperties.getDefaultCurrency())) {
            transactionDTO.setTransactionAmount((double) Math.round(request.getAmount()));
        }

        // call API MB để thực hiện tạo OTP
        Transaction transferSaved = this.requestOtpT24(transactionDTO);

        this.saveInterPayment(request, transferSaved, customer, rate, feeTransactionResponse);

        try {
            // lưu danh bạ thụ hưởng nếu gạt nút lưu
            if (request.isSaveBeneficiary()) {
                beneficiaryService.createBeneficiary(transactionDTO);
            }

            // set tài khoản mặc định nếu gạt nút setting default account
            if (request.isSetDefaultAccount()) {
                this.setDefaultAccountTransfer(customer.getCustomerId(), request.getCustomerAccNumber());
            }
        } catch (Exception e) {
            _log.error("requestOtpTransfer, after requestOTP error:", e);
        }

        return OtpTransferTransResponse.builder()
                .expiredTime(this.otpProperties.getDuration())
                .requiredOtp(Boolean.TRUE)
                .transferTransactionId(transferSaved.getTransferTransactionId())
                .transactionId(transferSaved.getTransactionId())
                .transData(transactionDTO.getTransData())
                .build();
    }

    /**
     * Xử lý thanh toán quốc tế (chỉ áp dụng cho Cambodia)
     *
     * Method này kiểm tra và chuẩn bị thông tin cho giao dịch thanh toán quốc tế:
     * 1. Chỉ xử lý QR Code với currency KHR (Cambodia Riel)
     * 2. Parse và validate QR Code Cambodia
     * 3. Kiểm tra số tiền tối thiểu theo quy định
     * 4. Set flag international query cho T24
     * 5. Chuẩn bị thông tin quốc gia và member cho LAPNET
     *
     * Business rules:
     * - Chỉ hỗ trợ QR Code Cambodia (KHR)
     * - Số tiền tối thiểu theo internationalPaymentProperties
     * - Phải parse được QR Code Cambodia hợp lệ
     *
     * @param request Thông tin chuyển tiền từ client
     * @param type Loại account (QR hoặc ACCOUNT)
     * @param accNumberVerifyRequest Request verify account sẽ được modify
     * @param transactionDTO Transaction DTO sẽ được modify
     * @throws BadRequestAlertException Khi QR Code không hợp lệ hoặc amount < min
     */
    private void isInterPayment(OtpTransferTransRequest request, AccountType type, AccNumberVerifyRequest accNumberVerifyRequest, TransactionDTO transactionDTO) {
        // Kiểm tra điều kiện: phải là QR Code và currency phải là KHR (Cambodia Riel)
        if (Validator.equals(type, AccountType.QR) && Validator.equals(request.getBeneficiaryCurrency(), CurrencyType.KHR.name())) {

            // Parse QR Code Cambodia để lấy thông tin chi tiết
            // Method này sẽ validate QR Code theo chuẩn Cambodia và extract thông tin
            QrCodeResponse response = this.qrCodeServiceImpl.getValueFromQrCodeCambodia(request.getBeneficiaryQrValue());

            // Double check: currency trong QR Code phải khớp với currency trong request
            // Đảm bảo không có mismatch giữa QR Code và request data
            if (!Validator.equals(response.getBeneficiaryCurrency(), request.getBeneficiaryCurrency())) {
                throw new BadRequestAlertException(ErrorCode.MSG100056);
            }

            // Validate số tiền tối thiểu cho giao dịch quốc tế Cambodia
            // Mỗi quốc gia có quy định số tiền tối thiểu khác nhau
            if (request.getAmount() < this.internationalPaymentProperties.getMinAmountKhr()) {
                // Tạo error message với format số tiền và currency
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_AMOUNT_MIN_IS_INVALID,
                                new Object[]{StringUtil.formatMoney(
                                        Double.valueOf(this.internationalPaymentProperties.getMinAmountKhr()))
                                        + StringPool.SPACE + request.getBeneficiaryCurrency()}),
                        Fee.class.getSimpleName(), LabelKey.ERROR_AMOUNT_MIN_IS_INVALID);
            }

            // Lấy thông tin quốc gia từ master data dựa vào currency
            // Common chứa mapping giữa currency và nation info
            Common common = getCommon(request.getBeneficiaryCurrency());

            // Set flag international query = Y để T24 biết đây là giao dịch quốc tế
            // T24 sẽ áp dụng logic khác cho international transaction
            accNumberVerifyRequest.setInternationalQuery(InternationalQuery.Y.name());

            // Set nation code (ví dụ: "KH" cho Cambodia)
            // T24 cần biết quốc gia đích để routing đúng
            accNumberVerifyRequest.setToNation(common.getValue());

            // Set member/payment system (ví dụ: "CAMBODIA" cho hệ thống thanh toán Cambodia)
            // Xác định hệ thống thanh toán đích
            accNumberVerifyRequest.setToMember(common.getDescription());

            // Set bank code vào transaction DTO để tracking
            transactionDTO.setBankCode(request.getBeneficiaryBankCode());
        }
    }

    /**
     * Lưu thông tin thanh toán quốc tế vào database
     *
     * Method này chỉ được gọi cho giao dịch QR_CODE_INTERNATIONAL_MERCHANT:
     * 1. Tạo InternationalPaymentDTO với thông tin đầy đủ
     * 2. Validate foreign fee bắt buộc phải có
     * 3. Set exchange rate và currency thông tin
     * 4. Lấy nation code từ master data
     * 5. Copy properties từ transaction chính
     * 6. Lưu vào bảng international_payment
     * 7. Verify balance để đảm bảo đủ tiền
     *
     * Bảng international_payment lưu trữ:
     * - Tỷ giá tại thời điểm giao dịch
     * - Phí ngoại tệ (foreign fee)
     * - Nation code và payment system
     * - Link với transaction chính qua transferTransactionId
     *
     * @param request Thông tin request gốc
     * @param transferSaved Transaction đã được lưu
     * @param customer Thông tin khách hàng
     * @param rate Tỷ giá áp dụng
     * @param feeTransactionResponse Thông tin phí đã tính
     * @throws BadRequestAlertException Khi thiếu foreign fee hoặc số dư không đủ
     */
    private void saveInterPayment(OtpTransferTransRequest request, Transaction transferSaved, Customer customer, double rate, FeeTransactionResponse feeTransactionResponse) {
        // Chỉ xử lý cho giao dịch QR Code merchant quốc tế
        // Các loại giao dịch khác không cần lưu international payment
        if (Validator.equals(transferSaved.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)) {

            // Tạo DTO để chuẩn bị dữ liệu cho international payment
            InternationalPaymentDTO dto = new InternationalPaymentDTO();

            // Validate foreign fee bắt buộc phải có cho giao dịch quốc tế
            // Foreign fee là phí tính bằng ngoại tệ (KHR, USD...)
            if (Validator.isNull(feeTransactionResponse.getForeignFee())) {
                throw new BadRequestAlertException(ErrorCode.MSG1143);
            }

            // Set transfer transaction ID để link với transaction chính
            dto.setTransferTransactionId(transferSaved.getTransferTransactionId());

            // Set customer ID để tracking giao dịch theo khách hàng
            dto.setCustomerId(customer.getCustomerId());

            // Set tỷ giá tại thời điểm giao dịch (quan trọng cho audit và reconciliation)
            dto.setExchangeRate(rate);

            // Set currency đích (KHR, USD, THB...)
            dto.setBeneficiaryCurrency(request.getBeneficiaryCurrency());

            // Set phí ngoại tệ (tính bằng currency đích)
            dto.setForeignFee(feeTransactionResponse.getForeignFee());

            // Lấy nation code từ master data dựa vào currency
            // Ví dụ: KHR -> "KH", USD -> "US"
            dto.setNationCode(this.commonRepository
                    .findByCategoryAndCodeAndStatus(CommonCategory.NATION_ID.name(),
                            request.getBeneficiaryCurrency(), EntityStatus.ACTIVE.getStatus())
                    .map(Common::getValue)
                    .orElse(null));

            // Copy tất cả properties từ transaction chính sang DTO
            // Bao gồm: amount, status, created date, etc.
            BeanUtils.copyProperties(transferSaved, dto);

            // Set payment system name (ví dụ: "CAMBODIA", "VIETNAM")
            dto.setPaymentSystem(getCommon(dto.getBeneficiaryCurrency()).getDescription());

            // Convert DTO thành entity và lưu vào database
            InternationalPayment internationalPayment = this.internationalPaymentRepository.save(
                    this.internationalPaymentMapper.toEntity(dto));

            // Verify balance cuối cùng để đảm bảo khách hàng có đủ tiền
            // Tính cả tỷ giá và phí quốc tế
            this.verifyBalance(transferSaved, customer, rate, internationalPayment);
        }
    }

    /**
     * Tính phí giao dịch LAPNET dựa vào loại QR Code
     *
     * Method này phân loại QR Code và tính phí tương ứng:
     * 1. Verify QR Code để xác định payment type
     * 2. Phân biệt merchant QR vs transfer QR
     * 3. Với merchant QR: phân biệt domestic vs international
     * 4. Áp dụng fee calculation tương ứng
     * 5. Set transaction type phù hợp
     *
     * Các loại phí:
     * - QR_CODE_INTERNATIONAL_MERCHANT: Phí quốc tế (có tỷ giá)
     * - QR_CODE_LAPNET_MERCHANT: Phí LAPNET nội địa
     * - QR_CODE_LAPNET_TRANSFER: Phí chuyển tiền liên ngân hàng
     *
     * @param request Thông tin request gốc
     * @param transactionDTO Transaction DTO sẽ được modify
     * @param accNumberVerifyDTO Thông tin verify từ T24
     * @param moneyAccountOfCustomer Tài khoản khách hàng
     * @return FeeTransactionResponse Thông tin phí đã tính
     */
    private FeeTransactionResponse getLapnetTransactionFee(OtpTransferTransRequest request, TransactionDTO transactionDTO, AccNumberVerifyDTO accNumberVerifyDTO, MoneyAccount moneyAccountOfCustomer) {
        // Khai báo biến response để return
        FeeTransactionResponse feeTransactionResponse;

        // Verify QR Code để lấy thông tin chi tiết (payment type, merchant info...)
        QrCodeResponse qrCodeResponse = qrCodeService.verifyQrCode(new QrVerifyRequest(request.getBeneficiaryQrValue()));

        // Kiểm tra payment type để phân biệt merchant QR vs transfer QR
        // PaymentTypeQrValue = "QR" cho merchant payment
        if (Validator.equals(qrProperties.getMerchantAccountInfo().getPaymentTypeQrValue(), qrCodeResponse.getPaymentType())) {

            // Đây là QR Code merchant - cần tính phí merchant billing
            // Tạo fee request với thông tin cơ bản
            FeeTransactionRequest feeTransRequest = new FeeTransactionRequest();
            feeTransRequest.setTransactionAmount(request.getAmount());
            feeTransRequest.setBeneficiaryCurrency(request.getBeneficiaryCurrency());

            // Phân biệt merchant quốc tế vs nội địa dựa vào currency
            if (!Validator.equals(request.getBeneficiaryCurrency(), CurrencyType.LAK.name())) {

                // MERCHANT QUỐC TẾ (currency khác LAK: KHR, USD, THB...)
                // Set transaction type là international merchant
                transactionDTO.setType(TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT);

                // Set foreign amount (số tiền ngoại tệ) để tracking
                transactionDTO.setForeignAmount(request.getAmount());

                // Tính phí quốc tế với tỷ giá và compliance fee
                // Method này sẽ tính cả domestic fee (LAK) và foreign fee (ngoại tệ)
                feeTransactionResponse = this.feeService.getFeeLapnetInterPayment(feeTransRequest, accNumberVerifyDTO);

            } else {

                // MERCHANT NỘI ĐỊA (currency = LAK)
                // Set transaction type là LAPNET merchant nội địa
                transactionDTO.setType(TransferTransactionType.QR_CODE_LAPNET_MERCHANT);

                // Tính phí LAPNET billing cho merchant nội địa
                // Có thể có chương trình miễn phí theo số lượng giao dịch/tháng
                feeTransactionResponse = this.feeService.getFeeQrLapnetBilling(feeTransRequest, accNumberVerifyDTO, request.getCustomerCurrency());
            }

        } else {

            // CHUYỂN TIỀN LAPNET (không phải merchant)
            // Đây là QR Code chuyển tiền cá nhân qua LAPNET
            transactionDTO.setType(TransferTransactionType.QR_CODE_LAPNET_TRANSFER);

            // Tính phí chuyển tiền liên ngân hàng tiêu chuẩn
            // Sử dụng fee code TRANSFER_INTER_BANK
            feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                    TransactionFeeTypeCode.TRANSFER_INTER_BANK,
                    // Convert transaction amount sang long, default 0 nếu null
                    Validator.isNotNull(transactionDTO.getTransactionAmount()) ?
                            transactionDTO.getTransactionAmount().longValue() : 0L,
                    // Currency của tài khoản khách hàng
                    moneyAccountOfCustomer.getCurrency());
        }

        // Return fee response đã tính
        return feeTransactionResponse;
    }

    private String validateQrCode(String qrCode) {
        String tag = null;
        int i = 0;
        while (i < qrCode.length()) {
            if (i == 12) {
                tag = qrCode.substring(i, 14);
                break;
            }
            i++;
        }
        return tag;
    }

    @Override
    public OtpTransferTransResponse requestOtpTransferInsurance(OtpTransferInsuranceRequest request) {
        Customer customer = this.getCustomerLogin();

        // tạo giao dịch chuyển tiền
        TransactionDTO transactionDTO = new TransactionDTO(request);

        transactionDTO.setPhoneNumber(customer.getPhoneNumber());
        // tạm thời lấy default branch code vì bên MB Laos hiện tại chỉ có 1 chi nhánh duy nhất
        transactionDTO.setBranchCode(this.customerProperties.getDefaultBranchCode());

        // kiem tra so tai khoan ghi no va so tai khoan ghi co
        // tài khoản ghi nợ
        MoneyAccount moneyAccountOfCustomer = this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(
                request.getCustomerAccNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG1038));

        if (!Validator.equals(moneyAccountOfCustomer.getCustomerId(), customer.getCustomerId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1151);
        }

        transactionDTO.setCustomerId(customer.getCustomerId());

        // Gen message
        if (Validator.isNull(request.getRemark())) {
            transactionDTO.setMessage(StringUtil.join(
                    new String[]{
                            customer.getFullname(), Labels.getLabels(LabelKey.MESSAGE_TRANSFER_MONEY)
                    }, StringPool.SPACE));
        }
        // check ekyc và thời gian hết hạn đổi mật khẩu
        CustomerEkyc ekyc = this.customerEkycRepository.findByCustomerIdAndStatusNot(customer.getCustomerId(), EntityStatus.DELETED.getStatus());

        boolean ekycRequired = Validator.isNull(ekyc) && customer.isEkycRequired();

        // Do bỏ rule hết hạn thay đổi mật khẩu. Nên không check pwExpiredTime
        if (ekycRequired) {
            throw new BadRequestAlertException(ErrorCode.MSG101094);
        }

        FeeTransactionResponse feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                TransactionFeeTypeCode.INSURANCE, Validator.isNotNull(transactionDTO.getTransactionAmount()) ? transactionDTO.getTransactionAmount().longValue() : 0L, moneyAccountOfCustomer.getCurrency());

        // tài khoản ghi có
        // kiem tra neu la chuyen khoan lapnet thi khong check tai khoan nhan
        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())) {
            // lay tien phi giao dich noi bo trong cau hinh

            MoneyAccount creditAccount = this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(
                            request.getBeneficiaryAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus())
                    .orElse(null);

            Customer beneficiaryCustomer =
                    Objects.nonNull(creditAccount)
                            ? this.customerRepository.findByCustomerIdAndReferenceIdIsNull(
                            creditAccount.getCustomerId())
                            : null;

            if (Objects.isNull(beneficiaryCustomer)) {
                _log.info("BeneficiaryCustomer with account number {} has not any MBLAOS user yet",
                        request.getBeneficiaryAccountNumber());
                // nếu khách hàng hưởng thụ chưa có tài khoản mb laos thì gọi sang bên t24 check tk
//                BeneficiaryInfoResponse beneficiaryInfo = this.internalBankMB(request.getBeneficiaryAccountNumber());

                BeneficiaryInfoResponse beneficiaryInfo = BeneficiaryInfoResponse.builder()
                        .beneficiaryName(transactionDTO.getBeneficiaryCustomerName())
                        .accountNumber(transactionDTO.getBeneficiaryAccountNumber())
                        .build();
                transactionDTO.setBeneficiaryCustomerName(beneficiaryInfo.getBeneficiaryName());
            } else {
                transactionDTO.setBeneficiaryCustomerId(beneficiaryCustomer.getCustomerId());
                transactionDTO.setBeneficiaryCustomerName(beneficiaryCustomer.getFullname());
            }
            transactionDTO.setType(TransferTransactionType.INTERNAL_BANK);
        } else {
            //bo sung tinh phi QR lapnet
            AccountType type = request.getType();
            transactionDTO.setType(TransferTransactionType.INTER_BANK);

            // call sang T24 de check tai khoan nhan
            AccNumberVerifyRequest accNumberVerifyRequest = AccNumberVerifyRequest.builder()
                    .toAccount(request.getBeneficiaryAccountNumber())
                    .toMember(request.getBeneficiaryBankCode())
                    .toType(this.mbApiConstantProperties.getOtpGen().getAccountType())
                    .fromUserFullName(customer.getFullname())
                    .fromAccountType(FromAccountType.PERSONAL.name())
                    .internationalQuery(InternationalQuery.N.name())
                    .fromAccount(request.getCustomerAccNumber())
                    .fromUser(customer.getFullname())
                    .build();

            AccNumberVerifyDTO accNumberVerifyDTO = this.apiGeeTransferService.verifyAccNumber(accNumberVerifyRequest);
            if (Validator.isNull(accNumberVerifyDTO) || Validator.isNull(accNumberVerifyDTO.getAccountName())) {
                throw new BadRequestAlertException(ErrorCode.MSG1036);
            }

            transactionDTO.setTransactionId(accNumberVerifyDTO.getReferenceNumber());
        }

        if (Validator.isNotNull(feeTransactionResponse)) {
            transactionDTO.setTransactionFee(feeTransactionResponse.getFee());
            transactionDTO.setDiscount(feeTransactionResponse.getDiscount());
            transactionDTO.setConfigurationFeeType(feeTransactionResponse.getConfigurationFeeType());
        }

        // call API MB để thực hiện tạo OTP
        Transaction transferSaved = this.requestOtpT24(transactionDTO);

        try {
            // set tài khoản mặc định nếu gạt nút setting default account
            if (request.isSetDefaultAccount()) {
                this.setDefaultAccountTransfer(customer.getCustomerId(), request.getCustomerAccNumber());
            }
        } catch (Exception e) {
            _log.error("requestOtpTransferInsurance, after requestOTP error:", e);
        }

        return OtpTransferTransResponse.builder()
                .expiredTime(this.otpProperties.getDuration())
                .requiredOtp(Boolean.TRUE)
                .transferTransactionId(transferSaved.getTransferTransactionId())
                .transactionId(transferSaved.getTransactionId())
                .transData(transactionDTO.getTransData())
                .build();
    }

    @Override
    public OtpTransferTransResponse resendOtp(ResendOtpRequest request) {
        Transaction transaction = this.transactionRepository
                .findById(request.getTransferTransactionId())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1048));

        OtpTransferRequest mbOtpTransferRequest = OtpTransferRequest.builder()
                .accountNumber(transaction.getCustomerAccNumber())
                .accountType(this.mbApiConstantProperties.getOtpGen().getAccountType())
                .build();

        ReqFundTransferDTO response = this.apiGeeTransferService.requestFundTransfer(mbOtpTransferRequest);

        transaction.setTransactionId(response.getReferenceNumber());

        return OtpTransferTransResponse.builder()
                .expiredTime(this.otpProperties.getDuration())
                .requiredOtp(Boolean.TRUE)
                .transferTransactionId(transaction.getTransferTransactionId())
                .transactionId(transaction.getTransactionId())
                .build();
    }

    @Override
    public void setDefaultAccountTransfer(Long customerId, String accountNumber) {
        List<MoneyAccount> moneyAccounts = this.moneyAccountRepository.findByCustomerIdAndStatus(
                customerId, EntityStatus.ACTIVE.getStatus());

        List<MoneyAccountDTO> moneyAccountDTOS = this.moneyAccountMapper.toDto(moneyAccounts);

        moneyAccountDTOS.forEach(MoneyAccountDTO::isNotDefault);

        moneyAccountDTOS.forEach(item -> {
            if (Validator.equals(item.getAccountNumber(), accountNumber)) {
                item.setIsDefault();
            }
        });

        this.moneyAccountRepository.saveAll(this.moneyAccountMapper.toEntity(moneyAccountDTOS));
    }

    /**
     * validate các trường thông tin trong request
     *
     * @param request OtpTransferTransRequest
     */
    public void validateTransferTrans(OtpTransferTransRequest request) {
        // validate số tiền giao dịch
        if (request.getAmount() < 0) {
            throw new BadRequestAlertException(ErrorCode.MSG1111);
        }

        if (request.getAmount() == 0) {
            throw new BadRequestAlertException(ErrorCode.MSG100169);
        }

        if (Validator.equals(request.getCustomerAccNumber(), request.getBeneficiaryAccountNumber())) {
            throw new BadRequestAlertException(ErrorCode.MSG1053);
        }

        if (Validator.equals(request.getType(), AccountType.ACCOUNT)) {
            if (StringUtil.isBlank(request.getBeneficiaryAccountNumber())) {
                throw new BadRequestAlertException(ErrorCode.MSG1031);
            }
        }

        String nationCode = NationCode.LA.name();

        if (!Validator.equals(request.getBeneficiaryCurrency(), CurrencyType.LAK.name())) {
            nationCode = this.commonRepository
                    .findByCategoryAndCodeAndStatus(CommonCategory.NATION_ID.name(), request.getBeneficiaryCurrency(), EntityStatus.ACTIVE.getStatus())
                    .map(Common::getValue).orElse(NationCode.LA.name());
        }

        this.bankRepository.findByBankCodeAndStatusAndNation(request.getBeneficiaryBankCode(), EntityStatus.ACTIVE.getStatus(), nationCode)
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG100162));
    }

    @Override
    public BillingResponse verifyBillingCode(BillingV2Request billingRequest) {
        Customer customer = this.getCustomerLogin();

        LocalDateTime now = LocalDateTime.now();
        Long amount = 0L;
        String fullName = null;
        BillingType billingType = BillingType.getBillingType(billingRequest.getBillingType());
        boolean isPhoneNumber = billingType.equals(BillingType.PSTN) || billingType.equals(BillingType.POSTPAID_MOBILE);

        Telco telco = this.telcoRepository.findByTelcoCodeAndStatus(
                billingRequest.getTelcoCode(), EntityStatus.ACTIVE.getStatus()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG3002));

        MoneyAccount moneyAccount = moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(billingRequest.getAccountNumber(), customer.getCustomerId(), EntityStatus.ACTIVE.getStatus())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1035));

        if (Validator.isNull(billingType)) {
            throw new BadRequestAlertException(ErrorCode.MSG1169);
        }

        BillingResponse billingResponse = BillingResponse.builder()
                .billingAt(now)
                .networkType(telco.getTelcoName())
                .build();

        // call api sang bên nhà mạng
        //UNITEL
        if (Validator.equals(telco.getTelcoCode(), TelcoType.UNITEL.name())) {

            ApiUnitelRequest queryRequest = ApiUnitelRequest.builder()
                    .mti(consumerProperties.getApiUnitel().getMti())
                    .billingType(billingType)
                    .transTime(InstantUtil.formatStringLongTimestamp(Instant.now(), ZoneId.of(StringPool.UTC)))
                    .systemTrace(this.apiUnitelService.generateTransactionId())
                    .clientId(consumerProperties.getApiUnitel().getClientId())
                    .build();

            if (billingType.equals(BillingType.INTERNET)) {
                queryRequest.setCustomerCode(billingRequest.getBillingCode());
            } else {
                queryRequest.setMsisdn(billingRequest.getBillingCode());
            }

            QueryDebtUnitelResponse queryDebtResponse = this.apiUnitelService.queryDebtPostpaidSubscriber(queryRequest);

            if (Objects.isNull(queryDebtResponse) || !queryDebtResponse.isSuccess()) {
                if (isPhoneNumber) {
                    throw new BadRequestAlertException(ErrorCode.MSG1149);
                } else {
                    throw new BadRequestAlertException(ErrorCode.MSG101006);
                }
            }

            amount = queryDebtResponse.getDebt();
            billingResponse.setBalance(amount);
            billingResponse.setFullName(billingRequest.getBillingCode());

            //LTC
        } else if (Validator.equals(telco.getTelcoCode(), TelcoType.LTC.name()) || Validator.equals(telco.getTelcoCode(), TelcoType.TPLUS.name())) {
            VerifyInternetLtcResponse verifyInternetLtcResponse = null;
            VerifyPostPaidLtcResponse verifyPostPaidLtcResponse = null;
            VerifyPstnLtcResponse verifyPstnLtcResponse = null;
            String transactionId = RandomGenerator.generateRandomNumber(1, 23);
            String msisdn = NumberUtil.formatPhoneNumber(billingRequest.getBillingCode());

            if (Validator.equals(billingType, BillingType.INTERNET)) {
                verifyInternetLtcResponse = this.apiMmoneyService.verifyInternet(
                        VerifyInternetLtcMmoneyRequest.builder()
                                .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                                .transactionId(transactionId)
                                .msisdn(mmoneyEncryptService.encrypt(msisdn))
                                .operator(telco.getTelcoCode())
                                .type(BillingType.INTERNET.toString())
                                .build()
                );

                if (Validator.isNull(verifyInternetLtcResponse) || !verifyInternetLtcResponse.isSuccess()) {
                    throw new BadRequestAlertException(ErrorCode.MSG101006);
                }

                amount = Long.valueOf(verifyInternetLtcResponse.getBalance());
                fullName = mmoneyEncryptService.decrypt(verifyInternetLtcResponse.getName());
            } else if (Validator.equals(billingType, BillingType.POSTPAID_MOBILE)) {
                verifyPostPaidLtcResponse = this.apiMmoneyService.verifyPostPaid(
                        VerifyPostPaidLtcMmoneyRequest.builder()
                                .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                                .transactionId(transactionId)
                                .msisdn(mmoneyEncryptService.encrypt(msisdn))
                                .operator(telco.getTelcoCode())
                                .type("POSTPAID")
                                .build()
                );

                if (Validator.isNull(verifyPostPaidLtcResponse) || !verifyPostPaidLtcResponse.isSuccess()) {
                    throw new BadRequestAlertException(ErrorCode.MSG1149);
                }

                amount = GetterUtil.getLong(verifyPostPaidLtcResponse.getBalance());
                fullName = mmoneyEncryptService.decrypt(verifyPostPaidLtcResponse.getName());
            } else if (Validator.equals(billingType, BillingType.PSTN)) {
                verifyPstnLtcResponse = this.apiMmoneyService.verifyPstn(
                        VerifyPstnMmoneyPstnRequest.builder()
                                .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                                .transactionId(transactionId)
                                .msisdn(mmoneyEncryptService.encrypt(msisdn))
                                .operator(telco.getTelcoCode())
                                .type(BillingType.PSTN.toString())
                                .build()
                );

                if (Validator.isNull(verifyPstnLtcResponse) || !verifyPstnLtcResponse.isSuccess()) {
                    throw new BadRequestAlertException(ErrorCode.MSG1149);
                }

                amount = GetterUtil.getLong(verifyPstnLtcResponse.getBalance());
                fullName = mmoneyEncryptService.decrypt(verifyPstnLtcResponse.getName());
            }

            billingResponse.setBalance(amount);
            billingResponse.setFullName(fullName);
        }
        // ETL
        else if (Validator.equals(telco.getTelcoCode(), TelcoType.ETL.name())) {
            QueryEtlResponse queryEtlResponse = new QueryEtlResponse();

            String transactionId = this.apiETLService.generateTransactionId();
            if (billingType.equals(BillingType.INTERNET)) {
                queryEtlResponse = this.apiETLService.queryInternetDebt(this.etlPaymentTopupWSRequest(billingRequest.getBillingCode(), "0", transactionId)); // This interface input 0
            } else if (billingType.equals(BillingType.PSTN)) {
                queryEtlResponse = this.apiETLService.queryPSTNDebt(this.etlPaymentTopupWSRequest(billingRequest.getBillingCode(), "0", transactionId)); // This interface input 0
            } else if (billingType.equals(BillingType.POSTPAID_MOBILE)) {
                queryEtlResponse = this.apiETLService.queryPostPaidDebt(this.etlPaymentTopupWSRequest(billingRequest.getBillingCode(), "0", transactionId)); // This interface input 0
            }

            if (Objects.isNull(queryEtlResponse) || !queryEtlResponse.isSuccess()) {
                if (isPhoneNumber) {
                    throw new BadRequestAlertException(ErrorCode.MSG1149);
                } else {
                    throw new BadRequestAlertException(ErrorCode.MSG101006);
                }
            }

            amount = Long.valueOf(queryEtlResponse.getUnPaidAmount());
            billingResponse.setBalance(amount);
            billingResponse.setFullName(queryEtlResponse.getCustomerName());
        } else if (Validator.equals(telco.getTelcoCode(), TelcoType.BTC.name())) {
            BestTelecomResponse bestTelecomResponse = new BestTelecomResponse();

            if (billingType.equals(BillingType.POSTPAID_MOBILE)) {
                bestTelecomResponse = this.apiBestTelecomService.validateMobileNumber(this.bestTelecomRequest(billingRequest.getBillingCode()));
            } else {
                throw new BadRequestAlertException(ErrorCode.MSG1170);
            }

            amount = Math.round(Math.ceil(bestTelecomResponse.getBillOutStandings().stream().mapToDouble(item -> Double.parseDouble(item.getBalOutstandingAmt())).sum()));
            billingResponse.setBalance(amount);
            billingResponse.setFullName(billingRequest.getBillingCode());
        } else {
            throw new BadRequestAlertException(ErrorCode.MSG1170);
        }

//         validate số tiền thanh toán hóa đơn
        if (Validator.isNull(amount) || amount == 0) {
            throw new BadRequestAlertException(ErrorCode.MSG101021);
        }

        // Tính phí giao dịch
        FeeTransactionResponse feeTransactionResponse;
        if (BillingType.getTransactionFeeCodeByType(billingRequest.getBillingType()) != null) {
            feeTransactionResponse = this.transactionService.getFeeOfTransaction(
                    TransactionFeeTypeCode.valueOf(BillingType.getTransactionFeeCodeByType(billingRequest.getBillingType())),
                    amount, this.customerProperties.getDefaultCurrency());

            billingResponse.setTransactionDiscount(feeTransactionResponse.getDiscount());
            billingResponse.setTransactionFee(feeTransactionResponse.getFee());
            billingResponse.setConfigurationFeeType(feeTransactionResponse.getConfigurationFeeType());
            billingResponse.setDiscountFixed(feeTransactionResponse.getDiscountFixed());
        }

        long totalAmount = Math.round(billingResponse.getBalance() + billingResponse.getTransactionFee() - billingResponse.getDiscountFixed() - billingResponse.getBalance() * billingResponse.getTransactionDiscount() / 100);
        billingResponse.setTotalAmount(totalAmount);
        billingResponse.setStatus(
                moneyAccount.getAvailableAmount() < billingResponse.getTotalAmount() ?
                        EntityStatus.INACTIVE.getStatus() : EntityStatus.ACTIVE.getStatus());
        return billingResponse;
    }

    @Override
    public BillingResponse verifyBillingCode(BillingRequest billingRequest) {
        // call api sang bên nhà mạng
        LocalDateTime now = LocalDateTime.now();
        CheckBalanceLtcResponse checkBalanceLtcResponse = this.apiLtcService.checkBalanceResult(
                VerifyPhoneNumberRequest.builder().phoneNumber(billingRequest.getBillingCode()).build());

        if (Validator.isNull(checkBalanceLtcResponse) ||
                !Validator.equals(checkBalanceLtcResponse.getHeader().getResultCode(),
                        this.consumerProperties.getApiLtc().getSuccessCode())) {
            throw new BadRequestAlertException(ErrorCode.MSG3004);
        }

        Long amount = GetterUtil.getLong(checkBalanceLtcResponse.getBalance());
        // validate số tiền thanh toán hóa đơn

        if (Validator.isNull(amount) || amount == 0) {
            throw new BadRequestAlertException(ErrorCode.MSG1163);
        }

        BillingResponse billingResponse = BillingResponse.builder()
                .networkType(checkBalanceLtcResponse.getNetworkType())
                .balance(amount)
                .billingAt(now)
                .type(checkBalanceLtcResponse.getType())
                .build();

        if (Validator.equals(checkBalanceLtcResponse.getNetworkType(), TelcoType.LTC.name())) {
            billingResponse.setFullName(billingRequest.getBillingCode());
        }

        return billingResponse;
    }

    @Override
    public Page<TransactionDTO> getQrHistory(TransactionSearchRequest request, Pageable pageable) {

        Customer customer = this.getCustomerLogin();
        String countryCodeValue = qrProperties.getMerchantAccountInfo().getCountryCodeValue();
        LocalDateTime localDateTime = LocalDateTime.now(ZoneOffset.UTC).plusHours(7);

        if (request.getFromDate().isAfter(localDateTime.toLocalDate()) || request.getToDate().isAfter(localDateTime.toLocalDate())) {
            throw new BadRequestAlertException(ErrorCode.MSG1060);
        }

        if (request.getFromDate().isAfter(request.getToDate())) {
            throw new BadRequestAlertException(ErrorCode.MSG1058);
        }

        if (Period.between(request.getToDate().minus(this.transferTransProperties.getMaxPeriod().minusDays(1)), request.getFromDate()).isNegative()) {
            throw new BadRequestAlertException(ErrorCode.MSG1096);
        }

        if (request.getFromDate().isBefore(LocalDate.now().minus(this.transferTransProperties.getExceedDatePeriod().minusDays(1)))
                || request.getToDate().isBefore(LocalDate.now().minus(this.transferTransProperties.getExceedDatePeriod().minusDays(1)))) {
            throw new BadRequestAlertException(ErrorCode.MSG1148);
        }

        List<MoneyAccount> moneyAccounts = moneyAccountRepository.findAllByCustomerIdAndStatus(
                customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

        List<String> customerAccounts = moneyAccounts.stream().map(MoneyAccount::getAccountNumber).collect(Collectors.toList());

        List<TransferTransactionType> types = TransferTransactionType.getQrTransaction();
        request.setTypes(types);
        request.setTransactionStatus(Collections.singletonList(TransactionStatus.SUCCESS));
        request.setHasPageable(true);
        request.setCustomerAccNumbers(customerAccounts);
        request.setHistory(true);

        List<TransactionDTO> transactions = this.transactionMapper.toDto(this.transactionRepository.searchTransaction(request, pageable));

        List<String> bankCodes = transactions.stream().map(TransactionDTO::getBankCode).collect(Collectors.toList());
        List<Bank> banks = this.bankRepository.findAllByBankCodeInAndStatusNot(bankCodes, EntityStatus.DELETED.getStatus());

        List<String> merchantCodes = transactions.stream().map(TransactionDTO::getTarget).collect(Collectors.toList());

        // lấy merchant không lấy master merchant
        List<Merchant> merchants = this.merchantRepository.findAllByMerchantCodeInAndStatusNotAndParentIdNotNull(merchantCodes, EntityStatus.DELETED.getStatus());

        Optional<Bank> bankMb = this.bankRepository.findByBankCodeAndStatusAndNation(
                this.mbApiConstantProperties.getInBanKTransfer().getChannel(),
                EntityStatus.ACTIVE.getStatus(), request.getNation());

        transactions.forEach(item -> {
            if (TransferTransactionType.getQrLapnetTransaction().contains(item.getType())) {
                item.setBeneficiaryAccountNumber(null);
            }
            banks.forEach(bank -> {
                if (!Validator.equals(item.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)) {
                    if (Validator.equals(bank.getBankCode(), item.getBankCode()) && Validator.equals(bank.getNation(), countryCodeValue)) {
                        item.setBankName(bank.getBankName());
                        item.setNation(countryCodeValue);
                    }
                } else {
                    if (Validator.equals(bank.getBankCode(), item.getBankCode())) {
                        item.setBankName(bank.getBankName());
                        item.setNation(bank.getNation());
                    }
                }
            });
            merchants.forEach(merchant -> {
                if (Validator.equals(merchant.getMerchantCode(), item.getTarget())) {
                    item.setMerchant(merchant);
                }
            });
            if (TransferTransactionType.QR_CODE_INTERNAL_MERCHANT.equals(item.getType()) && bankMb.isPresent()) {
                item.setBankName(bankMb.get().getBankName());
            }
        });

        return new PageImpl<>(transactions, pageable, transactions.size());
    }

    @Override
    public Set<CashInHistoryResponse> getHistoryCashIn(CashInHistorySearch request) {

        Customer customer = this.getCustomerLogin();

        Pageable pageable = PageRequest.of(request.getPageIndex(), request.getPageSize());

        List<Transaction> transactions = this.transactionRepository.getCashInHistories(request, pageable, customer.getCustomerId());

        Set<CashInHistoryResponse> responses = new LinkedHashSet<>();

        transactions.forEach(transaction -> {

            CashInHistoryResponse cashInHistoryResponse = CashInHistoryResponse.builder()
                    .phoneNumber(transaction.getTarget())
                    .accountName(transaction.getBeneficiaryCustomerName())
                    .amount(transaction.getTransactionAmount().longValue())
                    .build();
            responses.add(cashInHistoryResponse);
        });

        return responses;
    }

    @Override // Ghi đè method từ interface TransferService
    public TransactionDTO getDetailQrHistory(TransactionSearchRequest request) {

        // Lấy thông tin customer hiện tại đang đăng nhập
        Customer customer = this.getCustomerLogin();

        // Kiểm tra transfer transaction ID có được cung cấp không
        if (Validator.isNull(request.getTransferTransactionId())) {
            // Throw exception nếu thiếu transfer transaction ID
            throw new BadRequestAlertException(ErrorCode.MSG100171);
        }

        // Lấy danh sách tất cả money account của customer với trạng thái ACTIVE
        List<MoneyAccount> moneyAccounts = moneyAccountRepository.findAllByCustomerIdAndStatus(
                customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

        // Tìm money account cụ thể theo số tài khoản trong request
        MoneyAccount moneyAccount = moneyAccounts.stream()
                .filter(item -> item.getAccountNumber().equals(request.getCustomerAccNumber())) // Lọc theo số tài khoản
                .findFirst().orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1038)); // Throw exception nếu không tìm thấy

        // Tìm transaction theo transfer transaction ID, số tài khoản, trạng thái SUCCESS và loại QR transaction
        Optional<Transaction> transaction = this.transactionRepository.findByTransferTransactionIdAndCustomerAccNumberAndTransactionStatusAndTypeIn(
                request.getTransferTransactionId(), // Transfer transaction ID cần tìm
                moneyAccount.getAccountNumber(), // Số tài khoản customer
                TransactionStatus.SUCCESS, // Chỉ lấy transaction thành công
                TransferTransactionType.getQrTransaction()); // Các loại QR transaction

        // Kiểm tra transaction có tồn tại không
        if (!transaction.isPresent()) {
            // Throw exception nếu không tìm thấy transaction
            throw new BadRequestAlertException(ErrorCode.MSG100167);
        }

        // Chuyển đổi transaction entity thành DTO
        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction.get());

        // Kiểm tra nếu là QR LAPNET transaction thì ẩn số tài khoản người thụ hưởng
        if (TransferTransactionType.getQrLapnetTransaction().contains(transaction.get().getType())) {
            // Set beneficiary account number thành null để ẩn thông tin nhạy cảm
            transactionDTO.setBeneficiaryAccountNumber(null);
        }

        // Khai báo biến bank để lưu thông tin ngân hàng
        Bank bank;

        // Xác định ngân hàng dựa trên loại transaction
        bank = TransferTransactionType.QR_CODE_INTERNAL_MERCHANT.equals(transactionDTO.getType()) ?
                // Nếu là QR internal merchant thì dùng channel từ properties
                this.bankRepository.findByBankCodeAndNationAndStatusNot(
                        this.mbApiConstantProperties.getInBanKTransfer().getChannel(), // Bank code từ properties
                        request.getNation(), // Quốc gia từ request
                        EntityStatus.DELETED.getStatus()) // Loại trừ bank đã bị xóa
                :
                // Nếu không phải internal merchant thì dùng bank code từ transaction
                this.bankRepository.findByBankCodeAndNationAndStatusNot(
                        transactionDTO.getBankCode(), // Bank code từ transaction
                        request.getNation(), // Quốc gia từ request
                        EntityStatus.DELETED.getStatus()); // Loại trừ bank đã bị xóa

        // Tìm thông tin merchant theo merchant code (target) và loại trừ merchant đã bị xóa
        // Chỉ lấy merchant con (có parent ID)
        Merchant merchant = this.merchantRepository.findByMerchantCodeAndStatusAndParentIdIsNotNull(
                transactionDTO.getTarget(), // Merchant code (target của transaction)
                EntityStatus.DELETED.getStatus()); // Loại trừ merchant đã bị xóa

        // Nếu tìm thấy thông tin ngân hàng thì set tên ngân hàng vào transaction DTO
        if (Validator.isNotNull(bank)) {
            transactionDTO.setBankName(bank.getBankName());
        }

        // Nếu tìm thấy thông tin merchant thì set merchant vào transaction DTO
        if (Validator.isNotNull(merchant)) {
            transactionDTO.setMerchant(merchant);
        }

        // Trả về transaction DTO đã được bổ sung thông tin bank và merchant
        return transactionDTO;
    }

    private OtpTransferConfirmResponse confirmTransferToMerchant(TransactionDTO transactionDTO, String otp, String deviceId) {

        // kiểm tra trạng thái của giao dịch
        if (Validator.equals(transactionDTO.getTransactionStatus(), TransactionStatus.SUCCESS)) {
            throw new BadRequestAlertException(ErrorCode.MSG1104);
        }

        Transaction transaction = this.transactionMapper.toEntity(transactionDTO);
        double amount = Validator.equals(transactionDTO.getType(), TransferTransactionType.QR_CODE_INTERNATIONAL_MERCHANT)
                ? transactionDTO.getForeignAmount() : transactionDTO.getTransactionAmount();

        // limit transaction
        this.transactionService.checkTransactionLimit(String.valueOf(transactionDTO.getTransferType()), transactionDTO.getCustomerId(),
                transactionDTO.getCustomerAccNumber(), amount, transactionDTO.getType(), transaction);

        Optional<TransactionMerchant> transactionMerchant = this.transactionMerchantRepository
                .findByTransferTransactionId(transactionDTO.getTransferTransactionId());

        if (!transactionMerchant.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG100010);
        }

        Merchant merchant = this.merchantRepository
                .findByMerchantIdAndStatus(transactionMerchant.get().getMerchantId(),
                        EntityStatus.ACTIVE.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }


        MBOtpTransConfirmResponse response = this.confirmFundTransferMB(transactionDTO, otp, deviceId);

        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.TIMEOUT)) {
            _log.info("Transaction has been timeout, {}", transactionDTO.getTransactionId());

            InquiryTransactionStatusRequest transactionStatusRequest = InquiryTransactionStatusRequest.builder()
                    .transactionId(response.getReferenceNumber())
                    .build();

            InquiryTransactionStatusDTO inquiryTransactionStatusDTO = this.apiGeeTransferService
                    .inquiryTransactionStatus(transactionStatusRequest);

            if (Validator.isNull(inquiryTransactionStatusDTO)
                    || Validator.isNotNull(inquiryTransactionStatusDTO.getErrorCode())) {

                transactionDTO.setTransactionStatus(TransactionStatus.FAILED);
                this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));

                throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
            }
        }

        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.FAILED)) {
            _log.info("Transaction has been failed, {}, status {}", transactionDTO.getTransactionId(),
                    response.getTransactionStatus());

            transactionDTO.setTransactionStatus(TransactionStatus.FAILED);
            transactionDTO.setTransactionFinishTime(Instant.now());

            this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));

            if (Validator.isNotNull(response.getT24ErrorCode())) {
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            }
            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
        }

        // Thanh toán hóa đơn
        if (Validator.isNotNull(transactionDTO.getBillingType())) {
            Optional<Telco> telco = telcoRepository.findByTelcoCodeAndStatus(merchant.getMerchantCode(), EntityStatus.ACTIVE.getStatus());
            this.makeBillPayment(response, telco, transactionDTO, transactionMerchant.get());
        }

        if (Validator.equals(merchant.getMerchantCode(), MerchantCode.UMONEY.name())) {
            TransferAccountResponse transferUmoneyResponse = this.ewalletService.transferUmoney(
                    TransferAccountRequest.builder()
                            .phoneNumber(transactionDTO.getTarget())
                            .content(transactionDTO.getMessage())
                            .amount(Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L))
                            .bankAccount(transactionDTO.getCustomerAccNumber())
                            .bankTransId(response.getT24ReferenceNumber())
                            .build());

            // revert giao dịch
            if (Validator.isNull(transferUmoneyResponse) ||
                    !Validator.equals(
                            transferUmoneyResponse.getErrorCode(),
                            this.consumerProperties.getApiUmoney().getSuccessCode())) {

                this.apiGeeTransferService.revert(RevertTransactionRequest.builder()
                        .t24ReferenceNumber(response.getT24ReferenceNumber())
                        .transactionId(response.getReferenceNumber())
                        .build());

                throw new BadRequestAlertException(ErrorCode.MSG1162);
            } else {
                transactionDTO.setExtraTransactionCode(transferUmoneyResponse.getTransId());
            }
        }

        transactionDTO.setTransactionStatus(TransactionStatus.SUCCESS);
        transactionDTO.setTransactionFinishTime(Instant.now());
        transactionDTO.setTransactionCode(response.getT24ReferenceNumber());

        this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));

        return OtpTransferConfirmResponse.builder()
                .transactionTime(Instant.now())
                .transactionCode(response.getT24ReferenceNumber())
                .transactionFee(transactionDTO.getTransactionFee())
                .transactionId(transactionDTO.getTransactionId())
                .build();
    }

    private String formatMoney(Long price) {
        DecimalFormat decimalFormat = new DecimalFormat(StringPool.CURRENCY_FORMAT);
        return decimalFormat.format(price);
    }

    private String formatMoney(Double price) {
        DecimalFormat decimalFormat = new DecimalFormat(StringPool.CURRENCY_FORMAT_DECIMAL_FOREIGN);
        return decimalFormat.format(price);
    }

    private void createNotification(TransactionDTO transactionDTO, Long customerId) {
        Map<String, String> valuesMapDebitAccount = new HashMap<>();
        Map<String, String> valuesMapCreditAccount = new HashMap<>();

        String transactionFinishTime = transactionDTO.formatTransactionFinishTime();

        // transfer
        valuesMapDebitAccount.put(TemplateField.LABEL_REFERENCE_ID.name(), Labels.getLabels(LabelKey.LABEL_REFERENCE_ID));
        valuesMapDebitAccount.put(TemplateField.REFERENCE_ID.name(), transactionDTO.getTransactionId());
        valuesMapDebitAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(), Labels.getLabels(LabelKey.LABEL_ACCOUNT_TYPE_PAYMENT_ACCOUNT));
        valuesMapDebitAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), transactionDTO.getCustomerAccNumber());
        valuesMapDebitAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(), Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION));
        valuesMapDebitAccount.put(TemplateField.TRANSACTION_AMOUNT.name(), StringPool.MINUS + formatMoney(transactionDTO.getActualTransactionAmount()));
        valuesMapDebitAccount.put((TemplateField.CURRENCY.name()), transactionDTO.getTransactionCurrency());
        valuesMapDebitAccount.put((TemplateField.LABEL_TRANSACTION_TIME.name()), Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));
        valuesMapDebitAccount.put(TemplateField.TRANSACTION_TIME.name(), transactionFinishTime);
        valuesMapDebitAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(), Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT));
        valuesMapDebitAccount.put((TemplateField.MESSAGE.name()), transactionDTO.getMessage());
        valuesMapDebitAccount.put((TemplateField.TRANSACTION_CODE.name()), transactionDTO.getTransactionCode());

        // receive (chuyển khoản liên ngân hàng không có thông báo biến động số dư tài khoản nhận tiền)
        valuesMapCreditAccount.put(TemplateField.LABEL_REFERENCE_ID.name(), Labels.getLabels(LabelKey.LABEL_REFERENCE_ID));
        valuesMapCreditAccount.put(TemplateField.REFERENCE_ID.name(), transactionDTO.getTransactionId());
        valuesMapCreditAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(), Labels.getLabels(LabelKey.LABEL_BENEFICIARY_ACCOUNT));
        valuesMapCreditAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), transactionDTO.getTarget());
        valuesMapCreditAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(), Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION));
        valuesMapCreditAccount.put(TemplateField.TRANSACTION_AMOUNT.name(), StringPool.PLUS + formatMoney(transactionDTO.getTransactionAmount()));
        valuesMapCreditAccount.put((TemplateField.CURRENCY.name()), transactionDTO.getTransactionCurrency());
        valuesMapCreditAccount.put((TemplateField.LABEL_TRANSACTION_TIME.name()), Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));
        valuesMapCreditAccount.put(TemplateField.TRANSACTION_TIME.name(), transactionFinishTime);
        valuesMapCreditAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(), Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT));
        valuesMapCreditAccount.put((TemplateField.MESSAGE.name()), transactionDTO.getMessage());
        valuesMapCreditAccount.put((TemplateField.TRANSACTION_CODE.name()), transactionDTO.getTransactionCode());

        ContentTemplate templateTransferMoney = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.BALANCE_CHANGE_TRANSFER_MONEY.name(), EntityStatus.ACTIVE.getStatus());

        ContentTemplate templateTransferLapnet = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.BALANCE_CHANGE_TRANSFER_LAPNET.name(), EntityStatus.ACTIVE.getStatus()
        );

        NotificationDTO notiForReceiver = new NotificationDTO();
        NotificationDTO notiForSender = new NotificationDTO();

        if (Validator.isNotNull(templateTransferMoney) || Validator.isNotNull(templateTransferLapnet)) {
            notiForReceiver.setNotificationStatus(NotificationStatus.PENDING);
            notiForReceiver.setNotificationType(NotificationType.TRANSFER_TRANSACTION);
            notiForReceiver.setTargetType(TargetType.USER);
            notiForReceiver.setEndUserType(EndUserType.CUSTOMER);
            notiForReceiver.setClassPk(transactionDTO.getCustomerId());
            notiForReceiver.setPublishTime(LocalDateTime.now());

            if (Validator.equals(transactionDTO.getBankCode(), this.constantProperties.getUmoney().getAccountBank())) {
                notiForReceiver.setTitle(Labels.getLabels(templateTransferMoney.getTitle()));
                notiForReceiver
                        .setContent(StringUtil.replaceMapValue(templateTransferMoney.getContent(), valuesMapDebitAccount));
                notiForReceiver.setDescription(templateTransferMoney.getDescription());
            } else {
                notiForReceiver.setTitle(Labels.getLabels(templateTransferLapnet.getTitle()));
                notiForReceiver
                        .setContent(StringUtil.replaceMapValue(templateTransferLapnet.getContent(), valuesMapDebitAccount));
                notiForReceiver.setDescription(templateTransferLapnet.getDescription());
            }

            Notification notificationAfterSaved = this.notificationRepository
                    .save(this.notificationMapper.toEntity(notiForReceiver));
            notiForReceiver = this.notificationMapper.toDto(notificationAfterSaved);

            // save notification transaction
            NotiTransaction notiTransaction = new NotiTransaction();

            notiTransaction.setNotificationId(notificationAfterSaved.getNotificationId());
            notiTransaction.setTransactionId(transactionDTO.getTransferTransactionId());
            notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus());

            this.notiTransactionRepository.save(notiTransaction);
        }


        ContentTemplate templateReceiveMoney = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.BALANCE_CHANGE_RECEIVE_MONEY.name(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(templateReceiveMoney)) {

            notiForSender.setNotificationStatus(NotificationStatus.PENDING);
            notiForSender.setNotificationType(NotificationType.TRANSFER_TRANSACTION);
            notiForSender.setTargetType(TargetType.USER);
            notiForSender.setEndUserType(EndUserType.CUSTOMER);

            // Neu giao dich la chuyen tien cho các tài khoản bên ngoài hệ thống => ko gửi
            // thông báo
            List<MoneyAccount> moneyAccounts = this.moneyAccountRepository
                    .findByAccountNumberAndStatus(transactionDTO.getTarget(), EntityStatus.ACTIVE.getStatus());

            List<NotiTransaction> notiTransactions = new ArrayList<>();
            for (MoneyAccount moneyAccount : moneyAccounts) {
                Long beneficiaryCustomerId = moneyAccount.getCustomerId();

                notiForSender.setClassPk(beneficiaryCustomerId);

                notiForSender.setPublishTime(LocalDateTime.now());

                notiForSender.setTitle(Labels.getLabels(templateReceiveMoney.getTitle()));
                notiForSender.setContent(
                        StringUtil.replaceMapValue(templateReceiveMoney.getContent(), valuesMapCreditAccount));
                notiForSender.setDescription(templateReceiveMoney.getDescription());

                Notification notificationAfterSaved = this.notificationRepository
                        .save(this.notificationMapper.toEntity(notiForSender));

                notiForSender = this.notificationMapper.toDto(notificationAfterSaved);

                // save notification transaction
                NotiTransaction notiTransaction = new NotiTransaction();

                notiTransaction.setNotificationId(notificationAfterSaved.getNotificationId());
                notiTransaction.setTransactionId(transactionDTO.getTransferTransactionId());
                notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus());

                notiTransactions.add(notiTransaction);
            }

            if (Validator.equals(notiForSender.getNotificationType(), NotificationType.TRANSFER_TRANSACTION)) {
                this.sendService.sendNotification(notiForSender);
            }
            if (Validator.equals(notiForReceiver.getNotificationType(), NotificationType.TRANSFER_TRANSACTION)) {
                this.sendService.sendNotification(notiForReceiver);
            }

            this.notiTransactionRepository.saveAll(notiTransactions);
        }
    }

    private void createTransactionMerchant(Long transferTransactionId, Long merchantId, Integer merchantType) {
        TransactionMerchant transactionMerchant = new TransactionMerchant();

        transactionMerchant.setTransferTransactionId(transferTransactionId);

        transactionMerchant.setMerchantId(merchantId);

        transactionMerchant.setType(merchantType);

        this.transactionMerchantRepository.save(transactionMerchant);
    }

    private void enrichInformationHistory(List<TransactionHistoryDTO> transactionHistoryDTOS) {
        Customer customer = this.getCustomerLogin();

        // get transactionId
        List<String> transactionCodes = transactionHistoryDTOS.stream()
                .map(TransactionHistoryDTO::getReferenceNumber)
                .collect(Collectors.toList());

        // query transaction by transactionId and enrich to dto
        List<Transaction> transactions = this.transactionRepository.findByTransactionCodes(transactionCodes,
                customer.getCustomerId(), Collections.singletonList(TransactionStatus.SUCCESS));

        // get bank list
        List<String> bankCodes = transactions.stream().map(Transaction::getBankCode).collect(Collectors.toList());

        List<Bank> banks = this.bankRepository.findAllByBankCodeInAndStatus(bankCodes, EntityStatus.ACTIVE.getStatus());

        // get beneficiary account list
        List<String> accountNumbers = transactions.stream()
                .map(Transaction::getTarget)
                .distinct()
                .collect(Collectors.toList());

        List<MoneyAccount> moneyAccounts = this.moneyAccountRepository
                .findAllByMoneyAccountsAndStatus(accountNumbers, EntityStatus.ACTIVE.getStatus());

        List<MoneyAccountDTO> moneyAccountDTOS = this.moneyAccountMapper.toDto(moneyAccounts);

        // Tài khoản bên phía lapnet thì sẽ không tồn tại trong hệ thống nên sẽ chỉ lấy thông tin tài khoản để xử lý
        List<String> accountNumberInternal = moneyAccountDTOS.stream()
                .map(MoneyAccountDTO::getAccountNumber)
                .collect(Collectors.toList());

        accountNumbers.removeAll(accountNumberInternal);
        accountNumbers.forEach(accountNumber -> moneyAccountDTOS.add(new MoneyAccountDTO(accountNumber)));

        List<Long> customerIds = moneyAccounts.stream().map(MoneyAccount::getCustomerId).collect(Collectors.toList());
        List<Customer> customers = this.customerRepository.findAllByCustomerIdInAndStatus(customerIds,
                EntityStatus.ACTIVE.getStatus());

        // map information of customer to money account
        // nếu tài khoản tồn tại trong hệ thống => get thông tin từ bảng customers
        // nếu tài khoản ko tồn tại trong hệ thống => get thông tin từ transactions
        moneyAccountDTOS.forEach(item -> {
            Optional<Customer> optionalCustomer = customers.stream()
                    .filter(c -> Validator.equals(Long.valueOf(c.getCustomerId()), item.getCustomerId()))
                    .findFirst();
            if (optionalCustomer.isPresent()) {
                item.setCustomerName(optionalCustomer.get().getFullname());
            } else {
                Optional<Transaction> optionalTransaction = transactions.stream()
                        .filter(t -> Validator.equals(t.getTarget(), item.getAccountNumber()))
                        .findFirst();

                optionalTransaction.ifPresent(t -> item.setCustomerName(t.getBeneficiaryCustomerName()));
            }
        });

        // enrich các thông tin vào lịch sử giao dịch
        transactionHistoryDTOS
                .forEach(item -> this.processHistoryTransaction(item, transactions, moneyAccountDTOS, banks));

    }

    /**
     * enrich thêm các thông tin như nội dung thu phí, thông tin ngân hàng nhận hoặc các thông tin thêm (nếu có)
     *
     * @param otpVerifyTransferRequest OtpVerifyTransferRequest
     * @param transaction              void
     */
    private void enrichVerifyTransferRequest(OtpVerifyTransferRequest otpVerifyTransferRequest,
                                             TransactionDTO transaction) {
        List<AddInfoList> addInfoList = new ArrayList<>();

        AddInfoList benAcc = new AddInfoList();

        benAcc.setName(this.mbApiConstantProperties.getLapnetTransfer().getAddInforNo());
        benAcc.setType(this.mbApiConstantProperties.getLapnetTransfer().getAddInforType());
        benAcc.setValue(this.mbApiConstantProperties.getLapnetTransfer().getAddInforNoValue());

        AddInfoList benCustomer = new AddInfoList();

        benCustomer.setName(this.mbApiConstantProperties.getLapnetTransfer().getAddInforName());
        benCustomer.setType(this.mbApiConstantProperties.getLapnetTransfer().getAddInforType());

        // @TODO khách hàng confirm với tài khoản <= 11 ký tự phải cộng thêm chuỗi ở đằng trước
        int customerAccountLength = transaction.getCustomerAccNumber().length();

        if (customerAccountLength <= Integer.parseInt(StringPool.MIN_CUSTOMER_ACCOUNT_LENGTH)) {
            int endLength = Integer.parseInt(StringPool.CUSTOMER_ACCOUNT_LENGTH) - customerAccountLength;
            benCustomer.setValue(UUIDUtil.generateRandomAlpha(endLength) + transaction.getCustomerAccNumber());
        } else {
            benCustomer.setValue(transaction.getCustomerAccNumber());
        }

        // enrich creditBank nếu là ck lapnet (bankCode != MB)
        if (Validator.equals(transaction.getTransferType(), TransferType.TRANSFER_MONEY) &&
                !Validator.equals(
                        transaction.getBankCode(), this.mbApiConstantProperties.getInBanKTransfer().getChannel())) {
            CreditBank creditBank = new CreditBank();

            creditBank.setCode(transaction.getBankCode());

            otpVerifyTransferRequest.setCreditBank(creditBank);

            benAcc.setValue(this.mbApiConstantProperties.getLapnetTransfer().getAddInforNoValue());
        }

        addInfoList.add(benAcc);
        addInfoList.add(benCustomer);

        // enrich chargeInfo
        ChargeInfo chargeInfo = new ChargeInfo();

        chargeInfo.setAmount(String.valueOf(Math.round(transaction.getTransactionFee())));
        chargeInfo.setCurrency(transaction.getTransactionCurrency());
        chargeInfo.setDescription(transaction.getMessage());

        otpVerifyTransferRequest.setAddInfoList(addInfoList);
        otpVerifyTransferRequest.setChargeInfo(chargeInfo);
    }

    private Customer getCustomerLogin() {
        return GwSecurityUtils.getCustomerLogin()
                .orElseThrow(() -> new UnauthorizedException(ErrorCode.MSG1077));
    }

    /**
     * Xử lý chuyển tiền liên ngân hàng qua LAPNET
     *
     * Method này xử lý verify thông tin tài khoản người nhận ở ngân hàng khác:
     * 1. Extract thông tin từ request (account, bank, type...)
     * 2. Xác định currency dựa vào loại account (QR vs Account)
     * 3. Gọi LAPNET API để verify account ở ngân hàng đích
     * 4. Parse response và return thông tin beneficiary
     *
     * Khác với chuyển tiền nội bộ:
     * - Phải gọi qua LAPNET network
     * - Có thể có phí liên ngân hàng
     * - Cần verify bank code và account tồn tại
     * - Có thể có delay do network latency
     *
     * @param beneficiaryConfirmRequest Thông tin xác nhận người thụ hưởng
     * @return BeneficiaryInfoResponse Thông tin người nhận đã verify
     * @throws BadRequestAlertException Khi account không tồn tại hoặc bank không hợp lệ
     */
    private BeneficiaryInfoResponse interbank(BeneficiaryConfirmRequest beneficiaryConfirmRequest) {
        // Extract account number của người gửi (để tracking và audit)
        String accountNumber = beneficiaryConfirmRequest.getAccountNumber();

        // Extract bank code của ngân hàng đích (ví dụ: BCEL, LDB, APB...)
        String bankCode = beneficiaryConfirmRequest.getBankCode();

        // Extract account number của người nhận (cần verify)
        String beneficiaryAccNumber = beneficiaryConfirmRequest.getBeneficiaryAccountNumber();

        // Extract loại account: ACCOUNT (số tài khoản) hoặc QR (QR Code)
        AccountType type = beneficiaryConfirmRequest.getType();

        // Biến để lưu currency, sẽ được xác định dựa vào type
        String currency = null;

        Customer customer = this.getCustomerLogin();

        if (Validator.isNotNull(accountNumber)) {
            // kiem tra so tai khoan ghi no va so tai khoan ghi co
            // tài khoản ghi nợ
            MoneyAccount moneyAccountOfCustomer = this.moneyAccountRepository.findByAccountNumberAndCustomerIdAndStatus(
                    accountNumber, customer.getCustomerId(), EntityStatus.ACTIVE.getStatus()).orElseThrow(
                    () -> new BadRequestAlertException(ErrorCode.MSG1038));

            // Double check tài khoản thuộc về khách hàng (bảo mật bổ sung), tránh trường hợp tài khoản bị hack
            if (!Validator.equals(moneyAccountOfCustomer.getCustomerId(), customer.getCustomerId())) {
                throw new BadRequestAlertException(ErrorCode.MSG1151);
            }
        } else { //xử lý trường hợp client không truyền accountNumber
            // lấy tk trong danh sách tk
            List<MoneyAccount> moneyAccounts = this.moneyAccountRepository.findByCustomerIdAndStatus(
                    customer.getCustomerId(), EntityStatus.ACTIVE.getStatus());

            // lấy ra tài khoản default
            if (!moneyAccounts.isEmpty()) {
                MoneyAccount defaultAccount =
                        moneyAccounts.stream().filter(MoneyAccount::isDefault).findFirst().orElse(null);

                // nếu ko có tài khoản mặc định thì lấy tài khoản đầu tiên
                if (Objects.isNull(defaultAccount)) {
                    defaultAccount = moneyAccounts.get(QueryUtil.FIRST_INDEX);
                }

                // set accountNumber để verify
                accountNumber = defaultAccount.getAccountNumber();
            }
        }

        // Tạo request cho LAPNET API verify account
        AccNumberVerifyRequest request = AccNumberVerifyRequest.builder()
                .toAccount(beneficiaryAccNumber)
                .toMember(bankCode)
                .toType(AccountType.QR.equals(type) ? this.mbApiConstantProperties.getOtpGen().getQrType() : this.mbApiConstantProperties.getOtpGen().getAccountType())
                .fromAccount(accountNumber)
                .fromUser(customer.getFullname())
                .fromUserFullName(customer.getFullname())
                .fromAccountType(FromAccountType.PERSONAL.name())
                .internationalQuery(InternationalQuery.N.name())
                .build();

        // Xác định currency cho thanh toán quốc tế dựa vào ToMember
        // Method này sẽ map ToMember (payment system) sang currency code
        // Ví dụ: ToMember="CAMBODIA" → Currency="KHR"
        // Đồng thời set international flags cho request
        currency = this.getCurrencyInterPayment(request);

        // Gọi LAPNET API để verify account ở ngân hàng đích
        AccNumberVerifyDTO accNumberVerifyDTO = this.apiGeeTransferService.verifyAccNumber(request);

        Double rate = null;
        if (Validator.isNotNull(accNumberVerifyDTO.getCrossBorderExchangeRate())) {
            rate = Double.parseDouble(accNumberVerifyDTO.getCrossBorderExchangeRate().getRate());
        }

        return BeneficiaryInfoResponse.builder()
                .beneficiaryName(accNumberVerifyDTO.getAccountName())
                .accountNumber(accNumberVerifyDTO.getCreditCard())
                .currency(Validator.isNotNull(currency) ? currency : accNumberVerifyDTO.getAccountCurrency())
                .feeList(accNumberVerifyDTO.getFeeList().getLak())
                .rate(rate)
                .build();
    }

    /**
     * Lấy currency cho thanh toán quốc tế dựa vào ToMember
     *
     * Method này xử lý mapping từ ToMember (payment system) sang currency code:
     * 1. Lấy tất cả nation mapping từ master data
     * 2. Tìm nation dựa vào ToMember (CAMBODIA, THAILAND...)
     * 3. Validate nation có được hỗ trợ không (block Vietnam)
     * 4. Set international flags cho request
     * 5. Return currency code tương ứng
     *
     * Master data mapping:
     * - ToMember "CAMBODIA" → Nation "KH" → Currency "KHR"
     * - ToMember "THAILAND" → Nation "TH" → Currency "THB"
     * - ToMember "VIETNAM" → Nation "VN" → BLOCKED (MSG101066)
     *
     * @param request AccNumberVerifyRequest sẽ được modify với international flags
     * @return String Currency code (KHR, THB...) hoặc null nếu không tìm thấy
     * @throws BadRequestAlertException Khi ToMember là Vietnam (bị block)
     */
    private String getCurrencyInterPayment(AccNumberVerifyRequest request) {
        // Lấy tất cả nation mapping từ master data
        // Category NATION_ID chứa mapping: code=currency, value=nation, description=payment_system
        List<Common> commons = this.commonRepository
                .findAllByCategoryAndStatus(CommonCategory.NATION_ID.name(), EntityStatus.ACTIVE.getStatus());

        // Tìm nation dựa vào ToMember (payment system name)
        // Ví dụ: ToMember="CAMBODIA" → tìm record có description="CAMBODIA"
        Optional<Common> common = commons.stream()
                .filter(item -> Validator.equals(item.getDescription(), request.getToMember()))
                .findFirst();

        // Nếu không tìm thấy nation mapping thì return null
        // Có thể là payment system không được hỗ trợ
        if (!common.isPresent()) {
            return null;
        }

        // Kiểm tra nếu là Vietnam thì block (business rule)
        // Vietnam hiện tại không được hỗ trợ cho international payment
        if (Validator.equals(common.get().getValue(), NationCode.VN.name())) {
            throw new BadRequestAlertException(ErrorCode.MSG101066);
        }

        // Set international query flag = Y để T24 biết đây là giao dịch quốc tế
        // T24 sẽ áp dụng logic khác cho international transaction
        request.setInternationalQuery(InternationalQuery.Y.name());

        // Set nation code (ví dụ: "KH" cho Cambodia, "TH" cho Thailand)
        // T24 cần nation code để routing và compliance
        request.setToNation(common.get().getValue());

        // Set payment system name (ví dụ: "CAMBODIA", "THAILAND")
        // Confirm lại ToMember để đảm bảo consistency
        request.setToMember(common.get().getDescription());

        // Return currency code (ví dụ: "KHR" cho Cambodia, "THB" cho Thailand)
        // Currency code được sử dụng để tính tỷ giá và fee
        return common.get().getCode();
    }

    @NotNull
    private Common getCommon(String otpTransRequest) {
        Optional<Common> common = this.commonRepository
                .findByCategoryAndCodeAndStatus(CommonCategory.NATION_ID.name(),
                        otpTransRequest, EntityStatus.ACTIVE.getStatus());

        if (!common.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG100056);
        }
        return common.get();
    }

    private BeneficiaryInfoResponse getCustomerInterBankInfo(BeneficiaryConfirmRequest beneficiaryConfirmRequest) {
        String clientName = SecurityContextHolder.getContext().getAuthentication().getName();
        String accountNumber = "";
        String accountName = "";
        if (Objects.equals(clientName, "umoney")) {
            CashoutAccount cashoutAccount = cashoutAccountRepository.findFirstByStatusIs(EntityStatus.ACTIVE.getStatus())
                    .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101054));
            accountNumber = cashoutAccount.getAccountNumber();
            accountName = cashoutAccount.getAccountName();
        }
        String bankCode = beneficiaryConfirmRequest.getBankCode();
        String beneficiaryAccNumber = beneficiaryConfirmRequest.getBeneficiaryAccountNumber();
        AccountType type = beneficiaryConfirmRequest.getType();

        AccNumberVerifyRequest request = AccNumberVerifyRequest.builder()
                .fromAccount(accountNumber)
                .fromUser(accountName)
                .toAccount(beneficiaryAccNumber)
                .toMember(bankCode)
                .toType(AccountType.QR.equals(type) ? this.mbApiConstantProperties.getOtpGen().getQrType() : this.mbApiConstantProperties.getOtpGen().getAccountType())
                .fromUserFullName(accountName)
                .fromAccountType(FromAccountType.PERSONAL.name())
                .internationalQuery(InternationalQuery.N.name())
                .build();

        AccNumberVerifyDTO accNumberVerifyDTO = this.apiGeeTransferService.verifyAccNumber(request);

        return BeneficiaryInfoResponse.builder()
                .beneficiaryName(accNumberVerifyDTO.getAccountName())
                .accountNumber(accNumberVerifyDTO.getCreditCard())
                .currency(accNumberVerifyDTO.getAccountCurrency())
                .feeList(accNumberVerifyDTO.getFeeList().getLak())
                .referenceNumber(accNumberVerifyDTO.getReferenceNumber())
                .build();
    }

    /**
     * Xử lý chuyển tiền nội bộ trong MB Bank
     *
     * Method này verify thông tin tài khoản người nhận trong cùng ngân hàng MB:
     * 1. Tạo request verify account với account number
     * 2. Gọi trực tiếp T24 API (không qua LAPNET)
     * 3. Return thông tin account đã verify
     *
     * Ưu điểm chuyển tiền nội bộ:
     * - Tốc độ nhanh (direct T24 call)
     * - Phí thấp hoặc miễn phí
     * - Real-time processing
     * - Không cần qua LAPNET network
     *
     * @param beneficiaryAccNumber Số tài khoản người nhận trong MB Bank
     * @return BeneficiaryInfoResponse Thông tin tài khoản đã verify
     * @throws BadRequestAlertException Khi account không tồn tại trong MB Bank
     */
    private BeneficiaryInfoResponse internalBankMB(String beneficiaryAccNumber) {

        // Tạo request verify account cho chuyển tiền nội bộ
        // Chỉ cần account number, không cần bank code vì cùng MB Bank
        AccNumberVerifyRequest request = AccNumberVerifyRequest.builder()
                .accountNumber(beneficiaryAccNumber)  // Số tài khoản người nhận
                .build();

        AccNumberVerifyDTO accNumberVerifyDTO = this.apiGeeTransferService.verifyAccNumber(request);

        MoneyAccount creditAccount = this.moneyAccountRepository.findByAccountNumberAndStatus(
                beneficiaryAccNumber, EntityStatus.ACTIVE.getStatus()).stream().findFirst().orElse(null);

        return BeneficiaryInfoResponse.builder()
                .beneficiaryName(accNumberVerifyDTO.getAccountName())
                .accountNumber(accNumberVerifyDTO.getCreditCard())
                .referenceNumber(accNumberVerifyDTO.getReferenceNumber())
                .currency(Validator.isNotNull(creditAccount) ? creditAccount.getCurrency() : null)
                .build();
    }

    /**
     * Thực hiện chuyển tiền đến merchant (thanh toán)
     *
     * Method này xử lý thanh toán cho các loại merchant:
     * - Umoney: Nạp tiền vào ví điện tử Umoney
     * - Mmoney: Nạp tiền vào ví điện tử Mmoney
     * - Telco: Nạp tiền điện thoại (Unitel, LTC, ETL)
     * - Utility: Thanh toán hóa đơn (điện, nước, internet...)
     * - Insurance: Thanh toán bảo hiểm
     * - Merchant: Thanh toán tại cửa hàng qua QR Code
     *
     * Luồng xử lý:
     * 1. Tìm thông tin merchant theo merchantCode
     * 2. Validate tài khoản khách hàng
     * 3. Set thông tin giao dịch (currency, beneficiary...)
     * 4. Tạo OTP qua T24
     * 5. Lưu transaction merchant record
     * 6. Set tài khoản mặc định (nếu có)
     * 7. Trả về OTP response
     *
     * @param transactionDTO Thông tin giao dịch đã được chuẩn bị
     * @param merchantCode Mã merchant (UMONEY, UNITEL, LTC...)
     * @param customer Thông tin khách hàng
     * @param isSetDefaultAccount Có set làm tài khoản mặc định không
     * @return OtpTransferTransResponse Chứa OTP info và transaction ID
     * @throws BadRequestAlertException Khi merchant không tồn tại hoặc tài khoản không hợp lệ
     */
    public OtpTransferTransResponse makeTransferToMerchant(TransactionDTO transactionDTO, String merchantCode,
                                                           Customer customer, boolean isSetDefaultAccount) {
        Merchant merchant = null;

        // xử lý cho umoney
        if (Validator.equals(merchantCode, MerchantCode.UMONEY.name())) {
            merchant = this.merchantRepository.findByMerchantCodeAndStatusAndOtherType(merchantCode, EntityStatus.ACTIVE.getStatus());

            // xử lý cho billing
        } else if (Validator.equals(MasterMerchantType.BILLING.name(), transactionDTO.getTransferType().name())) {
            //kiểm tra xem nó là merchant hay master merchant
            if (this.merchantRepository.existsByMerchantCodeAndStatusAndParentIdNotNull(merchantCode, EntityStatus.ACTIVE.getStatus())) {
                merchant = this.merchantRepository.findByMerchantCodeAndStatusAndParentIdIsNotNull(merchantCode, EntityStatus.ACTIVE.getStatus());
            } else {
                merchant = this.merchantRepository.findByMerchantCodeAndServiceTypeAndStatus(merchantCode,
                        MasterMerchantType.BILLING.toString(), EntityStatus.ACTIVE.getStatus());
            }
        } else {
            // xử lý cho tài khoản merchant, không phải tài khoản master merchant
            merchant = this.merchantRepository.findByMerchantCodeAndStatusAndParentIdIsNotNull(merchantCode, EntityStatus.ACTIVE.getStatus());
        }

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        // kiem tra so tai khoan ghi no va ewallet
        Optional<MoneyAccount> moneyAccountOptional = this.moneyAccountRepository
                .findByAccountNumberAndCustomerIdAndStatus(transactionDTO.getCustomerAccNumber(), customer.getCustomerId(),
                        EntityStatus.ACTIVE.getStatus());

        if (!moneyAccountOptional.isPresent()) {
            throw new BadRequestAlertException(ErrorCode.MSG1038);
        }

        if (!Validator.equals(moneyAccountOptional.get().getCustomerId(), customer.getCustomerId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1151);
        } else {
            transactionDTO.setCustomerId(moneyAccountOptional.get().getCustomerId());

            if (Validator.isNotNull(moneyAccountOptional.get().getCurrency())) {
                transactionDTO.setTransactionCurrency(moneyAccountOptional.get().getCurrency());
            } else {
                transactionDTO.setTransactionCurrency(this.customerProperties.getDefaultCurrency());
            }
        }
        transactionDTO.setBeneficiaryAccountNumber(merchant.getMerchantAccountNumber());

        Transaction transferSaved = this.requestOtpT24(transactionDTO);

        this.createTransactionMerchant(transferSaved.getTransferTransactionId(),
                merchant.getMerchantId(), transactionDTO.getMerchantType());

        try {
            // set tài khoản mặc định nếu gạt nút setting default account
            if (isSetDefaultAccount) {
                this.setDefaultAccountTransfer(customer.getCustomerId(), transferSaved.getCustomerAccNumber());
            }
        } catch (Exception e) {
            _log.error("request Otp, after request OTP error:", e);
        }

        return OtpTransferTransResponse.builder()
                .expiredTime(this.otpProperties.getDuration())
                .transactionId(transferSaved.getTransactionId())
                .transferTransactionId(transferSaved.getTransferTransactionId())
                .transData(transactionDTO.getTransData())
                .build();
    }

    private void processHistoryTransaction(TransactionHistoryDTO transactionHistoryDTO, List<Transaction> transactions,
                                           List<MoneyAccountDTO> moneyAccountDTOS, List<Bank> banks) {
        Optional<Transaction> optionalTransaction = transactions.stream()
                .filter(transaction -> Validator.equals(transaction.getTransactionCode(),
                        transactionHistoryDTO.getReferenceNumber()))
                .findFirst();

        optionalTransaction.ifPresent(transaction -> {
            transactionHistoryDTO.setToBankCode(transaction.getBankCode());
            transactionHistoryDTO.setTransferType(transaction.getTransactionType().name());

            // Neu la giao dich cong tien => enrich thong tin nguoi gui
            if (Validator.equals(transactionHistoryDTO.getAccountNumber(), transaction.getTarget())) {
                transactionHistoryDTO.setTransactionType(TransactionType.PLUS.name());
                transactionHistoryDTO.setFromAccount(transaction.getTarget());

                Optional<MoneyAccountDTO> moneyAccountDTOOptional = moneyAccountDTOS.stream()
                        .filter(account -> Validator.equals(transaction.getCustomerAccNumber(),
                                account.getAccountNumber()))
                        .findFirst();
                moneyAccountDTOOptional.ifPresent(
                        account -> transactionHistoryDTO.setFromAccountName(account.getCustomerName()));
            } else {
                transactionHistoryDTO.setTransactionType(TransactionType.MINUS.name());
                transactionHistoryDTO.setToBeneficiaryAccount(transaction.getTarget());

                Optional<MoneyAccountDTO> moneyAccountDTOOptional = moneyAccountDTOS.stream()
                        .filter(account -> Validator.equals(account.getAccountNumber(),
                                transaction.getTarget()))
                        .findFirst();

                moneyAccountDTOOptional.ifPresent(
                        account -> transactionHistoryDTO.setToBeneficiaryName(account.getCustomerName()));
            }

            Optional<Bank> bankTransaction = banks.stream()
                    .filter(bank -> Validator.equals(bank.getBankCode(), transactionHistoryDTO.getToBankCode()))
                    .findFirst();
            bankTransaction.ifPresent(bank -> transactionHistoryDTO.setToBankName(bank.getBankName()));
        });
    }

    /**
     * call API request OTP bên T24 và cập nhật transactionId cho giao dịch
     *
     * @param transactionDTO TransactionDTO
     * @return Transaction
     */
    private Transaction requestOtpT24(TransactionDTO transactionDTO) {

        if (transactionDTO.getTransactionId() != null) {
            transactionDTO.setTransactionId(transactionDTO.getTransactionId());
        } else {
            transactionDTO.setTransactionId(UUIDUtil.generateUUID(20));
        }

        if (!OtpConfirmType.DOTP.equals(transactionDTO.getOtpType())) {
            this.consumerOtpService.checkSpam(transactionDTO.getPhoneNumber(), OtpType.TRANSFER);

            ReqFundTransferDTO response = this.apiGeeTransferService.requestFundTransfer(
                    OtpTransferRequest.builder()
                            .accountNumber(transactionDTO.getCustomerAccNumber())
                            .accountType(AccountType.ACCOUNT.name())
                            .apiGeeTransactionId(transactionDTO.getTransactionId())
                            .build());
            transactionDTO.setTransactionId(response.getReferenceNumber());
        }

        this.consumerOtpService.requestOtp(transactionDTO.getTransactionId(),
                transactionDTO.getPhoneNumber(), OtpType.TRANSFER);
        Transaction transaction = this.transactionMapper.toEntity(transactionDTO);

        return this.transactionRepository.save_(transaction);

    }

    private void makeBillPayment(MBOtpTransConfirmResponse response, Optional<Telco> optionalTelco, TransactionDTO transactionDTO, TransactionMerchant transactionMerchant) {
        Telco telco = optionalTelco.orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG1105));

        TopupUnitelResponse topupUnitelResponse = null;
        TopupEtlResponse topupEtlResponse = null;
        BestTelecomResponse bestTelecomResponse = null;
        String transactionId = "";
        PaymentInternetLtcResponse paymentInternetLtcResponse = null;
        PaymentPostPaidLtcResponse paymentPostPaidLtcResponse = null;
        PaymentPstnLtcResponse paymentPstnLtcResponse = null;
        String phoneUser = NumberUtil.formatPhoneNumber(this.getCustomerLogin().getUsername());
        String msisdn = NumberUtil.formatPhoneNumber(transactionDTO.getTarget());

        //LTC
        if (Validator.equals(telco.getTelcoCode(), TelcoType.LTC.name()) || Validator.equals(telco.getTelcoCode(), TelcoType.TPLUS.name())) {
            transactionId = RandomGenerator.generateRandomNumber(1, 23);

            if (Validator.equals(transactionDTO.getBillingType(), BillingType.POSTPAID_MOBILE)) {

                PaymentPostpaidLtcRequest paymentLtcRequest = PaymentPostpaidLtcRequest.builder()
                        .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                        .transactionId(transactionId)
                        .msisdn(mmoneyEncryptService.encrypt(msisdn))
                        .phoneUser(mmoneyEncryptService.encrypt(phoneUser))
                        .amount(Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L))
                        .operator(telco.getTelcoCode())
                        .type("POSTPAID")
                        .build();

                paymentLtcRequest.setApiGeeTransactionId(transactionId);
                paymentPostPaidLtcResponse = this.apiMmoneyService.paymentPostPaid(paymentLtcRequest);

            } else if (Validator.equals(transactionDTO.getBillingType(), BillingType.INTERNET)) {

                PaymentInternetLtcRequest paymentInternetLtcRequest = PaymentInternetLtcRequest.builder()
                        .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                        .transactionId(transactionId)
                        .msisdn(mmoneyEncryptService.encrypt(msisdn))
                        .phoneUser(mmoneyEncryptService.encrypt(phoneUser))
                        .amount(Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L))
                        .operator(telco.getTelcoCode())
                        .type(String.valueOf(transactionDTO.getBillingType()))
                        .build();

                paymentInternetLtcRequest.setApiGeeTransactionId(transactionId);
                paymentInternetLtcResponse = this.apiMmoneyService.paymentInternet(paymentInternetLtcRequest);

            } else if (Validator.equals(transactionDTO.getBillingType(), BillingType.PSTN)) {

                PaymentPstnLtcRequest paymentPstnLtcRequest = PaymentPstnLtcRequest.builder()
                        .apiKey(this.consumerProperties.getApiMmoney().getApiKey())
                        .transactionId(transactionId)
                        .msisdn(mmoneyEncryptService.encrypt(msisdn))
                        .phoneUser(mmoneyEncryptService.encrypt(phoneUser))
                        .amount(Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L))
                        .operator(telco.getTelcoCode())
                        .type(String.valueOf(transactionDTO.getBillingType()))
                        .build();

                paymentPstnLtcRequest.setApiGeeTransactionId(transactionId);
                paymentPstnLtcResponse = this.apiMmoneyService.paymentPstn(paymentPstnLtcRequest);
            }
        }
        //Unitel
        else if (Validator.equals(telco.getTelcoCode(), TelcoType.UNITEL.name())) {
            transactionId = this.apiUnitelService.generateTransactionId();
            ApiUnitelRequest topupUnitelRequest = ApiUnitelRequest.builder()
                    .mti(consumerProperties.getApiUnitel().getMti())
                    .billingType(transactionDTO.getBillingType())
                    .transTime(InstantUtil.formatStringLongTimestamp(Instant.now(), ZoneId.of(StringPool.UTC)))
                    .transAmount(Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L))
                    .systemTrace(transactionId)
                    .clientId(consumerProperties.getApiUnitel().getClientId())
                    .build();

            if (transactionDTO.getBillingType().equals(BillingType.INTERNET)) {
                topupUnitelRequest.setCustomerCode(transactionDTO.getTarget());
            } else {
                topupUnitelRequest.setMsisdn(transactionDTO.getTarget());
            }
            topupUnitelResponse = this.apiUnitelService.topup(topupUnitelRequest);
        }
        //Etl
        else if (Validator.equals(telco.getTelcoCode(), TelcoType.ETL.name())) {
            transactionId = this.apiETLService.generateTransactionId();
            if (Validator.equals(transactionDTO.getBillingType(), BillingType.POSTPAID_MOBILE)) {
                // thanh toán di động trả sau
                topupEtlResponse = this.apiETLService.paymentPostPaid(this.etlPaymentTopupWSRequest(transactionDTO.getTarget(), Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L), transactionId));

            } else if (Validator.equals(transactionDTO.getBillingType(), BillingType.INTERNET)) {
                // thanh toán internet
                topupEtlResponse = this.apiETLService.topupInternetPrepaid(this.etlPaymentTopupWSRequest(transactionDTO.getTarget(), Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L), transactionId));

            } else if (Validator.equals(transactionDTO.getBillingType(), BillingType.PSTN)) {
                // thanh toán di động cố định
                topupEtlResponse = this.apiETLService.paymentPSTN(this.etlPaymentTopupWSRequest(transactionDTO.getTarget(), Validator.isNotNull(transactionDTO.getTransactionAmount()) ? String.valueOf(transactionDTO.getTransactionAmount().longValue()) : String.valueOf(0L), transactionId));
            }
            // Best TELECOM
        } else if (Validator.equals(telco.getTelcoCode(), TelcoType.BTC.name())) {
            transactionId = RandomGenerator.generateRandomNumber(8, 10);
            if (Validator.equals(transactionDTO.getBillingType(), BillingType.POSTPAID_MOBILE)) {
                // thanh toán di động trả sau
                bestTelecomResponse = this.apiBestTelecomService.mobilePayment(this.bestTelecomPaymentRequest(transactionDTO.getTarget(), Validator.isNotNull(transactionDTO.getTransactionAmount()) ? transactionDTO.getTransactionAmount().longValue() : 0L, transactionId));
            }
        } else {
            // Nhà mạng khác
            transactionDTO.setTransactionStatus(TransactionStatus.FAILED);
            transactionDTO.setTransactionFinishTime(Instant.now());

            this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));
            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1170);
        }

        // save lai transaction Id phia nha mang de trace log
        transactionMerchant.setMerchantTransactionId(transactionId);
        transactionMerchantRepository.save(transactionMerchant);

        boolean isSuccess = Validator.isNotNull(paymentInternetLtcResponse) && paymentInternetLtcResponse.isSuccess()
                || Validator.isNotNull(paymentPostPaidLtcResponse) && paymentPostPaidLtcResponse.isSuccess()
                || Validator.isNotNull(paymentPstnLtcResponse) && paymentPstnLtcResponse.isSuccess();

        // Nếu Call API thất bại -> revert giao dịch
        if (!(isSuccess
                || Validator.isNotNull(topupUnitelResponse) && topupUnitelResponse.isSuccess()
                || Validator.isNotNull(topupEtlResponse) && topupEtlResponse.isSuccess()
                || Validator.isNotNull(bestTelecomResponse)
                && Validator.equals(this.consumerProperties.getApiBestTelecom().getSuccessPayment(), bestTelecomResponse.getValidity())
        )) {
            boolean isSuccessTrans = false;

            if (Validator.isNotNull(telco)
                    && (Validator.equals(telco.getTelcoCode(), TelcoType.LTC.toString()) || Validator.equals(telco.getTelcoCode(), TelcoType.TPLUS.toString()))) {
                isSuccessTrans = this.checkTransactionMmoney(transactionId, MmoneyServiceName.TELECOM.getServiceName());

                if (Validator.equals(false, isSuccessTrans)
                        || Validator.equals(Objects.requireNonNull(paymentPostPaidLtcResponse).getErrorDesccription(), LabelKey.ERROR_TRANSACTION_ID_IS_DUPLICATE)
                        || Validator.equals(Objects.requireNonNull(paymentInternetLtcResponse).getErrorDesccription(), LabelKey.ERROR_TRANSACTION_ID_IS_DUPLICATE)
                        || Validator.equals(Objects.requireNonNull(paymentPstnLtcResponse).getErrorDesccription(), LabelKey.ERROR_TRANSACTION_ID_IS_DUPLICATE)
                ) {
                    this.apiGeeTransferService.revert(RevertTransactionRequest.builder()
                            .t24ReferenceNumber(response.getT24ReferenceNumber())
                            .transactionId(response.getReferenceNumber())
                            .build());

                    transactionDTO.setTransactionStatus(TransactionStatus.FAILED);

                    this.transactionRepository.save_(this.transactionMapper.toEntity(transactionDTO));

                    throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
                }
            }
        }
    }

    @Override
    public boolean checkTransactionMmoney(String transactionId, String serviceName) {
        CheckTransactionMmoneyResponse checkTransactionMmoneyResponse = this.apiMmoneyService.checkTransaction(
                CheckTransactionMmoneyRequest.builder()
                        .serviceName(serviceName)
                        .transactionId(transactionId)
                        .build()
        );

        if (!(Validator.isNotNull(checkTransactionMmoneyResponse) && checkTransactionMmoneyResponse.isSuccess())) {
            return false;
        }

        return true;
    }

    public MBOtpTransConfirmResponse confirmFundTransferMBCurrencyInternal(TransactionDTO transaction, String otp, String deviceId) {
        // get information customer
        Customer customer = getCustomerLogin();

        // debit account
        AccountMoney debitAccount = new AccountMoney();

        debitAccount.setAccountNumber(transaction.getCustomerAccNumber());
        debitAccount.setAccountName(customer.getFullname());
        debitAccount.setAccountType(this.mbApiConstantProperties.getInBanKTransfer().getAccountType());
        debitAccount.setAccountCurrency(transaction.getTransactionCurrency());

        // credit account
        AccountMoney creditAccount = new AccountMoney();

        if (TransferTransactionType.getQrLapnetTransaction().contains(transaction.getType())) {
            creditAccount.setAccountType(this.mbApiConstantProperties.getOtpGen().getQrType());
            creditAccount.setAccountNumber(transaction.getQrCodeValue());
        } else {
            creditAccount.setAccountType(this.mbApiConstantProperties.getInBanKTransfer().getAccountType());
            creditAccount.setAccountNumber(transaction.getBeneficiaryAccountNumber());
        }
        creditAccount.setAccountCurrency(transaction.getTransactionCurrency());

        // amount
        Amount amount = new Amount();

        double amountTransaction = this.getAmountTransactionCurrencyInternal(transaction);

        amount.setAmount(StringUtil.formatNumberDoubleValue(amountTransaction));
        amount.setCurrency(transaction.getTransactionCurrency());

        String userId = customer.getCif() + customer.getIdCardNumber();
        AuthenMethod authenMethod = AuthenMethod.builder()
                .otpValue(otp)
                .type(transaction.getOtpType().name() != null ? transaction.getOtpType() : OtpConfirmType.SMS)
                .userId(userId)
                .transData(MD5Generator.md5(transaction.getTransData()))
                .deviceId(deviceId)
                .build();
        if (Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP)) {
            authenMethod.setToken(dotpService.getCustomerDevice(deviceId).getToken());
        }
        // create request send to t24 system
        OtpVerifyTransferRequest otpVerifyTransferRequest = OtpVerifyTransferRequest.builder()
                .otpValue(otp)
                .transferType(transaction.getT24TransferType())
                .channel(transaction.getT24Channel())
                .serviceType(transaction.getT24ServiceType())
                .requestId(transaction.getTransactionId())
                .remark(transaction.getMessage())
                .branchCode(transaction.getBranchCode())
                .creditAccount(creditAccount)
                .debitAccount(debitAccount)
                .amount(amount)
                .authenMethod(authenMethod)
                .build();

        this.enrichVerifyTransferRequestCurrencyForeign(otpVerifyTransferRequest, transaction);

        VerifyFundTransferDTO reqFundTransferDTO = this.consumerOtpService.verifyOtp(transaction.getTransactionId(),
                transaction.getPhoneNumber(), OtpType.TRANSFER, otpVerifyTransferRequest,
                this.apiGeeTransferService::verifyFundTransfer,
                Validator.equals(transaction.getOtpType(), OtpConfirmType.DOTP) ? OtpConfirmType.DOTP : OtpConfirmType.SMS);

        return MBOtpTransConfirmResponse.builder()
                .t24ReferenceNumber(reqFundTransferDTO.getT24ReferenceNumber())
                .referenceNumber(reqFundTransferDTO.getReferenceNumber())
                .transactionStatus(reqFundTransferDTO.getTransactionStatus())
                .transactionId(reqFundTransferDTO.getTransactionId())
                .t24ErrorCode(reqFundTransferDTO.getT24ErrorCode())
                .build();
    }

    private TelcoType getTelcoByTransactionHistory(Transaction transaction) {
        TransactionMerchant transactionMerchant = transactionMerchantRepository.findByTransferTransactionId(transaction.getTransferTransactionId())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG100010));

        // FIX TH: merchant bị xoá trước đấy không hiển thị được lịch sử
        Merchant merchant = this.merchantRepository.getOne(transactionMerchant.getMerchantId());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        Telco telco = telcoRepository.findByTelcoCodeAndStatus(merchant.getMerchantCode(), EntityStatus.ACTIVE.getStatus()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG3002));

        return TelcoType.valueOf(telco.getTelcoCode());
    }

    private TelcoType getTelcoByTransaction(Transaction transaction) {

        TransactionMerchant transactionMerchant = transactionMerchantRepository.findByTransferTransactionId(transaction.getTransferTransactionId())
                .orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG100010));

        Merchant merchant = this.merchantRepository.findByMerchantIdAndStatus(transactionMerchant.getMerchantId(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        Telco telco = telcoRepository.findByTelcoCodeAndStatus(merchant.getMerchantCode(), EntityStatus.ACTIVE.getStatus()).orElseThrow(
                () -> new BadRequestAlertException(ErrorCode.MSG3002));

        return TelcoType.valueOf(telco.getTelcoCode());

    }

    private ETLPaymentTopupWSRequest etlPaymentTopupWSRequest(String phoneNumber, String amount, String
            transactionId) {
        return ETLPaymentTopupWSRequest.builder()
                .msisdn(phoneNumber)
                .transactionId(transactionId)
                .dateTimeProcess(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.DATE_TIME_PATTERN)))
                .amount(amount)
                .build();
    }

//    private void enrichVerifyTransferRequestForBilling(OtpVerifyTransferRequest otpVerifyTransferRequest,
//                                                       TransactionDTO transaction) {
//        // Nghiệp vụ luồng Billing:
//        // Tiền phí sẽ chuyển thẳng vào tài khoản merchent cùng với tiền hóa đơn rồi sau đó 2 bên sẽ đối soát tiền phí sau
//        // Discount cũng sẽ trừ thẳng vào amount
//        otpVerifyTransferRequest.getAmount().setAmount(StringUtil.valueOf(transaction.getActualTransactionAmount()));
//        otpVerifyTransferRequest.getChargeInfo().setAmount(StringUtil.valueOf(0));
//
//    }

    private ValidateMobileNumberRequest bestTelecomRequest(String phoneNumber) {
        return ValidateMobileNumberRequest.builder()
                .phoneNumber(phoneNumber)
                .displayAddon(this.consumerProperties.getApiBestTelecom().getBillingCode())
                .build();
    }

    private MobilePaymentRequest bestTelecomPaymentRequest(String phoneNumber, Long amount, String transactionId) {
        return MobilePaymentRequest.builder()
                .phoneNumber(phoneNumber)
                .amount(amount)
                .transId(transactionId)
                .serviceType(this.consumerProperties.getApiBestTelecom().getBillingType())
                .build();
    }

    private double getAmountTransactionCurrencyInternal(TransactionDTO transaction) {
        double transactionAmount = transaction.getTransactionAmount();
        long discountFixed = Validator.isNull(transaction.getDiscountFixed()) ? 0L : transaction.getDiscountFixed();

        transactionAmount = transactionAmount - discountFixed - transactionAmount * transaction.getDiscount() / 100;

        return transactionAmount;
    }

    private void enrichVerifyTransferRequestCurrencyForeign(OtpVerifyTransferRequest otpVerifyTransferRequest,
                                                            TransactionDTO transaction) {
        List<AddInfoList> addInfoList = new ArrayList<>();

        AddInfoList benAcc = new AddInfoList();

        benAcc.setName(this.mbApiConstantProperties.getLapnetTransfer().getAddInforNo());
        benAcc.setType(this.mbApiConstantProperties.getLapnetTransfer().getAddInforType());
        benAcc.setValue(this.mbApiConstantProperties.getLapnetTransfer().getAddInforNoValue());

        AddInfoList benCustomer = new AddInfoList();

        benCustomer.setName(this.mbApiConstantProperties.getLapnetTransfer().getAddInforName());
        benCustomer.setType(this.mbApiConstantProperties.getLapnetTransfer().getAddInforType());

        // @TODO khách hàng confirm với tài khoản <= 11 ký tự phải cộng thêm chuỗi ở đằng trước
        int customerAccountLength = transaction.getCustomerAccNumber().length();

        if (customerAccountLength <= Integer.parseInt(StringPool.MIN_CUSTOMER_ACCOUNT_LENGTH)) {
            int endLength = Integer.parseInt(StringPool.CUSTOMER_ACCOUNT_LENGTH) - customerAccountLength;
            benCustomer.setValue(UUIDUtil.generateRandomAlpha(endLength) + transaction.getCustomerAccNumber());
        } else {
            benCustomer.setValue(transaction.getCustomerAccNumber());
        }

        addInfoList.add(benAcc);
        addInfoList.add(benCustomer);

        // enrich chargeInfo
        ChargeInfo chargeInfo = new ChargeInfo();

        chargeInfo.setAmount(Validator.equals(String.valueOf(transaction.getTransactionFee()), StringPool.NUMBER_DOUBLE_0) ? StringPool.NUMBER_0 : String.valueOf(transaction.getTransactionFee()));
        chargeInfo.setCurrency(transaction.getTransactionCurrency());
        chargeInfo.setDescription(transaction.getMessage());

        otpVerifyTransferRequest.setAddInfoList(addInfoList);
        otpVerifyTransferRequest.setChargeInfo(chargeInfo);

    }

    /**
     * Kiểm tra hạn mức giao dịch của khách hàng và xác thực tài khoản tiền
     *
     * @param moneyAccountOfCustomer Tài khoản tiền của khách hàng thực hiện giao dịch
     * @param request Request chuyển tiền OTP
     * @param customer Thông tin khách hàng
     * @param feeTransactionResponse Phản hồi phí giao dịch (có thể null)
     * @param moneyAccountOfBenefit Tài khoản tiền của người thụ hưởng (có thể null)
     */
    private void checkTransactionLimitCustomerAndValidateMoneyAccount(MoneyAccount moneyAccountOfCustomer, OtpTransferTransRequest request, Customer customer,
                                                                      FeeTransactionResponse feeTransactionResponse, MoneyAccount moneyAccountOfBenefit) {
        // Kiểm tra quyền sở hữu tài khoản: đảm bảo tài khoản tiền thuộc về khách hàng hiện tại
        if (!Validator.equals(moneyAccountOfCustomer.getCustomerId(), customer.getCustomerId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1151);
        }

        // Kiểm tra tính nhất quán đồng tiền: đồng tiền của tài khoản khách hàng phải khớp với đồng tiền trong request
        if (!Validator.equals(moneyAccountOfCustomer.getCurrency(), request.getCustomerCurrency())) {
            throw new BadRequestAlertException(ErrorCode.MSG101213);
        }

        // Kiểm tra đồng tiền của người thụ hưởng (nếu có): đồng tiền tài khoản người thụ hưởng phải khớp với đồng tiền trong request
        if (Validator.isNotNull(moneyAccountOfBenefit) && !Validator.equals(moneyAccountOfBenefit.getCurrency(), request.getBeneficiaryCurrency())) {
            throw new BadRequestAlertException(ErrorCode.MSG101213);
        }

        // Xử lý và kiểm tra số tiền giao dịch cho đồng tiền mặc định
        try {
            if (Validator.equals(moneyAccountOfCustomer.getCurrency(), customerProperties.getDefaultCurrency())) {
                // Chuyển đổi số tiền thành Long để xử lý
                Long amount = request.getAmount().longValue();
            }
        } catch (Exception e) {
            // Nếu có lỗi trong quá trình xử lý số tiền thì ném lỗi
            throw new BadRequestAlertException(ErrorCode.MSG101239);
        }

        // Kiểm tra giao dịch nội bộ ngân hàng với đồng tiền khác nhau
        // Điều kiện: là giao dịch nội bộ VÀ (đồng tiền không phải mặc định) VÀ (đồng tiền tài khoản không khớp)
        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())
                && (!Validator.equals(request.getBeneficiaryCurrency(), customerProperties.getDefaultCurrency())
                || !Validator.equals(request.getCustomerCurrency(), customerProperties.getDefaultCurrency()))
                && (!Validator.equals(moneyAccountOfCustomer.getCurrency(), request.getBeneficiaryCurrency())
                || !Validator.equals(moneyAccountOfCustomer.getCurrency(), request.getCustomerCurrency()))) {
            throw new BadRequestAlertException(ErrorCode.MSG101213);
        }

        // Kiểm tra số dư tối thiểu cho giao dịch nội bộ ngân hàng với đồng tiền USD
        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())
                && Validator.equals(moneyAccountOfCustomer.getCurrency(), "USD")
                && moneyAccountOfCustomer.getAvailableAmount() < this.customerProperties.getTransactionLimitUsd()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID,
                            new Object[]{this.customerProperties.getTransactionLimitUsd(),
                                    moneyAccountOfCustomer.getCurrency()}),
                    Currency.class.getSimpleName(), LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID);
        }
        // Kiểm tra số dư tối thiểu cho giao dịch nội bộ ngân hàng với đồng tiền THB
        else if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())
                && Validator.equals(moneyAccountOfCustomer.getCurrency(), "THB")
                && moneyAccountOfCustomer.getAvailableAmount() < this.customerProperties.getTransactionLimitThb()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID,
                            new Object[]{this.customerProperties.getTransactionLimitThb(),
                                    moneyAccountOfCustomer.getCurrency()}),
                    Currency.class.getSimpleName(), LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID);
        }

        // Kiểm tra quyền thực hiện giao dịch ngoại tệ theo sector của khách hàng
        // Chỉ sector 1740 và 1890 mới được phép thực hiện giao dịch ngoại tệ nội bộ
        if (this.mbApiConstantProperties.getInBanKTransfer().getChannel().equals(request.getBeneficiaryBankCode())
                && !Validator.equals(request.getCustomerCurrency(), customerProperties.getDefaultCurrency())
                && !Arrays.asList(Sector.SECTOR_1740.getId(), Sector.SECTOR_1890.getId()).contains(customer.getCustomerSectorId())) {
            throw new BadRequestAlertException(ErrorCode.MSG101217);
        }

        // Kiểm tra số dư sau khi trừ phí giao dịch (nếu có phí)
        if (Validator.isNotNull(feeTransactionResponse)) {
            // Tính tổng số tiền còn lại sau khi trừ số tiền giao dịch và phí
            double totalAmount = moneyAccountOfCustomer.getAvailableAmount() - (request.getAmount() + feeTransactionResponse.getFee());

            // Kiểm tra số dư còn lại cho đồng tiền USD
            if (Validator.equals(moneyAccountOfCustomer.getCurrency(), "USD") && totalAmount < this.customerProperties.getTransactionLimitUsd()) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID,
                                new Object[]{this.customerProperties.getTransactionLimitUsd(),
                                        moneyAccountOfCustomer.getCurrency()}),
                        Currency.class.getSimpleName(), LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID);
            }
            // Kiểm tra số dư còn lại cho đồng tiền THB
            else if (Validator.equals(moneyAccountOfCustomer.getCurrency(), "THB") && totalAmount < this.customerProperties.getTransactionLimitThb()) {
                throw new BadRequestAlertException(
                        Labels.getLabels(LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID,
                                new Object[]{this.customerProperties.getTransactionLimitThb(),
                                        moneyAccountOfCustomer.getCurrency()}),
                        Currency.class.getSimpleName(), LabelKey.ERROR_CURRENCY_CUSTOMER_BALANCE_INVALID);
            }
        }
    }

    private void validateDebitTransferUmoney(DebitMoneyRequest request) {
        if (Validator.isNull(request.getDebitAccount().getAccountName())
                || Validator.isNull(request.getDebitAccount().getAccountNumber())
                || Validator.isNull(request.getDebitAccount().getAccountType())
                || Validator.isNull(request.getCreditAccount().getAccountName())
                || Validator.isNull(request.getCreditAccount().getAccountNumber())
                || Validator.isNull(request.getCreditAccount().getAccountType())) {
            throw new BadRequestAlertException(ErrorCode.MSG101277);
        }

        if (!request.getDebitAccount().getAccountName().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS)
                || !request.getCreditAccount().getAccountName().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS)
                || (Validator.isNotNull(request.getFromMember())
                && !request.getFromMember().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS))
                || (Validator.isNotNull(request.getChargeInfo().getDescription())
                && !request.getChargeInfo().getDescription().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS))
                || (Validator.isNotNull(request.getRemark())
                && !request.getRemark().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS))) {
            throw new BadRequestAlertException(ErrorCode.MSG101277);
        }

        if (Validator.isNotNull(request.getAmount().getAmount()) && Double.parseDouble(request.getAmount().getAmount()) < 0) {
            throw new BadRequestAlertException(ErrorCode.MSG101277);
        }

        if (Validator.equals(request.getDebitAccount().getAccountNumber(), request.getCreditAccount().getAccountNumber())) {
            throw new BadRequestAlertException(ErrorCode.MSG100013);
        }
    }

    private void validateDepositTransferUmoney(DepositMoneyRequest request) {
        if (Validator.isNull(request.getDebitAccount().getAccountName())
                || Validator.isNull(request.getDebitAccount().getAccountNumber())
                || Validator.isNull(request.getDebitAccount().getAccountType())
                || Validator.isNull(request.getCreditAccount().getAccountName())
                || Validator.isNull(request.getCreditAccount().getAccountNumber())
                || Validator.isNull(request.getCreditAccount().getAccountType())) {
            throw new BadRequestAlertException(ErrorCode.MSG101277);
        }

        if (Validator.isNotNull(request.getAmount().getAmount()) && Double.parseDouble(request.getAmount().getAmount()) < 0) {
            throw new BadRequestAlertException(ErrorCode.MSG101277);
        }

        if (!request.getDebitAccount().getAccountName().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS)
                || !request.getCreditAccount().getAccountName().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS)
                || (Validator.isNotNull(request.getFromMember())
                && !request.getFromMember().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS))
                || (Validator.isNotNull(request.getChargeInfo().getDescription())
                && !request.getChargeInfo().getDescription().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS))
                || (Validator.isNotNull(request.getRemark())
                && !request.getRemark().matches(ValidationConstraint.PATTERN.DEBIT_DEPOSIT_NO_SPECIAL_CHARACTERS))) {
            throw new BadRequestAlertException(ErrorCode.MSG101277);
        }

        if (Validator.equals(request.getDebitAccount().getAccountNumber(), request.getCreditAccount().getAccountNumber())) {
            throw new BadRequestAlertException(ErrorCode.MSG100013);
        }
    }

}
