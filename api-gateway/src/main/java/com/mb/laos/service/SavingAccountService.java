package com.mb.laos.service;

import com.mb.laos.api.request.Amount;
import com.mb.laos.gateway.request.ConfirmOtpSavingAccountRequest;
import com.mb.laos.gateway.request.InforInterestRequest;
import com.mb.laos.gateway.request.OtpSavingAccountRequest;
import com.mb.laos.gateway.request.QuerySavingAccountRequest;
import com.mb.laos.gateway.request.SearchSavingAccountRequest;
import com.mb.laos.gateway.response.CreateInterestResponse;
import com.mb.laos.gateway.response.OtpTransferConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferTransResponse;
import com.mb.laos.gateway.response.QuerySavingAccountResponse;
import com.mb.laos.model.dto.AccountSavingDTO;
import com.mb.laos.model.dto.IntRateDTO;
import com.mb.laos.model.dto.ProductFeatureDTO;
import com.mb.laos.model.dto.SavingAccountDTO;
import com.mb.laos.model.dto.TotalMoneySavingAccountDTO;
import com.mb.laos.model.dto.TransactionSavingAccountDTO;
import com.mb.laos.model.search.SavingAccountHistorySearch;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface SavingAccountService {
    /**
     * truy vâ lãi xuất kỳ hạn tiết kiệm
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    List<IntRateDTO> getInforInterest(HttpServletRequest httpServletRequest, InforInterestRequest request);


    /**
     * mở tài khoản tiết kiệm
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    CreateInterestResponse createInterest(HttpServletRequest httpServletRequest, ConfirmOtpSavingAccountRequest request);

    /**
     * yêu cầu mở tài khoản tiết kiệm
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    OtpTransferTransResponse requestOtpInterest(HttpServletRequest httpServletRequest, OtpSavingAccountRequest request);

    /**
     * truy vấn thông tin chi tiết tài khoản tiết kiệm
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    QuerySavingAccountResponse querySavingAccount(HttpServletRequest httpServletRequest, QuerySavingAccountRequest request);

    /**
     * danh sách tài khoản tiết kiệm
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    List<SavingAccountDTO> search(HttpServletRequest httpServletRequest, SearchSavingAccountRequest request);

    /**
     * tất toán tài khoản tiết kiệm
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    CreateInterestResponse closeInterest(HttpServletRequest httpServletRequest, ConfirmOtpSavingAccountRequest request);

    /**
     * gửi thêm tài khoản tiết kiệm
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    OtpTransferConfirmResponse depositMore(HttpServletRequest httpServletRequest, ConfirmOtpSavingAccountRequest request);

    /**
     * lịch sử giao dịch tài khoản tiết kiệm
     *
     * @param request
     * @param httpServletRequest
     * @return
     */
    Page<TransactionSavingAccountDTO> history(HttpServletRequest httpServletRequest, SavingAccountHistorySearch request);

    /**
     * Tổng tiền tài khoản tiết kiệm online và tại quầy
     *
     * @param httpServletRequest
     * @return
     */
    List<TotalMoneySavingAccountDTO> getTotalMoneySavingAccount(HttpServletRequest httpServletRequest);

    /**
     * Tính năng sản phẩm
     *
     * @param httpServletRequest
     * @return
     */
    ProductFeatureDTO productFeature(HttpServletRequest httpServletRequest, SearchSavingAccountRequest request);

    /**
     * Đồng bộ tài khoản tiết kiệm
     *
     * @return
     */
    void updateSavingAccount();

    /**
     * Danh sách tài khoản tiết kiệm tại quầy theo đơn vị tiền
     *
     * @return
     */
    List<AccountSavingDTO> getCustomerAccountSavingBranch(HttpServletRequest httpServletRequest, Amount request);
}
