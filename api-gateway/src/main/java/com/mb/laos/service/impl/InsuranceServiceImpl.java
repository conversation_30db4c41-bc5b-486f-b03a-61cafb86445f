package com.mb.laos.service.impl;

import com.google.api.client.util.Base64;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.exception.NoRollBackBadRequestAlertException;
import com.mb.laos.api.request.InquiryTransactionStatusRequest;
import com.mb.laos.api.request.RevertTransactionRequest;
import com.mb.laos.api.request.lvi.LviHealthInsuranceBuyRequest;
import com.mb.laos.api.request.lvi.LviHealthPackageFeeRequest;
import com.mb.laos.api.request.lvi.LviVehicleInsuranceBuyRequest;
import com.mb.laos.api.request.lvi.LviVehiclePackageFeeRequest;
import com.mb.laos.api.response.lvi.*;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.configuration.ConstantProperties;
import com.mb.laos.configuration.CustomerProperties;
import com.mb.laos.configuration.MBApiConstantProperties;
import com.mb.laos.enums.*;
import com.mb.laos.gateway.request.*;
import com.mb.laos.gateway.response.FeeTransactionResponse;
import com.mb.laos.gateway.response.MBOtpTransConfirmResponse;
import com.mb.laos.gateway.response.OtpTransansferLviConfirmResponse;
import com.mb.laos.gateway.response.OtpTransferTransResponse;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.*;
import com.mb.laos.model.dto.*;
import com.mb.laos.model.search.FileEntrySearch;
import com.mb.laos.repository.*;
import com.mb.laos.security.configuration.AuthenticationProperties;
import com.mb.laos.service.*;
import com.mb.laos.service.mapper.*;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.CacheControl;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.ion.Decimal;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class InsuranceServiceImpl implements InsuranceService {
    private final ApiLviService apiLviService;
    private final LviVehiclePackageMapper lviVehiclePackageMapper;
    private final LviVehiclePackageFeeMapper lviVehiclePackageFeeMapper;
    private final LviVehicleTypeMapper lviVehicleTypeMapper;
    private final LviHealthPackageMapper lviHealthPackageMapper;
    private final LviHealthPackageFeeMapper lviHealthPackageFeeMapper;
    private final LviDeliveryMapper lviDeliveryMapper;
    private final LviVehicleInsuranceMapper lviVehicleInsuranceMapper;
    private final TransactionInsuranceMapper transactionInsuranceMapper;
    private final StorageService storageService;
    private final TransactionInsuranceRepository transactionInsuranceRepository;
    private final CustomerServiceImpl customerService;
    private final TransferService transferService;
    private final TransactionRepository transactionRepository;
    private final ConstantProperties constantProperties;
    private final TransactionService transactionService;
    private final MBApiConstantProperties mbApiConstantProperties;
    private final TransactionMapper transactionMapper;
    private final ApiGeeTransferService apiGeeTransferService;
    private final IdCardTypeRepository idCardTypeRepository;
    private final MerchantRepository merchantRepository;
    private final AuthenticationProperties properties;
    private final ContentTemplateRepository contentTemplateRepository;
    private final NotiTransactionRepository notiTransactionRepository;
    private final NotificationRepository notificationRepository;
    private final NotificationMapper notificationMapper;
    private final CustomerProperties customerProperties;
    private final SendService sendService;

    @Override
    public LviVehiclePackageDTO getLviVehiclePackage() {
        return lviVehiclePackageMapper.toDto(apiLviService.getVehiclePackage());
    }

    @Override
    public LviVehicleTypeDTO getLviVehicleType() {
        return lviVehicleTypeMapper.toDto(apiLviService.getVehicleType());
    }

    @Override
    public LviVehiclePackageFeeDTO getLviVehiclePackageFee(String packageCode, String vehicleCode) {

        LviVehiclePackageFeeResponse lviVehiclePackageFeeResponse = apiLviService.getVehiclePackageFee(new LviVehiclePackageFeeRequest(packageCode, vehicleCode));

        LviVehiclePackageFeeDTO lviVehiclePackageFeeDTO = lviVehiclePackageFeeMapper.toDto(lviVehiclePackageFeeResponse);

        lviVehiclePackageFeeDTO.setTotalPaid(lviVehiclePackageFeeDTO.getPaid());

        this.enrichInsuranceFeeInformation(lviVehiclePackageFeeDTO, lviVehiclePackageFeeDTO.getPaid().longValue());

        return lviVehiclePackageFeeDTO;
    }

    @Override
    public LviHealthPackageDTO getLviHealthPackage() {
        return lviHealthPackageMapper.toDto(apiLviService.getHealthPackage());
    }

    @Override
    public LviHealthPackageFeeDTO getLviHealthPackageFeeDTO(String healthPackage) {

        LviHealthPackageFeeDTO lviHealthPackageFeeDTO = lviHealthPackageFeeMapper.toDto(apiLviService.getHealthPackageFee(new LviHealthPackageFeeRequest(healthPackage)));

        lviHealthPackageFeeDTO.setTotalPaid(lviHealthPackageFeeDTO.getPaid());

        this.enrichInsuranceFeeInformation(lviHealthPackageFeeDTO, lviHealthPackageFeeDTO.getPaid().longValue());

        return lviHealthPackageFeeDTO;
    }

    @Override
    public LviDeliveryDTO getDelivery() {
        return lviDeliveryMapper.toDto(apiLviService.getDelivery());
    }

    @Override
    public LviVehicleInsuranceDTO getVehicleInsurance(String certificate) {
        LviVehicleInsuranceDTO result = lviVehicleInsuranceMapper.toDto(apiLviService.getVehicleInsurance(certificate));
        Optional<TransactionInsurance> transactionInsuranceOptional = transactionInsuranceRepository.findFirstByIdentificationNo(result.getCertificate());
        if (transactionInsuranceOptional.isPresent()) {
            TransactionInsurance transactionInsurance = transactionInsuranceOptional.get();
            result.setPhoneNumber(transactionInsurance.getInsuredPhoneNumber());
            result.setPackageCode(transactionInsurance.getPackageCode());
        }

        result.setTotalPaid(result.getPaid());

        this.enrichInsuranceFeeInformation(result, result.getPaid().longValue());

        return result;
    }

    @Override
    @Transactional
    public OtpTransferTransResponse requestRenewLviVehicleInsurance(LviRenewVehicleInsuranceRequest request) {

        Customer customer = customerService.getCustomerLogin();
        String insuredPhoneNumber = StringUtil.formatLaosPhoneNumber(request.getInsuredPhoneNumber());
        LviVehicleInsuranceResponse vehicleInsuranceResponse = apiLviService.getVehicleInsurance(request.getCertificate());
        //create otp
        Merchant merchant = this.merchantRepository.findByMerchantCodeAndStatusAndOtherType("LVI",
                EntityStatus.ACTIVE.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        OtpTransferInsuranceRequest otpTransferTransRequest = new OtpTransferInsuranceRequest();
        otpTransferTransRequest.setCustomerAccNumber(request.getCustomerAccNumber());
        otpTransferTransRequest.setCustomerCurrency(request.getCustomerCurrency());
        otpTransferTransRequest.setBeneficiaryCustomerName(constantProperties.getLvi().getAccountName());
        otpTransferTransRequest.setBeneficiaryBankCode(constantProperties.getLvi().getAccountBank());
        otpTransferTransRequest.setOtpConfirmType(request.getOtpConfirmType());
        otpTransferTransRequest.setAmount(vehicleInsuranceResponse.getPaid().longValue());
        otpTransferTransRequest.setRemark(Labels.getLabels(LabelKey.LABEL_BUY_INSURANCE_RENEW));
        otpTransferTransRequest.setSetDefaultAccount(request.isSetDefaultAccount());
        otpTransferTransRequest.setBeneficiaryAccountNumber(merchant.getMerchantAccountNumber());


        OtpTransferTransResponse otpTransferTransResponse = transferService.requestOtpTransferInsurance(otpTransferTransRequest);
        //save request
        TransactionInsurance transactionInsurance = new TransactionInsurance();
        transactionInsurance.setAccountPhone(customer.getPhoneNumber());
        transactionInsurance.setAccountName(customer.getFullname());
        transactionInsurance.setInsuredName(request.getInsuredName());
        transactionInsurance.setInsuredPhoneNumber(insuredPhoneNumber);
        transactionInsurance.setPackageCode(vehicleInsuranceResponse.getVehiclePackage());
        transactionInsurance.setDiscount(vehicleInsuranceResponse.getDiscount());
        transactionInsurance.setCertificate(request.getCertificate());
        transactionInsurance.setTransferTransactionId(otpTransferTransResponse.getTransferTransactionId());
        transactionInsurance.setPayCurrency(vehicleInsuranceResponse.getCurrency());
        transactionInsurance.setType(InsuranceType.VEHICLE);
        transactionInsurance.setPayTotal(vehicleInsuranceResponse.getPaid());
        transactionInsurance.setPremium(vehicleInsuranceResponse.getPremium());
        transactionInsurance.setContractType(InsuranceContractType.RENEW);
        transactionInsurance.setCustomerId(customer.getCustomerId());
        transactionInsurance.setTransactionStatus(TransactionStatus.PROCESSING);

        transactionInsurance = transactionInsuranceRepository.save(transactionInsurance);

        return otpTransferTransResponse;
    }

    @Override
    public List<TransactionInsuranceDTO> getCustomerInsuranceTransaction() {
        Customer customer = customerService.getCustomerLogin();
        List<TransactionInsuranceDTO> result = transactionInsuranceMapper.toDto(transactionInsuranceRepository.findAllByCustomerIdAndTransactionStatusOrderByCreatedDateDesc(customer.getCustomerId(), TransactionStatus.SUCCESS));
        result.forEach(r -> {
            Optional<Transaction> transactionOptional = transactionRepository.findById(r.getTransferTransactionId());
            if (transactionOptional.isPresent()) {
                r.setPayTotal(Decimal.valueOf(transactionMapper.toDto(transactionOptional.get()).getActualTransactionAmount()));
            }
            List<FileEntry> fileEntries =
                    storageService.getFileEntries(TransactionInsurance.class.getName(), r.getTransactionInsuranceId(), Resource.INSURANCE);
            if (Validator.isNotNull(fileEntries)) {
                r.setIcon(storageService.getFileInfo(fileEntries.stream().findFirst().get()));
            }
        });
        return result;
    }

    @Override
    public ResponseEntity<ByteArrayResource> getRegistrationImage(FileEntrySearch search) {
        try {
            this.transactionInsuranceRepository.findById(search.getClassPk()).orElseThrow(() -> new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_IMAGE)}),
                    TransactionInsurance.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST));


            FileExtra file = this.storageService.getFileExtra(search.getFileEntryId(), search.getNormalizeName(),
                    TransactionInsurance.class.getName(), search.getClassPk(), Resource.INSURANCE);

            return ResponseEntity.ok().contentType(MediaType.parseMediaType(file.getContentType()))
                    .cacheControl(CacheControl.maxAge(this.properties.getCacheMaxAge(), TimeUnit.SECONDS))
                    .body(new ByteArrayResource(IOUtils.toByteArray(file.getInputStream())));
        } catch (Exception e) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_IMAGE)}),
                    Bank.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST);
        }
    }

    @Override
    @Transactional
    public OtpTransferTransResponse requestBuyLviVehicleInsurance(LviBuyVehicleInsuranceRequest request, MultipartFile registrationImage) {

        if (registrationImage == null || registrationImage.getSize() == 0) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_FILE_IS_EMPTY),
                    TransactionInsurance.class.getSimpleName(), LabelKey.ERROR_FILE_IS_EMPTY);
        }
        String insuredPhoneNumber = StringUtil.formatLaosPhoneNumber(request.getInsuredPhoneNumber());
        verifyDeliveryCode(request.getDeliveryCode());

        Customer customer = customerService.getCustomerLogin();

        // 2. Check idCardTypeId exists
        if (!this.idCardTypeRepository.existsByIdCardTypeIdAndStatus(request.getIdCardTypeId(),
                EntityStatus.ACTIVE.getStatus())) {
            throw new BadRequestAlertException(ErrorCode.MSG1043);
        }

        LviVehiclePackageFeeResponse packageFeeResponse = apiLviService.getVehiclePackageFee(new LviVehiclePackageFeeRequest(request.getPackageCode(), request.getVehicleCode()));
        //create otp
        Merchant merchant = this.merchantRepository.findByMerchantCodeAndStatusAndOtherType("LVI",
                EntityStatus.ACTIVE.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        OtpTransferInsuranceRequest otpTransferTransRequest = new OtpTransferInsuranceRequest();
        otpTransferTransRequest.setCustomerAccNumber(request.getCustomerAccNumber());
        otpTransferTransRequest.setCustomerCurrency(request.getCustomerCurrency());
        otpTransferTransRequest.setBeneficiaryCustomerName(constantProperties.getLvi().getAccountName());
        otpTransferTransRequest.setBeneficiaryBankCode(constantProperties.getLvi().getAccountBank());
        otpTransferTransRequest.setOtpConfirmType(request.getOtpConfirmType());
        otpTransferTransRequest.setAmount(packageFeeResponse.getPaid().longValue());
        otpTransferTransRequest.setRemark(Labels.getLabels(LabelKey.LABEL_BUY_INSURANCE));
        otpTransferTransRequest.setSetDefaultAccount(request.isSetDefaultAccount());
        otpTransferTransRequest.setBeneficiaryAccountNumber(merchant.getMerchantAccountNumber());


        OtpTransferTransResponse otpTransferTransResponse = transferService.requestOtpTransferInsurance(otpTransferTransRequest);
        //save request
        TransactionInsurance transactionInsurance = new TransactionInsurance();
        transactionInsurance.setAccountPhone(customer.getPhoneNumber());
        transactionInsurance.setAccountName(customer.getFullname());
        transactionInsurance.setInsuredName(request.getInsuredName());
        transactionInsurance.setInsuredPhoneNumber(insuredPhoneNumber);
        transactionInsurance.setInsuredId(request.getInsuredId());
        transactionInsurance.setIdCardTypeId(request.getIdCardTypeId());
        transactionInsurance.setPackageCode(request.getPackageCode());
        transactionInsurance.setVehicleCode(request.getVehicleCode());
        transactionInsurance.setDeliveryCode(request.getDeliveryCode());
        transactionInsurance.setPayCurrency(packageFeeResponse.getCurrency());
        transactionInsurance.setTransferTransactionId(otpTransferTransResponse.getTransferTransactionId());
        transactionInsurance.setPayTotal(packageFeeResponse.getPaid());
        transactionInsurance.setPremium(packageFeeResponse.getPremium());
        transactionInsurance.setDiscount(packageFeeResponse.getDiscount());
        transactionInsurance.setType(InsuranceType.VEHICLE);
        transactionInsurance.setContractType(InsuranceContractType.NEW);
        transactionInsurance.setCustomerId(customer.getCustomerId());
        transactionInsurance.setTransactionStatus(TransactionStatus.PROCESSING);

        transactionInsurance = transactionInsuranceRepository.save(transactionInsurance);

        //save image
        storageService.save(registrationImage, TransactionInsurance.class.getName(), transactionInsurance.getTransactionInsuranceId(), Resource.INSURANCE);
        return otpTransferTransResponse;
    }

    @Override
    @Transactional(noRollbackFor = NoRollBackBadRequestAlertException.class)
    public OtpTransansferLviConfirmResponse confirmBuyInsurance(OtpTransferConfirmRequest request) {
        Customer customer = customerService.getCustomerLogin();

        Transaction transaction = this.transactionRepository
                .findByTransactionIdAndCustomerId(request.getTransactionId(), customer.getCustomerId());

        transferService.verifyTransaction(transaction);

        TransactionInsurance transactionInsurance = transactionInsuranceRepository.findByTransferTransactionId(transaction.getTransferTransactionId()).get();

        TransactionDTO transactionDTO = this.transactionMapper.toDto(transaction);

        transactionDTO.setBeneficiaryAccountNumber(transactionDTO.getTarget());
        transactionDTO.setT24Channel(this.mbApiConstantProperties.getInsurancePayment().getChannel());
        transactionDTO.setT24ServiceType(this.mbApiConstantProperties.getInsurancePayment().getServiceType());
        transactionDTO.setT24TransferType(this.mbApiConstantProperties.getInsurancePayment().getTransferType());

        // call api confirm
        MBOtpTransConfirmResponse response = transferService.confirmFundTransferMB(transactionDTO, request.getOtp(), request.getDeviceId());
        String transactionCode = response.getT24ReferenceNumber();

        // Nếu lỗi => revert giao dịch
        // nếu thành công -> gọi api mua lvi
        if (Validator.equals(response.getTransactionStatus(), TransactionStatus.SUCCESS)) {
            buyLviInsurance(transaction, transactionInsurance, response);

        } else if (Validator.equals(response.getTransactionStatus(), TransactionStatus.TIMEOUT)) {
            _log.info("Transaction has been timeout, {}", transaction.getTransactionId());

            InquiryTransactionStatusRequest transactionStatusRequest = InquiryTransactionStatusRequest.builder()
                    .transactionId(response.getReferenceNumber())
                    .build();

            InquiryTransactionStatusDTO inquiryTransactionStatusDTO = this.apiGeeTransferService
                    .inquiryTransactionStatus(transactionStatusRequest);

            if (Validator.isNull(inquiryTransactionStatusDTO)
                    || Validator.isNotNull(inquiryTransactionStatusDTO.getErrorCode())) {
                transactionInsurance.setTransactionStatus(TransactionStatus.TIMEOUT);
                throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
            } else {
                transactionCode = inquiryTransactionStatusDTO.getT24ReferenceNumber();
            }
        } else {
            transaction.setTransactionStatus(TransactionStatus.FAILED);
            transactionInsurance.setTransactionStatus(TransactionStatus.FAILED);
            transaction.setTransactionFinishTime(Instant.now());

            this.transactionRepository.save_(transaction);

            if (Validator.isNotNull(response.getT24ErrorCode())) {
                transaction.setDescription(response.getT24ErrorCode().getCode());
                throw new NoRollBackBadRequestAlertException(response.getT24ErrorCode().getErrorCode());
            }

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1023);
        }

        transaction.setTransactionFinishTime(Instant.now());
        transaction.setTransactionCode(transactionCode);
        transactionInsurance.setTransactionCode(transaction.getTransactionCode());

        this.transactionRepository.save_(transaction);

        this.createNotification(this.transactionMapper.toDto(transaction));

        return OtpTransansferLviConfirmResponse.builder()
                .transactionTime(Instant.now())
                .transactionCode(response.getT24ReferenceNumber())
                .certificateImage(transactionInsurance.getCertificateImage())
                .identificationNo(transactionInsurance.getIdentificationNo())
                .build();
    }

    private void buyLviInsurance(Transaction transaction, TransactionInsurance transactionInsurance, MBOtpTransConfirmResponse response) {
        LviSoapResponse lviSoapResponse = null;
        String registrationImage = "";
        try {
            if (transactionInsurance.getType().equals(InsuranceType.VEHICLE)) {
                if (transactionInsurance.getContractType().equals(InsuranceContractType.NEW)) {
                    List<FileEntry> registrationImages = storageService.getFileEntries(TransactionInsurance.class.getName(), transactionInsurance.getTransactionInsuranceId(), Resource.INSURANCE);
                    if (registrationImages.size() > 0) {
                        registrationImage = new String(Base64.encodeBase64(storageService.getMultipartFile(registrationImages.get(0)).getBytes()));
                    }
                    lviSoapResponse = this.apiLviService.buyVehicleInsurance(
                            LviVehicleInsuranceBuyRequest.builder()
                                    .accountName(transactionInsurance.getAccountName())
                                    .accountPhone(transactionInsurance.getAccountPhone())
                                    .packageCode(transactionInsurance.getPackageCode())
                                    .vehicleCode(transactionInsurance.getVehicleCode())
                                    .deliveryCode(transactionInsurance.getDeliveryCode())
                                    .payTotal(transactionInsurance.getPayTotal().toString())
                                    .registrationImage(registrationImage)
                                    .payCurrency(transactionInsurance.getPayCurrency())
                                    .transactionRef(response.getT24ReferenceNumber())
                                    .transactionDate(LocalDate.now().toString())
                                    .build()
                    );
                } else {
                    lviSoapResponse = this.apiLviService.renewVehicleInsurance(
                            LviVehicleInsuranceBuyRequest.builder()
                                    .accountName(transactionInsurance.getAccountName())
                                    .accountPhone(transactionInsurance.getAccountPhone())
                                    .certificate(transactionInsurance.getCertificate())
                                    .deliveryCode(transactionInsurance.getDeliveryCode())
                                    .payTotal(transactionInsurance.getPayTotal().toString())
                                    .payCurrency(transactionInsurance.getPayCurrency())
                                    .transactionRef(response.getT24ReferenceNumber())
                                    .transactionDate(LocalDate.now().toString())
                                    .build());
                }
                if (lviSoapResponse.isSuccessful()) {
                    transactionInsurance.setIdentificationNo(((LviVehicleBuyInsuranceResponse) lviSoapResponse).getIdentificationNo());
                    transactionInsurance.setCertificateImage(((LviVehicleBuyInsuranceResponse) lviSoapResponse).getCertificateImage());
                }
            } else {
                lviSoapResponse = this.apiLviService.buyHealthInsurance(
                        LviHealthInsuranceBuyRequest.builder()
                                .accountName(transactionInsurance.getAccountName())
                                .accountPhone(transactionInsurance.getAccountPhone())
                                .insuredId(transactionInsurance.getInsuredId())
                                .insuredName(transactionInsurance.getInsuredName())
                                .insuredBirthDay(transactionInsurance.getInsuredBirthday())
                                .packageCode(transactionInsurance.getPackageCode())
                                .deliveryCode(transactionInsurance.getDeliveryCode())
                                .payTotal(transactionInsurance.getPayTotal().toString())
                                .payCurrency(transactionInsurance.getPayCurrency())
                                .transactionRef(response.getT24ReferenceNumber())
                                .transactionDate(LocalDate.now().toString())
                                .build()
                );
                if (lviSoapResponse.isSuccessful()) {
                    transactionInsurance.setIdentificationNo(((LviHealthBuyInsuranceResponse) lviSoapResponse).getIdentificationNo());
                    transactionInsurance.setCertificateImage(((LviHealthBuyInsuranceResponse) lviSoapResponse).getCertificateImage());
                }
            }
        } catch (Exception e) {
            _log.error("get registrationImage error: {}", e);
        }

        // revert giao dịch
        if (lviSoapResponse == null || !lviSoapResponse.isSuccessful()) {
            _log.info("buy insurance failed, revert transaction, transactionID: {}", transaction.getTransactionId());
            this.apiGeeTransferService.revert(RevertTransactionRequest.builder()
                    .t24ReferenceNumber(response.getT24ReferenceNumber())
                    .transactionId(response.getReferenceNumber())
                    .build());

            transaction.setTransactionStatus(TransactionStatus.FAILED);
            transactionInsurance.setTransactionStatus(TransactionStatus.FAILED);
            if (lviSoapResponse == null) {
                transaction.setFailureCause("Buy insurance return null");
            } else {
                transaction.setFailureCause(lviSoapResponse.getMsg().getMsgContent());
            }

            this.transactionRepository.save_(transaction);

            throw new NoRollBackBadRequestAlertException(ErrorCode.MSG1162);
        }

        transaction.setTransactionStatus(TransactionStatus.SUCCESS);
        transactionInsurance.setTransactionStatus(TransactionStatus.SUCCESS);
    }

    @Override
    @Transactional
    public OtpTransferTransResponse requestBuyLviHealthInsurance(LviBuyHealthInsuranceRequest request) {
        verifyDeliveryCode(request.getDeliveryCode());
        String insuredPhoneNumber = StringUtil.formatLaosPhoneNumber(request.getInsuredPhoneNumber());

        Customer customer = customerService.getCustomerLogin();

        // 2. Check idCardTypeId exists
        if (!this.idCardTypeRepository.existsByIdCardTypeIdAndStatus(request.getIdCardTypeId(),
                EntityStatus.ACTIVE.getStatus())) {
            throw new BadRequestAlertException(ErrorCode.MSG1043);
        }

        LviHealthPackageFeeResponse packageFeeResponse = apiLviService.getHealthPackageFee(new LviHealthPackageFeeRequest(request.getPackageCode()));
        //create otp
        Merchant merchant = this.merchantRepository.findByMerchantCodeAndStatusAndOtherType("LVI",
                EntityStatus.ACTIVE.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG1103);
        }

        OtpTransferInsuranceRequest otpTransferTransRequest = new OtpTransferInsuranceRequest();
        otpTransferTransRequest.setCustomerAccNumber(request.getCustomerAccNumber());
        otpTransferTransRequest.setCustomerCurrency(request.getCustomerCurrency());
        otpTransferTransRequest.setBeneficiaryCustomerName(constantProperties.getLvi().getAccountName());
        otpTransferTransRequest.setBeneficiaryBankCode(constantProperties.getLvi().getAccountBank());
        otpTransferTransRequest.setOtpConfirmType(request.getOtpConfirmType());
        otpTransferTransRequest.setAmount(packageFeeResponse.getPaid().longValue());
        otpTransferTransRequest.setRemark(Labels.getLabels(LabelKey.LABEL_BUY_INSURANCE));
        otpTransferTransRequest.setSetDefaultAccount(request.isSetDefaultAccount());
        otpTransferTransRequest.setBeneficiaryAccountNumber(merchant.getMerchantAccountNumber());

        OtpTransferTransResponse otpTransferTransResponse = transferService.requestOtpTransferInsurance(otpTransferTransRequest);
        //save request
        TransactionInsurance transactionInsurance = new TransactionInsurance();
        transactionInsurance.setAccountPhone(customer.getPhoneNumber());
        transactionInsurance.setAccountName(customer.getFullname());
        transactionInsurance.setInsuredName(request.getInsuredName());
        transactionInsurance.setInsuredPhoneNumber(insuredPhoneNumber);
        transactionInsurance.setInsuredBirthday(request.getInsuredBirthDay().toString());
        transactionInsurance.setInsuredId(request.getInsuredId());
        transactionInsurance.setIdCardTypeId(request.getIdCardTypeId());
        transactionInsurance.setPackageCode(request.getPackageCode());
        transactionInsurance.setDeliveryCode(request.getDeliveryCode());
        transactionInsurance.setPayCurrency("LAK");
        transactionInsurance.setTransferTransactionId(otpTransferTransResponse.getTransferTransactionId());
        transactionInsurance.setPayTotal(packageFeeResponse.getPaid());
        transactionInsurance.setDiscount(packageFeeResponse.getDiscount());
        transactionInsurance.setPremium(packageFeeResponse.getPremium());
        transactionInsurance.setType(InsuranceType.HEALTH);
        transactionInsurance.setContractType(InsuranceContractType.NEW);
        transactionInsurance.setCustomerId(customer.getCustomerId());
        transactionInsurance.setTransactionStatus(TransactionStatus.PROCESSING);

        transactionInsurance = transactionInsuranceRepository.save(transactionInsurance);

        return otpTransferTransResponse;

    }

    private void verifyDeliveryCode(String deliveryCode) {
        LviDeliveryDTO deliveryDTO = getDelivery();
        if (deliveryDTO.getDelivery().stream().noneMatch(d -> d.getCode().equals(deliveryCode))) {
            throw new BadRequestAlertException(ErrorCode.MSG101005);
        }
    }

    private void createNotification(TransactionDTO transactionDTO) {
        Map<String, String> valuesMapDebitAccount = new HashMap<>();

        String transactionFinishTime = transactionDTO.formatTransactionFinishTime();

        // transfer
        valuesMapDebitAccount.put(TemplateField.LABEL_PAYMENT_ACCOUNT.name(), Labels.getLabels(LabelKey.LABEL_ACCOUNT_TYPE_PAYMENT_ACCOUNT));
        valuesMapDebitAccount.put(TemplateField.PAYMENT_ACCOUNT.name(), transactionDTO.getCustomerAccNumber());
        valuesMapDebitAccount.put(TemplateField.LABEL_TRANSACTION_AMOUNT.name(), Labels.getLabels(LabelKey.LABEL_AMOUNT_TRANSACTION));
        valuesMapDebitAccount.put(TemplateField.TRANSACTION_AMOUNT.name(), StringPool.MINUS + StringUtil.formatMoney(transactionDTO.getActualTransactionAmount()));
        valuesMapDebitAccount.put((TemplateField.CURRENCY.name()), transactionDTO.getTransactionCurrency());
        valuesMapDebitAccount.put((TemplateField.LABEL_TRANSACTION_TIME.name()), Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE));
        valuesMapDebitAccount.put(TemplateField.TRANSACTION_TIME.name(), transactionFinishTime);
        valuesMapDebitAccount.put(TemplateField.LABEL_CONTENT_TRANSACTION.name(), Labels.getLabels(LabelKey.LABEL_TRANSACTION_CONTENT));
        valuesMapDebitAccount.put(TemplateField.CONTENT_TRANSACTION.name(), transactionDTO.getMessage());

        valuesMapDebitAccount.put(TemplateField.LABEL_AT.name(), Labels.getLabels(LabelKey.LABEL_AT));
        valuesMapDebitAccount.put((TemplateField.BANK_CODE.name()), transactionDTO.getBankCode());
        valuesMapDebitAccount.put((TemplateField.MESSAGE.name()), transactionDTO.getMessage());
        valuesMapDebitAccount.put((TemplateField.TRANSACTION_CODE.name()), transactionDTO.getTransactionCode());

        ContentTemplate templateTransferMoney = this.contentTemplateRepository.findByTemplateCodeAndStatus(
                TemplateCode.BALANCE_CHANGE_PAY_INSURANCE.name(), EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(templateTransferMoney)) {
            NotificationDTO notiForReceiver = new NotificationDTO();

            notiForReceiver.setNotificationStatus(NotificationStatus.PENDING);
            notiForReceiver.setNotificationType(NotificationType.TRANSFER_TRANSACTION);
            notiForReceiver.setTargetType(TargetType.USER);
            notiForReceiver.setEndUserType(EndUserType.CUSTOMER);
            notiForReceiver.setClassPk(transactionDTO.getCustomerId());
            notiForReceiver.setPublishTime(LocalDateTime.now());

            notiForReceiver.setTitle(Labels.getLabels(templateTransferMoney.getTitle()));
            notiForReceiver
                    .setContent(StringUtil.replaceMapValue(templateTransferMoney.getContent(), valuesMapDebitAccount));
            notiForReceiver.setDescription(templateTransferMoney.getDescription());

            Notification notificationAfterSaved = this.notificationRepository
                    .save(this.notificationMapper.toEntity(notiForReceiver));

            // save notification transaction
            NotiTransaction notiTransaction = new NotiTransaction();

            notiTransaction.setNotificationId(notificationAfterSaved.getNotificationId());
            notiTransaction.setTransactionId(transactionDTO.getTransferTransactionId());
            notiTransaction.setStatus(EntityStatus.ACTIVE.getStatus());

            this.notiTransactionRepository.save(notiTransaction);

            this.sendService.sendNotification(this.notificationMapper.toDto(notificationAfterSaved));
        }
    }

    private void enrichInsuranceFeeInformation(InsuranceFeeDTO dto, Long amount) {

        FeeTransactionResponse feeTransactionResponse = transactionService.getFeeOfTransaction(TransactionFeeTypeCode.INSURANCE, amount, this.customerProperties.getDefaultCurrency());

        if (Validator.isNotNull(feeTransactionResponse.getConfigurationFeeType())) {
            BigDecimal totalPaid = BigDecimal.valueOf(Math.round(amount.doubleValue() * (1 - feeTransactionResponse.getDiscount() / 100) + feeTransactionResponse.getFee()));

            dto.setTotalPaid(totalPaid);
            dto.setMbDiscount(BigDecimal.valueOf(feeTransactionResponse.getDiscount()));
            dto.setFee(BigDecimal.valueOf(feeTransactionResponse.getFee()));
        }
    };
}
