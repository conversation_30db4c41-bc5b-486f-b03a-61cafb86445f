<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.mb</groupId>
		<artifactId>parent</artifactId>
		<version>${app.version}</version>
	</parent>

	<groupId>com.mb.laos</groupId>
	<artifactId>api-gateway</artifactId>
	<name>api-gateway</name>
	<packaging>jar</packaging>
	<description>Api Gateway module</description>

	<properties>
		<commons-codec.version>1.4</commons-codec.version>
		<java-crypto.version>0.13.0</java-crypto.version>
		<libfb303.version>0.8.0</libfb303.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>area</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>base</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>utils</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>api-common</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>label-bundle</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>security</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>cache</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>annotation</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>request-log</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>kaptcha</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>storage</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>springfox</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>sms-sender</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>excel-helper</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>otp-generator</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>mail-sender</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>api-consumer</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>business-model</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>notification</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>topup</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>repository</artifactId>
			<version>${app.version}</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-hibernate5</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-hppc</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.module</groupId>
			<artifactId>jackson-module-afterburner</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>

		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itextpdf</artifactId>
			<version>${itextpdf.version}</version>
		</dependency>

		<dependency>
			<groupId>org.liquibase</groupId>
			<artifactId>liquibase-core</artifactId>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.liquibase.ext/liquibase-hibernate5 -->
		<dependency>
			<groupId>org.liquibase.ext</groupId>
			<artifactId>liquibase-hibernate5</artifactId>
			<version>${liquibase.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
<!--				<exclusion>-->
<!--					<artifactId>log4j-to-slf4j</artifactId>-->
<!--					<groupId>org.apache.logging.log4j</groupId>-->
<!--				</exclusion>-->
			</exclusions>
		</dependency>


		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.tomcat</groupId>
					<artifactId>tomcat-jdbc</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-mail</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>

		<!-- <dependency> -->
		<!-- <groupId>mysql</groupId> -->
		<!-- <artifactId>mysql-connector-java</artifactId> -->
		<!-- </dependency> -->
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-jpamodelgen</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
		</dependency>

		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>${mapstruct.version}</version>
		</dependency>

		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-processor</artifactId>
			<scope>provided</scope>
			<version>${mapstruct.version}</version>
		</dependency>

		<dependency>
			<groupId>com.zaxxer</groupId>
			<artifactId>HikariCP</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>${commons-text.version}</version>
		</dependency>

		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>${common-file-upload.version}</version>
		</dependency>

		<dependency>
			<groupId>jakarta.servlet</groupId>
			<artifactId>jakarta.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>
			<version>${commons-compress.version}</version>
		</dependency>

		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>${commons-net.version}</version>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-spatial</artifactId>
		</dependency>

		<dependency>
			<groupId>com.bedatadriven</groupId>
			<artifactId>jackson-datatype-jts</artifactId>
			<version>${jackson-datatype-jts.version}</version>
		</dependency>

		<dependency>
			<groupId>org.locationtech.jts</groupId>
			<artifactId>jts-core</artifactId>
			<version>${jts-core.version}</version>
		</dependency>

		<dependency>
			<groupId>org.imgscalr</groupId>
			<artifactId>imgscalr-lib</artifactId>
			<version>${imgscalr-lib.version}</version>
		</dependency>

		<dependency>
			<groupId>com.hazelcast</groupId>
			<artifactId>hazelcast</artifactId>
		</dependency>

		<dependency>
			<groupId>com.hazelcast</groupId>
			<artifactId>hazelcast-hibernate53</artifactId>
		</dependency>

		<dependency>
			<groupId>com.hazelcast</groupId>
			<artifactId>hazelcast-spring</artifactId>
		</dependency>

<!-- 		<dependency> -->
<!-- 			<groupId>org.springframework.boot</groupId> -->
<!-- 			<artifactId>spring-boot-starter-actuator</artifactId> -->
<!-- 		</dependency> -->

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.jsoup/jsoup -->
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>${jsoup.version}</version>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>${commons-io.version}</version>
		</dependency>

		<dependency>
			<groupId>org.jxls</groupId>
			<artifactId>jxls</artifactId>
			<version>${jxls.version}</version>
		</dependency>

		<dependency>
			<groupId>org.jxls</groupId>
			<artifactId>jxls-jexcel</artifactId>
			<version>${jxls-jexcel.version}</version>
		</dependency>

		<dependency>
			<groupId>org.jxls</groupId>
			<artifactId>jxls-poi</artifactId>
			<version>${jxls.version}</version>
		</dependency>

		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>${okhttp.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<!--jackson for xml -->
		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-xml</artifactId>
		</dependency>

		<!-- rsa lib -->
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>${bcprov.version}</version>
		</dependency>
		<!-- kaptcha -->
		<dependency>
			<groupId>com.github.penggle</groupId>
			<artifactId>kaptcha</artifactId>
			<version>${kaptcha.version}</version>
		</dependency>
		<!-- hibernate-search -->
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-search-orm</artifactId>
			<version>${hibernate-search.version}</version>
		</dependency>

		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-search-engine</artifactId>
			<version>${hibernate-search.version}</version>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>

		<!-- Dynamic Key -->
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>${commons-codec.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.thrift</groupId>
			<artifactId>libfb303</artifactId>
			<version>${libfb303.version}</version>
			<type>pom</type>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-log4j2</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
			<version>2.9.10</version>
		</dependency>

		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-log4j-appender</artifactId>
			<version>3.2.0</version>
		</dependency>


		<!-- test scoped -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<excludes>
					<exclude>**/*.html</exclude>
					<exclude>**/*.css</exclude>
					<exclude>**/*.js</exclude>
				</excludes>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<annotationProcessorPaths>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>${mapstruct.version}</version>
						</path>
						<!-- For JPA static metamodel generation -->
						<path>
							<groupId>org.hibernate</groupId>
							<artifactId>hibernate-jpamodelgen</artifactId>
							<version>${hibernate.version}</version>
						</path>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>${lombok.version}</version>
						</path>
						<!-- This is needed when using Lombok 1.8.16 and above -->
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok-mapstruct-binding</artifactId>
							<version>${lombok-mapstruct-binding.version}</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.liquibase</groupId>
				<artifactId>liquibase-maven-plugin</artifactId>
				<configuration>
					<propertyFile>src/main/resources/liquibase.properties</propertyFile>
					<diffChangeLogFile>src/main/resources/db/changelog/${build.time}.xml</diffChangeLogFile>
					<logging>debug</logging>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>org.liquibase.ext</groupId>
						<artifactId>liquibase-hibernate5</artifactId>
						<version>${liquibase.version}</version>
					</dependency>
					<dependency>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot-starter-data-jpa</artifactId>
						<version>${spring-boot.version}</version>
					</dependency>
					<dependency>
						<groupId>javax.validation</groupId>
						<artifactId>validation-api</artifactId>
						<version>${validation-api.version}</version>
					</dependency>
				</dependencies>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>${skipTests}</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
						<configuration>
							<dataFileIncludes>
								<dataFileInclude>${project.build.directory}/jacoco.exec</dataFileInclude>
							</dataFileIncludes>
							<outputDirectory>${project.build.directory}/coverage-report</outputDirectory>
						</configuration>
					</execution>
					<execution>
						<id>report</id>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<systemPropertyVariables>
						<jacoco-agent.destfile>${project.build.directory}/jacoco.exec</jacoco-agent.destfile>
					</systemPropertyVariables>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<profiles>
		<profile>
			<id>dev</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<war.name>dev</war.name>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<war.name>prod</war.name>
			</properties>
		</profile>
		<profile>
			<id>qa</id>
			<properties>
				<war.name>qa</war.name>
			</properties>
		</profile>
	</profiles>
</project>
