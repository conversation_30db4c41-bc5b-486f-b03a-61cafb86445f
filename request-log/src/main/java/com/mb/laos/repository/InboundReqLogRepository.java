/**
 * 
 */
package com.mb.laos.repository;

import java.time.Instant;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.mb.laos.model.InboundReqLog;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface InboundReqLogRepository extends CrudRepository<InboundReqLog, Long> {

    /**
     * @param timestampAt
     * @return
     */
    long deleteByCreatedDateLessThanEqual(Instant timestamp);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    default InboundReqLog save_(InboundReqLog entity) {
        return save(entity);
    }


}
