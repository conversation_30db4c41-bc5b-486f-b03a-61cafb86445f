/**
 * 
 */
package com.mb.laos.repository;

import java.time.Instant;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.mb.laos.model.OutboundReqLog;

/**
 * <AUTHOR>
 *
 */
@Repository
public interface OutboundReqLogRepository extends CrudRepository<OutboundReqLog, Long> {

    /**
     * @param timestampAt
     * @return
     */
    long deleteByCreatedDateLessThanEqual(Instant timestampAt);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    default OutboundReqLog save_(OutboundReqLog entity) {
        return save(entity);
    }


}
