<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.mb</groupId>
		<artifactId>parent</artifactId>
		<version>${app.version}</version>
	</parent>

	<groupId>com.mb.laos.common</groupId>
	<artifactId>request-log</artifactId>
	<packaging>jar</packaging>
	<name>request-log</name>

	<dependencies>
		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>base</artifactId>
			<version>${app.version}</version>
		</dependency>
		
		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>utils</artifactId>
			<version>${app.version}</version>
		</dependency>
		
		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>api-common</artifactId>
			<version>${app.version}</version>
		</dependency>
	
		<dependency>
			<groupId>com.mb.laos.common</groupId>
			<artifactId>security</artifactId>
			<version>${app.version}</version>
		</dependency>
	
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.tomcat</groupId>
					<artifactId>tomcat-jdbc</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-boot-starter</artifactId>
			<version>${springfox.version}</version>
		</dependency>
	
		<dependency>
			<groupId>org.zalando</groupId>
			<artifactId>problem-spring-web</artifactId>
			<version>${problem-spring-web.version}</version>
		</dependency>
	
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		
		<!-- test scoped -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>
</project>