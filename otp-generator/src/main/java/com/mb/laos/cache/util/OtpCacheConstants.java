package com.mb.laos.cache.util;

public interface OtpCacheConstants {
    public interface Others {
        public static final String OTP = "otp";

        public static final String OTP_ATTEMPT = "otp-attempt";

        public static final String D_OTP_REGISTER_DEVICE = "d-otp-register_device";

        public static final String D_OTP_VERIFY_QR = "d-otp-verify-qr";

        public static final String OTP_VERIFY = "otp-verify";

        public static final String D_OTP_SAVING_ACCOUNT = "d-otp-saving-account";

        public static final String MMONEY_BILLING = "mmoney-billing";

        public static final String OTP_REGISTER_SMS = "otp-register-sms";

        public static final String INTERNATIONAL_PAYMENT = "international-payment";

        public static final String OTP_CANCEL_SMS = "otp-cancel-sms";

        public static final String REQUEST_HEADER = "session-request-header";

        public static final String OTP_PREMIUM_ACC_NUMBER = "otp-premium-acc-number";

        public static final String NOTIFICATION_TRANSFER = "notification-transfer";
    }
}
