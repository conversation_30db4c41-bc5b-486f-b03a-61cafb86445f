/**
 *
 */
package com.mb.laos.repository;

import java.util.UUID;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import com.mb.laos.cache.util.OtpCacheConstants;
import com.mb.laos.model.OtpValue;
import com.mb.laos.util.StringPool;

/**
 * <AUTHOR>
 *
 */
public interface OtpRepository {

	/**
	 * @param key
	 * @param data
	 */
	@CachePut(cacheNames = OtpCacheConstants.Others.OTP, key = "#key", unless = "#result == null")
	default OtpValue put(String key, OtpValue otp) {
		return otp;
	}

	/**
	 * @param key
	 * @return
	 */
	@Cacheable(cacheNames = OtpCacheConstants.Others.OTP, key = "#key", unless = "#result == null")
	default OtpValue getIfPresent(String key) {
		return null;
	}

	/**
	 * @param key
	 */
	@CacheEvict(cacheNames = OtpCacheConstants.Others.OTP, key = "#key")
	default String invalidate(String key) {
		return key;
	}

	@CachePut(cacheNames = OtpCacheConstants.Others.OTP_ATTEMPT, key = "#key", unless = "#result == null")
	default Integer putAttempt(String key, Integer attempt) {
		return attempt;
	}

	@Cacheable(cacheNames = OtpCacheConstants.Others.OTP_ATTEMPT, key = "#key", unless = "#result == null")
	default Integer getAttemptIfPresent(String key) {
		return 0;
	}

	@CacheEvict(cacheNames = OtpCacheConstants.Others.OTP_ATTEMPT, key = "#key")
	default String invalidateAttempt(String key) {
		return key;
	}

	/**
	 *
	 * @param phoneNumber
	 * @return
	 */
	@CachePut(cacheNames = OtpCacheConstants.Others.OTP_VERIFY, key = "#phoneNumber", unless = "#result == null")
	default String putTransaction(String phoneNumber) {
		return UUID.randomUUID().toString();
	}

	@CachePut(cacheNames = OtpCacheConstants.Others.OTP_VERIFY, key = "#phoneNumber", unless = "#result == null")
    default String putTransaction(String phoneNumber, String extend) {
        StringBuilder sb = new StringBuilder(3);

        sb.append(UUID.randomUUID());
        sb.append(StringPool.COLON);
        sb.append(extend);

	    return sb.toString();
    }

	/**
	 * get transaction by phone number
	 * @param key
	 * @return
	 */
	@Cacheable(cacheNames = OtpCacheConstants.Others.OTP_VERIFY, key = "#p0", unless = "#result == null")
	default String getTransactionIfPresent(String phoneNumber) {
		return null;
	}

	/**
	 * @param key
	 */
	@CacheEvict(cacheNames = OtpCacheConstants.Others.OTP_VERIFY, key = "#phoneNumber")
	default String invalidateTransaction(String phoneNumber) {
		return phoneNumber;
	}
}
