sonar.projectKey=mb-laos-cms-be
sonar.sources=cms-admin/src/main
sonar.tests=cms-admin/src/test
sonar.host.url=http://*************:9006
sonar.login=****************************************
sonar.qualitygate.wait=true
sonar.java.binaries=./cms-admin/target/classes
sonar.test.inclusions=cms-admin/src/test/**/*.*, cms-admin/src/main/webapp/**/*.spec.ts
sonar.coverage.jacoco.xmlReportPaths=target/jacoco/jacoco.xml
sonar.java.codeCoveragePlugin=jacoco
sonar.junit.reportPaths=target/surefire-reports,target/failsafe-reports

sonar.sourceEncoding=UTF-8
sonar.exclusions=.mvn, cms-admin/src/main/webapp/main.ts, cms-admin/src/main/webapp/app/main.ts, cms-admin/src/main/webapp/content/**/*.*, cms-admin/src/main/webapp/i18n/*.js, target/classes/static/**/*.*, cms-admin/src/main/resources/**, cms-admin/src/main/webapp/app/router/index.ts

sonar.typescript.tsconfigPath=tsconfig.json

sonar.issue.ignore.multicriteria=S1068,S3437,S4502,S4684,S4032,UndocumentedApi

# Rule https://rules.sonarsource.com/java/RSPEC-3437 is ignored, as a JPA-managed field cannot be transient
sonar.issue.ignore.multicriteria.S3437.resourceKey=cms-admin/src/main/java/**/*
sonar.issue.ignore.multicriteria.S3437.ruleKey=squid:S3437

# Rule https://rules.sonarsource.com/java/RSPEC-1176 is ignored, as we want to follow "clean code" guidelines and classes, methods and arguments names should be self-explanatory
sonar.issue.ignore.multicriteria.UndocumentedApi.resourceKey=cms-admin/src/main/java/**/*
sonar.issue.ignore.multicriteria.UndocumentedApi.ruleKey=squid:UndocumentedApi

# Rule https://rules.sonarsource.com/java/RSPEC-4502 is ignored, as for JWT tokens we are not subject to CSRF attack
sonar.issue.ignore.multicriteria.S4502.resourceKey=cms-admin/src/main/java/**/*
sonar.issue.ignore.multicriteria.S4502.ruleKey=java:S4502

# Rule https://rules.sonarsource.com/java/RSPEC-4684
sonar.issue.ignore.multicriteria.S4684.resourceKey=cms-admin/src/main/java/**/*
sonar.issue.ignore.multicriteria.S4684.ruleKey=java:S4684

# Rule: Packages containing only "package-info.java" should be removed
sonar.issue.ignore.multicriteria.S4032.resourceKey=cms-admin/src/main/java/**/*
sonar.issue.ignore.multicriteria.S4032.ruleKey=java:S4032

# Rule https://rules.sonarsource.com/java/RSPEC-S1068 is ignored
sonar.issue.ignore.multicriteria.S1068.resourceKey=cms-admin/src/main/java/**/*
sonar.issue.ignore.multicriteria.S1068.ruleKey=java:S1068
