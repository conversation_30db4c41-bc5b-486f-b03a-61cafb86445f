package com.mb.laos.repository;

import com.mb.laos.model.User;
import com.mb.laos.repository.extend.UserRepositoryExtend;
import org.apache.commons.collections4.ListUtils;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long>, UserRepositoryExtend {
	
	
	/**
	 * @param email
	 * @return
	 */
	boolean existsByEmail(String email);
	
	/**
	 * @param phoneNumber
	 * @return
	 */
	boolean existsByPhoneNumber(String phoneNumber);

	/**
	 * @param phoneNumber
	 * @param status
	 * @return
	 */
	boolean existsByPhoneNumberAndStatusIs(String phoneNumber, int status);

	/**
	 * @param username
	 * @return
	 */
	boolean existsByUsername(String username);

	/**
	 * 
	 * @param username
	 * @return
	 */
	User findByUsername(String username);

	Optional<User> findFirstByPhoneNumber(String phoneNumber);
	
	Optional<User> findFirstByEmail(String email);

	default User findByPhoneNumber(String phoneNumber) {
		return findFirstByPhoneNumber(phoneNumber).orElse(null);
	}

	default User findByEmail(String email) {
		return findFirstByEmail(email).orElse(null);
	}

	default User findByUserId(Long userId) {
		return findById(userId).orElse(null);
	}
	// @formatter:off
	/**
	 * @param oldUser
	 * @param user
	 */
	default User saveChangePhoneNumber(User oldUser, User user) {
		return save(user);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	User save(User user);
	// @formatter:on

	/**
	 *
	 * @param userId
	 * @param status
	 * @return
	 */
	Optional<User> findByUserIdAndStatus(long userId, int status);

	/**
	 * findByUsernameIn
	 *
	 * @param userCreatedAndModified List<String>
	 * @return List<User>
	 */
	List<User> findByUsernameIn(List<String> userCreatedAndModified);

	default List<User> findAllByUsernameInList(List<String> usernames){
		List<User> result = new ArrayList<>();
		//tach ra vi oracle chi cho phep find in toi da 1000 phan tu
		List<List<String>> usernameSublist = ListUtils.partition(usernames, 900);
		for (List<String> usernameList : usernameSublist) {
			result.addAll(findByUsernameIn(usernameList));
		}
		return result;
	}

	/**
	 * exists By PositionId And Status Not
	 *
	 * @param positionId Long
	 * @param status Integer
	 * @return Boolean
	 */
	@Query("SELECT CASE WHEN COUNT(e) > 0 THEN TRUE ELSE FALSE END FROM User e WHERE e.positionId = ?1 AND e.status IN (?2)")
	Boolean existsByPositionIdAndStatusIn(Long positionId, List<Integer> status);

	/**
	 * find list of users by department id
	 *
	 * @param departmentId
	 * @return
	 */
	List<User> findByDepartmentIdAndStatusNot(Long departmentId, Integer status);
	
	@Query("SELECT CASE WHEN COUNT(e) > 0 THEN TRUE ELSE FALSE END FROM User e WHERE e.departmentId = ?1 AND e.status IN (?2)")
	Boolean findByDepartmentIdAndStatus(Long departmentId, List<Integer> status);

	/**
	 *
	 *
	 * @param userIds List<Long>
	 * @return List<User>
	 */
	List<User> findAllByUserIdIn(List<Long> userIds);

	default List<User> findAllByUserIdInList(List<Long> userIds){
		List<User> result = new ArrayList<>();
		//tach ra vi oracle chi cho phep find in toi da 1000 phan tu
		List<List<Long>> userIdSublist = ListUtils.partition(userIds, 900);
		for (List<Long> ids : userIdSublist) {
			result.addAll(findAllByUserIdIn(ids));
		}
		return result;
	}
}
