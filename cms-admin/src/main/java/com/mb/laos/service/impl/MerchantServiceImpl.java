package com.mb.laos.service.impl;

import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.api.request.AccountBalanceRequest;
import com.mb.laos.api.request.CustomerQueryInfoRequest;
import com.mb.laos.api.util.ErrorCode;
import com.mb.laos.cache.util.CmsCacheConstants;
import com.mb.laos.enums.SystemStatus;
import com.mb.laos.request.FeeCreateOrUpdateRequest;
import com.mb.laos.cms.request.ServiceTypeRequest;
import com.mb.laos.cms.response.MerchantTransactionHistoryResponse;
import com.mb.laos.configuration.MerchantHistoryProperties;
import com.mb.laos.configuration.TemplateProperties;
import com.mb.laos.enums.ConfigurationFeeType;
import com.mb.laos.enums.EntityStatus;
import com.mb.laos.enums.EnumTransferType;
import com.mb.laos.enums.MasterMerchantType;
import com.mb.laos.enums.MerchantCreatedOrigin;
import com.mb.laos.enums.TelcoType;
import com.mb.laos.enums.TransactionFeeTypeCode;
import com.mb.laos.enums.TransactionStatus;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.model.Common;
import com.mb.laos.model.Customer;
import com.mb.laos.model.Merchant;
import com.mb.laos.model.ReceiveTransferMoney;
import com.mb.laos.model.ResultSet;
import com.mb.laos.model.Transaction;
import com.mb.laos.model.TransactionFeeType;
import com.mb.laos.model.TransactionMerchant;
import com.mb.laos.model.dto.AccountBalanceDTO;
import com.mb.laos.model.dto.CommonDTO;
import com.mb.laos.model.dto.CustomerInfoT24DTO;
import com.mb.laos.model.dto.FeeDTO;
import com.mb.laos.model.dto.MerchantDTO;
import com.mb.laos.model.dto.TransactionDTO;
import com.mb.laos.model.search.MerchantSearch;
import com.mb.laos.model.search.MerchantTransactionHistorySearch;
import com.mb.laos.model.search.ReceiveTransferMoneySearch;
import com.mb.laos.repository.CommonRepository;
import com.mb.laos.repository.CustomerRepository;
import com.mb.laos.repository.MerchantRepository;
import com.mb.laos.repository.ReceiveTransferMoneyRepository;
import com.mb.laos.repository.TransactionFeeTypeRepository;
import com.mb.laos.repository.TransactionMerchantRepository;
import com.mb.laos.repository.TransactionRepository;
import com.mb.laos.service.ApiGeeCustomerService;
import com.mb.laos.service.ApiGeeTransferService;
import com.mb.laos.service.ApiUnitelService;
import com.mb.laos.service.BlockConcurrencyService;
import com.mb.laos.service.FeeService;
import com.mb.laos.service.MerchantService;
import com.mb.laos.service.QrCodeService;
import com.mb.laos.service.StorageService;
import com.mb.laos.service.mapper.CommonMapper;
import com.mb.laos.service.mapper.MerchantMapper;
import com.mb.laos.service.mapper.TransferTransactionMapper;
import com.mb.laos.util.DateUtil;
import com.mb.laos.util.FileUtil;
import com.mb.laos.util.InstantUtil;
import com.mb.laos.util.LocalDateUtil;
import com.mb.laos.util.QueryUtil;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.UUIDUtil;
import com.mb.laos.util.Validator;
import com.mb.laos.validator.ValidationConstraint;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j // Lombok tự động tạo logger instance
@Service // Đánh dấu class này là một Spring Service component
@Transactional // Tất cả method trong class này sẽ chạy trong transaction
@RequiredArgsConstructor // Lombok tự động tạo constructor với tất cả final fields
public class MerchantServiceImpl implements MerchantService {

    // ==================== CÁC REPOSITORY DEPENDENCIES ====================

    /** Repository quản lý merchant - xử lý thông tin merchant */
    private final MerchantRepository merchantRepository;

    /** Repository quản lý transaction - xử lý giao dịch */
    private final TransactionRepository transactionRepository;

    /** Repository quản lý customer - xử lý thông tin khách hàng */
    private final CustomerRepository customerRepository;

    /** Repository quản lý transaction merchant - xử lý giao dịch merchant */
    private final TransactionMerchantRepository transactionMerchantRepository;

    /** Repository quản lý common data - xử lý dữ liệu chung */
    private final CommonRepository commonRepository;

    /** Repository quản lý receive transfer money - xử lý tiền nhận chuyển */
    private final ReceiveTransferMoneyRepository receiveTransferMoneyRepository;

    /** Repository quản lý transaction fee type - xử lý loại phí giao dịch */
    private final TransactionFeeTypeRepository transactionFeeTypeRepository;

    // ==================== CÁC MAPPER DEPENDENCIES ====================

    /** Mapper chuyển đổi merchant entity/dto */
    private final MerchantMapper merchantMapper;

    /** Mapper chuyển đổi common entity/dto */
    private final CommonMapper commonMapper;

    /** Mapper chuyển đổi transfer transaction entity/dto */
    private final TransferTransactionMapper transferTransactionMapper;

    // ==================== CÁC SERVICE DEPENDENCIES ====================

    /** Service xử lý QR code - tạo và quản lý QR code */
    private final QrCodeService qrCodeService;

    /** Service quản lý file storage - xử lý upload/download file */
    private final StorageService storageService;

    /** Service tích hợp Unitel API - xử lý nạp tiền Unitel */
    private final ApiUnitelService apiUnitelService;

    /** Service chặn concurrency - xử lý chống spam */
    private final BlockConcurrencyService blockConcurrencyService;

    /** Service tích hợp ApiGee Customer - xử lý thông tin khách hàng T24 */
    private final ApiGeeCustomerService apiGeeCustomerService;

    /** Service tích hợp ApiGee Transfer - xử lý chuyển tiền T24 */
    private final ApiGeeTransferService apiGeeTransferService;

    /** Service quản lý fee - xử lý phí giao dịch */
    private final FeeService feeService;

    // ==================== CÁC PROPERTIES DEPENDENCIES ====================

    /** Properties chứa cấu hình template Excel */
    private final TemplateProperties templateProperties;

    /** Properties chứa cấu hình merchant history */
    private final MerchantHistoryProperties properties;


    /**
     * Tạo master merchant
     *
     * Master merchant là merchant cha, có thể có nhiều merchant con.
     * Hỗ trợ 3 loại service: TOPUP, BILLING, OTHER.
     *
     * @param merchantDTO MerchantDTO cần tạo
     * @return MerchantDTO đã được tạo
     */
    @Override // Ghi đè method từ interface MerchantService
    public MerchantDTO createMasterMerchant(MerchantDTO merchantDTO) {
        // Tạo key để chống concurrency (spam) dựa trên merchant code + account number + service type
        String key = merchantDTO.getMerchantCode() + merchantDTO.getMerchantAccountNumber() + merchantDTO.getServiceType();
        Merchant merchant;

        // Chặn concurrency để tránh tạo duplicate master merchant (max 1 request trong 60s)
        this.blockConcurrencyService.blockConcurrency(key, 1, CmsCacheConstants.BlockConcurrency.MASTER_MERCHANT, ErrorCode.MSG101265, 60);

        // VALIDATION 1: Service type bắt buộc
        if (Validator.isNull(merchantDTO.getServiceType())) {
            // Reset spam count và throw exception
            this.blockConcurrencyService.resetSpamCount(key, CmsCacheConstants.BlockConcurrency.MASTER_MERCHANT);
            throw new BadRequestAlertException(ErrorCode.MSG101052); // Service type required
        }

        // VALIDATION 2: Merchant ID phải null (đây là tạo mới)
        if (Validator.isNotNull(merchantDTO.getMerchantId())) {
            // Reset spam count và throw exception
            this.blockConcurrencyService.resetSpamCount(key, CmsCacheConstants.BlockConcurrency.MASTER_MERCHANT);
            throw new BadRequestAlertException(ErrorCode.MSG1087); // Merchant ID must be null for create
        }

        // VALIDATION 3: Nếu không phải OTHER thì merchant code bắt buộc
        if (!Validator.equals(merchantDTO.getServiceType(), MasterMerchantType.OTHER) && Validator.isNull(merchantDTO.getMerchantCode())) {
            // Reset spam count và throw exception
            this.blockConcurrencyService.resetSpamCount(key, CmsCacheConstants.BlockConcurrency.MASTER_MERCHANT);
            throw new BadRequestAlertException(ErrorCode.MSG101156); // Merchant code required for non-OTHER service type
        }

        // VALIDATION 4: Kiểm tra duplicate merchant code và account number
        if (Validator.isNotNull(merchantDTO.getMerchantCode())) {
            // Check trùng mã master merchant với nạp tiền điện thoại và thanh toán hóa đơn
            if (!Validator.equals(merchantDTO.getServiceType(), MasterMerchantType.OTHER) &&
                    (Validator.isNotNull(this.merchantRepository.findByMerchantCodeAndServiceTypeAndStatusNotAndParentIdIsNull(
                            merchantDTO.getMerchantCode(), merchantDTO.getServiceType().name(), EntityStatus.DELETED.getStatus())))) {
                // Reset spam count và throw exception
                this.blockConcurrencyService.resetSpamCount(key, CmsCacheConstants.BlockConcurrency.MASTER_MERCHANT);
                throw new BadRequestAlertException(ErrorCode.MSG10006); // Duplicate merchant code for same service type
            }

            // Kiểm tra account number đã được sử dụng chưa
            // Nếu đã được sử dụng thì phải trùng merchant code và khác loại service thì mới được tạo
            List<Merchant> masterMerchants = this.merchantRepository.findAllByMerchantAccountNumberAndStatusNotAndParentIdIsNull(
                    merchantDTO.getMerchantAccountNumber(), EntityStatus.DELETED.getStatus());

            // Validate business rules cho account number đã tồn tại
            if (Validator.isNotNull(masterMerchants) && (masterMerchants.size() > 1 // Có nhiều hơn 1 master merchant
                    || !Validator.equals(masterMerchants.get(0).getMerchantCode(), merchantDTO.getMerchantCode()) // Merchant code khác nhau
                    || Validator.equals(masterMerchants.get(0).getServiceType(), merchantDTO.getServiceType()))) { // Cùng service type
                throw new BadRequestAlertException(ErrorCode.MSG10008); // Account number already used with different rules
            }
        } else {
            // Nếu không có merchant code thì account number phải chưa được sử dụng
            if (this.merchantRepository.existsByMerchantAccountNumberAndStatusNotAndParentIdIsNull(merchantDTO.getMerchantAccountNumber(), EntityStatus.DELETED.getStatus())) {
                // Reset spam count và throw exception
                this.blockConcurrencyService.resetSpamCount(key, CmsCacheConstants.BlockConcurrency.MASTER_MERCHANT);
                throw new BadRequestAlertException(ErrorCode.MSG10008); // Account number already exists
            }
        }

        // VALIDATION 5: Enrich và kiểm tra CIF tồn tại
        this.enrichAndCheckExistedCif(merchantDTO);

        // VALIDATION 6: Kiểm tra merchant code hợp lệ
        if (!Validator.equals(merchantDTO.getServiceType(), MasterMerchantType.OTHER)) {
            // Với TOPUP và BILLING, kiểm tra merchant code có trong common table
            this.checkMerchantCode(merchantDTO);
        } else {
            // Với OTHER, tự động generate merchant code
            merchantDTO.setMerchantCode(UUIDUtil.generateUUID(ValidationConstraint.LENGTH.MERCHANT.MERCHANT_CODE_MAX_LENGTH_V2));
        }

        // Set các thông tin mặc định cho master merchant
        merchantDTO.setStatus(EntityStatus.ACTIVE.getStatus()); // Trạng thái ACTIVE
        merchantDTO.setParentId(null); // Master merchant không có parent
        merchantDTO.setOrigin(MerchantCreatedOrigin.CMS); // Tạo từ CMS
        merchantDTO.setCurrency(merchantDTO.getCurrency()); // Set currency

        // Lưu master merchant vào database
        merchant = this.merchantRepository.save(merchantMapper.toEntity(merchantDTO));

        // Tự động tạo cấu hình chiết khấu cho master merchant loại service OTHER
        if (MasterMerchantType.OTHER.equals(merchantDTO.getServiceType())) {
            // Tìm transaction fee type cho INTERNAL_QR_BILLING
            Optional<TransactionFeeType> transactionFeeTypeOpt = this.transactionFeeTypeRepository
                    .findByTransactionFeeTypeCodeAndStatus(TransactionFeeTypeCode.INTERNAL_QR_BILLING.toString(), EntityStatus.ACTIVE.getStatus());

            // Nếu tìm thấy transaction fee type
            if (transactionFeeTypeOpt.isPresent()) {
                // Tạo fee request cho merchant discount
                FeeCreateOrUpdateRequest feeRequest = FeeCreateOrUpdateRequest.builder()
                        .feeName(String.format("%s %s", Labels.getLabels(LabelKey.LABEL_MERCHANT_DISCOUNT), merchantDTO.getMerchantName())) // Tên fee
                        .transactionFeeTypeId(transactionFeeTypeOpt.get().getTransactionFeeTypeId()) // Transaction fee type ID
                        .configurationFeeType(ConfigurationFeeType.DISCOUNT) // Loại cấu hình DISCOUNT
                        .status(EntityStatus.ACTIVE.getStatus()) // Trạng thái ACTIVE
                        .feeType(Labels.getLabels(LabelKey.LABEL_FIXED_AMOUNT)) // Loại phí cố định
                        .merchantId(merchant.getMerchantId()) // Merchant ID
                        .isSystem(SystemStatus.SYSTEM.getStatus()) // Là system fee
                        .build();

                // Tạo fee và set fee ID cho merchant
                FeeDTO fee = this.feeService.create(feeRequest);
                merchant.setFeeId(fee.getFeeId());
            }
        }

        // Reset spam count sau khi tạo thành công
        this.blockConcurrencyService.resetSpamCount(key, CmsCacheConstants.BlockConcurrency.MASTER_MERCHANT);

        // Trả về merchant DTO đã được tạo
        return merchantMapper.toDto(this.merchantRepository.save_(merchant));
    }

    /**
     * Kiểm tra merchant code hợp lệ
     *
     * Với TOPUP và BILLING, merchant code phải tồn tại trong common table.
     *
     * @param merchantDTO MerchantDTO cần kiểm tra
     * @throws BadRequestAlertException nếu merchant code không hợp lệ
     */
    public void checkMerchantCode(MerchantDTO merchantDTO) {
        // Tạo key để reset spam count nếu có lỗi
        String key = merchantDTO.getMerchantCode() + merchantDTO.getMerchantAccountNumber() + merchantDTO.getServiceType();

        // Chỉ kiểm tra với TOPUP và BILLING service types
        if (Validator.equals(merchantDTO.getServiceType(), MasterMerchantType.TOPUP)
                || Validator.equals(merchantDTO.getServiceType(), MasterMerchantType.BILLING)) {

            // Tìm merchant code trong common table theo category và code
            Optional<Common> merchant = this.commonRepository
                    .findByCategoryAndCodeAndStatus(String.valueOf(merchantDTO.getServiceType()), // Category = service type
                            merchantDTO.getMerchantCode(), // Code = merchant code
                            EntityStatus.ACTIVE.getStatus()); // Chỉ lấy status ACTIVE

            // Nếu không tìm thấy merchant code trong common table
            if (!merchant.isPresent()) {
                // Reset spam count và throw exception
                this.blockConcurrencyService.resetSpamCount(key, CmsCacheConstants.BlockConcurrency.MASTER_MERCHANT);
                throw new BadRequestAlertException(ErrorCode.MSG101051); // Invalid merchant code
            }
        }
    }

    /**
     * Kiểm tra duplicate merchant name
     *
     * Trong cùng một account number, không được có 2 merchant con có cùng tên.
     *
     * @param dto MerchantDTO cần kiểm tra
     * @throws BadRequestAlertException nếu merchant name bị duplicate
     */
    private void checkMerchantNameDuplicate(MerchantDTO dto) {
        // Tìm merchant có cùng account number, merchant name và là merchant con (có parent)
        Merchant merchant = this.merchantRepository
                .findByMerchantAccountNumberAndStatusNotAndMerchantNameAndParentIdIsNotNull(
                        dto.getMerchantAccountNumber(), // Account number
                        EntityStatus.DELETED.getStatus(), // Loại trừ status DELETED
                        dto.getMerchantName()); // Merchant name

        // Nếu tìm thấy merchant trùng tên
        if (Validator.isNotNull(merchant)) {
            throw new BadRequestAlertException(ErrorCode.MSG101139); // Duplicate merchant name
        }
    }

    /**
     * Tạo merchant con (sub-merchant)
     *
     * Merchant con thuộc về một master merchant và kế thừa service type.
     * Có thể có nhiều merchant con cho một master merchant.
     *
     * @param dto MerchantDTO cần tạo
     * @return MerchantDTO đã được tạo
     */
    @Override // Ghi đè method từ interface MerchantService
    public MerchantDTO createMerchant(MerchantDTO dto) {
        // VALIDATION 1: Merchant ID phải null (đây là tạo mới)
        if (Validator.isNotNull(dto.getMerchantId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1087); // Merchant ID must be null for create
        }

        // VALIDATION 2: Parent ID bắt buộc (merchant con phải có parent)
        if (Validator.isNull(dto.getParentId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1087); // Parent ID required
        }

        // VALIDATION 3: Merchant code không được trùng (case insensitive)
        if (this.merchantRepository.existsByMerchantCodeIgnoreCaseAndStatusNot(dto.getMerchantCode(),
                EntityStatus.DELETED.getStatus())) {
            throw new BadRequestAlertException(ErrorCode.MSG10006); // Duplicate merchant code
        }

        // VALIDATION 4: Kiểm tra master merchant tồn tại
        Merchant masterMerchant =
                this.merchantRepository
                        .findByMerchantIdAndStatusNot(dto.getParentId(), // Parent ID
                                EntityStatus.DELETED.getStatus()); // Loại trừ status DELETED

        // Nếu không tìm thấy master merchant
        if (Validator.isNull(masterMerchant)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(
                                    LabelKey.LABEL_MERCHANT)}), // Error message với merchant label
                    Merchant.class.getSimpleName(),
                    LabelKey.ERROR_DATA_DOES_NOT_EXIST); // Master merchant not found
        }

        // VALIDATION 5: Double check master merchant existence (redundant check)
        if (!this.merchantRepository.existsByMerchantIdAndStatusNot(dto.getParentId(),
                EntityStatus.DELETED.getStatus())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}), // Error message với merchant label
                    Merchant.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST); // Master merchant not found
        }

        // VALIDATION 6: Kiểm tra merchant name không trùng trong cùng master merchant
        checkMerchantNameDuplicate(dto);

        // Set các thông tin mặc định cho merchant con
        dto.setStatus(EntityStatus.ACTIVE.getStatus()); // Trạng thái ACTIVE
        dto.setMerchantAccountNumber(masterMerchant.getMerchantAccountNumber()); // Kế thừa account number từ master
        dto.setOrigin(MerchantCreatedOrigin.CMS); // Tạo từ CMS

        // Merchant code sinh ngẫu nhiên 15 ký tự theo chuẩn Lapnet phục vụ xuyên biên giới
        dto.setMerchantCode(UUIDUtil.generateUUID(ValidationConstraint.LENGTH.MERCHANT.MERCHANT_CODE_MAX_LENGTH_V2));

        // Chuyển đổi DTO thành entity
        Merchant merchant = this.merchantMapper.toEntity(dto);

        // Lưu merchant vào database và chuyển đổi thành DTO
        MerchantDTO resultDTO = this.merchantMapper.toDto(this.merchantRepository.save_(merchant));

        // Tạo QR code cho merchant và set vào result DTO
        resultDTO.setQrCode(this.qrCodeService.genMerchantQrCodeV2(resultDTO));

        // Trả về merchant DTO đã được tạo với QR code
        return resultDTO;
    }

    /**
     * Xóa merchant (soft delete)
     *
     * Không xóa vật lý mà chỉ đổi status thành DELETED.
     * Cũng xóa fee và QR code liên quan.
     *
     * @param merchantId ID của merchant cần xóa
     */
    @Override // Ghi đè method từ interface MerchantService
    public void delete(Long merchantId) {
        // VALIDATION 1: Merchant ID bắt buộc
        if (Validator.isNull(merchantId)) {
            throw new BadRequestAlertException(ErrorCode.MSG1087); // Merchant ID required
        }

        // Tìm merchant theo ID và loại trừ status DELETED
        Merchant merchant =
                this.merchantRepository
                        .findByMerchantIdAndStatusNot(merchantId, EntityStatus.DELETED.getStatus());

        // Tạo key để reset spam count
        String key = merchant.getMerchantCode() + merchant.getMerchantAccountNumber() + merchant.getServiceType();

        // Reset spam count cho merchant
        this.blockConcurrencyService.resetSpamCount(key, CmsCacheConstants.BlockConcurrency.MASTER_MERCHANT);

        // VALIDATION 2: Kiểm tra merchant tồn tại
        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}), // Error message với merchant label
                    Merchant.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST); // Merchant not found
        }

        // VALIDATION 3: Kiểm tra merchant có merchant con đang ACTIVE/INACTIVE không
        if (this.merchantRepository.existsParentIdAndStatusIn(merchantId,
                Arrays.asList(EntityStatus.ACTIVE.getStatus(), EntityStatus.INACTIVE.getStatus()))) {
            throw new BadRequestAlertException(ErrorCode.MSG10007); // Cannot delete merchant with active children
        }

        // Đổi status thành DELETED (soft delete)
        merchant.setStatus(EntityStatus.DELETED.getStatus());

        // Lưu merchant với status DELETED vào database
        this.merchantRepository.save_(merchant);

        // Nếu merchant có fee thì xóa fee
        if (Validator.isNotNull(merchant.getFeeId())) {
            this.feeService.delete(merchant.getFeeId());
        }

        // Xóa QR code của merchant
        this.qrCodeService.deleteQrCodeByMerchant(merchant.getMerchantCode());
    }

    /**
     * Export lịch sử giao dịch merchant ra Excel
     *
     * Hỗ trợ export cho:
     * - Master merchant: Export tất cả giao dịch của merchant con
     * - Sub-merchant: Export giao dịch của chính nó
     *
     * @param response HttpServletResponse để download file
     * @param search MerchantTransactionHistorySearch - Criteria export
     */
    @Override // Ghi đè method từ interface MerchantService
    public void exportTransactionHistory(HttpServletResponse response, MerchantTransactionHistorySearch search) {
        // Khởi tạo merchant IDs list rỗng
        search.setMerchantIds(new ArrayList<>());

        // VALIDATION 1: Transaction date start và end bắt buộc
        if (Validator.isNull(search.getTransactionDateStart())
                || Validator.isNull(search.getTransactionDateEnd())) {
            throw new BadRequestAlertException(ErrorCode.MSG1087); // Transaction dates required
        // VALIDATION 2: End date phải >= start date
        } else if (search.getTransactionDateEnd().isBefore(search.getTransactionDateStart())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_FROM_DATE_MUST_NOT_BE_GREATER_THAN_TO_DATE,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}), // Error message với merchant label
                    Merchant.class.getSimpleName(),
                    LabelKey.ERROR_FROM_DATE_MUST_NOT_BE_GREATER_THAN_TO_DATE);
        // VALIDATION 3: Khoảng thời gian không quá max period (thường 90 ngày)
        } else if (Period.between(search.getTransactionDateEnd().minus(this.properties.getMaxPeriodExportData()), search.getTransactionDateStart()).isNegative()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_TO_DATE_MUST_NOT_EXCEED_90_DAY_COMPARED_FROM_DATE), // Error message về 90 ngày
                    Merchant.class.getSimpleName(),
                    LabelKey.ERROR_TO_DATE_MUST_NOT_EXCEED_90_DAY_COMPARED_FROM_DATE);
        }

        // Khởi tạo danh sách responses để lưu kết quả
        List<MerchantTransactionHistoryResponse> responses = new ArrayList<>();

        // Khởi tạo danh sách merchants
        List<Merchant> merchants = new ArrayList<>();

        // Flag để xác định có phải master merchant không
        Boolean isMasterMerchant = Boolean.TRUE;

        // Tìm merchant theo ID và loại trừ status DELETED
        Merchant merchant =
                this.merchantRepository
                        .findByMerchantIdAndStatusNot(search.getMerchantId(),
                                EntityStatus.DELETED.getStatus());

        // VALIDATION 4: Merchant phải tồn tại
        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}), // Error message với merchant label
                    Merchant.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST); // Merchant not found
        }

        // Xác định loại merchant và lấy merchant IDs tương ứng
        if (Validator.isNull(merchant.getParentId())) {
            // CASE 1: Đây là master merchant (không có parent)
            // Lấy tất cả merchant con của master merchant
            merchants = this.merchantRepository.findAllByParentIdAndStatusNot(merchant.getMerchantId(),
                    EntityStatus.DELETED.getStatus());

            if (Validator.isNotNull(merchants)) {
                // Set merchant IDs = tất cả merchant con
                search.setMerchantIds(merchants.stream().map(Merchant::getMerchantId).collect(Collectors.toList()));
            } else {
                // Master merchant phải có ít nhất 1 merchant con
                throw new BadRequestAlertException(ErrorCode.MSG10009); // Master merchant has no children
            }
        } else {
            // CASE 2: Đây là sub-merchant (có parent)
            // Set merchant IDs = chỉ merchant này
            search.setMerchantIds(Collections.singletonList(merchant.getMerchantId()));

            // Đánh dấu không phải master merchant
            isMasterMerchant = Boolean.FALSE;
        }

        // Chỉ export giao dịch SUCCESS
        search.setTransactionStatuses(Collections.singletonList(TransactionStatus.SUCCESS));
        // Lấy danh sách transactions theo merchant IDs và criteria
        List<Transaction> transactions = this.transactionRepository.exportTransactionByMerchant(search);

        // Khởi tạo danh sách transaction merchants
        List<TransactionMerchant> transactionMerchants = new ArrayList<>();

        // Khởi tạo danh sách receive transfer moneys
        List<ReceiveTransferMoney> receiveTransferMoneys = new ArrayList<>();

        // Lấy data khác nhau tùy theo loại merchant
        if (Validator.equals(isMasterMerchant, Boolean.TRUE)) {
            // CASE 1: Master merchant
            // Lấy transaction merchants của tất cả merchant con
            transactionMerchants = this.transactionMerchantRepository.findAllByMerchantIdInAndStatusNot(
                    merchants.stream().map(Merchant::getMerchantId).collect(Collectors.toList()), // Tất cả merchant con
                    EntityStatus.DELETED.getStatus());
            // Lấy receive transfer money theo account number của master
            receiveTransferMoneys = this.receiveTransferMoneyRepository.search(ReceiveTransferMoneySearch.builder()
                    .customerAccountNumber(merchant.getMerchantAccountNumber()) // Account number của master
                    .keyword(search.getKeyword()) // Keyword search
                    .fromDate(search.getTransactionDateStart()).toDate(search.getTransactionDateEnd()) // Date range
                    .build());
        } else {
            // CASE 2: Sub-merchant
            // Lấy receive transfer money theo merchant ID cụ thể
            receiveTransferMoneys = this.receiveTransferMoneyRepository.search(ReceiveTransferMoneySearch.builder()
                    .merchantId(merchant.getMerchantId()) // Merchant ID cụ thể
                    .keyword(search.getKeyword()) // Keyword search
                    .fromDate(search.getTransactionDateStart()).toDate(search.getTransactionDateEnd()) // Date range
                    .build());
        }

        // Lấy danh sách customer IDs từ transactions để batch load customers
        List<Long> customerIds = transactions.stream().map(Transaction::getCustomerId).collect(Collectors.toList());
        // Batch load customers để tránh N+1 query problem
        List<Customer> customers = this.customerRepository.findAllByCustomerIdIn(customerIds);

        // Tạo final variables để sử dụng trong lambda
        List<Merchant> finalMerchants = merchants;
        Boolean finalIsMasterMerchant = isMasterMerchant;
        List<TransactionMerchant> finalTransactionMerchants = transactionMerchants;

        // Tạo formatter để format date theo pattern dài
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.LONG_DATE_PATTERN_DASH);

        // Duyệt qua từng transaction để tạo response
        transactions.forEach(transaction -> {
            // Tạo response object cho transaction history
            MerchantTransactionHistoryResponse historyResponse = new MerchantTransactionHistoryResponse();

            // Tìm customer tương ứng với transaction
            Customer customer = customers.stream()
                    .filter(cus -> Validator.equals(cus.getCustomerId(), transaction.getCustomerId()))
                    .findFirst().orElse(null);

            // Nếu tìm thấy customer thì set thông tin customer
            if (Validator.isNotNull(customer)) {
                historyResponse.setCustomerId(customer.getCustomerId()); // Customer ID
                historyResponse.setCustomerCif(customer.getCif()); // CIF
                historyResponse.setCustomerFullName(customer.getFullname()); // Tên đầy đủ
            }

            // Xử lý khác nhau cho master merchant vs sub-merchant
            if (Validator.equals(finalIsMasterMerchant, Boolean.TRUE)) {
                // CASE 1: Master merchant - cần tìm merchant con thực hiện giao dịch
                // Tìm transaction merchant tương ứng
                TransactionMerchant transactionMerchant = finalTransactionMerchants.stream()
                        .filter(transactionMerchant1 -> Validator.equals(
                                transaction.getTransferTransactionId(), // Transfer transaction ID
                                transactionMerchant1.getTransferTransactionId())) // Transaction merchant transfer ID
                        .findFirst().orElse(null);

                // Nếu tìm thấy transaction merchant
                if (Validator.isNotNull(transactionMerchant)) {
                    // Tìm merchant con thực hiện giao dịch
                    Merchant childMerchant =
                            finalMerchants.stream()
                                    .filter(merchant1 -> Validator.equals(merchant1.getMerchantId(),
                                            transactionMerchant.getMerchantId())) // Merchant ID
                                    .findFirst().orElse(null);

                    // Nếu tìm thấy merchant con
                    if (Validator.isNotNull(childMerchant)) {
                        // Set thông tin merchant con thực hiện giao dịch
                        historyResponse.setMerchantName(childMerchant.getMerchantName()); // Tên merchant con
                        historyResponse.setMerchantCode(childMerchant.getMerchantCode()); // Code merchant con
                    }
                }
            }

            // Set thông tin transaction chung
            historyResponse.setCustomerAccountNumber(transaction.getCustomerAccNumber()); // Số tài khoản customer
            historyResponse.setTransactionAmountStr(this.formatMoney(transaction.getTransactionAmount())); // Format số tiền giao dịch
            historyResponse.setTransactionFeeStr(this.formatMoney(transaction.getTransactionFee())); // Format phí giao dịch
            historyResponse.setTransactionTotalAmountStr(
                    this.formatMoney(transaction.getTransactionAmount() + transaction.getTransactionFee())); // Format tổng tiền
            historyResponse.setTransactionId(transaction.getTransactionId()); // Transaction ID

            // Format transaction date theo timezone
            String transactionDateStr = Validator.isNull(search.getSourceZone()) ?
                    InstantUtil.formatStringLongDate(transaction.getCreatedDate(), ZoneId.of(StringPool.UTC)) : // UTC nếu không có source zone
                    InstantUtil.formatStringLongDate(transaction.getCreatedDate(), search.getSourceZone()); // Source zone nếu có
            historyResponse.setTransactionDateStr(transactionDateStr);

            // Tính lại total amount (handle null transaction fee)
            if (Validator.isNull(transaction.getTransactionFee())) {
                // Nếu không có phí thì total = transaction amount
                historyResponse.setTransactionTotalAmountStr(this.formatMoney(transaction.getTransactionAmount()));
            } else {
                // Nếu có phí thì total = transaction amount + fee
                historyResponse.setTransactionTotalAmountStr(this
                        .formatMoney(transaction.getTransactionAmount() + transaction.getTransactionFee()));
            }

            // Set transfer type mặc định là INTERNAL_BANK
            historyResponse.setTransferType(EnumTransferType.INTERNAL_BANK);
            historyResponse.setTransferTypeStr(Labels.getLabels(LabelKey.LABEL_TRANSFER_TRANSACTION_INTERNAL_BANK)); // "Chuyển tiền nội bộ"

            // Thêm response vào danh sách
            responses.add(historyResponse);
        });

        // Enrich thêm thông tin từ receive transfer money
        this.enrichMerchantTransactionHistory(receiveTransferMoneys, responses, isMasterMerchant, merchant, merchants);

        // VALIDATION: Phải có ít nhất 1 transaction để export
        if (Validator.isNull(responses)) {
            throw new BadRequestAlertException(ErrorCode.MSG100010); // No data to export
        }

//        }

        // Tạo tên file Excel với merchant code và timestamp
        String resultFileName = String.format(
                Labels.getLabels(Validator.equals(isMasterMerchant, Boolean.TRUE)
                        ? LabelKey.LABEL_TEMPLATE_MASTER_MERCHANT_FILE_NAME // Template cho master merchant
                        : LabelKey.LABEL_TEMPLATE_MERCHANT_FILE_NAME), // Template cho sub-merchant
                merchant.getMerchantCode(), // Merchant code
                DateUtil.formatStringLongTimestamp(new Date())); // Timestamp
        // Replace spaces với dashes để tạo file name hợp lệ
        resultFileName = StringUtil.replace(StringUtil.trimAll(resultFileName), StringPool.SPACE, StringPool.DASH);

        // Mở Excel template từ resources (khác nhau cho master vs sub-merchant)
        try (InputStream inputStream = this.storageService
                .getExcelTemplateFromResource(Validator.equals(isMasterMerchant, Boolean.TRUE)
                        ? this.templateProperties.getMasterMerchant().getTemplateFile() // Template master merchant
                        : this.templateProperties.getMerchant().getTemplateFile())) { // Template sub-merchant

            // Mở output stream để write Excel file
            try (OutputStream os = response.getOutputStream()) {
                // Tạo context cho Excel template engine
                Context context = new Context();

                // Set các context variables chung
                context.putVar("createdDateStartAt", LocalDateUtil.getLocalDate(search.getTransactionDateStart(),
                        DateUtil.SHORT_DATE_PATTERN_DASH)); // From date
                context.putVar("createdDateEndAt", LocalDateUtil.getLocalDate(search.getTransactionDateEnd(),
                        DateUtil.SHORT_DATE_PATTERN_DASH)); // To date
                context.putVar("localDateNow",
                        LocalDateUtil.getLocalDate(LocalDate.now(), DateUtil.SHORT_DATE_PATTERN_DASH)); // Ngày hiện tại
                context.putVar("serial", Labels.getLabels(LabelKey.LABEL_SERIAL)); // Header "STT"
                context.putVar("date", Labels.getLabels(LabelKey.LABEL_DATE)); // Header "Ngày"
                context.putVar("fromDate", Labels.getLabels(LabelKey.LABEL_FROM_DATE)); // Header "Từ ngày"
                context.putVar("toDate", Labels.getLabels(LabelKey.LABEL_TO_DATE)); // Header "Đến ngày"

                // Xử lý khác nhau cho sub-merchant
                if (Validator.equals(isMasterMerchant, Boolean.FALSE)) {
                    // Với sub-merchant, cần lấy thông tin master merchant
                    Merchant merchantMaster = this.merchantRepository
                            .findByMerchantIdAndStatusNot(merchant.getParentId(),
                                    EntityStatus.DELETED.getStatus());

                    // Validate master merchant tồn tại
                    if (Validator.isNull(merchantMaster)) {
                        throw new BadRequestAlertException(
                                Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                                        new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}), // Error message với merchant label
                                Merchant.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST); // Master merchant not found
                    }

                    // Set context variables cho sub-merchant template
                    context.putVar("title", Labels.getLabels(LabelKey.LABEL_TEMPLATE_MERCHANT_TITLE)); // Title "Lịch sử giao dịch merchant"
                    context.putVar("merchantMasterCodeTitle", Labels.getLabels(LabelKey.LABEL_MERCHANT_MASTER_CODE)); // Header "Mã master merchant"
                    context.putVar("merchantMasterNameTitle", Labels.getLabels(LabelKey.LABEL_MERCHANT_MASTER_NAME)); // Header "Tên master merchant"
                    context.putVar("masterMerchantCode", merchantMaster.getMerchantName()); // Tên master merchant
                    context.putVar("masterMerchantName", merchantMaster.getMerchantCode()); // Code master merchant
                } else {
                    // Set context variables cho master merchant template
                    context.putVar("title", Labels.getLabels(LabelKey.LABEL_TEMPLATE_MASTER_MERCHANT_TITLE)); // Title "Lịch sử giao dịch master merchant"
                }

                // Set các context variables chung cho cả 2 template
                context.putVar("merchantCodeTitle", Labels.getLabels(LabelKey.LABEL_MERCHANT_CODE)); // Header "Mã merchant"
                context.putVar("merchantNameTitle", Labels.getLabels(LabelKey.LABEL_MERCHANT_NAME)); // Header "Tên merchant"
                context.putVar("accountNumber", Labels.getLabels(LabelKey.LABEL_ACCOUNT_NUMBER)); // Header "Số tài khoản"
                context.putVar("serviceTypeTitle", Labels.getLabels(LabelKey.LABEL_SERVICE_TYPE)); // Header "Loại dịch vụ"
                context.putVar("merchantAccountNumber", merchant.getMerchantAccountNumber()); // Số tài khoản merchant
                context.putVar("merchantCode", merchant.getMerchantCode()); // Mã merchant
                context.putVar("merchantName", merchant.getMerchantName()); // Tên merchant
                context.putVar("serviceType", Labels.getLabels(LabelKey.LABEL_PAYMENT_QR_CODE)); // Loại dịch vụ "Thanh toán QR"
                context.putVar("transactionId", Labels.getLabels(LabelKey.LABEL_TRANSACTION_ID)); // Header "Mã giao dịch"
                context.putVar("customerCif", Labels.getLabels(LabelKey.LABEL_CUSTOMER_CIF)); // Header "CIF khách hàng"
                context.putVar("customerName", Labels.getLabels(LabelKey.LABEL_CUSTOMER_NAME)); // Header "Tên khách hàng"
                context.putVar("customerAccountNumber", Labels.getLabels(LabelKey.LABEL_CUSTOMER_ACCOUNT_NUMBER)); // Header "STK khách hàng"
                context.putVar("transactionAmount", Labels.getLabels(LabelKey.LABEL_TRANSACTION_AMOUNT)); // Header "Số tiền giao dịch"
                context.putVar("transactionFee", Labels.getLabels(LabelKey.LABEL_TRANSACTION_FEE)); // Header "Phí giao dịch"
                context.putVar("transactionTotalAmount", Labels.getLabels(LabelKey.LABEL_TRANSACTION_TOTAL_AMOUNT)); // Header "Tổng tiền"
                context.putVar("transactionDate", Labels.getLabels(LabelKey.LABEL_TRANSACTION_DATE)); // Header "Ngày giao dịch"
                context.putVar("transferType", Labels.getLabels(LabelKey.LABEL_TRANSACTION_TRANSFER_TYPE)); // Header "Loại giao dịch"

                // Set data list cho Excel
                context.putVar("lists", responses);
                // Process Excel template với data và write ra output stream
                JxlsHelper.getInstance().processTemplate(inputStream, os, context);
            }

            // Set response headers cho file download
            response.setContentType(FileUtil.getContentType(FileUtil.XLSX)); // Content type Excel
            // Set file name cho download
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, FileUtil.ATTACHMENT + resultFileName);
            // Flush buffer để đảm bảo data được ghi hết
            response.flushBuffer();
        } catch (IOException e) {
            // Log lỗi khi có exception trong quá trình export Excel
            _log.error("Error occurred when export when export excel", e);
        }
    }

    @Override
    public void exportMasterMerchant(HttpServletResponse response, MerchantSearch search) {

        search.setKeyword(search.getKeyword());
        search.setStatus(search.getStatus());
        search.setSearchMaster(Boolean.TRUE);
//        params.setOrderByColumn(Merchant.FieldName.MERCHANT_ID);
        search.setOrderByType(QueryUtil.DESC);

//        List<MerchantTransactionHistoryResponse> responses = new ArrayList<>();

        List<MerchantDTO> merchants = this.merchantMapper.toDto(this.merchantRepository.search(search, null));

        // Xuất ra danh sách merchant con
//        List<Long> masterMerchantIds = merchants.stream().map(MerchantDTO::getMerchantId).collect(Collectors.toList());
//
//        List<MerchantDTO> merchantChildren = this.merchantMapper.toDto(this.merchantRepository.
//                findAllByParentIdInAndStatusNot(masterMerchantIds, EntityStatus.DELETED.getStatus()));
//        merchantChildren.forEach(item -> {
//            merchants.forEach(masterMerchant -> {
//                if (Validator.equals(masterMerchant.getMerchantId(), item.getParentId())) {
//                    item.setMerchantParentCode(masterMerchant.getMerchantCode());
//                    item.setMerchantParentName(masterMerchant.getMerchantName());
//                }
//            });
//            if (item.getStatus() == EntityStatus.ACTIVE.getStatus()) {
//                item.setStatusStr(Labels.getLabels(LabelKey.LABEL_ACTIVE));
//            } else {
//                item.setStatusStr(Labels.getLabels(LabelKey.LABEL_INACTIVE));
//            }
//        });

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.LONG_DATE_PATTERN_DASH);

        this.enrichTotalAmountAndTotalQuantityTransaction(merchants);
        merchants.forEach(masterMerchant -> {
            // Gán giá trị ra th cha
//            masterMerchant.setMerchantParentCode(masterMerchant.getMerchantCode());
//            masterMerchant.setMerchantParentName(masterMerchant.getMerchantName());
//            masterMerchant.setMerchantCode(null);
//            masterMerchant.setMerchantName(null);
            if (masterMerchant.getStatus() == EntityStatus.ACTIVE.getStatus()) {
                masterMerchant.setStatusStr(Labels.getLabels(LabelKey.LABEL_ACTIVE));
            } else {
                masterMerchant.setStatusStr(Labels.getLabels(LabelKey.LABEL_INACTIVE));
            }

            if (Validator.equals(masterMerchant.getServiceType(), MasterMerchantType.TOPUP)) {
                masterMerchant.setServiceTypeStr(Labels.getLabels(LabelKey.LABEL_TOPUP));
            } else if (Validator.equals(masterMerchant.getServiceType(), MasterMerchantType.BILLING)) {
                masterMerchant.setServiceTypeStr(Labels.getLabels(LabelKey.LABEL_BILLING));
            } else {
                masterMerchant.setServiceTypeStr(Labels.getLabels(LabelKey.LABEL_OTHER));
            }

            if (Validator.isNotNull(masterMerchant.getCreatedDate())) {
                ZonedDateTime zonedDateTime = ZonedDateTime.now().ofInstant(masterMerchant.getCreatedDate(),
                        ZoneId.of(search.getTimeZoneStr()));
                masterMerchant.setCreatedDateStr(zonedDateTime.format(formatter));
            }

            if (Validator.isNotNull(masterMerchant.getTotalAmount())) {
                masterMerchant.setTotalAmountStr(StringUtil.formatMoney(masterMerchant.getTotalAmount()));
            }
        });

        // add tất cả danh sách merchant con
//        merchants.addAll(merchantChildren);
        // sort danh sách merchant
//        merchants.sort(Comparator.comparing(MerchantDTO::getMerchantParentCode));

        String resultFileName = String.format(Labels.getLabels(LabelKey.LABEL_TEMPLATE_MASTER_MERCHANT_EXPORT_FILE_NAME),
                DateUtil.formatStringLongTimestamp(new Date()));
        resultFileName = StringUtil.replace(StringUtil.trimAll(resultFileName), StringPool.SPACE, StringPool.DASH);

        try (InputStream inputStream = this.storageService
                .getExcelTemplateFromResource(this.templateProperties.getMasterMerchantList().getTemplateFile())) {

            try (OutputStream os = response.getOutputStream()) {
                Context context = new Context();

                context.putVar("localDateNow",
                        LocalDateUtil.getLocalDate(LocalDate.now(), DateUtil.SHORT_DATE_PATTERN_DASH));
                context.putVar("serial", Labels.getLabels(LabelKey.LABEL_SERIAL));
                context.putVar("date", Labels.getLabels(LabelKey.LABEL_DATE));
                context.putVar("fromDate", Labels.getLabels(LabelKey.LABEL_FROM_DATE));
                context.putVar("toDate", Labels.getLabels(LabelKey.LABEL_TO_DATE));
                context.putVar("title", Labels.getLabels(LabelKey.LABEL_TEMPLATE_MASTER_MERCHANT_LIST_TITLE));
                context.putVar("merchantMasterCodeTitle", Labels.getLabels(LabelKey.LABEL_MERCHANT_MASTER_CODE));
                context.putVar("merchantMasterNameTitle", Labels.getLabels(LabelKey.LABEL_MERCHANT_MASTER_NAME));
//                context.putVar("merchantCodeTitle", Labels.getLabels(LabelKey.LABEL_MERCHANT_CODE));
//                context.putVar("merchantNameTitle", Labels.getLabels(LabelKey.LABEL_MERCHANT_NAME));
                context.putVar("accountNumber", Labels.getLabels(LabelKey.LABEL_ACCOUNT_NUMBER));
                context.putVar("description", Labels.getLabels(LabelKey.LABEL_DESCRIPTION));
                context.putVar("status", Labels.getLabels(LabelKey.LABEL_STATUS));
                context.putVar("serviceType", Labels.getLabels(LabelKey.LABEL_SERVICE_TYPE));
                context.putVar("origin", Labels.getLabels(LabelKey.LABEL_MERCHANT_CREATE_ORIGIN));
                context.putVar("createdDate", Labels.getLabels(LabelKey.LABEL_VERSION_CREATED_DATE));
                context.putVar("totalAmount", Labels.getLabels(LabelKey.LABEL_MERCHANT_TOTAL_AMOUNT));
                context.putVar("totalQuantity", Labels.getLabels(LabelKey.LABEL_MERCHANT_TOTAL_QUANTITY));
                context.putVar("cif", Labels.getLabels(LabelKey.LABEL_CIF_NUMBER));
                context.putVar("phoneNumber", Labels.getLabels(LabelKey.LABEL_PHONE_NUMBER));
                context.putVar("lists", merchants);
                JxlsHelper.getInstance().processTemplate(inputStream, os, context);
            }

            // format tail file excel .xlsx
            response.setContentType(FileUtil.getContentType(FileUtil.XLSX));
            // set fileName
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, FileUtil.ATTACHMENT + resultFileName);
            response.flushBuffer();
        } catch (IOException e) {
            _log.error("Error occurred when export when export excel", e);
        }
    }

    @Override
    public MerchantDTO getMerchantInfo(Long merchantId) {
        if (Validator.isNull(merchantId)) {
            throw new BadRequestAlertException(ErrorCode.MSG1087);
        }

        Merchant merchant = this.merchantRepository
                .findByMerchantIdAndStatusNot(merchantId, EntityStatus.DELETED.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}),
                    Merchant.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST);
        }

        MerchantDTO merchantDTO = this.merchantMapper.toDto(merchant);

        // List<Merchant> childMerchants = this.merchantRepository.findAllByParentIdAndStatusNot(merchantId,
        // EntityStatus.DELETED.getStatus());
        // merchantDTO.setChild(this.merchantMapper.toDto(childMerchants));

        if (Validator.isNotNull(merchantDTO.getParentId())) {
            merchantDTO.setQrCode(this.qrCodeService.genMerchantQrCodeV2(merchantDTO));
        }

        if (MasterMerchantType.TOPUP.equals(merchantDTO.getServiceType()) && TelcoType.UNITEL.name().equals(merchantDTO.getMerchantCode())) {
            merchantDTO.setBalance(apiUnitelService.getMasterBalance().getBalance());
        }

        return merchantDTO;
    }

    @Override
    public void lock(Long merchantId) {
        if (Validator.isNull(merchantId)) {
            throw new BadRequestAlertException(ErrorCode.MSG1087);
        }

        Merchant merchant =
                this.merchantRepository
                        .findByMerchantIdAndStatusNot(merchantId, EntityStatus.DELETED.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}),
                    Merchant.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST);
        }

        if (this.merchantRepository.existsParentIdAndStatusIn(merchantId,
                Collections.singletonList(EntityStatus.ACTIVE.getStatus()))) {
            throw new BadRequestAlertException(ErrorCode.MSG10007);
        }

        merchant.setStatus(EntityStatus.INACTIVE.getStatus());

        this.merchantRepository.save_(merchant);

        this.qrCodeService.inActiveQrCodeByMerchant(merchant.getMerchantCode());
    }

    /**
     * Tìm kiếm sub-merchant theo keyword
     *
     * @param params MerchantSearch - Criteria tìm kiếm
     * @return Page<MerchantDTO> - Kết quả phân trang sub-merchants
     */
    @Override // Ghi đè method từ interface MerchantService
    public Page<MerchantDTO> searchByKeyword(MerchantSearch params) {
        // Set search master = FALSE để chỉ tìm sub-merchants
        params.setSearchMaster(Boolean.FALSE);
        // Set order type = DESC để sắp xếp giảm dần
        params.setOrderByType(QueryUtil.DESC);
        // Gọi method search chung
        return this.search(params);
    }

    /**
     * Tìm kiếm master merchant theo keyword
     *
     * @param params MerchantSearch - Criteria tìm kiếm
     * @return Page<MerchantDTO> - Kết quả phân trang master merchants với transaction data
     */
    @Override // Ghi đè method từ interface MerchantService
    public Page<MerchantDTO> searchMasterByKeyword(MerchantSearch params) {
        // Set search master = TRUE để chỉ tìm master merchants
        params.setSearchMaster(Boolean.TRUE);
        // Set order type = DESC để sắp xếp giảm dần
        params.setOrderByType(QueryUtil.DESC);

        // Gọi method search chung (sẽ enrich transaction data cho master merchants)
        return this.search(params);
    }

    @Override
    public Page<MerchantTransactionHistoryResponse> transactionHistory(MerchantTransactionHistorySearch search) {
        search.setMerchantIds(new ArrayList<>());

        if (Validator.isNull(search.getTransactionDateStart())
                || Validator.isNull(search.getTransactionDateEnd())) {
            throw new BadRequestAlertException(ErrorCode.MSG1087);
        } else if (Validator.isNotNull(search.getTransactionDateStart())
                && Validator.isNotNull(search.getTransactionDateEnd())
                && search.getTransactionDateEnd().isBefore(search.getTransactionDateStart())) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_FROM_DATE_MUST_NOT_BE_GREATER_THAN_TO_DATE,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}),
                    Merchant.class.getSimpleName(),
                    LabelKey.ERROR_FROM_DATE_MUST_NOT_BE_GREATER_THAN_TO_DATE);
        } else if (Period.between(search.getTransactionDateEnd().minus(this.properties.getMaxPeriodExportData()), search.getTransactionDateStart()).isNegative()) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_TO_DATE_MUST_NOT_EXCEED_90_DAY_COMPARED_FROM_DATE),
                    Merchant.class.getSimpleName(),
                    LabelKey.ERROR_TO_DATE_MUST_NOT_EXCEED_90_DAY_COMPARED_FROM_DATE);
        }

        List<MerchantTransactionHistoryResponse> responses = new ArrayList<>();
        List<Merchant> merchants = new ArrayList<>();

        Boolean isMasterMerchant = Boolean.TRUE;

        Merchant merchant = this.merchantRepository.findByMerchantIdAndStatusNot(search.getMerchantId(),
                EntityStatus.DELETED.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}),
                    Merchant.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST);
        }

        if (Validator.isNull(merchant.getParentId())) {
            merchants = this.merchantRepository.findAllByParentIdAndStatusNot(merchant.getMerchantId(),
                    EntityStatus.DELETED.getStatus());

            if (Validator.isNotNull(merchants)) {
                search.setMerchantIds(merchants.stream().map(Merchant::getMerchantId).collect(Collectors.toList()));
            } else {
                return new PageImpl<>(responses, PageRequest.of(search.getPageIndex(), search.getPageSize()), 0L);
            }
        } else {
            search.setMerchantIds(Collections.singletonList(merchant.getMerchantId()));

            isMasterMerchant = Boolean.FALSE;
        }

        search.setTransactionStatuses(Collections.singletonList(TransactionStatus.SUCCESS));
        List<Transaction> transactions = this.transactionRepository.searchTransactionByMerchant(search,
                PageRequest.of(search.getPageIndex(), search.getPageSize()));

        List<TransactionMerchant> transactionMerchants = new ArrayList<>();

        List<ReceiveTransferMoney> receiveTransferMoneys = new ArrayList<>();

        if (Validator.equals(isMasterMerchant, Boolean.TRUE)) {
            transactionMerchants = this.transactionMerchantRepository.findAllByMerchantIdInAndStatusNot(
                    merchants.stream().map(Merchant::getMerchantId).collect(Collectors.toList()),
                    EntityStatus.DELETED.getStatus());

            receiveTransferMoneys = this.receiveTransferMoneyRepository.search(ReceiveTransferMoneySearch.builder()
                    .customerAccountNumber(merchant.getMerchantAccountNumber())
                    .keyword(search.getKeyword())
                    .fromDate(search.getTransactionDateStart()).toDate(search.getTransactionDateEnd()).build());
        } else {
            receiveTransferMoneys = this.receiveTransferMoneyRepository.search(ReceiveTransferMoneySearch.builder()
                    .merchantId(merchant.getMerchantId())
                    .keyword(search.getKeyword())
                    .fromDate(search.getTransactionDateStart()).toDate(search.getTransactionDateEnd()).build());
        }

        if (Validator.isNotNull(transactions)) {
            List<Long> customerIds = transactions.stream().map(Transaction::getCustomerId).collect(Collectors.toList());
            List<Customer> customers = this.customerRepository.findAllByCustomerIdIn(customerIds);

//            if (Validator.isNotNull(customers)) {

            Boolean finalIsMasterMerchant = isMasterMerchant;

            List<Merchant> finalMerchants = merchants;
            List<TransactionMerchant> finalTransactionMerchants = transactionMerchants;

            transactions.forEach(transaction -> {
                MerchantTransactionHistoryResponse historyResponse = new MerchantTransactionHistoryResponse();

                Customer customer = customers.stream().filter(cus -> Validator.equals(cus.getCustomerId(),
                        transaction.getCustomerId())).findFirst().orElse(null);

                if (Validator.isNotNull(customer)) {
                    historyResponse.setCustomerId(customer.getCustomerId());
                    historyResponse.setCustomerCif(customer.getCif());
                    historyResponse.setCustomerFullName(customer.getFullname());
                }

                if (Validator.equals(finalIsMasterMerchant, Boolean.TRUE)) {
                    TransactionMerchant transactionMerchant = finalTransactionMerchants.stream()
                            .filter(transactionMerchant1 -> Validator.equals(
                                    transaction.getTransferTransactionId(),
                                    transactionMerchant1.getTransferTransactionId()))
                            .findFirst().orElse(null);

                    if (Validator.isNotNull(transactionMerchant)) {
                        Merchant childMerchant = finalMerchants.stream()
                                .filter(merchant1 -> Validator.equals(merchant1.getMerchantId(),
                                        transactionMerchant.getMerchantId()))
                                .findFirst().orElse(null);

                        if (Validator.isNotNull(childMerchant)) {
                            historyResponse.setMerchantName(childMerchant.getMerchantName());
                            historyResponse.setMerchantCode(childMerchant.getMerchantCode());
                        }
                    }
                }

                historyResponse.setCustomerAccountNumber(transaction.getCustomerAccNumber());
                historyResponse.setTransactionAmount(transaction.getTransactionAmount());
                historyResponse.setTransactionFee(transaction.getTransactionFee());
                historyResponse.setTransactionId(transaction.getTransactionId());
                historyResponse.setTransferType(EnumTransferType.INTERNAL_BANK);
                historyResponse.setTransferTypeStr(Labels.getLabels(LabelKey.LABEL_TRANSFER_TRANSACTION_INTERNAL_BANK));

                String transactionDateStr = Validator.isNull(search.getSourceZone()) ?
                        InstantUtil.formatStringLongDate(transaction.getCreatedDate(), ZoneId.of(StringPool.UTC)) :
                        InstantUtil.formatStringLongDate(transaction.getCreatedDate(), search.getSourceZone());

                historyResponse.setTransactionDateStr(transactionDateStr);
                historyResponse.setCreatedDate(transaction.getCreatedDate());

                responses.add(historyResponse);
            });
        }

        this.enrichMerchantTransactionHistory(receiveTransferMoneys, responses, isMasterMerchant, merchant, merchants);

        List<MerchantTransactionHistoryResponse> responsesPaginated = this.getPaginatedTransactions(search, responses);

        return new PageImpl<>(responsesPaginated, PageRequest.of(search.getPageIndex(), search.getPageSize()),
                responses.size());
    }

    private void enrichMerchantTransactionHistory(List<ReceiveTransferMoney> receiveTransferMoneys, List<MerchantTransactionHistoryResponse> responses,
                                                  Boolean isMasterMerchant, Merchant merchant,
                                                  List<Merchant> merchants) {
        List<Long> customerIds = receiveTransferMoneys.stream().map(ReceiveTransferMoney::getCustomerId).collect(Collectors.toList());
        List<Customer> customers = this.customerRepository.findAllByCustomerIdIn(customerIds);

        receiveTransferMoneys.forEach(item -> {
            MerchantTransactionHistoryResponse historyResponse = new MerchantTransactionHistoryResponse();

            Customer customer = customers.stream().filter(cus -> Validator.equals(Long.valueOf(cus.getCustomerId()),
                    item.getCustomerId())).findFirst().orElse(null);

            if (Validator.isNotNull(customer)) {
                historyResponse.setCustomerId(customer.getCustomerId());
            }

            if (Validator.equals(isMasterMerchant, Boolean.FALSE)) {
                historyResponse.setMerchantName(merchant.getMerchantName());
                historyResponse.setMerchantCode(merchant.getMerchantCode());
            } else {
                Merchant childMerchant = merchants.stream().filter(merchantChild -> Validator.equals(Long.valueOf(merchantChild.getMerchantId()), item.getMerchantId())).findFirst().orElse(null);

                if (Validator.isNotNull(childMerchant)) {
                    historyResponse.setMerchantName(childMerchant.getMerchantName());
                    historyResponse.setMerchantCode(childMerchant.getMerchantCode());
                }
            }

            historyResponse.setTransactionAmount(item.getTransactionAmount());
            historyResponse.setTransactionFee(item.getTransactionFee());
            historyResponse.setTransactionId(item.getTransactionId());

            historyResponse.setTransactionAmountStr(this.formatMoney(item.getTransactionAmount()));
            historyResponse.setTransactionFeeStr(this.formatMoney(item.getTransactionFee()));
            historyResponse.setTransactionTotalAmountStr(
                    this.formatMoney(item.getTransactionAmount() + item.getTransactionFee()));

            String transactionDateStr = InstantUtil.formatStringLongDate(item.getTransactionFinishTime(), ZoneId.of(StringPool.CURRENT_TIME_ZONE));

            historyResponse.setTransactionDateStr(transactionDateStr);
            if (Validator.equals(item.getTransferType(), EnumTransferType.INTER_BANK.name())) {
                historyResponse.setTransferType(EnumTransferType.INTER_BANK);
                historyResponse.setTransferTypeStr(Labels.getLabels(LabelKey.LABEL_TRANSFER_TRANSACTION_INTER_BANK));
            } else if (Validator.equals(item.getTransferType(), EnumTransferType.OTHER_BRANCH.name())) {
                historyResponse.setTransferType(EnumTransferType.OTHER_BRANCH);
                historyResponse.setTransferTypeStr(Labels.getLabels(LabelKey.LABEL_OTHER_BRANCH));
            }
            historyResponse.setCreatedDate(item.getTransactionFinishTime());

            responses.add(historyResponse);
        });
    }

    @Override
    public void unlock(Long merchantId) {
        if (Validator.isNull(merchantId)) {
            throw new BadRequestAlertException(ErrorCode.MSG1087);
        }

        Merchant merchant = this.merchantRepository.findByMerchantIdAndStatus(merchantId
                , EntityStatus.INACTIVE.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}),
                    Merchant.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST);
        }

        merchant.setStatus(EntityStatus.ACTIVE.getStatus());

        this.merchantRepository.save_(merchant);

        this.qrCodeService.activeQrCodeByMerchant(merchant.getMerchantCode());
    }

    /**
     * Cập nhật master merchant
     *
     * Validate business rules và cập nhật thông tin master merchant.
     * Cũng cập nhật service type cho tất cả merchant con.
     *
     * @param merchantDTO MerchantDTO cần cập nhật
     * @return MerchantDTO đã được cập nhật
     */
    @Override // Ghi đè method từ interface MerchantService
    public MerchantDTO updateMasterMerchant(MerchantDTO merchantDTO) {
        // VALIDATION 1: Merchant ID bắt buộc
        if (Validator.isNull(merchantDTO.getMerchantId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1087); // Merchant ID required
        }

        // VALIDATION 2: Service type bắt buộc
        if (Validator.isNull(merchantDTO.getServiceType())) {
            throw new BadRequestAlertException(ErrorCode.MSG101052); // Service type required
        }

        // VALIDATION 3: Merchant code không được trùng với master merchant khác (cùng service type)
        if (this.merchantRepository.existsByMerchantCodeAndServiceTypeIgnoreCaseAndMerchantIdNotAndStatusNot(
                merchantDTO.getMerchantCode(), merchantDTO.getServiceType().toString(), // Merchant code và service type
                merchantDTO.getMerchantId(), EntityStatus.DELETED.getStatus())) { // Loại trừ chính nó và DELETED
            throw new BadRequestAlertException(ErrorCode.MSG10006); // Duplicate merchant code
        }

        // Tìm merchant hiện tại theo ID
        Merchant merchant =
                this.merchantRepository
                        .findByMerchantIdAndStatusNot(merchantDTO.getMerchantId(),
                                EntityStatus.DELETED.getStatus());

        // VALIDATION 4: Merchant phải tồn tại
        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}), // Error message với merchant label
                    Merchant.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST); // Merchant not found
        }

        // VALIDATION 5: Nếu thay đổi account number hoặc service type
        if (!Validator.equals(merchant.getMerchantAccountNumber(), merchantDTO.getMerchantAccountNumber())
                || !Validator.equals(merchantDTO.getServiceType(), merchant.getServiceType())) {
            // Kiểm tra account number đã được sử dụng hay chưa
            // Nếu đã được sử dụng thì merchant code phải trùng với merchant đã sử dụng account number đó
            List<Merchant> masterMerchants = this.merchantRepository.findAllByMerchantAccountNumberAndStatusNotAndParentIdIsNull(
                    merchantDTO.getMerchantAccountNumber(), EntityStatus.DELETED.getStatus());

            // Validate business rules cho account number đã tồn tại
            if (Validator.isNotNull(masterMerchants) && (masterMerchants.size() > 1 // Có nhiều hơn 1 master merchant
                    || !Validator.equals(masterMerchants.get(0).getMerchantCode(), merchantDTO.getMerchantCode()) // Merchant code khác nhau
                    || Validator.equals(masterMerchants.get(0).getServiceType(), merchantDTO.getServiceType()))) { // Cùng service type
                throw new BadRequestAlertException(ErrorCode.MSG10008); // Account number already used with different rules
            }
        }

        // VALIDATION 6: Kiểm tra merchant code hợp lệ (với TOPUP/BILLING)
        this.checkMerchantCode(merchantDTO);

        // VALIDATION 7: Enrich và kiểm tra CIF tồn tại
        this.enrichAndCheckExistedCif(merchantDTO);

        // Nếu thay đổi account number thì cập nhật lại tất cả merchant con
        if (!Validator.equals(merchantDTO.getMerchantAccountNumber(), merchant.getMerchantAccountNumber())) {
            this.checkUpdateMerchant(merchantDTO); // Cập nhật account number cho merchant con
        }

        // Xử lý merchant code theo service type
        if (!Validator.equals(merchantDTO.getServiceType(), merchant.getServiceType()) && Validator.equals(merchantDTO.getServiceType(), MasterMerchantType.OTHER)) {
            // Nếu chuyển sang OTHER service type thì generate merchant code mới
            merchant.setMerchantCode(UUIDUtil.generateUUID(ValidationConstraint.LENGTH.MERCHANT.MERCHANT_CODE_MAX_LENGTH_V2));
        } else {
            // Ngược lại thì dùng merchant code từ request
            merchant.setMerchantCode(merchantDTO.getMerchantCode());
        }

        // Cập nhật các thông tin khác của master merchant
        merchant.setMerchantName(merchantDTO.getMerchantName()); // Tên merchant
        merchant.setMerchantAccountNumber(merchantDTO.getMerchantAccountNumber()); // Số tài khoản
        merchant.setDescription(merchantDTO.getDescription()); // Mô tả
        merchant.setServiceType(String.valueOf(merchantDTO.getServiceType())); // Service type
        merchantDTO.setParentId(null); // Master merchant không có parent
        merchant.setCurrency(merchantDTO.getCurrency()); // Currency
        merchant.setPhoneNumber(merchantDTO.getPhoneNumber()); // Số điện thoại
        merchant.setCif(merchantDTO.getCif()); // CIF

        // Lưu master merchant đã được cập nhật và trả về DTO
        return this.merchantMapper.toDto(this.merchantRepository.save_(merchant));
    }

    /**
     * Cập nhật account number cho tất cả merchant con
     *
     * Khi master merchant thay đổi account number, tất cả merchant con
     * cũng phải được cập nhật account number theo.
     *
     * @param merchantDTO MerchantDTO chứa account number mới
     */
    private void checkUpdateMerchant(MerchantDTO merchantDTO) {
        // Lấy tất cả merchant con của master merchant
        List<Merchant> merchants = this.merchantRepository.findAllByParentIdAndStatusNot(merchantDTO.getMerchantId(), EntityStatus.DELETED.getStatus());

        // Nếu có merchant con
        if (Validator.isNotNull(merchants)) {
            // Cập nhật account number cho tất cả merchant con
            merchants.forEach(merchant -> merchant.setMerchantAccountNumber(merchantDTO.getMerchantAccountNumber()));
            // Lưu tất cả merchant con đã được cập nhật
            this.merchantRepository.saveAll(merchants);
        }
    }

    /**
     * Cập nhật sub-merchant
     *
     * Validate business rules và cập nhật thông tin sub-merchant.
     * Sub-merchant không thể thay đổi merchant code.
     *
     * @param merchantDTO MerchantDTO cần cập nhật
     * @return MerchantDTO đã được cập nhật
     */
    @Override // Ghi đè method từ interface MerchantService
    public MerchantDTO updateMerchant(MerchantDTO merchantDTO) {
        // VALIDATION 1: Merchant ID bắt buộc
        if (Validator.isNull(merchantDTO.getMerchantId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1087); // Merchant ID required
        }

        // VALIDATION 2: Parent ID bắt buộc (sub-merchant phải có parent)
        if (Validator.isNull(merchantDTO.getParentId())) {
            throw new BadRequestAlertException(ErrorCode.MSG1087); // Parent ID required
        }

        // VALIDATION 3: Merchant code không được trùng với merchant khác
        if (this.merchantRepository.existsByMerchantCodeIgnoreCaseAndMerchantIdNotAndStatusNot(
                merchantDTO.getMerchantCode(), merchantDTO.getMerchantId(), EntityStatus.DELETED.getStatus())) {
            throw new BadRequestAlertException(ErrorCode.MSG10006); // Duplicate merchant code
        }

        // VALIDATION 4: Kiểm tra master merchant tồn tại
        Merchant masterMerchant =
                this.merchantRepository
                        .findByMerchantIdAndStatusNot(merchantDTO.getParentId(),
                                EntityStatus.DELETED.getStatus());

        if (Validator.isNull(masterMerchant)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}), // Error message với merchant label
                    Merchant.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST); // Master merchant not found
        }

        // VALIDATION 5: Kiểm tra merchant hiện tại tồn tại
        Merchant merchant =
                this.merchantRepository
                        .findByMerchantIdAndStatusNot(merchantDTO.getMerchantId(),
                                EntityStatus.DELETED.getStatus());

        if (Validator.isNull(merchant)) {
            throw new BadRequestAlertException(
                    Labels.getLabels(LabelKey.ERROR_DATA_DOES_NOT_EXIST,
                            new Object[]{Labels.getLabels(LabelKey.LABEL_MERCHANT)}), // Error message với merchant label
                    Merchant.class.getSimpleName(), LabelKey.ERROR_DATA_DOES_NOT_EXIST); // Merchant not found
        }

        // VALIDATION 6: Nếu thay đổi merchant name thì kiểm tra duplicate
        if (!Validator.equals(merchantDTO.getMerchantName(), merchant.getMerchantName())) {
            // Cùng một master merchant, merchant name không được trùng
            checkMerchantNameDuplicate(merchantDTO);
        }

        // Xóa QR code cũ trước khi cập nhật
        this.qrCodeService.deleteQrCodeByMerchant(merchant.getMerchantCode());

        // VALIDATION 7: Merchant được tạo từ app không được thay đổi master merchant
        if (Validator.equals(merchant.getOrigin(), MerchantCreatedOrigin.APP)
                && merchant.getParentId() != masterMerchant.getMerchantId()) {
            throw new BadRequestAlertException(ErrorCode.MSG101161); // Cannot change master merchant for app-created merchant
        }

        // Cập nhật thông tin merchant
        merchant.setMerchantAccountNumber(masterMerchant.getMerchantAccountNumber()); // Kế thừa account number từ master
        merchant.setMerchantName(merchantDTO.getMerchantName()); // Tên merchant
        merchant.setParentId(merchantDTO.getParentId()); // Parent ID
        merchant.setDescription(merchantDTO.getDescription()); // Mô tả

        // Lưu merchant đã được cập nhật
        MerchantDTO dto = this.merchantMapper.toDto(this.merchantRepository.save_(merchant));

        // Tạo QR code mới cho merchant
        dto.setQrCode(this.qrCodeService.genMerchantQrCodeV2(dto));

        this.merchantRepository.evictFindChildMerchantByCodeAndStatus(dto.getMerchantCode(), dto.getStatus());
        return dto;
    }

    @Override
    public void export(HttpServletResponse response, MerchantSearch merchantSearch) {
        try {
            merchantSearch.setSearchMaster(Boolean.FALSE);
            List<Merchant> result = this.merchantRepository.search(merchantSearch, null);
            List<MerchantDTO> merchantDTOs = this.merchantMapper.toDto(result);
//            merchantDTOs.forEach(merchantDTO -> {
//                merchantDTO.setStatusStr(merchantDTO.getStatus() == EntityStatus.ACTIVE.getStatus() ?
//                        Labels.getLabels(LabelKey.LABEL_ACTIVE) : Labels.getLabels(LabelKey.LABEL_INACTIVE));
//            });

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.LONG_DATE_PATTERN_DASH);

            merchantDTOs.forEach(item -> {
                if (Validator.isNotNull(item.getCreatedDate())) {
                    ZonedDateTime zonedDateTime = ZonedDateTime.now().ofInstant(item.getCreatedDate(),
                            ZoneId.of(merchantSearch.getTimeZoneStr()));
                    item.setCreatedDateStr(zonedDateTime.format(formatter));
                }
            });

            String resultFileName = String.format(Labels.getLabels(LabelKey.LABEL_MERCHANT_FILE_NAME),
                    DateUtil.formatStringLongTimestamp(new Date()));
            resultFileName = StringUtil.replace(StringUtil.trimAll(resultFileName), StringPool.SPACE, StringPool.DASH);
            try (InputStream inputStream = this.storageService.getExcelTemplateFromResource(this.templateProperties.getMerchant().getMerchantTemplate())) {
                try (OutputStream os = response.getOutputStream()) {
                    Context context = new Context();
                    context.putVar("merchantName", Labels.getLabels(LabelKey.LABEL_MERCHANT_NAME));
                    context.putVar("merchantCode", Labels.getLabels(LabelKey.LABEL_MERCHANT_CODE));
                    context.putVar("merchantBank", Labels.getLabels(LabelKey.LABEL_MERCHANT_BANK));
                    context.putVar("merchantDescription", Labels.getLabels(LabelKey.LABEL_MERCHANT_DESC));
                    context.putVar("status", Labels.getLabels(LabelKey.LABEL_STATUS));
                    context.putVar("serial", Labels.getLabels(LabelKey.LABEL_SERIAL));
                    context.putVar("lists", merchantDTOs);
                    context.putVar("date", Labels.getLabels(LabelKey.LABEL_DATE));
                    context.putVar("localDateNow", LocalDateUtil.getLocalDate(LocalDate.now(), DateUtil.SHORT_DATE_PATTERN_DASH));
                    context.putVar("titleMerchant", Labels.getLabels(LabelKey.LABEL_MERCHANT_TITLE));
                    context.putVar("origin", Labels.getLabels(LabelKey.LABEL_MERCHANT_CREATE_ORIGIN));
                    context.putVar("createdDate", Labels.getLabels(LabelKey.LABEL_VERSION_CREATED_DATE));
                    JxlsHelper.getInstance().processTemplate(inputStream, os, context);
                }
                response.setContentType(FileUtil.getContentType(FileUtil.XLSX));
                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, FileUtil.ATTACHMENT + resultFileName);
                response.flushBuffer();
            } catch (IOException e) {
                _log.error("Error occurred when export when export excel", e);
            }
        } catch (Exception e) {
            _log.error("Export excel error", e);
        }
    }

    @Override
    public List<MerchantDTO> getMerchantMobileTopup() {
        List<Merchant> merchants = this.merchantRepository.findAllByMerchantCodeInAndStatusAndServiceType
                (this.properties.getMobileTopup(), EntityStatus.ACTIVE.getStatus(), MasterMerchantType.TOPUP.toString());

        return this.merchantMapper.toDto(merchants);
    }

    @Override
    public List<CommonDTO> getAllServiceType(ServiceTypeRequest request) {
        return this.commonMapper.toDto(this.commonRepository
                .findAllByCategoryAndStatus(request.getServiceType().toString(), EntityStatus.ACTIVE.getStatus()));
    }

    @NotNull
    private List<MerchantTransactionHistoryResponse> getPaginatedTransactions(MerchantTransactionHistorySearch
                                                                                      request, List<MerchantTransactionHistoryResponse> responses) {

        responses.sort(Comparator.comparing(MerchantTransactionHistoryResponse::getCreatedDate).reversed());

        if (Validator.isNotNull(responses)) {
            long totalElement = responses.size();

            int fromIndex = request.getPageSize() * request.getPageIndex();
            int toIndex = fromIndex + request.getPageSize();

            if (toIndex > totalElement) {
                toIndex = (int) totalElement;
            }

            if (Validator.equals(fromIndex, toIndex)) {
                MerchantTransactionHistoryResponse response = responses.get(fromIndex);

                responses.clear();
                responses.add(response);
            }

            if (fromIndex > totalElement) {
                responses.clear();
            } else {
                responses = responses.subList(fromIndex, toIndex);
            }
        }
        return responses;
    }


    /**
     * Format số tiền thành string có dấu phẩy
     *
     * @param price Object số tiền cần format
     * @return String số tiền đã được format (có dấu phẩy), hoặc empty nếu null
     */
    private String formatMoney(Object price) {
        // Nếu price null thì trả về empty string
        if (Validator.isNull(price)) return StringPool.BLANK;

        // Tạo DecimalFormat với pattern currency (có dấu phẩy)
        DecimalFormat decimalFormat = new DecimalFormat(StringPool.CURRENCY_FORMAT);

        // Format price thành string và trả về
        return decimalFormat.format(price);
    }

    /**
     * Tìm kiếm merchant với pagination
     *
     * Method private được gọi bởi searchByKeyword và searchMasterByKeyword.
     * Format created date theo timezone và enrich transaction data cho master merchant.
     *
     * @param params MerchantSearch - Criteria tìm kiếm
     * @return Page<MerchantDTO> - Kết quả phân trang
     */
    private Page<MerchantDTO> search(MerchantSearch params) {
        // Gọi repository để search merchant theo criteria và pageable
        List<MerchantDTO> result = this.merchantMapper.toDto(this.merchantRepository.search(params,
                PageRequest.of(params.getPageIndex(), params.getPageSize())));

        // Tạo formatter để format date theo pattern dài
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.LONG_DATE_PATTERN_DASH);

        // Duyệt qua từng merchant để format created date
        result.forEach(item -> {
            // Nếu có created date thì format theo timezone
            if (Validator.isNotNull(item.getCreatedDate())) {
                ZonedDateTime zonedDateTime = ZonedDateTime.now().ofInstant(item.getCreatedDate(),
                        ZoneId.of(params.getTimeZoneStr())); // Convert sang timezone cụ thể
                item.setCreatedDateStr(zonedDateTime.format(formatter)); // Format với pattern dài
            }
        });

        // Nếu search master merchant thì enrich transaction data
        if (Validator.equals(params.getSearchMaster(), Boolean.TRUE)) {
            this.enrichTotalAmountAndTotalQuantityTransaction(result); // Enrich total amount và quantity
        }

        // Trả về PageImpl với kết quả, pageable và total count
        return new PageImpl<>(result, PageRequest.of(params.getPageIndex(),
                params.getPageSize()), this.merchantRepository.count(params));
    }

    /**
     * Search FullText
     *
     * @param params MerchantSearch
     * @return Page<MerchantDTO>
     */
    private Page<MerchantDTO> searchFullText(MerchantSearch params) {

        ResultSet<Merchant> result = this.merchantRepository.searchByKeyword(params,
                PageRequest.of(params.getPageIndex(), params.getPageSize()));

        List<MerchantDTO> merchantDTOS = this.merchantMapper.toDto(result.getResults());

        return new PageImpl<>(merchantDTOS, PageRequest.of(params.getPageIndex(),
                params.getPageSize()), result.getCount());
    }

    /**
     * Enrich và kiểm tra CIF tồn tại
     *
     * Validate:
     * 1. CIF tồn tại trên T24
     * 2. Account number thuộc về CIF
     * 3. Enrich phone number từ T24
     *
     * @param merchantDTO MerchantDTO cần validate và enrich
     * @throws BadRequestAlertException nếu CIF không hợp lệ
     */
    private void enrichAndCheckExistedCif(MerchantDTO merchantDTO) {
        // Chỉ validate nếu có CIF
        if (Validator.isNotNull(merchantDTO.getCif())) {
            // Tạo request để check account balance theo CIF
            AccountBalanceRequest accountBalanceRequest = AccountBalanceRequest.builder()
                    .custCode(merchantDTO.getCif()) // Customer code (CIF)
                    .build();

            // Gọi T24 để lấy danh sách account balance của CIF
            List<AccountBalanceDTO> accountBalanceDTOList = this.apiGeeTransferService
                    .checkAccountBalanceCustom(accountBalanceRequest);

            // VALIDATION 1: CIF phải tồn tại trên T24
            if (Validator.isNull(accountBalanceDTOList)) {
                throw new BadRequestAlertException(ErrorCode.MSG101260); // CIF not found on T24
            }

            // VALIDATION 2: Account number phải thuộc về CIF
            accountBalanceDTOList.stream()
                    .filter(item -> Validator.equals(item.getAccountNumber(), merchantDTO.getMerchantAccountNumber())) // Tìm account number
                    .findFirst().orElseThrow(() -> new BadRequestAlertException(ErrorCode.MSG101260)); // Account number not belong to CIF

            // ENRICH: Lấy thông tin customer từ T24 để enrich phone number
            CustomerInfoT24DTO customerInfoT24DTO = this.apiGeeCustomerService.queryCustomerInfoCifUmoney(CustomerQueryInfoRequest
                    .builder().customerId(merchantDTO.getCif()) // Customer ID (CIF)
                    .build());

            // Nếu có thông tin customer từ T24 thì set phone number
            if (Validator.isNotNull(customerInfoT24DTO)) {
                merchantDTO.setPhoneNumber(customerInfoT24DTO.getMobile()); // Set mobile phone từ T24
            }
        }
    }

    private void enrichTotalAmountAndTotalQuantityTransaction(List<MerchantDTO> result) {
        List<String> accountNumbers = result.stream().map(MerchantDTO::getMerchantAccountNumber).collect(Collectors.toList());
        List<Long> merchantIds = result.stream().map(MerchantDTO::getMerchantId).collect(Collectors.toList());

        List<Merchant> merchantChild = this.merchantRepository.findAllByParentIdInAndStatusNot(merchantIds,
                EntityStatus.DELETED.getStatus());
        List<Long> merchantChildIds = merchantChild.stream().map(Merchant::getMerchantId).collect(Collectors.toList());

        if (Validator.isNotNull(merchantChildIds)) {
            MerchantTransactionHistorySearch search = new MerchantTransactionHistorySearch();
            search.setMerchantIds(merchantChildIds);
            search.setHasPageable(false);
            search.setTransactionStatuses(Collections.singletonList(TransactionStatus.SUCCESS));
            List<TransactionDTO> transactions = this.transferTransactionMapper.toDto(this.transactionRepository.searchTransactionByMerchant(search,
                    PageRequest.of(search.getPageIndex(), search.getPageSize())));

            List<TransactionMerchant> transactionMerchants = this.transactionMerchantRepository.findAllByMerchantIdInAndStatusNot(merchantChildIds, EntityStatus.DELETED.getStatus());
            transactions.forEach(transaction -> {
                transactionMerchants.forEach(transactionMerchant -> {
                    if (Validator.equals(transactionMerchant.getTransferTransactionId(), transaction.getTransferTransactionId())) {
                        transaction.setMerchantId(transactionMerchant.getMerchantId());
                    }
                });
            });

            transactions.forEach(transaction -> {
                merchantChild.forEach(merchant -> {
                    if (Validator.equals(merchant.getMerchantId(), transaction.getMerchantId().longValue())) {
                        transaction.setMerchantParentId(merchant.getParentId());
                    }
                });
            });

            if (Validator.isNotNull(transactions)) {
                result.forEach(item -> {
                    item.setTotalAmount(transactions.stream().filter(transaction -> Validator.equals(item.getMerchantId(), transaction.getMerchantParentId())).map(TransactionDTO::getTransactionAmount).reduce(0D, Double::sum));
                    item.setTotalQuantity(transactions.stream().filter(transaction -> Validator.equals(item.getMerchantId(), transaction.getMerchantParentId())).count());
                });
            }
        }

        List<ReceiveTransferMoney> receiveTransferMoneys = this.receiveTransferMoneyRepository.findAllByCustomerAccountNumberInAndStatus(accountNumbers, EntityStatus.ACTIVE.getStatus());

        if (Validator.isNotNull(receiveTransferMoneys)) {
            result.forEach(item -> {
                item.setTotalAmount((Validator.isNull(item.getTotalAmount()) ? 0D : item.getTotalAmount()) +
                        receiveTransferMoneys.stream()
                                .filter(receiveTransferMoney -> Validator.equals(receiveTransferMoney.getCustomerAccountNumber(), item.getMerchantAccountNumber()))
                                .map(ReceiveTransferMoney::getTransactionAmount).reduce(0D, Double::sum));
                item.setTotalQuantity((Validator.isNull(item.getTotalQuantity()) ? 0L : item.getTotalQuantity()) +
                        receiveTransferMoneys.stream()
                                .filter(receiveTransferMoney -> Validator.equals(receiveTransferMoney.getCustomerAccountNumber(), item.getMerchantAccountNumber()))
                                .count());
            });
        }
    }

    @Override
    public void merchantCodeSync(MerchantSearch search) {
        if (Validator.isNotNull(search.getMerchantCode())) {
            Optional<Merchant> merchant = this.merchantRepository.findByMerchantCodeAndStatusNotAndParentIdNotNull(
                    search.getMerchantCode(), EntityStatus.DELETED.getStatus());

            if (!merchant.isPresent()) {
                throw new BadRequestAlertException(ErrorCode.MSG101125);
            }

            if (merchant.get().getMerchantCode().length() > ValidationConstraint.LENGTH.MERCHANT.MERCHANT_CODE_MAX_LENGTH_V2) {
                merchant.get().setMerchantCode(UUIDUtil.generateUUID(ValidationConstraint.LENGTH.MERCHANT.MERCHANT_CODE_MAX_LENGTH_V2));
            }
        } else {
            List<Merchant> merchants = this.merchantRepository.findAllByStatusNotAndParentIdIsNotNull(EntityStatus.DELETED.getStatus());

            if (Validator.isNotNull(merchants)) {
                merchants.forEach(merchant -> {
                    if (merchant.getMerchantCode().length() > ValidationConstraint.LENGTH.MERCHANT.MERCHANT_CODE_MAX_LENGTH_V2) {
                        merchant.setMerchantCode(UUIDUtil.generateUUID(ValidationConstraint.LENGTH.MERCHANT.MERCHANT_CODE_MAX_LENGTH_V2));
                    }
                });
            }
        }
    }
}
