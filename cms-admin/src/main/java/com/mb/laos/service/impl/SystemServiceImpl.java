package com.mb.laos.service.impl;

import java.util.ArrayList;
import java.util.List;
import org.jasypt.encryption.pbe.PBEStringEncryptor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.mb.laos.api.exception.BadRequestAlertException;
import com.mb.laos.messages.LabelKey;
import com.mb.laos.messages.Labels;
import com.mb.laos.service.SystemService;
import com.mb.laos.util.StringPool;
import com.mb.laos.util.StringUtil;
import com.mb.laos.util.Validator;
import lombok.RequiredArgsConstructor;

@Service
@Transactional
@RequiredArgsConstructor
public class SystemServiceImpl implements SystemService {
    
    private final PBEStringEncryptor stringEncryptor;
    
    @Override
    public String decrypt(String encryptedData) {
        if (Validator.isNull(encryptedData)) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY),
                            "System", LabelKey.ERROR_INPUT_CANNOT_BE_EMPTY);
        }
        
        String[] datas = StringUtil.split(encryptedData, StringPool.NEW_LINE);
        
        List<String> results = new ArrayList<>();
        
        try {
            for (String data: datas) {
                results.add(this.stringEncryptor.decrypt(data));
            }
        } catch (Exception e) {
            throw new BadRequestAlertException(Labels.getLabels(LabelKey.ERROR_ENCRYPTED_DATA_IS_INVALID),
                            "System", LabelKey.ERROR_ENCRYPTED_DATA_IS_INVALID);
        }
        
        return StringUtil.join(results, StringPool.NEW_LINE);
    }

}
