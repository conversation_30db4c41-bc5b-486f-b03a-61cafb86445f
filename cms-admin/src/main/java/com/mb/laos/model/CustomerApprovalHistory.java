package com.mb.laos.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.search.annotations.Indexed;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.time.Instant;

@Indexed
@Entity
@Table(name = "CUSTOMER_APPROVAL_HISTORY")
@Data
@EqualsAndHashCode(callSuper = false)
public class CustomerApprovalHistory extends AbstractAuditingEntity {
    /**
     *
     */
    private static final long serialVersionUID = 1257876475317306901L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CUSTOMER_APPROVAL_HISTORY_SEQ")
    @SequenceGenerator(sequenceName = "CUSTOMER_APPROVAL_HISTORY_SEQ", name = "CUSTOMER_APPROVAL_HISTORY_SEQ", initialValue = 1, allocationSize = 1)
    @Column(name = "CUSTOMER_APPROVAL_HISTORY_ID")
    private long customerApprovalHistoryId;

    @Column(name = "CLASS_PK")
    private Long classPk;

    @Column(name = "SENT_USER")
    private String sentUser;

    @Column(name = "APPROVAL_TYPE")
    private Integer approvalType;

    @Column(name = "SENT_DATE")
    private Instant sentDate;

    @Column(name = "STATUS")
    private int status;

    @Transient
    private User userSent;

    @Transient
    private User userApproval;
}
