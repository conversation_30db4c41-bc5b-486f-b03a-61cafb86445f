package com.mb.laos.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mb.laos.api.request.Request;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NotificationNumberDTO extends Request implements Serializable {
    private static final long serialVersionUID = 2361109732412055517L;

    private long numberNotificationLimit;

    private long numberPremiumAccountRevert;

    private long numberSmsBalanceChargeFail;
}
