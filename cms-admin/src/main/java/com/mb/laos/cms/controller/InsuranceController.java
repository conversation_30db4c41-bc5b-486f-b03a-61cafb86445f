package com.mb.laos.cms.controller;

import com.mb.laos.annotation.InboundRequestLog;
import com.mb.laos.enums.InsuranceType;
import com.mb.laos.model.dto.LviHealthPackageDTO;
import com.mb.laos.model.dto.LviVehiclePackageDTO;
import com.mb.laos.model.dto.LviVehicleTypeDTO;
import com.mb.laos.service.InsuranceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping(value = "/insurance")
@Slf4j
@RequiredArgsConstructor
public class InsuranceController {
    private final InsuranceService insuranceService;

    @GetMapping("/lvi/type")
    @InboundRequestLog
    public ResponseEntity<List<InsuranceType>> getInsuranceType(HttpServletRequest request) {
        return ResponseEntity.ok(InsuranceType.getInsuranceType());
    }

    @GetMapping("/lvi/vehicle/package")
    @InboundRequestLog
    public ResponseEntity<LviVehiclePackageDTO> getVehiclePackage(HttpServletRequest request) {
        return ResponseEntity.ok(insuranceService.getLviVehiclePackage());
    }

    @GetMapping("/lvi/vehicle/type")
    @InboundRequestLog
    public ResponseEntity<LviVehicleTypeDTO> getVehicleType(HttpServletRequest request) {
        return ResponseEntity.ok(insuranceService.getLviVehicleType());
    }

    @GetMapping("/lvi/health/package")
    @InboundRequestLog
    public ResponseEntity<LviHealthPackageDTO> getHealthPackage(HttpServletRequest request) {
        return ResponseEntity.ok(insuranceService.getLviHealthPackage());
    }
}
