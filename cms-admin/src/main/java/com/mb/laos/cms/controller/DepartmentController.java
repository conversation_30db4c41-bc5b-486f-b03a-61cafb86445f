package com.mb.laos.cms.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import com.mb.laos.model.dto.DepartmentDTO;
import com.mb.laos.model.search.DepartmentSearch;
import com.mb.laos.service.DepartmentService;
import lombok.RequiredArgsConstructor;

import java.util.List;

@Controller
@RequestMapping("/department")
@RequiredArgsConstructor
public class DepartmentController {
    
    private final DepartmentService departmentService;
    
    @PostMapping("/create")
    @PreAuthorize("hasPrivilege('DEPARTMENT_CREATE')")
    public ResponseEntity<DepartmentDTO> create(@RequestBody @Valid DepartmentDTO dto) {
        return new ResponseEntity<>(this.departmentService.create(dto), HttpStatus.OK);
    }
    
    @PostMapping("/lock")
    @PreAuthorize("hasPrivilege('DEPARTMENT_LOCK')")
    public ResponseEntity<Boolean> lock(@RequestBody DepartmentDTO dto) {
        return new ResponseEntity<>(this.departmentService.lock(dto.getDepartmentId()), HttpStatus.OK);
    }
    
    @PostMapping("/unlock")
    @PreAuthorize("hasPrivilege('DEPARTMENT_UNLOCK')")
    public ResponseEntity<Boolean> unLock(@RequestBody DepartmentDTO dto) {
        return new ResponseEntity<>(this.departmentService.unLock(dto.getDepartmentId()), HttpStatus.OK);
    }
    
    @PostMapping("/search")
    @PreAuthorize("hasPrivilege('DEPARTMENT_READ')")
    public ResponseEntity<Page<DepartmentDTO>> search(@RequestBody DepartmentSearch search) {
        return new ResponseEntity<>(this.departmentService.search(search), HttpStatus.OK);
    }
    
    @PostMapping("/delete")
    @PreAuthorize("hasPrivilege('DEPARTMENT_DELETE')")
    public ResponseEntity<Boolean> delete(@RequestBody DepartmentDTO dto) {
        return new ResponseEntity<>(this.departmentService.delete(dto.getDepartmentId()), HttpStatus.OK);
    }
    
    @PostMapping("/detail")
    @PreAuthorize("hasPrivilege('DEPARTMENT_READ')")
    public ResponseEntity<DepartmentDTO> detail(@RequestBody DepartmentDTO dto) {
        return new ResponseEntity<>(this.departmentService.findById(dto.getDepartmentId()),
                        HttpStatus.OK);
    }
    
    @PostMapping("/update")
    @PreAuthorize("hasPrivilege('DEPARTMENT_WRITE')")
    public ResponseEntity<DepartmentDTO> update(@RequestBody @Valid DepartmentDTO dto) {
        return new ResponseEntity<>(this.departmentService.update(dto), HttpStatus.OK);
    }
    
    @PostMapping("/search-by-keyword")
    @PreAuthorize("hasPrivilege('DEPARTMENT_READ')")
    public ResponseEntity<Page<DepartmentDTO>> update(@RequestBody DepartmentSearch search) {
        return new ResponseEntity<>(this.departmentService.searchByKeyword(search), HttpStatus.OK);
    }

    @PostMapping("/all")
    //    @PreAuthorize("hasPrivilege('DEPARTMENT_READ')")
    public ResponseEntity<List<DepartmentDTO>> getAll(HttpServletRequest request) {
        return ResponseEntity.ok().body(this.departmentService.findAllDepartment());
    }

//    @PostMapping("/export")
////    @PreAuthorize("hasPrivilege('DEPARTMENT_READ')")
//    public ResponseEntity<Void> export(HttpServletResponse response, @RequestBody @Valid DepartmentSearch departmentSearch) {
//        this.departmentService.export(response, departmentSearch);
//        return ResponseEntity.ok().build();
//    }
}
