package com.mb.laos.schedule.impl;

import com.mb.laos.schedule.Worker;
import com.mb.laos.service.VersionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Instant;

@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "scheduling.version-update-status", name = "enabled", havingValue = "true", matchIfMissing = false)
public class VersionUpdateStatusImpl implements Worker {
    private final VersionService versionService;

    @Override
    @Scheduled(cron = "${scheduling.version-update-status.cron}")
    @SchedulerLock(name = "version-update-status", lockAtLeastFor = "${scheduling.version-update-status.lock-at-least}", lockAtMostFor = "${scheduling.version-update-status.lock-at-most}")
    @Async("mbScheduleExecutor")
    public void run() {
        try {
            _log.info("----- VersionUpdateStatus thread is started at: {} --------", Instant.now());

            this.versionService.updateStatus();

            _log.info("----- VersionUpdateStatus thread is finished at: {} --------", Instant.now());

        } catch (Exception e) {
            _log.error("VersionUpdateStatus thread error", e);
        }
    }
}
