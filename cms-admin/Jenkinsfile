#!/usr/bin/env groovy

static def fillEnv(BRANCH_NAME) {
    def env = "dev"
    switch (BRANCH_NAME) {
        case ~ '.*develop.*|.*feature/build.*':
            env = "dev"
            break
        case ~ '.*qa.*':
            env = "qa"
            break
        case ~ '.*sit.*':
            env = "sit"
            break
        case ~ '.*prod.*':
            env = "prod"
            break
    }

    return env
}

static GString fillImgTag(BRANCH_NAME) {
    def now = new Date().format("yyyyMMddHHmm", TimeZone.getTimeZone('UTC'))
    GString tag = "0.0.${now}-${BRANCH_NAME.tokenize('/').pop()}"
    switch (BRANCH_NAME) {
        case ~ '.*develop.*|.*feature/build.*':
            tag = "0.0.${now}-${BRANCH_NAME.tokenize('/').pop()}"
            break
        case ~ '.*qa.*':
            tag = "0.1.${now}-${BRANCH_NAME.tokenize('/').pop()}"
            break
        case ~ '.*sit.*':
            tag = "0.2.${now}-${BRANCH_NAME.tokenize('/').pop()}"
            break
        case ~ '.*prod.*':
            tag = "1.0.${now}-${BRANCH_NAME.tokenize('/').pop()}"
            break
    }

    return tag
}

pipeline {

    agent any

    environment {
        PROJECT = "mb-laos"
        APP_ENV = fillEnv(env.GIT_BRANCH)
        SERVICE_NAME = "${env.GIT_URL.replaceFirst(/^.*\/([^\/]+?).git$/, '$1').replaceAll('_', '-')}-admin"
        HELM_REPO_APP = "${PROJECT}-repo"
        NAMESPACE = "${PROJECT}-${APP_ENV}"
        IMG_TAG = fillImgTag(env.GIT_BRANCH)
        SERVICE_PORT = 80
        K8S_AUTH = "k8s_auth"
        REGISTRY_DOMAIN = "registry.evotek.vn"
        REGISTRY_AUTH = "harbor_registry_auth"
        CHART_DOMAIN = "nexus.evotek.vn"
        CHART_REPO = "evotek_helm_repo"
        CHART_AUTH = "helm_chart_auth"
        IMAGE_PULL_SECRET = "registry-pull-secret"
    }

    stages {

        stage('check env') {
            steps {
                sh "java -version"
                sh "docker -v"
                sh "helm version"
            }
        }

        stage('clean') {
            steps {
                sh "chmod +x mvnw"
                sh "./mvnw clean"
            }
        }

        stage('build application') {
            steps {
                sh "./mvnw verify -Pprod -DskipTests"
                archiveArtifacts artifacts: '**/cms-admin/target/*.jar', fingerprint: true
            }
        }

        stage('build image') {
            steps {
                withCredentials([usernamePassword(credentialsId: "${REGISTRY_AUTH}", passwordVariable: 'password', usernameVariable: 'username')]) {
                    script {
                        GString imageName = "${REGISTRY_DOMAIN}/${PROJECT}/${SERVICE_NAME}:${IMG_TAG}"
                        sh "docker build -f cms-admin/Dockerfile -t ${imageName} ."
                        sh "docker push ${imageName}"
                        sh "docker rmi ${imageName}"
                    }
                }
            }
        }

        stage('build chart') {
            steps {
                withCredentials([usernamePassword(credentialsId: "${CHART_AUTH}", passwordVariable: 'password', usernameVariable: 'username')]) {
                    sh """
                        helm repo add ${CHART_REPO} https://${CHART_DOMAIN}/repository/${CHART_REPO}    \
                                --username ${username}  \
                                --password ${password}  \
                            | true
                    """
                    sh "helm create ${SERVICE_NAME}"
                    sh "cp ./cms-admin/cicd/${APP_ENV}/values.yaml ./${SERVICE_NAME}"
                    sh "cp ./cms-admin/cicd/deployment.yaml ./${SERVICE_NAME}/templates"
                    sh "sed -i 's|DEFAULT_CHART|${SERVICE_NAME}|g' ./${SERVICE_NAME}/templates/deployment.yaml"
                    sh """sed -i 's|tag: ""|tag: ${IMG_TAG}|g' ./${SERVICE_NAME}/values.yaml"""
                    sh "helm lint ./${SERVICE_NAME}"
                    sh "helm package ./${SERVICE_NAME} --version ${IMG_TAG}"
                    sh """
                        curl -u ${username}:${password} https://${CHART_DOMAIN}/repository/${CHART_REPO}/   \
                            --upload-file ${SERVICE_NAME}-${IMG_TAG}.tgz -v
                    """
                    sh "rm ./*.tgz"
                }
            }
        }

        stage('deploy') {
            steps {
                configFileProvider([configFile(fileId: "${K8S_AUTH}", targetLocation: "admin.kubeconfig")]) {
                    script {
                        GString commonArgs = "-n ${NAMESPACE} --kubeconfig admin.kubeconfig"
                        GString helmArgs = """${SERVICE_NAME} ${CHART_REPO}/${SERVICE_NAME} --version ${IMG_TAG}    \
                            --set imagePullSecrets[0].name=${IMAGE_PULL_SECRET} \
                            --set service.targetPort=${SERVICE_PORT}    \
                            --wait"""

                        sh "helm repo list"
                        sh "helm repo update"
                        sh "helm search repo ${CHART_REPO}"
                        sh "helm history ${SERVICE_NAME} ${commonArgs} | true"
                        sh "helm upgrade --install ${helmArgs} ${commonArgs}"
                    }
                }
            }
        }
    }
}
