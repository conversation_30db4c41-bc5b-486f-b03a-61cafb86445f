# Gi<PERSON>i thích chi tiết class FeeServiceImpl

## Tổng quan
Class `FeeServiceImpl` là **fee calculation engine** ch<PERSON>h của hệ thống MB Laos, xử lý tất cả các loại phí giao dịch từ đơn giản đến phức tạp.

## Vai trò trong hệ thống
```
Transaction Request → FeeServiceImpl → Fee Calculation → Transaction Processing
```

## Phân loại fee types được hỗ trợ

### 1. Internal Fees (Phí nội bộ)
| Fee Type | Mô tả | Calculation Method |
|----------|-------|-------------------|
| **TOPUP** | Phí nạp tiền điện thoại | Merchant-specific fees |
| **INTERNAL_QR_BILLING** | Phí QR nội bộ | Merchant + General fees |
| **TRANSFER_INTERNAL_BANK** | <PERSON><PERSON> chuyển tiền nội bộ | General fees |

### 2. External Fees (Phí external)
| Fee Type | Mô tả | Calculation Method |
|----------|-------|-------------------|
| **LAPNET_QR_BILLING** | Phí QR qua LAPNET | T24/LAPNET fees |
| **TRANSFER_INTER_BANK** | Phí chuyển tiền liên ngân hàng | General fees |

### 3. Bill Payment Fees (Phí thanh toán hóa đơn)
| Fee Type | Mô tả | Special Rules |
|----------|-------|---------------|
| **WATER_BILLING** | Phí thanh toán nước | Min amount = bill amount |
| **ELECTRIC_BILLING** | Phí thanh toán điện | Min amount = bill amount |

## Dependency Injection Analysis

### Core Repositories:
```java
private final FeeRepository feeRepository;                    // Fee configurations
private final FeeRateRepository feeRateRepository;            // Fee rates với amount ranges
private final TransactionFeeTypeRepository transactionFeeTypeRepository; // Fee type definitions
private final TransactionRepository transactionRepository;    // Transaction history
private final MerchantRepository merchantRepository;          // Merchant data
private final MoneyAccountRepository moneyAccountRepository;  // Customer accounts
private final CommonRepository commonRepository;              // Master data
```

### External Services:
```java
private final ApiGeeTransferService apiGeeTransferService;    // T24/LAPNET integration
```

### Configuration Properties:
```java
private final MBApiConstantProperties mbApiConstantProperties;           // MB constants
private final CustomerProperties customerProperties;                     // Customer settings
private final InternationalPaymentProperties internationalPaymentProperties; // International rules
```

## Core Methods Analysis

### 1. Method `getFeeOfTransactionV2()` - Main Entry Point

#### Purpose:
Main fee calculation method với routing logic cho different transaction types.

#### Flow:
```java
1. enrichCurrency(request) → Get customer currency
2. getCustomerLogin() → Get customer info
3. Route based on transaction type:
   - LAPNET_QR_BILLING → getFeeQrBilling()
   - TOPUP/INTERNAL_QR_BILLING → Merchant-specific logic
   - Others → General fee calculation
4. enrichFeeTransactionResponse() → Final calculations
```

#### Business Rules:
- **TOPUP**: Merchant fees only, no general fees
- **INTERNAL_QR_BILLING**: Combine merchant + general fees
- **LAPNET_QR_BILLING**: External fee calculation via T24/LAPNET

### 2. Method `getFeeQrBilling()` - LAPNET QR Fee Calculator

#### Purpose:
Handle complex LAPNET QR billing với international support.

#### Validation Steps:
```java
1. Validate required fields: beneficiaryCurrency, beneficiaryQrValue, beneficiaryBankCode
2. Block internal MB Bank transactions (must use internal QR)
3. Check international vs domestic transaction
4. Validate minimum amount for international
5. Verify account via ApiGee
```

#### Routing Logic:
```java
if (common.isPresent()) {
    // International transaction
    return getFeeLapnetInterPayment(request, accNumberVerifyDTO);
} else {
    // Domestic transaction  
    return getFeeQrLapnetBilling(request, accNumberVerifyDTO, currency);
}
```

### 3. Method `getFeeLapnetInterPayment()` - International Fee Calculator

#### Purpose:
Calculate fees cho international QR transactions với exchange rate.

#### Complex Logic:
```java
1. Extract exchange rate LAK → KHR
2. Convert transaction amount
3. Validate currency = KHR only
4. Filter fee tiers cho both LAK và KHR
5. Calculate fees:
   - Percentage fee: amount * rate / 100
   - Fixed fee: predefined amounts
6. Return both LAK fee và foreign fee
```

#### Response Structure:
```java
FeeTransactionResponse.builder()
    .fee(lakFeeAmount)                    // LAK fee
    .foreignFee(khrFeeAmount)            // KHR fee
    .actualTransactionAmount(khrTotal)    // KHR total
    .rate(exchangeRate)                   // LAK→KHR rate
    .build();
```

### 4. Method `compareFeeOfTransaction()` - Core Fee Engine

#### Purpose:
Core fee calculation engine với amount range matching.

#### Algorithm:
```java
1. Validate max 2 fees (1 fee + 1 discount)
2. Get active fee rates at current time
3. Filter by currency
4. Find matching amount range:
   feeRate.min <= amount <= feeRate.max
5. Calculate fee with VAT:
   fee = baseAmount * (1 + VAT%)
6. Handle currency formatting:
   - LAK: round to integer
   - Foreign: preserve decimals
```

#### Fee vs Discount Logic:
```java
if (ConfigurationFeeType.FEE) {
    // Calculate fee with VAT
    response.setFee(calculatedFee);
} else {
    // Set discount values
    response.setDiscount(discountPercent);
    response.setDiscountFixed(discountFixed);
}
```

### 5. Method `enrichFeeTransactionResponse()` - Final Calculator

#### Purpose:
Calculate final amounts với business rules và validation.

#### Calculation Formula:
```java
// Step 1: Calculate post-discount amount
postDiscountAmount = transactionAmount - discountFixed - (transactionAmount * discount% / 100)

// Step 2: Currency-specific rounding
if (defaultCurrency) {
    postDiscountAmount = Math.ceil(postDiscountAmount);  // Round up for LAK
}

// Step 3: Calculate actual amount
actualTransactionAmount = postDiscountAmount + fee
```

#### Business Rules:
```java
// Rule 1: Discount validation
if (postDiscountAmount <= 0) {
    throw BadRequestAlertException(MSG101280);  // Discount too high
}

// Rule 2: QR Billing limit
if (INTERNAL_QR_BILLING && actualAmount >= 2 * originalAmount) {
    throw BadRequestAlertException(MSG101281);  // Fee + discount too high
}

// Rule 3: Bill payment minimum
if (WATER_BILLING || ELECTRIC_BILLING) {
    if (actualAmount < originalAmount) {
        actualAmount = originalAmount;  // Ensure minimum bill amount
    }
}
```

## Fee Configuration Structure

### Fee Entity Hierarchy:
```
TransactionFeeType (TOPUP, QR_BILLING...)
    ↓
Fee (Configuration: FEE vs DISCOUNT)
    ↓
FeeRate (Amount ranges, VAT, Effective dates)
```

### Fee Rate Matching:
```java
// Find applicable fee rate
feeRates.stream()
    .filter(rate -> rate.min <= amount <= rate.max)
    .filter(rate -> rate.currency.equals(currency))
    .filter(rate -> rate.effectiveDate <= now <= rate.expiryDate)
    .findFirst()
```

## Error Handling Strategy

### Validation Errors:
- **MSG101156**: Missing telco code for TOPUP/INTERNAL_QR_BILLING
- **MSG1103**: Merchant not found
- **MSG101037**: Invalid beneficiary currency
- **MSG1134**: Invalid QR value
- **MSG101065**: Missing beneficiary bank code

### Business Rule Errors:
- **MSG101280**: Discount exceeds transaction amount
- **MSG101281**: Total fee + discount too high for QR billing
- **MSG100165**: Unsupported currency for international
- **MSG1143**: No applicable fee tier found

### Integration Errors:
- **MSG1036**: Account verification failed via T24/LAPNET
- **MSG101066**: Invalid bank routing (internal bank via LAPNET)

## Performance Considerations

### Database Queries:
- **Fee lookup**: Indexed by transactionFeeTypeId + status
- **Fee rate lookup**: Indexed by feeId + effectiveDate
- **Merchant lookup**: Indexed by merchantCode + serviceType

### Caching Opportunities:
- **Fee configurations**: Rarely change, good for caching
- **Exchange rates**: Cache with TTL
- **Merchant data**: Cache merchant-fee mappings

### External API Calls:
- **T24/LAPNET verification**: Network latency impact
- **Retry logic**: Built into ApiGeeTransferService
- **Timeout handling**: Circuit breaker patterns

Class `FeeServiceImpl` là **comprehensive fee engine** hỗ trợ complex business rules và multiple integration points!
