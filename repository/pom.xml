<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.mb</groupId>
		<artifactId>parent</artifactId>
		<version>${app.version}</version>
	</parent>

	<groupId>com.mb.laos.common</groupId>
	<artifactId>repository</artifactId>
	<name>repository</name>
	<description>Local repository</description>

	<properties>
		<java-crypto.version>0.13.1</java-crypto.version>
		<gateway-client.version>4.3.4</gateway-client.version>
	</properties>

	<dependencies>		
		<dependency>
			<groupId>com.cossacklabs.com</groupId>
			<artifactId>java-crypto</artifactId>
			<version>${java-crypto.version}</version>
		</dependency>
		
		<dependency>
			<groupId>dynamic.key</groupId>
			<artifactId>gateway-client</artifactId>
			<version>${gateway-client.version}</version>
		</dependency>
	</dependencies>

	<repositories>
		<repository>
			<id>local</id>
			<url>file:${project.basedir}/src/repository</url>
		</repository>
	</repositories>
</project>